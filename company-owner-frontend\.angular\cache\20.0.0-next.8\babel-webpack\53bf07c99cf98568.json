{"ast": null, "code": "/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { $localize } from './localize-CajB9YLv.mjs';\n\n// Attach $localize to the global context, as a side-effect of this module.\nglobalThis.$localize = $localize;\nexport { $localize };", "map": {"version": 3, "names": ["$localize", "globalThis"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/@angular/localize/fesm2022/init.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { $localize } from './localize-CajB9YLv.mjs';\n\n// Attach $localize to the global context, as a side-effect of this module.\nglobalThis.$localize = $localize;\n\nexport { $localize };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,SAAS,QAAQ,yBAAyB;;AAEnD;AACAC,UAAU,CAACD,SAAS,GAAGA,SAAS;AAEhC,SAASA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}