{"ast": null, "code": "/** Cached result of whether the user's browser supports passive event listeners. */\nlet supportsPassiveEvents;\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\nfunction supportsPassiveEventListeners() {\n  if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n    try {\n      window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n        get: () => supportsPassiveEvents = true\n      }));\n    } finally {\n      supportsPassiveEvents = supportsPassiveEvents || false;\n    }\n  }\n  return supportsPassiveEvents;\n}\n/**\n * Normalizes an `AddEventListener` object to something that can be passed\n * to `addEventListener` on any browser, no matter whether it supports the\n * `options` parameter.\n * @param options Object to be normalized.\n */\nfunction normalizePassiveListenerOptions(options) {\n  return supportsPassiveEventListeners() ? options : !!options.capture;\n}\nexport { normalizePassiveListenerOptions as n, supportsPassiveEventListeners as s };", "map": {"version": 3, "names": ["supportsPassiveEvents", "supportsPassiveEventListeners", "window", "addEventListener", "Object", "defineProperty", "get", "normalizePassiveListenerOptions", "options", "capture", "n", "s"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/@angular/cdk/fesm2022/passive-listeners-esHZRgIN.mjs"], "sourcesContent": ["/** Cached result of whether the user's browser supports passive event listeners. */\nlet supportsPassiveEvents;\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\nfunction supportsPassiveEventListeners() {\n    if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n        try {\n            window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n                get: () => (supportsPassiveEvents = true),\n            }));\n        }\n        finally {\n            supportsPassiveEvents = supportsPassiveEvents || false;\n        }\n    }\n    return supportsPassiveEvents;\n}\n/**\n * Normalizes an `AddEventListener` object to something that can be passed\n * to `addEventListener` on any browser, no matter whether it supports the\n * `options` parameter.\n * @param options Object to be normalized.\n */\nfunction normalizePassiveListenerOptions(options) {\n    return supportsPassiveEventListeners() ? options : !!options.capture;\n}\n\nexport { normalizePassiveListenerOptions as n, supportsPassiveEventListeners as s };\n"], "mappings": "AAAA;AACA,IAAIA,qBAAqB;AACzB;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAAA,EAAG;EACrC,IAAID,qBAAqB,IAAI,IAAI,IAAI,OAAOE,MAAM,KAAK,WAAW,EAAE;IAChE,IAAI;MACAA,MAAM,CAACC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAEC,MAAM,CAACC,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;QACvEC,GAAG,EAAEA,CAAA,KAAON,qBAAqB,GAAG;MACxC,CAAC,CAAC,CAAC;IACP,CAAC,SACO;MACJA,qBAAqB,GAAGA,qBAAqB,IAAI,KAAK;IAC1D;EACJ;EACA,OAAOA,qBAAqB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,+BAA+BA,CAACC,OAAO,EAAE;EAC9C,OAAOP,6BAA6B,CAAC,CAAC,GAAGO,OAAO,GAAG,CAAC,CAACA,OAAO,CAACC,OAAO;AACxE;AAEA,SAASF,+BAA+B,IAAIG,CAAC,EAAET,6BAA6B,IAAIU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}