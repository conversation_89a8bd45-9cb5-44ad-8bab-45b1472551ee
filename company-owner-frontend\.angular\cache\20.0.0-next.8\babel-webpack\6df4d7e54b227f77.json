{"ast": null, "code": "import _asyncToGenerator from \"D:/employee-survey-app/company-owner-frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i3 from '@angular/common';\nimport { LOCATION_INITIALIZED, HashLocationStrategy, LocationStrategy, ViewportScroller, Location, PathLocationStrategy } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ɵINTERNAL_APPLICATION_ERROR_HANDLER as _INTERNAL_APPLICATION_ERROR_HANDLER, ɵRuntimeError as _RuntimeError, ɵɵsanitizeUrlOrResourceUrl as __sanitizeUrlOrResourceUrl, booleanAttribute, HostListener, Input, HostBinding, Attribute, Directive, EventEmitter, Output, ContentChildren, Optional, createEnvironmentInjector, Injectable, InjectionToken, ɵperformanceMarkFeature as _performanceMarkFeature, makeEnvironmentProviders, APP_BOOTSTRAP_LISTENER, ENVIRONMENT_INITIALIZER, provideAppInitializer, Injector, ApplicationRef, NgZone, SkipSelf, NgModule } from '@angular/core';\nimport { NavigationEnd, isUrlTree, Router, ActivatedRoute, RouterConfigLoader, IMPERATIVE_NAVIGATION, NavigationStart, NavigationSkipped, NavigationSkippedCode, Scroll, UrlSerializer, NavigationTransitions, ROUTES, afterNextNavigation, ROUTER_CONFIGURATION, NAVIGATION_ERROR_HANDLER, RoutedComponentInputBinder, INPUT_BINDER, createViewTransition, CREATE_VIEW_TRANSITION, VIEW_TRANSITION_OPTIONS, stringifyEvent, DefaultUrlSerializer, ChildrenOutletContexts, RouterOutlet, ɵEmptyOutletComponent as _EmptyOutletComponent } from './router-BxrGTdzL.mjs';\nimport { Subject, of, from } from 'rxjs';\nimport { mergeAll, catchError, filter, concatMap, mergeMap } from 'rxjs/operators';\n\n/**\n * @description\n *\n * When applied to an element in a template, makes that element a link\n * that initiates navigation to a route. Navigation opens one or more routed components\n * in one or more `<router-outlet>` locations on the page.\n *\n * Given a route configuration `[{ path: 'user/:name', component: UserCmp }]`,\n * the following creates a static link to the route:\n * `<a routerLink=\"/user/bob\">link to user component</a>`\n *\n * You can use dynamic values to generate the link.\n * For a dynamic link, pass an array of path segments,\n * followed by the params for each segment.\n * For example, `['/team', teamId, 'user', userName, {details: true}]`\n * generates a link to `/team/11/user/bob;details=true`.\n *\n * Multiple static segments can be merged into one term and combined with dynamic segments.\n * For example, `['/team/11/user', userName, {details: true}]`\n *\n * The input that you provide to the link is treated as a delta to the current URL.\n * For instance, suppose the current URL is `/user/(box//aux:team)`.\n * The link `<a [routerLink]=\"['/user/jim']\">Jim</a>` creates the URL\n * `/user/(jim//aux:team)`.\n * See {@link Router#createUrlTree} for more information.\n *\n * @usageNotes\n *\n * You can use absolute or relative paths in a link, set query parameters,\n * control how parameters are handled, and keep a history of navigation states.\n *\n * ### Relative link paths\n *\n * The first segment name can be prepended with `/`, `./`, or `../`.\n * * If the first segment begins with `/`, the router looks up the route from the root of the\n *   app.\n * * If the first segment begins with `./`, or doesn't begin with a slash, the router\n *   looks in the children of the current activated route.\n * * If the first segment begins with `../`, the router goes up one level in the route tree.\n *\n * ### Setting and handling query params and fragments\n *\n * The following link adds a query parameter and a fragment to the generated URL:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" fragment=\"education\">\n *   link to user component\n * </a>\n * ```\n * By default, the directive constructs the new URL using the given query parameters.\n * The example generates the link: `/user/bob?debug=true#education`.\n *\n * You can instruct the directive to handle query parameters differently\n * by specifying the `queryParamsHandling` option in the link.\n * Allowed values are:\n *\n *  - `'merge'`: Merge the given `queryParams` into the current query params.\n *  - `'preserve'`: Preserve the current query params.\n *\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" queryParamsHandling=\"merge\">\n *   link to user component\n * </a>\n * ```\n *\n * `queryParams`, `fragment`, `queryParamsHandling`, `preserveFragment`, and `relativeTo`\n * cannot be used when the `routerLink` input is a `UrlTree`.\n *\n * See {@link UrlCreationOptions#queryParamsHandling}.\n *\n * ### Preserving navigation history\n *\n * You can provide a `state` value to be persisted to the browser's\n * [`History.state` property](https://developer.mozilla.org/en-US/docs/Web/API/History#Properties).\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [state]=\"{tracingId: 123}\">\n *   link to user component\n * </a>\n * ```\n *\n * Use {@link Router#getCurrentNavigation} to retrieve a saved\n * navigation-state value. For example, to capture the `tracingId` during the `NavigationStart`\n * event:\n *\n * ```ts\n * // Get NavigationStart events\n * router.events.pipe(filter(e => e instanceof NavigationStart)).subscribe(e => {\n *   const navigation = router.getCurrentNavigation();\n *   tracingService.trace({id: navigation.extras.state.tracingId});\n * });\n * ```\n *\n * ### RouterLink compatible custom elements\n *\n * In order to make a custom element work with routerLink, the corresponding custom\n * element must implement the `href` attribute and must list `href` in the array of\n * the static property/getter `observedAttributes`.\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass RouterLink {\n  router;\n  route;\n  tabIndexAttribute;\n  renderer;\n  el;\n  locationStrategy;\n  /**\n   * Represents an `href` attribute value applied to a host element,\n   * when a host element is an `<a>`/`<area>` tag or a compatible custom element.\n   * For other tags, the value is `null`.\n   */\n  href = null;\n  /**\n   * Represents the `target` attribute on a host element.\n   * This is only used when the host element is\n   * an `<a>`/`<area>` tag or a compatible custom element.\n   */\n  target;\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * @see {@link UrlCreationOptions#queryParams}\n   * @see {@link Router#createUrlTree}\n   */\n  queryParams;\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * @see {@link UrlCreationOptions#fragment}\n   * @see {@link Router#createUrlTree}\n   */\n  fragment;\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * @see {@link UrlCreationOptions#queryParamsHandling}\n   * @see {@link Router#createUrlTree}\n   */\n  queryParamsHandling;\n  /**\n   * Passed to {@link Router#navigateByUrl} as part of the\n   * `NavigationBehaviorOptions`.\n   * @see {@link NavigationBehaviorOptions#state}\n   * @see {@link Router#navigateByUrl}\n   */\n  state;\n  /**\n   * Passed to {@link Router#navigateByUrl} as part of the\n   * `NavigationBehaviorOptions`.\n   * @see {@link NavigationBehaviorOptions#info}\n   * @see {@link Router#navigateByUrl}\n   */\n  info;\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * Specify a value here when you do not want to use the default value\n   * for `routerLink`, which is the current activated route.\n   * Note that a value of `undefined` here will use the `routerLink` default.\n   * @see {@link UrlCreationOptions#relativeTo}\n   * @see {@link Router#createUrlTree}\n   */\n  relativeTo;\n  /** Whether a host element is an `<a>`/`<area>` tag or a compatible custom element. */\n  isAnchorElement;\n  subscription;\n  /** @internal */\n  onChanges = new Subject();\n  applicationErrorHandler = inject(_INTERNAL_APPLICATION_ERROR_HANDLER);\n  constructor(router, route, tabIndexAttribute, renderer, el, locationStrategy) {\n    this.router = router;\n    this.route = route;\n    this.tabIndexAttribute = tabIndexAttribute;\n    this.renderer = renderer;\n    this.el = el;\n    this.locationStrategy = locationStrategy;\n    const tagName = el.nativeElement.tagName?.toLowerCase();\n    this.isAnchorElement = tagName === 'a' || tagName === 'area' || !!(\n    // Avoid breaking in an SSR context where customElements might not be defined.\n    typeof customElements === 'object' &&\n    // observedAttributes is an optional static property/getter on a custom element.\n    // The spec states that this must be an array of strings.\n    customElements.get(tagName)?.observedAttributes?.includes?.('href'));\n    if (this.isAnchorElement) {\n      this.subscription = router.events.subscribe(s => {\n        if (s instanceof NavigationEnd) {\n          this.updateHref();\n        }\n      });\n    } else {\n      this.setTabIndexIfNotOnNativeEl('0');\n    }\n  }\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * @see {@link UrlCreationOptions#preserveFragment}\n   * @see {@link Router#createUrlTree}\n   */\n  preserveFragment = false;\n  /**\n   * Passed to {@link Router#navigateByUrl} as part of the\n   * `NavigationBehaviorOptions`.\n   * @see {@link NavigationBehaviorOptions#skipLocationChange}\n   * @see {@link Router#navigateByUrl}\n   */\n  skipLocationChange = false;\n  /**\n   * Passed to {@link Router#navigateByUrl} as part of the\n   * `NavigationBehaviorOptions`.\n   * @see {@link NavigationBehaviorOptions#replaceUrl}\n   * @see {@link Router#navigateByUrl}\n   */\n  replaceUrl = false;\n  /**\n   * Modifies the tab index if there was not a tabindex attribute on the element during\n   * instantiation.\n   */\n  setTabIndexIfNotOnNativeEl(newTabIndex) {\n    if (this.tabIndexAttribute != null /* both `null` and `undefined` */ || this.isAnchorElement) {\n      return;\n    }\n    this.applyAttributeValue('tabindex', newTabIndex);\n  }\n  /** @nodoc */\n  // TODO(atscott): Remove changes parameter in major version as a breaking change.\n  ngOnChanges(changes) {\n    if (ngDevMode && isUrlTree(this.routerLinkInput) && (this.fragment !== undefined || this.queryParams || this.queryParamsHandling || this.preserveFragment || this.relativeTo)) {\n      throw new _RuntimeError(4016 /* RuntimeErrorCode.INVALID_ROUTER_LINK_INPUTS */, 'Cannot configure queryParams or fragment when using a UrlTree as the routerLink input value.');\n    }\n    if (this.isAnchorElement) {\n      this.updateHref();\n    }\n    // This is subscribed to by `RouterLinkActive` so that it knows to update when there are changes\n    // to the RouterLinks it's tracking.\n    this.onChanges.next(this);\n  }\n  routerLinkInput = null;\n  /**\n   * Commands to pass to {@link Router#createUrlTree} or a `UrlTree`.\n   *   - **array**: commands to pass to {@link Router#createUrlTree}.\n   *   - **string**: shorthand for array of commands with just the string, i.e. `['/route']`\n   *   - **UrlTree**: a `UrlTree` for this link rather than creating one from the commands\n   *     and other inputs that correspond to properties of `UrlCreationOptions`.\n   *   - **null|undefined**: effectively disables the `routerLink`\n   * @see {@link Router#createUrlTree}\n   */\n  set routerLink(commandsOrUrlTree) {\n    if (commandsOrUrlTree == null) {\n      this.routerLinkInput = null;\n      this.setTabIndexIfNotOnNativeEl(null);\n    } else {\n      if (isUrlTree(commandsOrUrlTree)) {\n        this.routerLinkInput = commandsOrUrlTree;\n      } else {\n        this.routerLinkInput = Array.isArray(commandsOrUrlTree) ? commandsOrUrlTree : [commandsOrUrlTree];\n      }\n      this.setTabIndexIfNotOnNativeEl('0');\n    }\n  }\n  /** @nodoc */\n  onClick(button, ctrlKey, shiftKey, altKey, metaKey) {\n    const urlTree = this.urlTree;\n    if (urlTree === null) {\n      return true;\n    }\n    if (this.isAnchorElement) {\n      if (button !== 0 || ctrlKey || shiftKey || altKey || metaKey) {\n        return true;\n      }\n      if (typeof this.target === 'string' && this.target != '_self') {\n        return true;\n      }\n    }\n    const extras = {\n      skipLocationChange: this.skipLocationChange,\n      replaceUrl: this.replaceUrl,\n      state: this.state,\n      info: this.info\n    };\n    // navigateByUrl is mocked frequently in tests... Reduce breakages when adding `catch`\n    this.router.navigateByUrl(urlTree, extras)?.catch(e => {\n      this.applicationErrorHandler(e);\n    });\n    // Return `false` for `<a>` elements to prevent default action\n    // and cancel the native behavior, since the navigation is handled\n    // by the Router.\n    return !this.isAnchorElement;\n  }\n  /** @nodoc */\n  ngOnDestroy() {\n    this.subscription?.unsubscribe();\n  }\n  updateHref() {\n    const urlTree = this.urlTree;\n    this.href = urlTree !== null && this.locationStrategy ? this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(urlTree)) : null;\n    const sanitizedValue = this.href === null ? null :\n    // This class represents a directive that can be added to both `<a>` elements,\n    // as well as other elements. As a result, we can't define security context at\n    // compile time. So the security context is deferred to runtime.\n    // The `ɵɵsanitizeUrlOrResourceUrl` selects the necessary sanitizer function\n    // based on the tag and property names. The logic mimics the one from\n    // `packages/compiler/src/schema/dom_security_schema.ts`, which is used at compile time.\n    //\n    // Note: we should investigate whether we can switch to using `@HostBinding('attr.href')`\n    // instead of applying a value via a renderer, after a final merge of the\n    // `RouterLinkWithHref` directive.\n    __sanitizeUrlOrResourceUrl(this.href, this.el.nativeElement.tagName.toLowerCase(), 'href');\n    this.applyAttributeValue('href', sanitizedValue);\n  }\n  applyAttributeValue(attrName, attrValue) {\n    const renderer = this.renderer;\n    const nativeElement = this.el.nativeElement;\n    if (attrValue !== null) {\n      renderer.setAttribute(nativeElement, attrName, attrValue);\n    } else {\n      renderer.removeAttribute(nativeElement, attrName);\n    }\n  }\n  get urlTree() {\n    if (this.routerLinkInput === null) {\n      return null;\n    } else if (isUrlTree(this.routerLinkInput)) {\n      return this.routerLinkInput;\n    }\n    return this.router.createUrlTree(this.routerLinkInput, {\n      // If the `relativeTo` input is not defined, we want to use `this.route` by default.\n      // Otherwise, we should use the value provided by the user in the input.\n      relativeTo: this.relativeTo !== undefined ? this.relativeTo : this.route,\n      queryParams: this.queryParams,\n      fragment: this.fragment,\n      queryParamsHandling: this.queryParamsHandling,\n      preserveFragment: this.preserveFragment\n    });\n  }\n  static ɵfac = function RouterLink_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterLink)(i0.ɵɵdirectiveInject(Router), i0.ɵɵdirectiveInject(ActivatedRoute), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.LocationStrategy));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLink,\n    selectors: [[\"\", \"routerLink\", \"\"]],\n    hostVars: 1,\n    hostBindings: function RouterLink_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function RouterLink_click_HostBindingHandler($event) {\n          return ctx.onClick($event.button, $event.ctrlKey, $event.shiftKey, $event.altKey, $event.metaKey);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"target\", ctx.target);\n      }\n    },\n    inputs: {\n      target: \"target\",\n      queryParams: \"queryParams\",\n      fragment: \"fragment\",\n      queryParamsHandling: \"queryParamsHandling\",\n      state: \"state\",\n      info: \"info\",\n      relativeTo: \"relativeTo\",\n      preserveFragment: [2, \"preserveFragment\", \"preserveFragment\", booleanAttribute],\n      skipLocationChange: [2, \"skipLocationChange\", \"skipLocationChange\", booleanAttribute],\n      replaceUrl: [2, \"replaceUrl\", \"replaceUrl\", booleanAttribute],\n      routerLink: \"routerLink\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLink, [{\n    type: Directive,\n    args: [{\n      selector: '[routerLink]'\n    }]\n  }], () => [{\n    type: Router\n  }, {\n    type: ActivatedRoute\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i3.LocationStrategy\n  }], {\n    target: [{\n      type: HostBinding,\n      args: ['attr.target']\n    }, {\n      type: Input\n    }],\n    queryParams: [{\n      type: Input\n    }],\n    fragment: [{\n      type: Input\n    }],\n    queryParamsHandling: [{\n      type: Input\n    }],\n    state: [{\n      type: Input\n    }],\n    info: [{\n      type: Input\n    }],\n    relativeTo: [{\n      type: Input\n    }],\n    preserveFragment: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    skipLocationChange: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    replaceUrl: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    routerLink: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event.button', '$event.ctrlKey', '$event.shiftKey', '$event.altKey', '$event.metaKey']]\n    }]\n  });\n})();\n\n/**\n *\n * @description\n *\n * Tracks whether the linked route of an element is currently active, and allows you\n * to specify one or more CSS classes to add to the element when the linked route\n * is active.\n *\n * Use this directive to create a visual distinction for elements associated with an active route.\n * For example, the following code highlights the word \"Bob\" when the router\n * activates the associated route:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n *\n * Whenever the URL is either '/user' or '/user/bob', the \"active-link\" class is\n * added to the anchor tag. If the URL changes, the class is removed.\n *\n * You can set more than one class using a space-separated string or an array.\n * For example:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"class1 class2\">Bob</a>\n * <a routerLink=\"/user/bob\" [routerLinkActive]=\"['class1', 'class2']\">Bob</a>\n * ```\n *\n * To add the classes only when the URL matches the link exactly, add the option `exact: true`:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact:\n * true}\">Bob</a>\n * ```\n *\n * To directly check the `isActive` status of the link, assign the `RouterLinkActive`\n * instance to a template variable.\n * For example, the following checks the status without assigning any CSS classes:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive #rla=\"routerLinkActive\">\n *   Bob {{ rla.isActive ? '(already open)' : ''}}\n * </a>\n * ```\n *\n * You can apply the `RouterLinkActive` directive to an ancestor of linked elements.\n * For example, the following sets the active-link class on the `<div>`  parent tag\n * when the URL is either '/user/jim' or '/user/bob'.\n *\n * ```html\n * <div routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact: true}\">\n *   <a routerLink=\"/user/jim\">Jim</a>\n *   <a routerLink=\"/user/bob\">Bob</a>\n * </div>\n * ```\n *\n * The `RouterLinkActive` directive can also be used to set the aria-current attribute\n * to provide an alternative distinction for active elements to visually impaired users.\n *\n * For example, the following code adds the 'active' class to the Home Page link when it is\n * indeed active and in such case also sets its aria-current attribute to 'page':\n *\n * ```html\n * <a routerLink=\"/\" routerLinkActive=\"active\" ariaCurrentWhenActive=\"page\">Home Page</a>\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass RouterLinkActive {\n  router;\n  element;\n  renderer;\n  cdr;\n  link;\n  links;\n  classes = [];\n  routerEventsSubscription;\n  linkInputChangesSubscription;\n  _isActive = false;\n  get isActive() {\n    return this._isActive;\n  }\n  /**\n   * Options to configure how to determine if the router link is active.\n   *\n   * These options are passed to the `Router.isActive()` function.\n   *\n   * @see {@link Router#isActive}\n   */\n  routerLinkActiveOptions = {\n    exact: false\n  };\n  /**\n   * Aria-current attribute to apply when the router link is active.\n   *\n   * Possible values: `'page'` | `'step'` | `'location'` | `'date'` | `'time'` | `true` | `false`.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-current}\n   */\n  ariaCurrentWhenActive;\n  /**\n   *\n   * You can use the output `isActiveChange` to get notified each time the link becomes\n   * active or inactive.\n   *\n   * Emits:\n   * true  -> Route is active\n   * false -> Route is inactive\n   *\n   * ```html\n   * <a\n   *  routerLink=\"/user/bob\"\n   *  routerLinkActive=\"active-link\"\n   *  (isActiveChange)=\"this.onRouterLinkActive($event)\">Bob</a>\n   * ```\n   */\n  isActiveChange = new EventEmitter();\n  constructor(router, element, renderer, cdr, link) {\n    this.router = router;\n    this.element = element;\n    this.renderer = renderer;\n    this.cdr = cdr;\n    this.link = link;\n    this.routerEventsSubscription = router.events.subscribe(s => {\n      if (s instanceof NavigationEnd) {\n        this.update();\n      }\n    });\n  }\n  /** @nodoc */\n  ngAfterContentInit() {\n    // `of(null)` is used to force subscribe body to execute once immediately (like `startWith`).\n    of(this.links.changes, of(null)).pipe(mergeAll()).subscribe(_ => {\n      this.update();\n      this.subscribeToEachLinkOnChanges();\n    });\n  }\n  subscribeToEachLinkOnChanges() {\n    this.linkInputChangesSubscription?.unsubscribe();\n    const allLinkChanges = [...this.links.toArray(), this.link].filter(link => !!link).map(link => link.onChanges);\n    this.linkInputChangesSubscription = from(allLinkChanges).pipe(mergeAll()).subscribe(link => {\n      if (this._isActive !== this.isLinkActive(this.router)(link)) {\n        this.update();\n      }\n    });\n  }\n  set routerLinkActive(data) {\n    const classes = Array.isArray(data) ? data : data.split(' ');\n    this.classes = classes.filter(c => !!c);\n  }\n  /** @nodoc */\n  ngOnChanges(changes) {\n    this.update();\n  }\n  /** @nodoc */\n  ngOnDestroy() {\n    this.routerEventsSubscription.unsubscribe();\n    this.linkInputChangesSubscription?.unsubscribe();\n  }\n  update() {\n    if (!this.links || !this.router.navigated) return;\n    queueMicrotask(() => {\n      const hasActiveLinks = this.hasActiveLinks();\n      this.classes.forEach(c => {\n        if (hasActiveLinks) {\n          this.renderer.addClass(this.element.nativeElement, c);\n        } else {\n          this.renderer.removeClass(this.element.nativeElement, c);\n        }\n      });\n      if (hasActiveLinks && this.ariaCurrentWhenActive !== undefined) {\n        this.renderer.setAttribute(this.element.nativeElement, 'aria-current', this.ariaCurrentWhenActive.toString());\n      } else {\n        this.renderer.removeAttribute(this.element.nativeElement, 'aria-current');\n      }\n      // Only emit change if the active state changed.\n      if (this._isActive !== hasActiveLinks) {\n        this._isActive = hasActiveLinks;\n        this.cdr.markForCheck();\n        // Emit on isActiveChange after classes are updated\n        this.isActiveChange.emit(hasActiveLinks);\n      }\n    });\n  }\n  isLinkActive(router) {\n    const options = isActiveMatchOptions(this.routerLinkActiveOptions) ? this.routerLinkActiveOptions :\n    // While the types should disallow `undefined` here, it's possible without strict inputs\n    this.routerLinkActiveOptions.exact || false;\n    return link => {\n      const urlTree = link.urlTree;\n      return urlTree ? router.isActive(urlTree, options) : false;\n    };\n  }\n  hasActiveLinks() {\n    const isActiveCheckFn = this.isLinkActive(this.router);\n    return this.link && isActiveCheckFn(this.link) || this.links.some(isActiveCheckFn);\n  }\n  static ɵfac = function RouterLinkActive_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterLinkActive)(i0.ɵɵdirectiveInject(Router), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(RouterLink, 8));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLinkActive,\n    selectors: [[\"\", \"routerLinkActive\", \"\"]],\n    contentQueries: function RouterLinkActive_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, RouterLink, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.links = _t);\n      }\n    },\n    inputs: {\n      routerLinkActiveOptions: \"routerLinkActiveOptions\",\n      ariaCurrentWhenActive: \"ariaCurrentWhenActive\",\n      routerLinkActive: \"routerLinkActive\"\n    },\n    outputs: {\n      isActiveChange: \"isActiveChange\"\n    },\n    exportAs: [\"routerLinkActive\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLinkActive, [{\n    type: Directive,\n    args: [{\n      selector: '[routerLinkActive]',\n      exportAs: 'routerLinkActive'\n    }]\n  }], () => [{\n    type: Router\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: RouterLink,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    links: [{\n      type: ContentChildren,\n      args: [RouterLink, {\n        descendants: true\n      }]\n    }],\n    routerLinkActiveOptions: [{\n      type: Input\n    }],\n    ariaCurrentWhenActive: [{\n      type: Input\n    }],\n    isActiveChange: [{\n      type: Output\n    }],\n    routerLinkActive: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Use instead of `'paths' in options` to be compatible with property renaming\n */\nfunction isActiveMatchOptions(options) {\n  return !!options.paths;\n}\n\n/**\n * @description\n *\n * Provides a preloading strategy.\n *\n * @publicApi\n */\nclass PreloadingStrategy {}\n/**\n * @description\n *\n * Provides a preloading strategy that preloads all modules as quickly as possible.\n *\n * ```ts\n * RouterModule.forRoot(ROUTES, {preloadingStrategy: PreloadAllModules})\n * ```\n *\n * @publicApi\n */\nclass PreloadAllModules {\n  preload(route, fn) {\n    return fn().pipe(catchError(() => of(null)));\n  }\n  static ɵfac = function PreloadAllModules_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PreloadAllModules)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PreloadAllModules,\n    factory: PreloadAllModules.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PreloadAllModules, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * @description\n *\n * Provides a preloading strategy that does not preload any modules.\n *\n * This strategy is enabled by default.\n *\n * @publicApi\n */\nclass NoPreloading {\n  preload(route, fn) {\n    return of(null);\n  }\n  static ɵfac = function NoPreloading_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NoPreloading)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NoPreloading,\n    factory: NoPreloading.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoPreloading, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * The preloader optimistically loads all router configurations to\n * make navigations into lazily-loaded sections of the application faster.\n *\n * The preloader runs in the background. When the router bootstraps, the preloader\n * starts listening to all navigation events. After every such event, the preloader\n * will check if any configurations can be loaded lazily.\n *\n * If a route is protected by `canLoad` guards, the preloaded will not load it.\n *\n * @publicApi\n */\nclass RouterPreloader {\n  router;\n  injector;\n  preloadingStrategy;\n  loader;\n  subscription;\n  constructor(router, injector, preloadingStrategy, loader) {\n    this.router = router;\n    this.injector = injector;\n    this.preloadingStrategy = preloadingStrategy;\n    this.loader = loader;\n  }\n  setUpPreloading() {\n    this.subscription = this.router.events.pipe(filter(e => e instanceof NavigationEnd), concatMap(() => this.preload())).subscribe(() => {});\n  }\n  preload() {\n    return this.processRoutes(this.injector, this.router.config);\n  }\n  /** @nodoc */\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  processRoutes(injector, routes) {\n    const res = [];\n    for (const route of routes) {\n      if (route.providers && !route._injector) {\n        route._injector = createEnvironmentInjector(route.providers, injector, `Route: ${route.path}`);\n      }\n      const injectorForCurrentRoute = route._injector ?? injector;\n      const injectorForChildren = route._loadedInjector ?? injectorForCurrentRoute;\n      // Note that `canLoad` is only checked as a condition that prevents `loadChildren` and not\n      // `loadComponent`. `canLoad` guards only block loading of child routes by design. This\n      // happens as a consequence of needing to descend into children for route matching immediately\n      // while component loading is deferred until route activation. Because `canLoad` guards can\n      // have side effects, we cannot execute them here so we instead skip preloading altogether\n      // when present. Lastly, it remains to be decided whether `canLoad` should behave this way\n      // at all. Code splitting and lazy loading is separate from client-side authorization checks\n      // and should not be used as a security measure to prevent loading of code.\n      if (route.loadChildren && !route._loadedRoutes && route.canLoad === undefined || route.loadComponent && !route._loadedComponent) {\n        res.push(this.preloadConfig(injectorForCurrentRoute, route));\n      }\n      if (route.children || route._loadedRoutes) {\n        res.push(this.processRoutes(injectorForChildren, route.children ?? route._loadedRoutes));\n      }\n    }\n    return from(res).pipe(mergeAll());\n  }\n  preloadConfig(injector, route) {\n    return this.preloadingStrategy.preload(route, () => {\n      let loadedChildren$;\n      if (route.loadChildren && route.canLoad === undefined) {\n        loadedChildren$ = this.loader.loadChildren(injector, route);\n      } else {\n        loadedChildren$ = of(null);\n      }\n      const recursiveLoadChildren$ = loadedChildren$.pipe(mergeMap(config => {\n        if (config === null) {\n          return of(void 0);\n        }\n        route._loadedRoutes = config.routes;\n        route._loadedInjector = config.injector;\n        // If the loaded config was a module, use that as the module/module injector going\n        // forward. Otherwise, continue using the current module/module injector.\n        return this.processRoutes(config.injector ?? injector, config.routes);\n      }));\n      if (route.loadComponent && !route._loadedComponent) {\n        const loadComponent$ = this.loader.loadComponent(route);\n        return from([recursiveLoadChildren$, loadComponent$]).pipe(mergeAll());\n      } else {\n        return recursiveLoadChildren$;\n      }\n    });\n  }\n  static ɵfac = function RouterPreloader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterPreloader)(i0.ɵɵinject(Router), i0.ɵɵinject(i0.EnvironmentInjector), i0.ɵɵinject(PreloadingStrategy), i0.ɵɵinject(RouterConfigLoader));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RouterPreloader,\n    factory: RouterPreloader.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterPreloader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: Router\n  }, {\n    type: i0.EnvironmentInjector\n  }, {\n    type: PreloadingStrategy\n  }, {\n    type: RouterConfigLoader\n  }], null);\n})();\nconst ROUTER_SCROLLER = new InjectionToken('');\nclass RouterScroller {\n  urlSerializer;\n  transitions;\n  viewportScroller;\n  zone;\n  options;\n  routerEventsSubscription;\n  scrollEventsSubscription;\n  lastId = 0;\n  lastSource = IMPERATIVE_NAVIGATION;\n  restoredId = 0;\n  store = {};\n  /** @nodoc */\n  constructor(urlSerializer, transitions, viewportScroller, zone, options = {}) {\n    this.urlSerializer = urlSerializer;\n    this.transitions = transitions;\n    this.viewportScroller = viewportScroller;\n    this.zone = zone;\n    this.options = options;\n    // Default both options to 'disabled'\n    options.scrollPositionRestoration ||= 'disabled';\n    options.anchorScrolling ||= 'disabled';\n  }\n  init() {\n    // we want to disable the automatic scrolling because having two places\n    // responsible for scrolling results race conditions, especially given\n    // that browser don't implement this behavior consistently\n    if (this.options.scrollPositionRestoration !== 'disabled') {\n      this.viewportScroller.setHistoryScrollRestoration('manual');\n    }\n    this.routerEventsSubscription = this.createScrollEvents();\n    this.scrollEventsSubscription = this.consumeScrollEvents();\n  }\n  createScrollEvents() {\n    return this.transitions.events.subscribe(e => {\n      if (e instanceof NavigationStart) {\n        // store the scroll position of the current stable navigations.\n        this.store[this.lastId] = this.viewportScroller.getScrollPosition();\n        this.lastSource = e.navigationTrigger;\n        this.restoredId = e.restoredState ? e.restoredState.navigationId : 0;\n      } else if (e instanceof NavigationEnd) {\n        this.lastId = e.id;\n        this.scheduleScrollEvent(e, this.urlSerializer.parse(e.urlAfterRedirects).fragment);\n      } else if (e instanceof NavigationSkipped && e.code === NavigationSkippedCode.IgnoredSameUrlNavigation) {\n        this.lastSource = undefined;\n        this.restoredId = 0;\n        this.scheduleScrollEvent(e, this.urlSerializer.parse(e.url).fragment);\n      }\n    });\n  }\n  consumeScrollEvents() {\n    return this.transitions.events.subscribe(e => {\n      if (!(e instanceof Scroll)) return;\n      // a popstate event. The pop state event will always ignore anchor scrolling.\n      if (e.position) {\n        if (this.options.scrollPositionRestoration === 'top') {\n          this.viewportScroller.scrollToPosition([0, 0]);\n        } else if (this.options.scrollPositionRestoration === 'enabled') {\n          this.viewportScroller.scrollToPosition(e.position);\n        }\n        // imperative navigation \"forward\"\n      } else {\n        if (e.anchor && this.options.anchorScrolling === 'enabled') {\n          this.viewportScroller.scrollToAnchor(e.anchor);\n        } else if (this.options.scrollPositionRestoration !== 'disabled') {\n          this.viewportScroller.scrollToPosition([0, 0]);\n        }\n      }\n    });\n  }\n  scheduleScrollEvent(routerEvent, anchor) {\n    var _this = this;\n    this.zone.runOutsideAngular(/*#__PURE__*/_asyncToGenerator(function* () {\n      // The scroll event needs to be delayed until after change detection. Otherwise, we may\n      // attempt to restore the scroll position before the router outlet has fully rendered the\n      // component by executing its update block of the template function.\n      //\n      // #57109 (we need to wait at least a macrotask before scrolling. AfterNextRender resolves in microtask event loop with Zones)\n      // We could consider _also_ waiting for a render promise though one should have already happened or been scheduled by this point\n      // and should definitely happen before rAF/setTimeout.\n      // #53985 (cannot rely solely on setTimeout because a frame may paint before the timeout)\n      yield new Promise(resolve => {\n        setTimeout(resolve);\n        if (typeof requestAnimationFrame !== 'undefined') {\n          requestAnimationFrame(resolve);\n        }\n      });\n      _this.zone.run(() => {\n        _this.transitions.events.next(new Scroll(routerEvent, _this.lastSource === 'popstate' ? _this.store[_this.restoredId] : null, anchor));\n      });\n    }));\n  }\n  /** @nodoc */\n  ngOnDestroy() {\n    this.routerEventsSubscription?.unsubscribe();\n    this.scrollEventsSubscription?.unsubscribe();\n  }\n  static ɵfac = function RouterScroller_Factory(__ngFactoryType__) {\n    i0.ɵɵinvalidFactory();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RouterScroller,\n    factory: RouterScroller.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterScroller, [{\n    type: Injectable\n  }], () => [{\n    type: UrlSerializer\n  }, {\n    type: NavigationTransitions\n  }, {\n    type: i3.ViewportScroller\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined\n  }], null);\n})();\n\n/**\n * Sets up providers necessary to enable `Router` functionality for the application.\n * Allows to configure a set of routes as well as extra features that should be enabled.\n *\n * @usageNotes\n *\n * Basic example of how you can add a Router to your application:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent, {\n *   providers: [provideRouter(appRoutes)]\n * });\n * ```\n *\n * You can also enable optional features in the Router by adding functions from the `RouterFeatures`\n * type:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes,\n *         withDebugTracing(),\n *         withRouterConfig({paramsInheritanceStrategy: 'always'}))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link RouterFeatures}\n *\n * @publicApi\n * @param routes A set of `Route`s to use for the application routing table.\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to setup a Router.\n */\nfunction provideRouter(routes, ...features) {\n  return makeEnvironmentProviders([{\n    provide: ROUTES,\n    multi: true,\n    useValue: routes\n  }, typeof ngDevMode === 'undefined' || ngDevMode ? {\n    provide: ROUTER_IS_PROVIDED,\n    useValue: true\n  } : [], {\n    provide: ActivatedRoute,\n    useFactory: rootRoute,\n    deps: [Router]\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useFactory: getBootstrapListener\n  }, features.map(feature => feature.ɵproviders)]);\n}\nfunction rootRoute(router) {\n  return router.routerState.root;\n}\n/**\n * Helper function to create an object that represents a Router feature.\n */\nfunction routerFeature(kind, providers) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\n/**\n * An Injection token used to indicate whether `provideRouter` or `RouterModule.forRoot` was ever\n * called.\n */\nconst ROUTER_IS_PROVIDED = new InjectionToken('', {\n  providedIn: 'root',\n  factory: () => false\n});\nconst routerIsProvidedDevModeCheck = {\n  provide: ENVIRONMENT_INITIALIZER,\n  multi: true,\n  useFactory() {\n    return () => {\n      if (!inject(ROUTER_IS_PROVIDED)) {\n        console.warn('`provideRoutes` was called without `provideRouter` or `RouterModule.forRoot`. ' + 'This is likely a mistake.');\n      }\n    };\n  }\n};\n/**\n * Registers a DI provider for a set of routes.\n * @param routes The route configuration to provide.\n *\n * @usageNotes\n *\n * ```ts\n * @NgModule({\n *   providers: [provideRoutes(ROUTES)]\n * })\n * class LazyLoadedChildModule {}\n * ```\n *\n * @deprecated If necessary, provide routes using the `ROUTES` `InjectionToken`.\n * @see {@link ROUTES}\n * @publicApi\n */\nfunction provideRoutes(routes) {\n  return [{\n    provide: ROUTES,\n    multi: true,\n    useValue: routes\n  }, typeof ngDevMode === 'undefined' || ngDevMode ? routerIsProvidedDevModeCheck : []];\n}\n/**\n * Enables customizable scrolling behavior for router navigations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable scrolling feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withInMemoryScrolling())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link ViewportScroller}\n *\n * @publicApi\n * @param options Set of configuration parameters to customize scrolling behavior, see\n *     `InMemoryScrollingOptions` for additional information.\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withInMemoryScrolling(options = {}) {\n  const providers = [{\n    provide: ROUTER_SCROLLER,\n    useFactory: () => {\n      const viewportScroller = inject(ViewportScroller);\n      const zone = inject(NgZone);\n      const transitions = inject(NavigationTransitions);\n      const urlSerializer = inject(UrlSerializer);\n      return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, options);\n    }\n  }];\n  return routerFeature(4 /* RouterFeatureKind.InMemoryScrollingFeature */, providers);\n}\nfunction getBootstrapListener() {\n  const injector = inject(Injector);\n  return bootstrappedComponentRef => {\n    const ref = injector.get(ApplicationRef);\n    if (bootstrappedComponentRef !== ref.components[0]) {\n      return;\n    }\n    const router = injector.get(Router);\n    const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n    if (injector.get(INITIAL_NAVIGATION) === 1 /* InitialNavigation.EnabledNonBlocking */) {\n      router.initialNavigation();\n    }\n    injector.get(ROUTER_PRELOADER, null, {\n      optional: true\n    })?.setUpPreloading();\n    injector.get(ROUTER_SCROLLER, null, {\n      optional: true\n    })?.init();\n    router.resetRootComponentType(ref.componentTypes[0]);\n    if (!bootstrapDone.closed) {\n      bootstrapDone.next();\n      bootstrapDone.complete();\n      bootstrapDone.unsubscribe();\n    }\n  };\n}\n/**\n * A subject used to indicate that the bootstrapping phase is done. When initial navigation is\n * `enabledBlocking`, the first navigation waits until bootstrapping is finished before continuing\n * to the activation phase.\n */\nconst BOOTSTRAP_DONE = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'bootstrap done indicator' : '', {\n  factory: () => {\n    return new Subject();\n  }\n});\nconst INITIAL_NAVIGATION = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'initial navigation' : '', {\n  providedIn: 'root',\n  factory: () => 1 /* InitialNavigation.EnabledNonBlocking */\n});\n/**\n * Configures initial navigation to start before the root component is created.\n *\n * The bootstrap is blocked until the initial navigation is complete. This should be set in case\n * you use [server-side rendering](guide/ssr), but do not enable [hydration](guide/hydration) for\n * your application.\n *\n * @usageNotes\n *\n * Basic example of how you can enable this navigation behavior:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withEnabledBlockingInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @publicApi\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withEnabledBlockingInitialNavigation() {\n  const providers = [{\n    provide: INITIAL_NAVIGATION,\n    useValue: 0 /* InitialNavigation.EnabledBlocking */\n  }, provideAppInitializer(() => {\n    const injector = inject(Injector);\n    const locationInitialized = injector.get(LOCATION_INITIALIZED, Promise.resolve());\n    return locationInitialized.then(() => {\n      return new Promise(resolve => {\n        const router = injector.get(Router);\n        const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n        afterNextNavigation(router, () => {\n          // Unblock APP_INITIALIZER in case the initial navigation was canceled or errored\n          // without a redirect.\n          resolve(true);\n        });\n        injector.get(NavigationTransitions).afterPreactivation = () => {\n          // Unblock APP_INITIALIZER once we get to `afterPreactivation`. At this point, we\n          // assume activation will complete successfully (even though this is not\n          // guaranteed).\n          resolve(true);\n          return bootstrapDone.closed ? of(void 0) : bootstrapDone;\n        };\n        router.initialNavigation();\n      });\n    });\n  })];\n  return routerFeature(2 /* RouterFeatureKind.EnabledBlockingInitialNavigationFeature */, providers);\n}\n/**\n * Disables initial navigation.\n *\n * Use if there is a reason to have more control over when the router starts its initial navigation\n * due to some complex initialization logic.\n *\n * @usageNotes\n *\n * Basic example of how you can disable initial navigation:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDisabledInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDisabledInitialNavigation() {\n  const providers = [provideAppInitializer(() => {\n    inject(Router).setUpLocationChangeListener();\n  }), {\n    provide: INITIAL_NAVIGATION,\n    useValue: 2 /* InitialNavigation.Disabled */\n  }];\n  return routerFeature(3 /* RouterFeatureKind.DisabledInitialNavigationFeature */, providers);\n}\n/**\n * Enables logging of all internal navigation events to the console.\n * Extra logging might be useful for debugging purposes to inspect Router event sequence.\n *\n * @usageNotes\n *\n * Basic example of how you can enable debug tracing:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDebugTracing())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDebugTracing() {\n  let providers = [];\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    providers = [{\n      provide: ENVIRONMENT_INITIALIZER,\n      multi: true,\n      useFactory: () => {\n        const router = inject(Router);\n        return () => router.events.subscribe(e => {\n          // tslint:disable:no-console\n          console.group?.(`Router Event: ${e.constructor.name}`);\n          console.log(stringifyEvent(e));\n          console.log(e);\n          console.groupEnd?.();\n          // tslint:enable:no-console\n        });\n      }\n    }];\n  } else {\n    providers = [];\n  }\n  return routerFeature(1 /* RouterFeatureKind.DebugTracingFeature */, providers);\n}\nconst ROUTER_PRELOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router preloader' : '');\n/**\n * Allows to configure a preloading strategy to use. The strategy is configured by providing a\n * reference to a class that implements a `PreloadingStrategy`.\n *\n * @usageNotes\n *\n * Basic example of how you can configure preloading:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withPreloading(PreloadAllModules))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param preloadingStrategy A reference to a class that implements a `PreloadingStrategy` that\n *     should be used.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withPreloading(preloadingStrategy) {\n  const providers = [{\n    provide: ROUTER_PRELOADER,\n    useExisting: RouterPreloader\n  }, {\n    provide: PreloadingStrategy,\n    useExisting: preloadingStrategy\n  }];\n  return routerFeature(0 /* RouterFeatureKind.PreloadingFeature */, providers);\n}\n/**\n * Allows to provide extra parameters to configure Router.\n *\n * @usageNotes\n *\n * Basic example of how you can provide extra configuration options:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withRouterConfig({\n *          onSameUrlNavigation: 'reload'\n *       }))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param options A set of parameters to configure Router, see `RouterConfigOptions` for\n *     additional information.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withRouterConfig(options) {\n  const providers = [{\n    provide: ROUTER_CONFIGURATION,\n    useValue: options\n  }];\n  return routerFeature(5 /* RouterFeatureKind.RouterConfigurationFeature */, providers);\n}\n/**\n * Provides the location strategy that uses the URL fragment instead of the history API.\n *\n * @usageNotes\n *\n * Basic example of how you can use the hash location option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withHashLocation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link /api/common/HashLocationStrategy HashLocationStrategy}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withHashLocation() {\n  const providers = [{\n    provide: LocationStrategy,\n    useClass: HashLocationStrategy\n  }];\n  return routerFeature(6 /* RouterFeatureKind.RouterHashLocationFeature */, providers);\n}\n/**\n * Provides a function which is called when a navigation error occurs.\n *\n * This function is run inside application's [injection context](guide/di/dependency-injection-context)\n * so you can use the [`inject`](api/core/inject) function.\n *\n * This function can return a `RedirectCommand` to convert the error to a redirect, similar to returning\n * a `UrlTree` or `RedirectCommand` from a guard. This will also prevent the `Router` from emitting\n * `NavigationError`; it will instead emit `NavigationCancel` with code NavigationCancellationCode.Redirect.\n * Return values other than `RedirectCommand` are ignored and do not change any behavior with respect to\n * how the `Router` handles the error.\n *\n * @usageNotes\n *\n * Basic example of how you can use the error handler option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withNavigationErrorHandler((e: NavigationError) =>\n * inject(MyErrorTracker).trackError(e)))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link NavigationError}\n * @see {@link /api/core/inject inject}\n * @see {@link runInInjectionContext}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withNavigationErrorHandler(handler) {\n  const providers = [{\n    provide: NAVIGATION_ERROR_HANDLER,\n    useValue: handler\n  }];\n  return routerFeature(7 /* RouterFeatureKind.NavigationErrorHandlerFeature */, providers);\n}\n/**\n * Enables binding information from the `Router` state directly to the inputs of the component in\n * `Route` configurations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withComponentInputBinding())\n *     ]\n *   }\n * );\n * ```\n *\n * The router bindings information from any of the following sources:\n *\n *  - query parameters\n *  - path and matrix parameters\n *  - static route data\n *  - data from resolvers\n *\n * Duplicate keys are resolved in the same order from above, from least to greatest,\n * meaning that resolvers have the highest precedence and override any of the other information\n * from the route.\n *\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. This prevents previous information from being\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n * Default values can be provided with a resolver on the route to ensure the value is always present\n * or an input and use an input transform in the component.\n *\n * @see {@link /guide/components/inputs#input-transforms Input Transforms}\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withComponentInputBinding() {\n  const providers = [RoutedComponentInputBinder, {\n    provide: INPUT_BINDER,\n    useExisting: RoutedComponentInputBinder\n  }];\n  return routerFeature(8 /* RouterFeatureKind.ComponentInputBindingFeature */, providers);\n}\n/**\n * Enables view transitions in the Router by running the route activation and deactivation inside of\n * `document.startViewTransition`.\n *\n * Note: The View Transitions API is not available in all browsers. If the browser does not support\n * view transitions, the Router will not attempt to start a view transition and continue processing\n * the navigation as usual.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withViewTransitions())\n *     ]\n *   }\n * );\n * ```\n *\n * @returns A set of providers for use with `provideRouter`.\n * @see https://developer.chrome.com/docs/web-platform/view-transitions/\n * @see https://developer.mozilla.org/en-US/docs/Web/API/View_Transitions_API\n * @developerPreview\n */\nfunction withViewTransitions(options) {\n  _performanceMarkFeature('NgRouterViewTransitions');\n  const providers = [{\n    provide: CREATE_VIEW_TRANSITION,\n    useValue: createViewTransition\n  }, {\n    provide: VIEW_TRANSITION_OPTIONS,\n    useValue: {\n      skipNextTransition: !!options?.skipInitialTransition,\n      ...options\n    }\n  }];\n  return routerFeature(9 /* RouterFeatureKind.ViewTransitionsFeature */, providers);\n}\n\n/**\n * The directives defined in the `RouterModule`.\n */\nconst ROUTER_DIRECTIVES = [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent];\n/**\n * @docsNotRequired\n */\nconst ROUTER_FORROOT_GUARD = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router duplicate forRoot guard' : '');\n// TODO(atscott): All of these except `ActivatedRoute` are `providedIn: 'root'`. They are only kept\n// here to avoid a breaking change whereby the provider order matters based on where the\n// `RouterModule`/`RouterTestingModule` is imported. These can/should be removed as a \"breaking\"\n// change in a major version.\nconst ROUTER_PROVIDERS = [Location, {\n  provide: UrlSerializer,\n  useClass: DefaultUrlSerializer\n}, Router, ChildrenOutletContexts, {\n  provide: ActivatedRoute,\n  useFactory: rootRoute,\n  deps: [Router]\n}, RouterConfigLoader,\n// Only used to warn when `provideRoutes` is used without `RouterModule` or `provideRouter`. Can\n// be removed when `provideRoutes` is removed.\ntypeof ngDevMode === 'undefined' || ngDevMode ? {\n  provide: ROUTER_IS_PROVIDED,\n  useValue: true\n} : []];\n/**\n * @description\n *\n * Adds directives and providers for in-app navigation among views defined in an application.\n * Use the Angular `Router` service to declaratively specify application states and manage state\n * transitions.\n *\n * You can import this NgModule multiple times, once for each lazy-loaded bundle.\n * However, only one `Router` service can be active.\n * To ensure this, there are two ways to register routes when importing this module:\n *\n * * The `forRoot()` method creates an `NgModule` that contains all the directives, the given\n * routes, and the `Router` service itself.\n * * The `forChild()` method creates an `NgModule` that contains all the directives and the given\n * routes, but does not include the `Router` service.\n *\n * @see [Routing and Navigation guide](guide/routing/common-router-tasks) for an\n * overview of how the `Router` service should be used.\n *\n * @publicApi\n */\nclass RouterModule {\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      inject(ROUTER_FORROOT_GUARD, {\n        optional: true\n      });\n    }\n  }\n  /**\n   * Creates and configures a module with all the router providers and directives.\n   * Optionally sets up an application listener to perform an initial navigation.\n   *\n   * When registering the NgModule at the root, import as follows:\n   *\n   * ```ts\n   * @NgModule({\n   *   imports: [RouterModule.forRoot(ROUTES)]\n   * })\n   * class MyNgModule {}\n   * ```\n   *\n   * @param routes An array of `Route` objects that define the navigation paths for the application.\n   * @param config An `ExtraOptions` configuration object that controls how navigation is performed.\n   * @return The new `NgModule`.\n   *\n   */\n  static forRoot(routes, config) {\n    return {\n      ngModule: RouterModule,\n      providers: [ROUTER_PROVIDERS, typeof ngDevMode === 'undefined' || ngDevMode ? config?.enableTracing ? withDebugTracing().ɵproviders : [] : [], {\n        provide: ROUTES,\n        multi: true,\n        useValue: routes\n      }, typeof ngDevMode === 'undefined' || ngDevMode ? {\n        provide: ROUTER_FORROOT_GUARD,\n        useFactory: provideForRootGuard,\n        deps: [[Router, new Optional(), new SkipSelf()]]\n      } : [], config?.errorHandler ? {\n        provide: NAVIGATION_ERROR_HANDLER,\n        useValue: config.errorHandler\n      } : [], {\n        provide: ROUTER_CONFIGURATION,\n        useValue: config ? config : {}\n      }, config?.useHash ? provideHashLocationStrategy() : providePathLocationStrategy(), provideRouterScroller(), config?.preloadingStrategy ? withPreloading(config.preloadingStrategy).ɵproviders : [], config?.initialNavigation ? provideInitialNavigation(config) : [], config?.bindToComponentInputs ? withComponentInputBinding().ɵproviders : [], config?.enableViewTransitions ? withViewTransitions().ɵproviders : [], provideRouterInitializer()]\n    };\n  }\n  /**\n   * Creates a module with all the router directives and a provider registering routes,\n   * without creating a new Router service.\n   * When registering for submodules and lazy-loaded submodules, create the NgModule as follows:\n   *\n   * ```ts\n   * @NgModule({\n   *   imports: [RouterModule.forChild(ROUTES)]\n   * })\n   * class MyNgModule {}\n   * ```\n   *\n   * @param routes An array of `Route` objects that define the navigation paths for the submodule.\n   * @return The new NgModule.\n   *\n   */\n  static forChild(routes) {\n    return {\n      ngModule: RouterModule,\n      providers: [{\n        provide: ROUTES,\n        multi: true,\n        useValue: routes\n      }]\n    };\n  }\n  static ɵfac = function RouterModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RouterModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: RouterModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterModule, [{\n    type: NgModule,\n    args: [{\n      imports: ROUTER_DIRECTIVES,\n      exports: ROUTER_DIRECTIVES\n    }]\n  }], () => [], null);\n})();\n/**\n * For internal use by `RouterModule` only. Note that this differs from `withInMemoryRouterScroller`\n * because it reads from the `ExtraOptions` which should not be used in the standalone world.\n */\nfunction provideRouterScroller() {\n  return {\n    provide: ROUTER_SCROLLER,\n    useFactory: () => {\n      const viewportScroller = inject(ViewportScroller);\n      const zone = inject(NgZone);\n      const config = inject(ROUTER_CONFIGURATION);\n      const transitions = inject(NavigationTransitions);\n      const urlSerializer = inject(UrlSerializer);\n      if (config.scrollOffset) {\n        viewportScroller.setOffset(config.scrollOffset);\n      }\n      return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, config);\n    }\n  };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` should\n// provide hash location directly via `{provide: LocationStrategy, useClass: HashLocationStrategy}`.\nfunction provideHashLocationStrategy() {\n  return {\n    provide: LocationStrategy,\n    useClass: HashLocationStrategy\n  };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` does not\n// need this at all because `PathLocationStrategy` is the default factory for `LocationStrategy`.\nfunction providePathLocationStrategy() {\n  return {\n    provide: LocationStrategy,\n    useClass: PathLocationStrategy\n  };\n}\nfunction provideForRootGuard(router) {\n  if (router) {\n    throw new _RuntimeError(4007 /* RuntimeErrorCode.FOR_ROOT_CALLED_TWICE */, `The Router was provided more than once. This can happen if 'forRoot' is used outside of the root injector.` + ` Lazy loaded modules should use RouterModule.forChild() instead.`);\n  }\n  return 'guarded';\n}\n// Note: For internal use only with `RouterModule`. Standalone router setup with `provideRouter`\n// users call `withXInitialNavigation` directly.\nfunction provideInitialNavigation(config) {\n  return [config.initialNavigation === 'disabled' ? withDisabledInitialNavigation().ɵproviders : [], config.initialNavigation === 'enabledBlocking' ? withEnabledBlockingInitialNavigation().ɵproviders : []];\n}\n// TODO(atscott): This should not be in the public API\n/**\n * A DI token for the router initializer that\n * is called after the app is bootstrapped.\n *\n * @publicApi\n */\nconst ROUTER_INITIALIZER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Router Initializer' : '');\nfunction provideRouterInitializer() {\n  return [\n  // ROUTER_INITIALIZER token should be removed. It's public API but shouldn't be. We can just\n  // have `getBootstrapListener` directly attached to APP_BOOTSTRAP_LISTENER.\n  {\n    provide: ROUTER_INITIALIZER,\n    useFactory: getBootstrapListener\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useExisting: ROUTER_INITIALIZER\n  }];\n}\nexport { NoPreloading, PreloadAllModules, PreloadingStrategy, ROUTER_INITIALIZER, ROUTER_PROVIDERS, RouterLink, RouterLinkActive, RouterModule, RouterPreloader, provideRouter, provideRoutes, withComponentInputBinding, withDebugTracing, withDisabledInitialNavigation, withEnabledBlockingInitialNavigation, withHashLocation, withInMemoryScrolling, withNavigationErrorHandler, withPreloading, withRouterConfig, withViewTransitions };", "map": {"version": 3, "names": ["i3", "LOCATION_INITIALIZED", "HashLocationStrategy", "LocationStrategy", "ViewportScroller", "Location", "PathLocationStrategy", "i0", "inject", "ɵINTERNAL_APPLICATION_ERROR_HANDLER", "_INTERNAL_APPLICATION_ERROR_HANDLER", "ɵRuntimeError", "_RuntimeError", "ɵɵsanitizeUrlOrResourceUrl", "__sanitizeUrlOrResourceUrl", "booleanAttribute", "HostListener", "Input", "HostBinding", "Attribute", "Directive", "EventEmitter", "Output", "ContentChildren", "Optional", "createEnvironmentInjector", "Injectable", "InjectionToken", "ɵperformanceMarkFeature", "_performanceMarkFeature", "makeEnvironmentProviders", "APP_BOOTSTRAP_LISTENER", "ENVIRONMENT_INITIALIZER", "provideAppInitializer", "Injector", "ApplicationRef", "NgZone", "SkipSelf", "NgModule", "NavigationEnd", "isUrlTree", "Router", "ActivatedRoute", "RouterConfigLoader", "IMPERATIVE_NAVIGATION", "NavigationStart", "NavigationSkipped", "NavigationSkippedCode", "<PERSON><PERSON>", "UrlSerializer", "NavigationTransitions", "ROUTES", "afterNextNavigation", "ROUTER_CONFIGURATION", "NAVIGATION_ERROR_HANDLER", "RoutedComponentInputBinder", "INPUT_BINDER", "createViewTransition", "CREATE_VIEW_TRANSITION", "VIEW_TRANSITION_OPTIONS", "stringifyEvent", "DefaultUrlSerializer", "ChildrenOutletContexts", "RouterOutlet", "ɵEmptyOutletComponent", "_EmptyOutletComponent", "Subject", "of", "from", "mergeAll", "catchError", "filter", "concatMap", "mergeMap", "RouterLink", "router", "route", "tabIndexAttribute", "renderer", "el", "locationStrategy", "href", "target", "queryParams", "fragment", "queryParamsHandling", "state", "info", "relativeTo", "isAnchorElement", "subscription", "onChanges", "applicationErrorHandler", "constructor", "tagName", "nativeElement", "toLowerCase", "customElements", "get", "observedAttributes", "includes", "events", "subscribe", "s", "updateHref", "setTabIndexIfNotOnNativeEl", "preserveFragment", "skipLocationChange", "replaceUrl", "newTabIndex", "applyAttributeValue", "ngOnChanges", "changes", "ngDevMode", "routerLinkInput", "undefined", "next", "routerLink", "commandsOrUrlTree", "Array", "isArray", "onClick", "button", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "urlTree", "extras", "navigateByUrl", "catch", "e", "ngOnDestroy", "unsubscribe", "prepareExternalUrl", "serializeUrl", "sanitizedValue", "attrName", "attrValue", "setAttribute", "removeAttribute", "createUrlTree", "ɵfac", "RouterLink_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "ɵɵinjectAttribute", "Renderer2", "ElementRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostVars", "hostBindings", "RouterLink_HostBindings", "rf", "ctx", "ɵɵlistener", "RouterLink_click_HostBindingHandler", "$event", "ɵɵattribute", "inputs", "features", "ɵɵNgOnChangesFeature", "ɵsetClassMetadata", "args", "selector", "decorators", "transform", "RouterLinkActive", "element", "cdr", "link", "links", "classes", "routerEventsSubscription", "linkInputChangesSubscription", "_isActive", "isActive", "routerLinkActiveOptions", "exact", "ariaCurrentWhenActive", "isActiveChange", "update", "ngAfterContentInit", "pipe", "_", "subscribeToEachLinkOnChanges", "allLinkChanges", "toArray", "map", "isLinkActive", "routerLinkActive", "data", "split", "c", "navigated", "queueMicrotask", "hasActiveLinks", "for<PERSON>ach", "addClass", "removeClass", "toString", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emit", "options", "isActiveMatchOptions", "isActiveCheckFn", "some", "RouterLinkActive_Factory", "ChangeDetectorRef", "contentQueries", "RouterLinkActive_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "outputs", "exportAs", "descendants", "paths", "PreloadingStrategy", "PreloadAllModules", "preload", "fn", "PreloadAllModules_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "NoPreloading", "NoPreloading_Factory", "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "injector", "preloadingStrategy", "loader", "setUpPreloading", "processRoutes", "config", "routes", "res", "providers", "_injector", "path", "injectorForCurrentRoute", "injectorForChildren", "_loadedInjector", "loadChildren", "_loadedRoutes", "canLoad", "loadComponent", "_loadedComponent", "push", "preloadConfig", "children", "loadedChildren$", "recursiveLoadChildren$", "loadComponent$", "RouterPreloader_Factory", "ɵɵinject", "EnvironmentInjector", "ROUTER_SCROLLER", "RouterS<PERSON>roller", "urlSerializer", "transitions", "viewportScroller", "zone", "scrollEventsSubscription", "lastId", "lastSource", "restoredId", "store", "scrollPositionRestoration", "anchorScrolling", "init", "setHistoryScrollRestoration", "createScrollEvents", "consumeScrollEvents", "getScrollPosition", "navigationTrigger", "restoredState", "navigationId", "id", "scheduleScrollEvent", "parse", "urlAfterRedirects", "code", "IgnoredSameUrlNavigation", "url", "position", "scrollToPosition", "anchor", "scrollToAnchor", "routerEvent", "_this", "runOutsideAngular", "_asyncToGenerator", "Promise", "resolve", "setTimeout", "requestAnimationFrame", "run", "RouterScroller_Factory", "ɵɵinvalidFactory", "provideRouter", "provide", "multi", "useValue", "ROUTER_IS_PROVIDED", "useFactory", "rootRoute", "deps", "getBootstrapListener", "feature", "ɵproviders", "routerState", "root", "routerFeature", "kind", "ɵkind", "routerIsProvidedDevModeCheck", "console", "warn", "provideRoutes", "withInMemoryScrolling", "bootstrappedComponentRef", "ref", "components", "bootstrapDone", "BOOTSTRAP_DONE", "INITIAL_NAVIGATION", "initialNavigation", "ROUTER_PRELOADER", "optional", "resetRootComponentType", "componentTypes", "closed", "complete", "withEnabledBlockingInitialNavigation", "locationInitialized", "then", "afterPreactivation", "withDisabledInitialNavigation", "setUpLocationChangeListener", "withDebugTracing", "group", "name", "log", "groupEnd", "withPreloading", "useExisting", "withRouterConfig", "withHashLocation", "useClass", "withNavigationErrorHandler", "handler", "withComponentInputBinding", "withViewTransitions", "skipNextTransition", "skipInitialTransition", "ROUTER_DIRECTIVES", "ROUTER_FORROOT_GUARD", "ROUTER_PROVIDERS", "RouterModule", "forRoot", "ngModule", "enableTracing", "provideForRootGuard", "<PERSON><PERSON><PERSON><PERSON>", "useHash", "provideHashLocationStrategy", "providePathLocationStrategy", "provideRouterScroller", "provideInitialNavigation", "bindToComponentInputs", "enableViewTransitions", "provideRouterInitializer", "<PERSON><PERSON><PERSON><PERSON>", "RouterModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "scrollOffset", "setOffset", "ROUTER_INITIALIZER"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/@angular/router/fesm2022/router_module-avVs2gWt.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i3 from '@angular/common';\nimport { LOCATION_INITIALIZED, HashLocationStrategy, LocationStrategy, ViewportScroller, Location, PathLocationStrategy } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ɵINTERNAL_APPLICATION_ERROR_HANDLER as _INTERNAL_APPLICATION_ERROR_HANDLER, ɵRuntimeError as _RuntimeError, ɵɵsanitizeUrlOrResourceUrl as __sanitizeUrlOrResourceUrl, booleanAttribute, HostListener, Input, HostBinding, Attribute, Directive, EventEmitter, Output, ContentChildren, Optional, createEnvironmentInjector, Injectable, InjectionToken, ɵperformanceMarkFeature as _performanceMarkFeature, makeEnvironmentProviders, APP_BOOTSTRAP_LISTENER, ENVIRONMENT_INITIALIZER, provideAppInitializer, Injector, ApplicationRef, <PERSON>Z<PERSON>, SkipSelf, NgModule } from '@angular/core';\nimport { NavigationEnd, isUrlTree, Router, ActivatedRoute, RouterConfigLoader, IMPERATIVE_NAVIGATION, NavigationStart, NavigationSkipped, NavigationSkippedCode, Scroll, UrlSerializer, NavigationTransitions, ROUTES, afterNextNavigation, ROUTER_CONFIGURATION, NAVIGATION_ERROR_HANDLER, RoutedComponentInputBinder, INPUT_BINDER, createViewTransition, CREATE_VIEW_TRANSITION, VIEW_TRANSITION_OPTIONS, stringifyEvent, DefaultUrlSerializer, ChildrenOutletContexts, RouterOutlet, ɵEmptyOutletComponent as _EmptyOutletComponent } from './router-BxrGTdzL.mjs';\nimport { Subject, of, from } from 'rxjs';\nimport { mergeAll, catchError, filter, concatMap, mergeMap } from 'rxjs/operators';\n\n/**\n * @description\n *\n * When applied to an element in a template, makes that element a link\n * that initiates navigation to a route. Navigation opens one or more routed components\n * in one or more `<router-outlet>` locations on the page.\n *\n * Given a route configuration `[{ path: 'user/:name', component: UserCmp }]`,\n * the following creates a static link to the route:\n * `<a routerLink=\"/user/bob\">link to user component</a>`\n *\n * You can use dynamic values to generate the link.\n * For a dynamic link, pass an array of path segments,\n * followed by the params for each segment.\n * For example, `['/team', teamId, 'user', userName, {details: true}]`\n * generates a link to `/team/11/user/bob;details=true`.\n *\n * Multiple static segments can be merged into one term and combined with dynamic segments.\n * For example, `['/team/11/user', userName, {details: true}]`\n *\n * The input that you provide to the link is treated as a delta to the current URL.\n * For instance, suppose the current URL is `/user/(box//aux:team)`.\n * The link `<a [routerLink]=\"['/user/jim']\">Jim</a>` creates the URL\n * `/user/(jim//aux:team)`.\n * See {@link Router#createUrlTree} for more information.\n *\n * @usageNotes\n *\n * You can use absolute or relative paths in a link, set query parameters,\n * control how parameters are handled, and keep a history of navigation states.\n *\n * ### Relative link paths\n *\n * The first segment name can be prepended with `/`, `./`, or `../`.\n * * If the first segment begins with `/`, the router looks up the route from the root of the\n *   app.\n * * If the first segment begins with `./`, or doesn't begin with a slash, the router\n *   looks in the children of the current activated route.\n * * If the first segment begins with `../`, the router goes up one level in the route tree.\n *\n * ### Setting and handling query params and fragments\n *\n * The following link adds a query parameter and a fragment to the generated URL:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" fragment=\"education\">\n *   link to user component\n * </a>\n * ```\n * By default, the directive constructs the new URL using the given query parameters.\n * The example generates the link: `/user/bob?debug=true#education`.\n *\n * You can instruct the directive to handle query parameters differently\n * by specifying the `queryParamsHandling` option in the link.\n * Allowed values are:\n *\n *  - `'merge'`: Merge the given `queryParams` into the current query params.\n *  - `'preserve'`: Preserve the current query params.\n *\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" queryParamsHandling=\"merge\">\n *   link to user component\n * </a>\n * ```\n *\n * `queryParams`, `fragment`, `queryParamsHandling`, `preserveFragment`, and `relativeTo`\n * cannot be used when the `routerLink` input is a `UrlTree`.\n *\n * See {@link UrlCreationOptions#queryParamsHandling}.\n *\n * ### Preserving navigation history\n *\n * You can provide a `state` value to be persisted to the browser's\n * [`History.state` property](https://developer.mozilla.org/en-US/docs/Web/API/History#Properties).\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [state]=\"{tracingId: 123}\">\n *   link to user component\n * </a>\n * ```\n *\n * Use {@link Router#getCurrentNavigation} to retrieve a saved\n * navigation-state value. For example, to capture the `tracingId` during the `NavigationStart`\n * event:\n *\n * ```ts\n * // Get NavigationStart events\n * router.events.pipe(filter(e => e instanceof NavigationStart)).subscribe(e => {\n *   const navigation = router.getCurrentNavigation();\n *   tracingService.trace({id: navigation.extras.state.tracingId});\n * });\n * ```\n *\n * ### RouterLink compatible custom elements\n *\n * In order to make a custom element work with routerLink, the corresponding custom\n * element must implement the `href` attribute and must list `href` in the array of\n * the static property/getter `observedAttributes`.\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass RouterLink {\n    router;\n    route;\n    tabIndexAttribute;\n    renderer;\n    el;\n    locationStrategy;\n    /**\n     * Represents an `href` attribute value applied to a host element,\n     * when a host element is an `<a>`/`<area>` tag or a compatible custom element.\n     * For other tags, the value is `null`.\n     */\n    href = null;\n    /**\n     * Represents the `target` attribute on a host element.\n     * This is only used when the host element is\n     * an `<a>`/`<area>` tag or a compatible custom element.\n     */\n    target;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#queryParams}\n     * @see {@link Router#createUrlTree}\n     */\n    queryParams;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#fragment}\n     * @see {@link Router#createUrlTree}\n     */\n    fragment;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#queryParamsHandling}\n     * @see {@link Router#createUrlTree}\n     */\n    queryParamsHandling;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#state}\n     * @see {@link Router#navigateByUrl}\n     */\n    state;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#info}\n     * @see {@link Router#navigateByUrl}\n     */\n    info;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * Specify a value here when you do not want to use the default value\n     * for `routerLink`, which is the current activated route.\n     * Note that a value of `undefined` here will use the `routerLink` default.\n     * @see {@link UrlCreationOptions#relativeTo}\n     * @see {@link Router#createUrlTree}\n     */\n    relativeTo;\n    /** Whether a host element is an `<a>`/`<area>` tag or a compatible custom element. */\n    isAnchorElement;\n    subscription;\n    /** @internal */\n    onChanges = new Subject();\n    applicationErrorHandler = inject(_INTERNAL_APPLICATION_ERROR_HANDLER);\n    constructor(router, route, tabIndexAttribute, renderer, el, locationStrategy) {\n        this.router = router;\n        this.route = route;\n        this.tabIndexAttribute = tabIndexAttribute;\n        this.renderer = renderer;\n        this.el = el;\n        this.locationStrategy = locationStrategy;\n        const tagName = el.nativeElement.tagName?.toLowerCase();\n        this.isAnchorElement =\n            tagName === 'a' ||\n                tagName === 'area' ||\n                !!(\n                // Avoid breaking in an SSR context where customElements might not be defined.\n                (typeof customElements === 'object' &&\n                    // observedAttributes is an optional static property/getter on a custom element.\n                    // The spec states that this must be an array of strings.\n                    customElements.get(tagName)?.observedAttributes?.includes?.('href')));\n        if (this.isAnchorElement) {\n            this.subscription = router.events.subscribe((s) => {\n                if (s instanceof NavigationEnd) {\n                    this.updateHref();\n                }\n            });\n        }\n        else {\n            this.setTabIndexIfNotOnNativeEl('0');\n        }\n    }\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#preserveFragment}\n     * @see {@link Router#createUrlTree}\n     */\n    preserveFragment = false;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#skipLocationChange}\n     * @see {@link Router#navigateByUrl}\n     */\n    skipLocationChange = false;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#replaceUrl}\n     * @see {@link Router#navigateByUrl}\n     */\n    replaceUrl = false;\n    /**\n     * Modifies the tab index if there was not a tabindex attribute on the element during\n     * instantiation.\n     */\n    setTabIndexIfNotOnNativeEl(newTabIndex) {\n        if (this.tabIndexAttribute != null /* both `null` and `undefined` */ || this.isAnchorElement) {\n            return;\n        }\n        this.applyAttributeValue('tabindex', newTabIndex);\n    }\n    /** @nodoc */\n    // TODO(atscott): Remove changes parameter in major version as a breaking change.\n    ngOnChanges(changes) {\n        if (ngDevMode &&\n            isUrlTree(this.routerLinkInput) &&\n            (this.fragment !== undefined ||\n                this.queryParams ||\n                this.queryParamsHandling ||\n                this.preserveFragment ||\n                this.relativeTo)) {\n            throw new _RuntimeError(4016 /* RuntimeErrorCode.INVALID_ROUTER_LINK_INPUTS */, 'Cannot configure queryParams or fragment when using a UrlTree as the routerLink input value.');\n        }\n        if (this.isAnchorElement) {\n            this.updateHref();\n        }\n        // This is subscribed to by `RouterLinkActive` so that it knows to update when there are changes\n        // to the RouterLinks it's tracking.\n        this.onChanges.next(this);\n    }\n    routerLinkInput = null;\n    /**\n     * Commands to pass to {@link Router#createUrlTree} or a `UrlTree`.\n     *   - **array**: commands to pass to {@link Router#createUrlTree}.\n     *   - **string**: shorthand for array of commands with just the string, i.e. `['/route']`\n     *   - **UrlTree**: a `UrlTree` for this link rather than creating one from the commands\n     *     and other inputs that correspond to properties of `UrlCreationOptions`.\n     *   - **null|undefined**: effectively disables the `routerLink`\n     * @see {@link Router#createUrlTree}\n     */\n    set routerLink(commandsOrUrlTree) {\n        if (commandsOrUrlTree == null) {\n            this.routerLinkInput = null;\n            this.setTabIndexIfNotOnNativeEl(null);\n        }\n        else {\n            if (isUrlTree(commandsOrUrlTree)) {\n                this.routerLinkInput = commandsOrUrlTree;\n            }\n            else {\n                this.routerLinkInput = Array.isArray(commandsOrUrlTree)\n                    ? commandsOrUrlTree\n                    : [commandsOrUrlTree];\n            }\n            this.setTabIndexIfNotOnNativeEl('0');\n        }\n    }\n    /** @nodoc */\n    onClick(button, ctrlKey, shiftKey, altKey, metaKey) {\n        const urlTree = this.urlTree;\n        if (urlTree === null) {\n            return true;\n        }\n        if (this.isAnchorElement) {\n            if (button !== 0 || ctrlKey || shiftKey || altKey || metaKey) {\n                return true;\n            }\n            if (typeof this.target === 'string' && this.target != '_self') {\n                return true;\n            }\n        }\n        const extras = {\n            skipLocationChange: this.skipLocationChange,\n            replaceUrl: this.replaceUrl,\n            state: this.state,\n            info: this.info,\n        };\n        // navigateByUrl is mocked frequently in tests... Reduce breakages when adding `catch`\n        this.router.navigateByUrl(urlTree, extras)?.catch((e) => {\n            this.applicationErrorHandler(e);\n        });\n        // Return `false` for `<a>` elements to prevent default action\n        // and cancel the native behavior, since the navigation is handled\n        // by the Router.\n        return !this.isAnchorElement;\n    }\n    /** @nodoc */\n    ngOnDestroy() {\n        this.subscription?.unsubscribe();\n    }\n    updateHref() {\n        const urlTree = this.urlTree;\n        this.href =\n            urlTree !== null && this.locationStrategy\n                ? this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(urlTree))\n                : null;\n        const sanitizedValue = this.href === null\n            ? null\n            : // This class represents a directive that can be added to both `<a>` elements,\n                // as well as other elements. As a result, we can't define security context at\n                // compile time. So the security context is deferred to runtime.\n                // The `ɵɵsanitizeUrlOrResourceUrl` selects the necessary sanitizer function\n                // based on the tag and property names. The logic mimics the one from\n                // `packages/compiler/src/schema/dom_security_schema.ts`, which is used at compile time.\n                //\n                // Note: we should investigate whether we can switch to using `@HostBinding('attr.href')`\n                // instead of applying a value via a renderer, after a final merge of the\n                // `RouterLinkWithHref` directive.\n                __sanitizeUrlOrResourceUrl(this.href, this.el.nativeElement.tagName.toLowerCase(), 'href');\n        this.applyAttributeValue('href', sanitizedValue);\n    }\n    applyAttributeValue(attrName, attrValue) {\n        const renderer = this.renderer;\n        const nativeElement = this.el.nativeElement;\n        if (attrValue !== null) {\n            renderer.setAttribute(nativeElement, attrName, attrValue);\n        }\n        else {\n            renderer.removeAttribute(nativeElement, attrName);\n        }\n    }\n    get urlTree() {\n        if (this.routerLinkInput === null) {\n            return null;\n        }\n        else if (isUrlTree(this.routerLinkInput)) {\n            return this.routerLinkInput;\n        }\n        return this.router.createUrlTree(this.routerLinkInput, {\n            // If the `relativeTo` input is not defined, we want to use `this.route` by default.\n            // Otherwise, we should use the value provided by the user in the input.\n            relativeTo: this.relativeTo !== undefined ? this.relativeTo : this.route,\n            queryParams: this.queryParams,\n            fragment: this.fragment,\n            queryParamsHandling: this.queryParamsHandling,\n            preserveFragment: this.preserveFragment,\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterLink, deps: [{ token: Router }, { token: ActivatedRoute }, { token: 'tabindex', attribute: true }, { token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i3.LocationStrategy }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0-next.8\", type: RouterLink, isStandalone: true, selector: \"[routerLink]\", inputs: { target: \"target\", queryParams: \"queryParams\", fragment: \"fragment\", queryParamsHandling: \"queryParamsHandling\", state: \"state\", info: \"info\", relativeTo: \"relativeTo\", preserveFragment: [\"preserveFragment\", \"preserveFragment\", booleanAttribute], skipLocationChange: [\"skipLocationChange\", \"skipLocationChange\", booleanAttribute], replaceUrl: [\"replaceUrl\", \"replaceUrl\", booleanAttribute], routerLink: \"routerLink\" }, host: { listeners: { \"click\": \"onClick($event.button,$event.ctrlKey,$event.shiftKey,$event.altKey,$event.metaKey)\" }, properties: { \"attr.target\": \"this.target\" } }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterLink, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[routerLink]',\n                }]\n        }], ctorParameters: () => [{ type: Router }, { type: ActivatedRoute }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i3.LocationStrategy }], propDecorators: { target: [{\n                type: HostBinding,\n                args: ['attr.target']\n            }, {\n                type: Input\n            }], queryParams: [{\n                type: Input\n            }], fragment: [{\n                type: Input\n            }], queryParamsHandling: [{\n                type: Input\n            }], state: [{\n                type: Input\n            }], info: [{\n                type: Input\n            }], relativeTo: [{\n                type: Input\n            }], preserveFragment: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], skipLocationChange: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], replaceUrl: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], routerLink: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', [\n                        '$event.button',\n                        '$event.ctrlKey',\n                        '$event.shiftKey',\n                        '$event.altKey',\n                        '$event.metaKey',\n                    ]]\n            }] } });\n\n/**\n *\n * @description\n *\n * Tracks whether the linked route of an element is currently active, and allows you\n * to specify one or more CSS classes to add to the element when the linked route\n * is active.\n *\n * Use this directive to create a visual distinction for elements associated with an active route.\n * For example, the following code highlights the word \"Bob\" when the router\n * activates the associated route:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n *\n * Whenever the URL is either '/user' or '/user/bob', the \"active-link\" class is\n * added to the anchor tag. If the URL changes, the class is removed.\n *\n * You can set more than one class using a space-separated string or an array.\n * For example:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"class1 class2\">Bob</a>\n * <a routerLink=\"/user/bob\" [routerLinkActive]=\"['class1', 'class2']\">Bob</a>\n * ```\n *\n * To add the classes only when the URL matches the link exactly, add the option `exact: true`:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact:\n * true}\">Bob</a>\n * ```\n *\n * To directly check the `isActive` status of the link, assign the `RouterLinkActive`\n * instance to a template variable.\n * For example, the following checks the status without assigning any CSS classes:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive #rla=\"routerLinkActive\">\n *   Bob {{ rla.isActive ? '(already open)' : ''}}\n * </a>\n * ```\n *\n * You can apply the `RouterLinkActive` directive to an ancestor of linked elements.\n * For example, the following sets the active-link class on the `<div>`  parent tag\n * when the URL is either '/user/jim' or '/user/bob'.\n *\n * ```html\n * <div routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact: true}\">\n *   <a routerLink=\"/user/jim\">Jim</a>\n *   <a routerLink=\"/user/bob\">Bob</a>\n * </div>\n * ```\n *\n * The `RouterLinkActive` directive can also be used to set the aria-current attribute\n * to provide an alternative distinction for active elements to visually impaired users.\n *\n * For example, the following code adds the 'active' class to the Home Page link when it is\n * indeed active and in such case also sets its aria-current attribute to 'page':\n *\n * ```html\n * <a routerLink=\"/\" routerLinkActive=\"active\" ariaCurrentWhenActive=\"page\">Home Page</a>\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass RouterLinkActive {\n    router;\n    element;\n    renderer;\n    cdr;\n    link;\n    links;\n    classes = [];\n    routerEventsSubscription;\n    linkInputChangesSubscription;\n    _isActive = false;\n    get isActive() {\n        return this._isActive;\n    }\n    /**\n     * Options to configure how to determine if the router link is active.\n     *\n     * These options are passed to the `Router.isActive()` function.\n     *\n     * @see {@link Router#isActive}\n     */\n    routerLinkActiveOptions = { exact: false };\n    /**\n     * Aria-current attribute to apply when the router link is active.\n     *\n     * Possible values: `'page'` | `'step'` | `'location'` | `'date'` | `'time'` | `true` | `false`.\n     *\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-current}\n     */\n    ariaCurrentWhenActive;\n    /**\n     *\n     * You can use the output `isActiveChange` to get notified each time the link becomes\n     * active or inactive.\n     *\n     * Emits:\n     * true  -> Route is active\n     * false -> Route is inactive\n     *\n     * ```html\n     * <a\n     *  routerLink=\"/user/bob\"\n     *  routerLinkActive=\"active-link\"\n     *  (isActiveChange)=\"this.onRouterLinkActive($event)\">Bob</a>\n     * ```\n     */\n    isActiveChange = new EventEmitter();\n    constructor(router, element, renderer, cdr, link) {\n        this.router = router;\n        this.element = element;\n        this.renderer = renderer;\n        this.cdr = cdr;\n        this.link = link;\n        this.routerEventsSubscription = router.events.subscribe((s) => {\n            if (s instanceof NavigationEnd) {\n                this.update();\n            }\n        });\n    }\n    /** @nodoc */\n    ngAfterContentInit() {\n        // `of(null)` is used to force subscribe body to execute once immediately (like `startWith`).\n        of(this.links.changes, of(null))\n            .pipe(mergeAll())\n            .subscribe((_) => {\n            this.update();\n            this.subscribeToEachLinkOnChanges();\n        });\n    }\n    subscribeToEachLinkOnChanges() {\n        this.linkInputChangesSubscription?.unsubscribe();\n        const allLinkChanges = [...this.links.toArray(), this.link]\n            .filter((link) => !!link)\n            .map((link) => link.onChanges);\n        this.linkInputChangesSubscription = from(allLinkChanges)\n            .pipe(mergeAll())\n            .subscribe((link) => {\n            if (this._isActive !== this.isLinkActive(this.router)(link)) {\n                this.update();\n            }\n        });\n    }\n    set routerLinkActive(data) {\n        const classes = Array.isArray(data) ? data : data.split(' ');\n        this.classes = classes.filter((c) => !!c);\n    }\n    /** @nodoc */\n    ngOnChanges(changes) {\n        this.update();\n    }\n    /** @nodoc */\n    ngOnDestroy() {\n        this.routerEventsSubscription.unsubscribe();\n        this.linkInputChangesSubscription?.unsubscribe();\n    }\n    update() {\n        if (!this.links || !this.router.navigated)\n            return;\n        queueMicrotask(() => {\n            const hasActiveLinks = this.hasActiveLinks();\n            this.classes.forEach((c) => {\n                if (hasActiveLinks) {\n                    this.renderer.addClass(this.element.nativeElement, c);\n                }\n                else {\n                    this.renderer.removeClass(this.element.nativeElement, c);\n                }\n            });\n            if (hasActiveLinks && this.ariaCurrentWhenActive !== undefined) {\n                this.renderer.setAttribute(this.element.nativeElement, 'aria-current', this.ariaCurrentWhenActive.toString());\n            }\n            else {\n                this.renderer.removeAttribute(this.element.nativeElement, 'aria-current');\n            }\n            // Only emit change if the active state changed.\n            if (this._isActive !== hasActiveLinks) {\n                this._isActive = hasActiveLinks;\n                this.cdr.markForCheck();\n                // Emit on isActiveChange after classes are updated\n                this.isActiveChange.emit(hasActiveLinks);\n            }\n        });\n    }\n    isLinkActive(router) {\n        const options = isActiveMatchOptions(this.routerLinkActiveOptions)\n            ? this.routerLinkActiveOptions\n            : // While the types should disallow `undefined` here, it's possible without strict inputs\n                this.routerLinkActiveOptions.exact || false;\n        return (link) => {\n            const urlTree = link.urlTree;\n            return urlTree ? router.isActive(urlTree, options) : false;\n        };\n    }\n    hasActiveLinks() {\n        const isActiveCheckFn = this.isLinkActive(this.router);\n        return (this.link && isActiveCheckFn(this.link)) || this.links.some(isActiveCheckFn);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterLinkActive, deps: [{ token: Router }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: RouterLink, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0-next.8\", type: RouterLinkActive, isStandalone: true, selector: \"[routerLinkActive]\", inputs: { routerLinkActiveOptions: \"routerLinkActiveOptions\", ariaCurrentWhenActive: \"ariaCurrentWhenActive\", routerLinkActive: \"routerLinkActive\" }, outputs: { isActiveChange: \"isActiveChange\" }, queries: [{ propertyName: \"links\", predicate: RouterLink, descendants: true }], exportAs: [\"routerLinkActive\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterLinkActive, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[routerLinkActive]',\n                    exportAs: 'routerLinkActive',\n                }]\n        }], ctorParameters: () => [{ type: Router }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: RouterLink, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { links: [{\n                type: ContentChildren,\n                args: [RouterLink, { descendants: true }]\n            }], routerLinkActiveOptions: [{\n                type: Input\n            }], ariaCurrentWhenActive: [{\n                type: Input\n            }], isActiveChange: [{\n                type: Output\n            }], routerLinkActive: [{\n                type: Input\n            }] } });\n/**\n * Use instead of `'paths' in options` to be compatible with property renaming\n */\nfunction isActiveMatchOptions(options) {\n    return !!options.paths;\n}\n\n/**\n * @description\n *\n * Provides a preloading strategy.\n *\n * @publicApi\n */\nclass PreloadingStrategy {\n}\n/**\n * @description\n *\n * Provides a preloading strategy that preloads all modules as quickly as possible.\n *\n * ```ts\n * RouterModule.forRoot(ROUTES, {preloadingStrategy: PreloadAllModules})\n * ```\n *\n * @publicApi\n */\nclass PreloadAllModules {\n    preload(route, fn) {\n        return fn().pipe(catchError(() => of(null)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: PreloadAllModules, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: PreloadAllModules, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: PreloadAllModules, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/**\n * @description\n *\n * Provides a preloading strategy that does not preload any modules.\n *\n * This strategy is enabled by default.\n *\n * @publicApi\n */\nclass NoPreloading {\n    preload(route, fn) {\n        return of(null);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: NoPreloading, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: NoPreloading, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: NoPreloading, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/**\n * The preloader optimistically loads all router configurations to\n * make navigations into lazily-loaded sections of the application faster.\n *\n * The preloader runs in the background. When the router bootstraps, the preloader\n * starts listening to all navigation events. After every such event, the preloader\n * will check if any configurations can be loaded lazily.\n *\n * If a route is protected by `canLoad` guards, the preloaded will not load it.\n *\n * @publicApi\n */\nclass RouterPreloader {\n    router;\n    injector;\n    preloadingStrategy;\n    loader;\n    subscription;\n    constructor(router, injector, preloadingStrategy, loader) {\n        this.router = router;\n        this.injector = injector;\n        this.preloadingStrategy = preloadingStrategy;\n        this.loader = loader;\n    }\n    setUpPreloading() {\n        this.subscription = this.router.events\n            .pipe(filter((e) => e instanceof NavigationEnd), concatMap(() => this.preload()))\n            .subscribe(() => { });\n    }\n    preload() {\n        return this.processRoutes(this.injector, this.router.config);\n    }\n    /** @nodoc */\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n    processRoutes(injector, routes) {\n        const res = [];\n        for (const route of routes) {\n            if (route.providers && !route._injector) {\n                route._injector = createEnvironmentInjector(route.providers, injector, `Route: ${route.path}`);\n            }\n            const injectorForCurrentRoute = route._injector ?? injector;\n            const injectorForChildren = route._loadedInjector ?? injectorForCurrentRoute;\n            // Note that `canLoad` is only checked as a condition that prevents `loadChildren` and not\n            // `loadComponent`. `canLoad` guards only block loading of child routes by design. This\n            // happens as a consequence of needing to descend into children for route matching immediately\n            // while component loading is deferred until route activation. Because `canLoad` guards can\n            // have side effects, we cannot execute them here so we instead skip preloading altogether\n            // when present. Lastly, it remains to be decided whether `canLoad` should behave this way\n            // at all. Code splitting and lazy loading is separate from client-side authorization checks\n            // and should not be used as a security measure to prevent loading of code.\n            if ((route.loadChildren && !route._loadedRoutes && route.canLoad === undefined) ||\n                (route.loadComponent && !route._loadedComponent)) {\n                res.push(this.preloadConfig(injectorForCurrentRoute, route));\n            }\n            if (route.children || route._loadedRoutes) {\n                res.push(this.processRoutes(injectorForChildren, (route.children ?? route._loadedRoutes)));\n            }\n        }\n        return from(res).pipe(mergeAll());\n    }\n    preloadConfig(injector, route) {\n        return this.preloadingStrategy.preload(route, () => {\n            let loadedChildren$;\n            if (route.loadChildren && route.canLoad === undefined) {\n                loadedChildren$ = this.loader.loadChildren(injector, route);\n            }\n            else {\n                loadedChildren$ = of(null);\n            }\n            const recursiveLoadChildren$ = loadedChildren$.pipe(mergeMap((config) => {\n                if (config === null) {\n                    return of(void 0);\n                }\n                route._loadedRoutes = config.routes;\n                route._loadedInjector = config.injector;\n                // If the loaded config was a module, use that as the module/module injector going\n                // forward. Otherwise, continue using the current module/module injector.\n                return this.processRoutes(config.injector ?? injector, config.routes);\n            }));\n            if (route.loadComponent && !route._loadedComponent) {\n                const loadComponent$ = this.loader.loadComponent(route);\n                return from([recursiveLoadChildren$, loadComponent$]).pipe(mergeAll());\n            }\n            else {\n                return recursiveLoadChildren$;\n            }\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterPreloader, deps: [{ token: Router }, { token: i0.EnvironmentInjector }, { token: PreloadingStrategy }, { token: RouterConfigLoader }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterPreloader, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterPreloader, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: Router }, { type: i0.EnvironmentInjector }, { type: PreloadingStrategy }, { type: RouterConfigLoader }] });\n\nconst ROUTER_SCROLLER = new InjectionToken('');\nclass RouterScroller {\n    urlSerializer;\n    transitions;\n    viewportScroller;\n    zone;\n    options;\n    routerEventsSubscription;\n    scrollEventsSubscription;\n    lastId = 0;\n    lastSource = IMPERATIVE_NAVIGATION;\n    restoredId = 0;\n    store = {};\n    /** @nodoc */\n    constructor(urlSerializer, transitions, viewportScroller, zone, options = {}) {\n        this.urlSerializer = urlSerializer;\n        this.transitions = transitions;\n        this.viewportScroller = viewportScroller;\n        this.zone = zone;\n        this.options = options;\n        // Default both options to 'disabled'\n        options.scrollPositionRestoration ||= 'disabled';\n        options.anchorScrolling ||= 'disabled';\n    }\n    init() {\n        // we want to disable the automatic scrolling because having two places\n        // responsible for scrolling results race conditions, especially given\n        // that browser don't implement this behavior consistently\n        if (this.options.scrollPositionRestoration !== 'disabled') {\n            this.viewportScroller.setHistoryScrollRestoration('manual');\n        }\n        this.routerEventsSubscription = this.createScrollEvents();\n        this.scrollEventsSubscription = this.consumeScrollEvents();\n    }\n    createScrollEvents() {\n        return this.transitions.events.subscribe((e) => {\n            if (e instanceof NavigationStart) {\n                // store the scroll position of the current stable navigations.\n                this.store[this.lastId] = this.viewportScroller.getScrollPosition();\n                this.lastSource = e.navigationTrigger;\n                this.restoredId = e.restoredState ? e.restoredState.navigationId : 0;\n            }\n            else if (e instanceof NavigationEnd) {\n                this.lastId = e.id;\n                this.scheduleScrollEvent(e, this.urlSerializer.parse(e.urlAfterRedirects).fragment);\n            }\n            else if (e instanceof NavigationSkipped &&\n                e.code === NavigationSkippedCode.IgnoredSameUrlNavigation) {\n                this.lastSource = undefined;\n                this.restoredId = 0;\n                this.scheduleScrollEvent(e, this.urlSerializer.parse(e.url).fragment);\n            }\n        });\n    }\n    consumeScrollEvents() {\n        return this.transitions.events.subscribe((e) => {\n            if (!(e instanceof Scroll))\n                return;\n            // a popstate event. The pop state event will always ignore anchor scrolling.\n            if (e.position) {\n                if (this.options.scrollPositionRestoration === 'top') {\n                    this.viewportScroller.scrollToPosition([0, 0]);\n                }\n                else if (this.options.scrollPositionRestoration === 'enabled') {\n                    this.viewportScroller.scrollToPosition(e.position);\n                }\n                // imperative navigation \"forward\"\n            }\n            else {\n                if (e.anchor && this.options.anchorScrolling === 'enabled') {\n                    this.viewportScroller.scrollToAnchor(e.anchor);\n                }\n                else if (this.options.scrollPositionRestoration !== 'disabled') {\n                    this.viewportScroller.scrollToPosition([0, 0]);\n                }\n            }\n        });\n    }\n    scheduleScrollEvent(routerEvent, anchor) {\n        this.zone.runOutsideAngular(async () => {\n            // The scroll event needs to be delayed until after change detection. Otherwise, we may\n            // attempt to restore the scroll position before the router outlet has fully rendered the\n            // component by executing its update block of the template function.\n            //\n            // #57109 (we need to wait at least a macrotask before scrolling. AfterNextRender resolves in microtask event loop with Zones)\n            // We could consider _also_ waiting for a render promise though one should have already happened or been scheduled by this point\n            // and should definitely happen before rAF/setTimeout.\n            // #53985 (cannot rely solely on setTimeout because a frame may paint before the timeout)\n            await new Promise((resolve) => {\n                setTimeout(resolve);\n                if (typeof requestAnimationFrame !== 'undefined') {\n                    requestAnimationFrame(resolve);\n                }\n            });\n            this.zone.run(() => {\n                this.transitions.events.next(new Scroll(routerEvent, this.lastSource === 'popstate' ? this.store[this.restoredId] : null, anchor));\n            });\n        });\n    }\n    /** @nodoc */\n    ngOnDestroy() {\n        this.routerEventsSubscription?.unsubscribe();\n        this.scrollEventsSubscription?.unsubscribe();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterScroller, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterScroller });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterScroller, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: UrlSerializer }, { type: NavigationTransitions }, { type: i3.ViewportScroller }, { type: i0.NgZone }, { type: undefined }] });\n\n/**\n * Sets up providers necessary to enable `Router` functionality for the application.\n * Allows to configure a set of routes as well as extra features that should be enabled.\n *\n * @usageNotes\n *\n * Basic example of how you can add a Router to your application:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent, {\n *   providers: [provideRouter(appRoutes)]\n * });\n * ```\n *\n * You can also enable optional features in the Router by adding functions from the `RouterFeatures`\n * type:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes,\n *         withDebugTracing(),\n *         withRouterConfig({paramsInheritanceStrategy: 'always'}))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link RouterFeatures}\n *\n * @publicApi\n * @param routes A set of `Route`s to use for the application routing table.\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to setup a Router.\n */\nfunction provideRouter(routes, ...features) {\n    return makeEnvironmentProviders([\n        { provide: ROUTES, multi: true, useValue: routes },\n        typeof ngDevMode === 'undefined' || ngDevMode\n            ? { provide: ROUTER_IS_PROVIDED, useValue: true }\n            : [],\n        { provide: ActivatedRoute, useFactory: rootRoute, deps: [Router] },\n        { provide: APP_BOOTSTRAP_LISTENER, multi: true, useFactory: getBootstrapListener },\n        features.map((feature) => feature.ɵproviders),\n    ]);\n}\nfunction rootRoute(router) {\n    return router.routerState.root;\n}\n/**\n * Helper function to create an object that represents a Router feature.\n */\nfunction routerFeature(kind, providers) {\n    return { ɵkind: kind, ɵproviders: providers };\n}\n/**\n * An Injection token used to indicate whether `provideRouter` or `RouterModule.forRoot` was ever\n * called.\n */\nconst ROUTER_IS_PROVIDED = new InjectionToken('', {\n    providedIn: 'root',\n    factory: () => false,\n});\nconst routerIsProvidedDevModeCheck = {\n    provide: ENVIRONMENT_INITIALIZER,\n    multi: true,\n    useFactory() {\n        return () => {\n            if (!inject(ROUTER_IS_PROVIDED)) {\n                console.warn('`provideRoutes` was called without `provideRouter` or `RouterModule.forRoot`. ' +\n                    'This is likely a mistake.');\n            }\n        };\n    },\n};\n/**\n * Registers a DI provider for a set of routes.\n * @param routes The route configuration to provide.\n *\n * @usageNotes\n *\n * ```ts\n * @NgModule({\n *   providers: [provideRoutes(ROUTES)]\n * })\n * class LazyLoadedChildModule {}\n * ```\n *\n * @deprecated If necessary, provide routes using the `ROUTES` `InjectionToken`.\n * @see {@link ROUTES}\n * @publicApi\n */\nfunction provideRoutes(routes) {\n    return [\n        { provide: ROUTES, multi: true, useValue: routes },\n        typeof ngDevMode === 'undefined' || ngDevMode ? routerIsProvidedDevModeCheck : [],\n    ];\n}\n/**\n * Enables customizable scrolling behavior for router navigations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable scrolling feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withInMemoryScrolling())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link ViewportScroller}\n *\n * @publicApi\n * @param options Set of configuration parameters to customize scrolling behavior, see\n *     `InMemoryScrollingOptions` for additional information.\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withInMemoryScrolling(options = {}) {\n    const providers = [\n        {\n            provide: ROUTER_SCROLLER,\n            useFactory: () => {\n                const viewportScroller = inject(ViewportScroller);\n                const zone = inject(NgZone);\n                const transitions = inject(NavigationTransitions);\n                const urlSerializer = inject(UrlSerializer);\n                return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, options);\n            },\n        },\n    ];\n    return routerFeature(4 /* RouterFeatureKind.InMemoryScrollingFeature */, providers);\n}\nfunction getBootstrapListener() {\n    const injector = inject(Injector);\n    return (bootstrappedComponentRef) => {\n        const ref = injector.get(ApplicationRef);\n        if (bootstrappedComponentRef !== ref.components[0]) {\n            return;\n        }\n        const router = injector.get(Router);\n        const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n        if (injector.get(INITIAL_NAVIGATION) === 1 /* InitialNavigation.EnabledNonBlocking */) {\n            router.initialNavigation();\n        }\n        injector.get(ROUTER_PRELOADER, null, { optional: true })?.setUpPreloading();\n        injector.get(ROUTER_SCROLLER, null, { optional: true })?.init();\n        router.resetRootComponentType(ref.componentTypes[0]);\n        if (!bootstrapDone.closed) {\n            bootstrapDone.next();\n            bootstrapDone.complete();\n            bootstrapDone.unsubscribe();\n        }\n    };\n}\n/**\n * A subject used to indicate that the bootstrapping phase is done. When initial navigation is\n * `enabledBlocking`, the first navigation waits until bootstrapping is finished before continuing\n * to the activation phase.\n */\nconst BOOTSTRAP_DONE = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'bootstrap done indicator' : '', {\n    factory: () => {\n        return new Subject();\n    },\n});\nconst INITIAL_NAVIGATION = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'initial navigation' : '', { providedIn: 'root', factory: () => 1 /* InitialNavigation.EnabledNonBlocking */ });\n/**\n * Configures initial navigation to start before the root component is created.\n *\n * The bootstrap is blocked until the initial navigation is complete. This should be set in case\n * you use [server-side rendering](guide/ssr), but do not enable [hydration](guide/hydration) for\n * your application.\n *\n * @usageNotes\n *\n * Basic example of how you can enable this navigation behavior:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withEnabledBlockingInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @publicApi\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withEnabledBlockingInitialNavigation() {\n    const providers = [\n        { provide: INITIAL_NAVIGATION, useValue: 0 /* InitialNavigation.EnabledBlocking */ },\n        provideAppInitializer(() => {\n            const injector = inject(Injector);\n            const locationInitialized = injector.get(LOCATION_INITIALIZED, Promise.resolve());\n            return locationInitialized.then(() => {\n                return new Promise((resolve) => {\n                    const router = injector.get(Router);\n                    const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n                    afterNextNavigation(router, () => {\n                        // Unblock APP_INITIALIZER in case the initial navigation was canceled or errored\n                        // without a redirect.\n                        resolve(true);\n                    });\n                    injector.get(NavigationTransitions).afterPreactivation = () => {\n                        // Unblock APP_INITIALIZER once we get to `afterPreactivation`. At this point, we\n                        // assume activation will complete successfully (even though this is not\n                        // guaranteed).\n                        resolve(true);\n                        return bootstrapDone.closed ? of(void 0) : bootstrapDone;\n                    };\n                    router.initialNavigation();\n                });\n            });\n        }),\n    ];\n    return routerFeature(2 /* RouterFeatureKind.EnabledBlockingInitialNavigationFeature */, providers);\n}\n/**\n * Disables initial navigation.\n *\n * Use if there is a reason to have more control over when the router starts its initial navigation\n * due to some complex initialization logic.\n *\n * @usageNotes\n *\n * Basic example of how you can disable initial navigation:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDisabledInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDisabledInitialNavigation() {\n    const providers = [\n        provideAppInitializer(() => {\n            inject(Router).setUpLocationChangeListener();\n        }),\n        { provide: INITIAL_NAVIGATION, useValue: 2 /* InitialNavigation.Disabled */ },\n    ];\n    return routerFeature(3 /* RouterFeatureKind.DisabledInitialNavigationFeature */, providers);\n}\n/**\n * Enables logging of all internal navigation events to the console.\n * Extra logging might be useful for debugging purposes to inspect Router event sequence.\n *\n * @usageNotes\n *\n * Basic example of how you can enable debug tracing:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDebugTracing())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDebugTracing() {\n    let providers = [];\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        providers = [\n            {\n                provide: ENVIRONMENT_INITIALIZER,\n                multi: true,\n                useFactory: () => {\n                    const router = inject(Router);\n                    return () => router.events.subscribe((e) => {\n                        // tslint:disable:no-console\n                        console.group?.(`Router Event: ${e.constructor.name}`);\n                        console.log(stringifyEvent(e));\n                        console.log(e);\n                        console.groupEnd?.();\n                        // tslint:enable:no-console\n                    });\n                },\n            },\n        ];\n    }\n    else {\n        providers = [];\n    }\n    return routerFeature(1 /* RouterFeatureKind.DebugTracingFeature */, providers);\n}\nconst ROUTER_PRELOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router preloader' : '');\n/**\n * Allows to configure a preloading strategy to use. The strategy is configured by providing a\n * reference to a class that implements a `PreloadingStrategy`.\n *\n * @usageNotes\n *\n * Basic example of how you can configure preloading:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withPreloading(PreloadAllModules))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param preloadingStrategy A reference to a class that implements a `PreloadingStrategy` that\n *     should be used.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withPreloading(preloadingStrategy) {\n    const providers = [\n        { provide: ROUTER_PRELOADER, useExisting: RouterPreloader },\n        { provide: PreloadingStrategy, useExisting: preloadingStrategy },\n    ];\n    return routerFeature(0 /* RouterFeatureKind.PreloadingFeature */, providers);\n}\n/**\n * Allows to provide extra parameters to configure Router.\n *\n * @usageNotes\n *\n * Basic example of how you can provide extra configuration options:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withRouterConfig({\n *          onSameUrlNavigation: 'reload'\n *       }))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param options A set of parameters to configure Router, see `RouterConfigOptions` for\n *     additional information.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withRouterConfig(options) {\n    const providers = [{ provide: ROUTER_CONFIGURATION, useValue: options }];\n    return routerFeature(5 /* RouterFeatureKind.RouterConfigurationFeature */, providers);\n}\n/**\n * Provides the location strategy that uses the URL fragment instead of the history API.\n *\n * @usageNotes\n *\n * Basic example of how you can use the hash location option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withHashLocation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link /api/common/HashLocationStrategy HashLocationStrategy}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withHashLocation() {\n    const providers = [{ provide: LocationStrategy, useClass: HashLocationStrategy }];\n    return routerFeature(6 /* RouterFeatureKind.RouterHashLocationFeature */, providers);\n}\n/**\n * Provides a function which is called when a navigation error occurs.\n *\n * This function is run inside application's [injection context](guide/di/dependency-injection-context)\n * so you can use the [`inject`](api/core/inject) function.\n *\n * This function can return a `RedirectCommand` to convert the error to a redirect, similar to returning\n * a `UrlTree` or `RedirectCommand` from a guard. This will also prevent the `Router` from emitting\n * `NavigationError`; it will instead emit `NavigationCancel` with code NavigationCancellationCode.Redirect.\n * Return values other than `RedirectCommand` are ignored and do not change any behavior with respect to\n * how the `Router` handles the error.\n *\n * @usageNotes\n *\n * Basic example of how you can use the error handler option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withNavigationErrorHandler((e: NavigationError) =>\n * inject(MyErrorTracker).trackError(e)))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link NavigationError}\n * @see {@link /api/core/inject inject}\n * @see {@link runInInjectionContext}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withNavigationErrorHandler(handler) {\n    const providers = [\n        {\n            provide: NAVIGATION_ERROR_HANDLER,\n            useValue: handler,\n        },\n    ];\n    return routerFeature(7 /* RouterFeatureKind.NavigationErrorHandlerFeature */, providers);\n}\n/**\n * Enables binding information from the `Router` state directly to the inputs of the component in\n * `Route` configurations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withComponentInputBinding())\n *     ]\n *   }\n * );\n * ```\n *\n * The router bindings information from any of the following sources:\n *\n *  - query parameters\n *  - path and matrix parameters\n *  - static route data\n *  - data from resolvers\n *\n * Duplicate keys are resolved in the same order from above, from least to greatest,\n * meaning that resolvers have the highest precedence and override any of the other information\n * from the route.\n *\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. This prevents previous information from being\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n * Default values can be provided with a resolver on the route to ensure the value is always present\n * or an input and use an input transform in the component.\n *\n * @see {@link /guide/components/inputs#input-transforms Input Transforms}\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withComponentInputBinding() {\n    const providers = [\n        RoutedComponentInputBinder,\n        { provide: INPUT_BINDER, useExisting: RoutedComponentInputBinder },\n    ];\n    return routerFeature(8 /* RouterFeatureKind.ComponentInputBindingFeature */, providers);\n}\n/**\n * Enables view transitions in the Router by running the route activation and deactivation inside of\n * `document.startViewTransition`.\n *\n * Note: The View Transitions API is not available in all browsers. If the browser does not support\n * view transitions, the Router will not attempt to start a view transition and continue processing\n * the navigation as usual.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withViewTransitions())\n *     ]\n *   }\n * );\n * ```\n *\n * @returns A set of providers for use with `provideRouter`.\n * @see https://developer.chrome.com/docs/web-platform/view-transitions/\n * @see https://developer.mozilla.org/en-US/docs/Web/API/View_Transitions_API\n * @developerPreview\n */\nfunction withViewTransitions(options) {\n    _performanceMarkFeature('NgRouterViewTransitions');\n    const providers = [\n        { provide: CREATE_VIEW_TRANSITION, useValue: createViewTransition },\n        {\n            provide: VIEW_TRANSITION_OPTIONS,\n            useValue: { skipNextTransition: !!options?.skipInitialTransition, ...options },\n        },\n    ];\n    return routerFeature(9 /* RouterFeatureKind.ViewTransitionsFeature */, providers);\n}\n\n/**\n * The directives defined in the `RouterModule`.\n */\nconst ROUTER_DIRECTIVES = [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent];\n/**\n * @docsNotRequired\n */\nconst ROUTER_FORROOT_GUARD = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router duplicate forRoot guard' : '');\n// TODO(atscott): All of these except `ActivatedRoute` are `providedIn: 'root'`. They are only kept\n// here to avoid a breaking change whereby the provider order matters based on where the\n// `RouterModule`/`RouterTestingModule` is imported. These can/should be removed as a \"breaking\"\n// change in a major version.\nconst ROUTER_PROVIDERS = [\n    Location,\n    { provide: UrlSerializer, useClass: DefaultUrlSerializer },\n    Router,\n    ChildrenOutletContexts,\n    { provide: ActivatedRoute, useFactory: rootRoute, deps: [Router] },\n    RouterConfigLoader,\n    // Only used to warn when `provideRoutes` is used without `RouterModule` or `provideRouter`. Can\n    // be removed when `provideRoutes` is removed.\n    typeof ngDevMode === 'undefined' || ngDevMode\n        ? { provide: ROUTER_IS_PROVIDED, useValue: true }\n        : [],\n];\n/**\n * @description\n *\n * Adds directives and providers for in-app navigation among views defined in an application.\n * Use the Angular `Router` service to declaratively specify application states and manage state\n * transitions.\n *\n * You can import this NgModule multiple times, once for each lazy-loaded bundle.\n * However, only one `Router` service can be active.\n * To ensure this, there are two ways to register routes when importing this module:\n *\n * * The `forRoot()` method creates an `NgModule` that contains all the directives, the given\n * routes, and the `Router` service itself.\n * * The `forChild()` method creates an `NgModule` that contains all the directives and the given\n * routes, but does not include the `Router` service.\n *\n * @see [Routing and Navigation guide](guide/routing/common-router-tasks) for an\n * overview of how the `Router` service should be used.\n *\n * @publicApi\n */\nclass RouterModule {\n    constructor() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            inject(ROUTER_FORROOT_GUARD, { optional: true });\n        }\n    }\n    /**\n     * Creates and configures a module with all the router providers and directives.\n     * Optionally sets up an application listener to perform an initial navigation.\n     *\n     * When registering the NgModule at the root, import as follows:\n     *\n     * ```ts\n     * @NgModule({\n     *   imports: [RouterModule.forRoot(ROUTES)]\n     * })\n     * class MyNgModule {}\n     * ```\n     *\n     * @param routes An array of `Route` objects that define the navigation paths for the application.\n     * @param config An `ExtraOptions` configuration object that controls how navigation is performed.\n     * @return The new `NgModule`.\n     *\n     */\n    static forRoot(routes, config) {\n        return {\n            ngModule: RouterModule,\n            providers: [\n                ROUTER_PROVIDERS,\n                typeof ngDevMode === 'undefined' || ngDevMode\n                    ? config?.enableTracing\n                        ? withDebugTracing().ɵproviders\n                        : []\n                    : [],\n                { provide: ROUTES, multi: true, useValue: routes },\n                typeof ngDevMode === 'undefined' || ngDevMode\n                    ? {\n                        provide: ROUTER_FORROOT_GUARD,\n                        useFactory: provideForRootGuard,\n                        deps: [[Router, new Optional(), new SkipSelf()]],\n                    }\n                    : [],\n                config?.errorHandler\n                    ? {\n                        provide: NAVIGATION_ERROR_HANDLER,\n                        useValue: config.errorHandler,\n                    }\n                    : [],\n                { provide: ROUTER_CONFIGURATION, useValue: config ? config : {} },\n                config?.useHash ? provideHashLocationStrategy() : providePathLocationStrategy(),\n                provideRouterScroller(),\n                config?.preloadingStrategy ? withPreloading(config.preloadingStrategy).ɵproviders : [],\n                config?.initialNavigation ? provideInitialNavigation(config) : [],\n                config?.bindToComponentInputs ? withComponentInputBinding().ɵproviders : [],\n                config?.enableViewTransitions ? withViewTransitions().ɵproviders : [],\n                provideRouterInitializer(),\n            ],\n        };\n    }\n    /**\n     * Creates a module with all the router directives and a provider registering routes,\n     * without creating a new Router service.\n     * When registering for submodules and lazy-loaded submodules, create the NgModule as follows:\n     *\n     * ```ts\n     * @NgModule({\n     *   imports: [RouterModule.forChild(ROUTES)]\n     * })\n     * class MyNgModule {}\n     * ```\n     *\n     * @param routes An array of `Route` objects that define the navigation paths for the submodule.\n     * @return The new NgModule.\n     *\n     */\n    static forChild(routes) {\n        return {\n            ngModule: RouterModule,\n            providers: [{ provide: ROUTES, multi: true, useValue: routes }],\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterModule, imports: [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent], exports: [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0-next.8\", ngImport: i0, type: RouterModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: ROUTER_DIRECTIVES,\n                    exports: ROUTER_DIRECTIVES,\n                }]\n        }], ctorParameters: () => [] });\n/**\n * For internal use by `RouterModule` only. Note that this differs from `withInMemoryRouterScroller`\n * because it reads from the `ExtraOptions` which should not be used in the standalone world.\n */\nfunction provideRouterScroller() {\n    return {\n        provide: ROUTER_SCROLLER,\n        useFactory: () => {\n            const viewportScroller = inject(ViewportScroller);\n            const zone = inject(NgZone);\n            const config = inject(ROUTER_CONFIGURATION);\n            const transitions = inject(NavigationTransitions);\n            const urlSerializer = inject(UrlSerializer);\n            if (config.scrollOffset) {\n                viewportScroller.setOffset(config.scrollOffset);\n            }\n            return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, config);\n        },\n    };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` should\n// provide hash location directly via `{provide: LocationStrategy, useClass: HashLocationStrategy}`.\nfunction provideHashLocationStrategy() {\n    return { provide: LocationStrategy, useClass: HashLocationStrategy };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` does not\n// need this at all because `PathLocationStrategy` is the default factory for `LocationStrategy`.\nfunction providePathLocationStrategy() {\n    return { provide: LocationStrategy, useClass: PathLocationStrategy };\n}\nfunction provideForRootGuard(router) {\n    if (router) {\n        throw new _RuntimeError(4007 /* RuntimeErrorCode.FOR_ROOT_CALLED_TWICE */, `The Router was provided more than once. This can happen if 'forRoot' is used outside of the root injector.` +\n            ` Lazy loaded modules should use RouterModule.forChild() instead.`);\n    }\n    return 'guarded';\n}\n// Note: For internal use only with `RouterModule`. Standalone router setup with `provideRouter`\n// users call `withXInitialNavigation` directly.\nfunction provideInitialNavigation(config) {\n    return [\n        config.initialNavigation === 'disabled' ? withDisabledInitialNavigation().ɵproviders : [],\n        config.initialNavigation === 'enabledBlocking'\n            ? withEnabledBlockingInitialNavigation().ɵproviders\n            : [],\n    ];\n}\n// TODO(atscott): This should not be in the public API\n/**\n * A DI token for the router initializer that\n * is called after the app is bootstrapped.\n *\n * @publicApi\n */\nconst ROUTER_INITIALIZER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Router Initializer' : '');\nfunction provideRouterInitializer() {\n    return [\n        // ROUTER_INITIALIZER token should be removed. It's public API but shouldn't be. We can just\n        // have `getBootstrapListener` directly attached to APP_BOOTSTRAP_LISTENER.\n        { provide: ROUTER_INITIALIZER, useFactory: getBootstrapListener },\n        { provide: APP_BOOTSTRAP_LISTENER, multi: true, useExisting: ROUTER_INITIALIZER },\n    ];\n}\n\nexport { NoPreloading, PreloadAllModules, PreloadingStrategy, ROUTER_INITIALIZER, ROUTER_PROVIDERS, RouterLink, RouterLinkActive, RouterModule, RouterPreloader, provideRouter, provideRoutes, withComponentInputBinding, withDebugTracing, withDisabledInitialNavigation, withEnabledBlockingInitialNavigation, withHashLocation, withInMemoryScrolling, withNavigationErrorHandler, withPreloading, withRouterConfig, withViewTransitions };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,oBAAoB,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,oBAAoB,QAAQ,iBAAiB;AAChJ,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,mCAAmC,IAAIC,mCAAmC,EAAEC,aAAa,IAAIC,aAAa,EAAEC,0BAA0B,IAAIC,0BAA0B,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,KAAK,EAAEC,WAAW,EAAEC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,yBAAyB,EAAEC,UAAU,EAAEC,cAAc,EAAEC,uBAAuB,IAAIC,uBAAuB,EAAEC,wBAAwB,EAAEC,sBAAsB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAC1kB,SAASC,aAAa,EAAEC,SAAS,EAAEC,MAAM,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,wBAAwB,EAAEC,0BAA0B,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,uBAAuB,EAAEC,cAAc,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,YAAY,EAAEC,qBAAqB,IAAIC,qBAAqB,QAAQ,uBAAuB;AACtiB,SAASC,OAAO,EAAEC,EAAE,EAAEC,IAAI,QAAQ,MAAM;AACxC,SAASC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;;AAElF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,MAAM;EACNC,KAAK;EACLC,iBAAiB;EACjBC,QAAQ;EACRC,EAAE;EACFC,gBAAgB;EAChB;AACJ;AACA;AACA;AACA;EACIC,IAAI,GAAG,IAAI;EACX;AACJ;AACA;AACA;AACA;EACIC,MAAM;EACN;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAW;EACX;AACJ;AACA;AACA;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;AACA;AACA;EACIC,mBAAmB;EACnB;AACJ;AACA;AACA;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;AACA;AACA;EACIC,IAAI;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,UAAU;EACV;EACAC,eAAe;EACfC,YAAY;EACZ;EACAC,SAAS,GAAG,IAAIzB,OAAO,CAAC,CAAC;EACzB0B,uBAAuB,GAAGpF,MAAM,CAACE,mCAAmC,CAAC;EACrEmF,WAAWA,CAAClB,MAAM,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,gBAAgB,EAAE;IAC1E,IAAI,CAACL,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,MAAMc,OAAO,GAAGf,EAAE,CAACgB,aAAa,CAACD,OAAO,EAAEE,WAAW,CAAC,CAAC;IACvD,IAAI,CAACP,eAAe,GAChBK,OAAO,KAAK,GAAG,IACXA,OAAO,KAAK,MAAM,IAClB,CAAC;IACD;IACC,OAAOG,cAAc,KAAK,QAAQ;IAC/B;IACA;IACAA,cAAc,CAACC,GAAG,CAACJ,OAAO,CAAC,EAAEK,kBAAkB,EAAEC,QAAQ,GAAG,MAAM,CAAC,CAAE;IACjF,IAAI,IAAI,CAACX,eAAe,EAAE;MACtB,IAAI,CAACC,YAAY,GAAGf,MAAM,CAAC0B,MAAM,CAACC,SAAS,CAAEC,CAAC,IAAK;QAC/C,IAAIA,CAAC,YAAYhE,aAAa,EAAE;UAC5B,IAAI,CAACiE,UAAU,CAAC,CAAC;QACrB;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACC,0BAA0B,CAAC,GAAG,CAAC;IACxC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,gBAAgB,GAAG,KAAK;EACxB;AACJ;AACA;AACA;AACA;AACA;EACIC,kBAAkB,GAAG,KAAK;EAC1B;AACJ;AACA;AACA;AACA;AACA;EACIC,UAAU,GAAG,KAAK;EAClB;AACJ;AACA;AACA;EACIH,0BAA0BA,CAACI,WAAW,EAAE;IACpC,IAAI,IAAI,CAAChC,iBAAiB,IAAI,IAAI,CAAC,qCAAqC,IAAI,CAACY,eAAe,EAAE;MAC1F;IACJ;IACA,IAAI,CAACqB,mBAAmB,CAAC,UAAU,EAAED,WAAW,CAAC;EACrD;EACA;EACA;EACAE,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIC,SAAS,IACTzE,SAAS,CAAC,IAAI,CAAC0E,eAAe,CAAC,KAC9B,IAAI,CAAC9B,QAAQ,KAAK+B,SAAS,IACxB,IAAI,CAAChC,WAAW,IAChB,IAAI,CAACE,mBAAmB,IACxB,IAAI,CAACqB,gBAAgB,IACrB,IAAI,CAAClB,UAAU,CAAC,EAAE;MACtB,MAAM,IAAI5E,aAAa,CAAC,IAAI,CAAC,mDAAmD,8FAA8F,CAAC;IACnL;IACA,IAAI,IAAI,CAAC6E,eAAe,EAAE;MACtB,IAAI,CAACe,UAAU,CAAC,CAAC;IACrB;IACA;IACA;IACA,IAAI,CAACb,SAAS,CAACyB,IAAI,CAAC,IAAI,CAAC;EAC7B;EACAF,eAAe,GAAG,IAAI;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIG,UAAUA,CAACC,iBAAiB,EAAE;IAC9B,IAAIA,iBAAiB,IAAI,IAAI,EAAE;MAC3B,IAAI,CAACJ,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACT,0BAA0B,CAAC,IAAI,CAAC;IACzC,CAAC,MACI;MACD,IAAIjE,SAAS,CAAC8E,iBAAiB,CAAC,EAAE;QAC9B,IAAI,CAACJ,eAAe,GAAGI,iBAAiB;MAC5C,CAAC,MACI;QACD,IAAI,CAACJ,eAAe,GAAGK,KAAK,CAACC,OAAO,CAACF,iBAAiB,CAAC,GACjDA,iBAAiB,GACjB,CAACA,iBAAiB,CAAC;MAC7B;MACA,IAAI,CAACb,0BAA0B,CAAC,GAAG,CAAC;IACxC;EACJ;EACA;EACAgB,OAAOA,CAACC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;IAChD,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAIA,OAAO,KAAK,IAAI,EAAE;MAClB,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAACtC,eAAe,EAAE;MACtB,IAAIiC,MAAM,KAAK,CAAC,IAAIC,OAAO,IAAIC,QAAQ,IAAIC,MAAM,IAAIC,OAAO,EAAE;QAC1D,OAAO,IAAI;MACf;MACA,IAAI,OAAO,IAAI,CAAC5C,MAAM,KAAK,QAAQ,IAAI,IAAI,CAACA,MAAM,IAAI,OAAO,EAAE;QAC3D,OAAO,IAAI;MACf;IACJ;IACA,MAAM8C,MAAM,GAAG;MACXrB,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;MAC3CC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BtB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,IAAI,EAAE,IAAI,CAACA;IACf,CAAC;IACD;IACA,IAAI,CAACZ,MAAM,CAACsD,aAAa,CAACF,OAAO,EAAEC,MAAM,CAAC,EAAEE,KAAK,CAAEC,CAAC,IAAK;MACrD,IAAI,CAACvC,uBAAuB,CAACuC,CAAC,CAAC;IACnC,CAAC,CAAC;IACF;IACA;IACA;IACA,OAAO,CAAC,IAAI,CAAC1C,eAAe;EAChC;EACA;EACA2C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC1C,YAAY,EAAE2C,WAAW,CAAC,CAAC;EACpC;EACA7B,UAAUA,CAAA,EAAG;IACT,MAAMuB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAI,CAAC9C,IAAI,GACL8C,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC/C,gBAAgB,GACnC,IAAI,CAACA,gBAAgB,EAAEsD,kBAAkB,CAAC,IAAI,CAAC3D,MAAM,CAAC4D,YAAY,CAACR,OAAO,CAAC,CAAC,GAC5E,IAAI;IACd,MAAMS,cAAc,GAAG,IAAI,CAACvD,IAAI,KAAK,IAAI,GACnC,IAAI;IACJ;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAnE,0BAA0B,CAAC,IAAI,CAACmE,IAAI,EAAE,IAAI,CAACF,EAAE,CAACgB,aAAa,CAACD,OAAO,CAACE,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC;IAClG,IAAI,CAACc,mBAAmB,CAAC,MAAM,EAAE0B,cAAc,CAAC;EACpD;EACA1B,mBAAmBA,CAAC2B,QAAQ,EAAEC,SAAS,EAAE;IACrC,MAAM5D,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMiB,aAAa,GAAG,IAAI,CAAChB,EAAE,CAACgB,aAAa;IAC3C,IAAI2C,SAAS,KAAK,IAAI,EAAE;MACpB5D,QAAQ,CAAC6D,YAAY,CAAC5C,aAAa,EAAE0C,QAAQ,EAAEC,SAAS,CAAC;IAC7D,CAAC,MACI;MACD5D,QAAQ,CAAC8D,eAAe,CAAC7C,aAAa,EAAE0C,QAAQ,CAAC;IACrD;EACJ;EACA,IAAIV,OAAOA,CAAA,EAAG;IACV,IAAI,IAAI,CAACb,eAAe,KAAK,IAAI,EAAE;MAC/B,OAAO,IAAI;IACf,CAAC,MACI,IAAI1E,SAAS,CAAC,IAAI,CAAC0E,eAAe,CAAC,EAAE;MACtC,OAAO,IAAI,CAACA,eAAe;IAC/B;IACA,OAAO,IAAI,CAACvC,MAAM,CAACkE,aAAa,CAAC,IAAI,CAAC3B,eAAe,EAAE;MACnD;MACA;MACA1B,UAAU,EAAE,IAAI,CAACA,UAAU,KAAK2B,SAAS,GAAG,IAAI,CAAC3B,UAAU,GAAG,IAAI,CAACZ,KAAK;MACxEO,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CqB,gBAAgB,EAAE,IAAI,CAACA;IAC3B,CAAC,CAAC;EACN;EACA,OAAOoC,IAAI,YAAAC,mBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAA+FtE,UAAU,EAApBnE,EAAE,CAAA0I,iBAAA,CAAoCxG,MAAM,GAA5ClC,EAAE,CAAA0I,iBAAA,CAAuDvG,cAAc,GAAvEnC,EAAE,CAAA2I,iBAAA,CAAkF,UAAU,GAA9F3I,EAAE,CAAA0I,iBAAA,CAA0H1I,EAAE,CAAC4I,SAAS,GAAxI5I,EAAE,CAAA0I,iBAAA,CAAmJ1I,EAAE,CAAC6I,UAAU,GAAlK7I,EAAE,CAAA0I,iBAAA,CAA6KjJ,EAAE,CAACG,gBAAgB;EAAA;EAClS,OAAOkJ,IAAI,kBADqF9I,EAAE,CAAA+I,iBAAA;IAAAC,IAAA,EACJ7E,UAAU;IAAA8E,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADRrJ,EAAE,CAAAuJ,UAAA,mBAAAC,oCAAAC,MAAA;UAAA,OACJH,GAAA,CAAApC,OAAA,CAAAuC,MAAA,CAAAtC,MAAA,EAAAsC,MAAA,CAAArC,OAAA,EAAAqC,MAAA,CAAApC,QAAA,EAAAoC,MAAA,CAAAnC,MAAA,EAAAmC,MAAA,CAAAlC,OAAiF,CAAC;QAAA,CAAzE,CAAC;MAAA;MAAA,IAAA8B,EAAA;QADRrJ,EAAE,CAAA0J,WAAA,WAAAJ,GAAA,CAAA3E,MAAA;MAAA;IAAA;IAAAgF,MAAA;MAAAhF,MAAA;MAAAC,WAAA;MAAAC,QAAA;MAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,UAAA;MAAAkB,gBAAA,8CACmS3F,gBAAgB;MAAA4F,kBAAA,kDAAoE5F,gBAAgB;MAAA6F,UAAA,kCAA4C7F,gBAAgB;MAAAsG,UAAA;IAAA;IAAA8C,QAAA,GADrc5J,EAAE,CAAA6J,oBAAA;EAAA;AAEtG;AACA;EAAA,QAAAnD,SAAA,oBAAAA,SAAA,KAHoG1G,EAAE,CAAA8J,iBAAA,CAGJ3F,UAAU,EAAc,CAAC;IAC/G6E,IAAI,EAAEnI,SAAS;IACfkJ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEhB,IAAI,EAAE9G;EAAO,CAAC,EAAE;IAAE8G,IAAI,EAAE7G;EAAe,CAAC,EAAE;IAAE6G,IAAI,EAAEpC,SAAS;IAAEqD,UAAU,EAAE,CAAC;MAC3FjB,IAAI,EAAEpI,SAAS;MACfmJ,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAEf,IAAI,EAAEhJ,EAAE,CAAC4I;EAAU,CAAC,EAAE;IAAEI,IAAI,EAAEhJ,EAAE,CAAC6I;EAAW,CAAC,EAAE;IAAEG,IAAI,EAAEvJ,EAAE,CAACG;EAAiB,CAAC,CAAC,EAAkB;IAAE+E,MAAM,EAAE,CAAC;MAClHqE,IAAI,EAAErI,WAAW;MACjBoJ,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,EAAE;MACCf,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEkE,WAAW,EAAE,CAAC;MACdoE,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEmE,QAAQ,EAAE,CAAC;MACXmE,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEoE,mBAAmB,EAAE,CAAC;MACtBkE,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEqE,KAAK,EAAE,CAAC;MACRiE,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEsE,IAAI,EAAE,CAAC;MACPgE,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEuE,UAAU,EAAE,CAAC;MACb+D,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEyF,gBAAgB,EAAE,CAAC;MACnB6C,IAAI,EAAEtI,KAAK;MACXqJ,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAE1J;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4F,kBAAkB,EAAE,CAAC;MACrB4C,IAAI,EAAEtI,KAAK;MACXqJ,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAE1J;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6F,UAAU,EAAE,CAAC;MACb2C,IAAI,EAAEtI,KAAK;MACXqJ,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAE1J;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsG,UAAU,EAAE,CAAC;MACbkC,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEwG,OAAO,EAAE,CAAC;MACV8B,IAAI,EAAEvI,YAAY;MAClBsJ,IAAI,EAAE,CAAC,OAAO,EAAE,CACR,eAAe,EACf,gBAAgB,EAChB,iBAAiB,EACjB,eAAe,EACf,gBAAgB,CACnB;IACT,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,gBAAgB,CAAC;EACnB/F,MAAM;EACNgG,OAAO;EACP7F,QAAQ;EACR8F,GAAG;EACHC,IAAI;EACJC,KAAK;EACLC,OAAO,GAAG,EAAE;EACZC,wBAAwB;EACxBC,4BAA4B;EAC5BC,SAAS,GAAG,KAAK;EACjB,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACD,SAAS;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,uBAAuB,GAAG;IAAEC,KAAK,EAAE;EAAM,CAAC;EAC1C;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,qBAAqB;EACrB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,cAAc,GAAG,IAAIlK,YAAY,CAAC,CAAC;EACnCwE,WAAWA,CAAClB,MAAM,EAAEgG,OAAO,EAAE7F,QAAQ,EAAE8F,GAAG,EAAEC,IAAI,EAAE;IAC9C,IAAI,CAAClG,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACgG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC7F,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC8F,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,wBAAwB,GAAGrG,MAAM,CAAC0B,MAAM,CAACC,SAAS,CAAEC,CAAC,IAAK;MAC3D,IAAIA,CAAC,YAAYhE,aAAa,EAAE;QAC5B,IAAI,CAACiJ,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC;EACN;EACA;EACAC,kBAAkBA,CAAA,EAAG;IACjB;IACAtH,EAAE,CAAC,IAAI,CAAC2G,KAAK,CAAC9D,OAAO,EAAE7C,EAAE,CAAC,IAAI,CAAC,CAAC,CAC3BuH,IAAI,CAACrH,QAAQ,CAAC,CAAC,CAAC,CAChBiC,SAAS,CAAEqF,CAAC,IAAK;MAClB,IAAI,CAACH,MAAM,CAAC,CAAC;MACb,IAAI,CAACI,4BAA4B,CAAC,CAAC;IACvC,CAAC,CAAC;EACN;EACAA,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,CAACX,4BAA4B,EAAE5C,WAAW,CAAC,CAAC;IAChD,MAAMwD,cAAc,GAAG,CAAC,GAAG,IAAI,CAACf,KAAK,CAACgB,OAAO,CAAC,CAAC,EAAE,IAAI,CAACjB,IAAI,CAAC,CACtDtG,MAAM,CAAEsG,IAAI,IAAK,CAAC,CAACA,IAAI,CAAC,CACxBkB,GAAG,CAAElB,IAAI,IAAKA,IAAI,CAAClF,SAAS,CAAC;IAClC,IAAI,CAACsF,4BAA4B,GAAG7G,IAAI,CAACyH,cAAc,CAAC,CACnDH,IAAI,CAACrH,QAAQ,CAAC,CAAC,CAAC,CAChBiC,SAAS,CAAEuE,IAAI,IAAK;MACrB,IAAI,IAAI,CAACK,SAAS,KAAK,IAAI,CAACc,YAAY,CAAC,IAAI,CAACrH,MAAM,CAAC,CAACkG,IAAI,CAAC,EAAE;QACzD,IAAI,CAACW,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC;EACN;EACA,IAAIS,gBAAgBA,CAACC,IAAI,EAAE;IACvB,MAAMnB,OAAO,GAAGxD,KAAK,CAACC,OAAO,CAAC0E,IAAI,CAAC,GAAGA,IAAI,GAAGA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;IAC5D,IAAI,CAACpB,OAAO,GAAGA,OAAO,CAACxG,MAAM,CAAE6H,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;EAC7C;EACA;EACArF,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACwE,MAAM,CAAC,CAAC;EACjB;EACA;EACApD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4C,wBAAwB,CAAC3C,WAAW,CAAC,CAAC;IAC3C,IAAI,CAAC4C,4BAA4B,EAAE5C,WAAW,CAAC,CAAC;EACpD;EACAmD,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACV,KAAK,IAAI,CAAC,IAAI,CAACnG,MAAM,CAAC0H,SAAS,EACrC;IACJC,cAAc,CAAC,MAAM;MACjB,MAAMC,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC;MAC5C,IAAI,CAACxB,OAAO,CAACyB,OAAO,CAAEJ,CAAC,IAAK;QACxB,IAAIG,cAAc,EAAE;UAChB,IAAI,CAACzH,QAAQ,CAAC2H,QAAQ,CAAC,IAAI,CAAC9B,OAAO,CAAC5E,aAAa,EAAEqG,CAAC,CAAC;QACzD,CAAC,MACI;UACD,IAAI,CAACtH,QAAQ,CAAC4H,WAAW,CAAC,IAAI,CAAC/B,OAAO,CAAC5E,aAAa,EAAEqG,CAAC,CAAC;QAC5D;MACJ,CAAC,CAAC;MACF,IAAIG,cAAc,IAAI,IAAI,CAACjB,qBAAqB,KAAKnE,SAAS,EAAE;QAC5D,IAAI,CAACrC,QAAQ,CAAC6D,YAAY,CAAC,IAAI,CAACgC,OAAO,CAAC5E,aAAa,EAAE,cAAc,EAAE,IAAI,CAACuF,qBAAqB,CAACqB,QAAQ,CAAC,CAAC,CAAC;MACjH,CAAC,MACI;QACD,IAAI,CAAC7H,QAAQ,CAAC8D,eAAe,CAAC,IAAI,CAAC+B,OAAO,CAAC5E,aAAa,EAAE,cAAc,CAAC;MAC7E;MACA;MACA,IAAI,IAAI,CAACmF,SAAS,KAAKqB,cAAc,EAAE;QACnC,IAAI,CAACrB,SAAS,GAAGqB,cAAc;QAC/B,IAAI,CAAC3B,GAAG,CAACgC,YAAY,CAAC,CAAC;QACvB;QACA,IAAI,CAACrB,cAAc,CAACsB,IAAI,CAACN,cAAc,CAAC;MAC5C;IACJ,CAAC,CAAC;EACN;EACAP,YAAYA,CAACrH,MAAM,EAAE;IACjB,MAAMmI,OAAO,GAAGC,oBAAoB,CAAC,IAAI,CAAC3B,uBAAuB,CAAC,GAC5D,IAAI,CAACA,uBAAuB;IAC5B;IACE,IAAI,CAACA,uBAAuB,CAACC,KAAK,IAAI,KAAK;IACnD,OAAQR,IAAI,IAAK;MACb,MAAM9C,OAAO,GAAG8C,IAAI,CAAC9C,OAAO;MAC5B,OAAOA,OAAO,GAAGpD,MAAM,CAACwG,QAAQ,CAACpD,OAAO,EAAE+E,OAAO,CAAC,GAAG,KAAK;IAC9D,CAAC;EACL;EACAP,cAAcA,CAAA,EAAG;IACb,MAAMS,eAAe,GAAG,IAAI,CAAChB,YAAY,CAAC,IAAI,CAACrH,MAAM,CAAC;IACtD,OAAQ,IAAI,CAACkG,IAAI,IAAImC,eAAe,CAAC,IAAI,CAACnC,IAAI,CAAC,IAAK,IAAI,CAACC,KAAK,CAACmC,IAAI,CAACD,eAAe,CAAC;EACxF;EACA,OAAOlE,IAAI,YAAAoE,yBAAAlE,iBAAA;IAAA,YAAAA,iBAAA,IAA+F0B,gBAAgB,EAhQ1BnK,EAAE,CAAA0I,iBAAA,CAgQ0CxG,MAAM,GAhQlDlC,EAAE,CAAA0I,iBAAA,CAgQ6D1I,EAAE,CAAC6I,UAAU,GAhQ5E7I,EAAE,CAAA0I,iBAAA,CAgQuF1I,EAAE,CAAC4I,SAAS,GAhQrG5I,EAAE,CAAA0I,iBAAA,CAgQgH1I,EAAE,CAAC4M,iBAAiB,GAhQtI5M,EAAE,CAAA0I,iBAAA,CAgQiJvE,UAAU;EAAA;EAC7P,OAAO2E,IAAI,kBAjQqF9I,EAAE,CAAA+I,iBAAA;IAAAC,IAAA,EAiQJmB,gBAAgB;IAAAlB,SAAA;IAAA4D,cAAA,WAAAC,gCAAAzD,EAAA,EAAAC,GAAA,EAAAyD,QAAA;MAAA,IAAA1D,EAAA;QAjQdrJ,EAAE,CAAAgN,cAAA,CAAAD,QAAA,EAiQqT5I,UAAU;MAAA;MAAA,IAAAkF,EAAA;QAAA,IAAA4D,EAAA;QAjQjUjN,EAAE,CAAAkN,cAAA,CAAAD,EAAA,GAAFjN,EAAE,CAAAmN,WAAA,QAAA7D,GAAA,CAAAiB,KAAA,GAAA0C,EAAA;MAAA;IAAA;IAAAtD,MAAA;MAAAkB,uBAAA;MAAAE,qBAAA;MAAAW,gBAAA;IAAA;IAAA0B,OAAA;MAAApC,cAAA;IAAA;IAAAqC,QAAA;IAAAzD,QAAA,GAAF5J,EAAE,CAAA6J,oBAAA;EAAA;AAkQtG;AACA;EAAA,QAAAnD,SAAA,oBAAAA,SAAA,KAnQoG1G,EAAE,CAAA8J,iBAAA,CAmQJK,gBAAgB,EAAc,CAAC;IACrHnB,IAAI,EAAEnI,SAAS;IACfkJ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BqD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAErE,IAAI,EAAE9G;EAAO,CAAC,EAAE;IAAE8G,IAAI,EAAEhJ,EAAE,CAAC6I;EAAW,CAAC,EAAE;IAAEG,IAAI,EAAEhJ,EAAE,CAAC4I;EAAU,CAAC,EAAE;IAAEI,IAAI,EAAEhJ,EAAE,CAAC4M;EAAkB,CAAC,EAAE;IAAE5D,IAAI,EAAE7E,UAAU;IAAE8F,UAAU,EAAE,CAAC;MACnJjB,IAAI,EAAE/H;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEsJ,KAAK,EAAE,CAAC;MACjCvB,IAAI,EAAEhI,eAAe;MACrB+I,IAAI,EAAE,CAAC5F,UAAU,EAAE;QAAEmJ,WAAW,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAEzC,uBAAuB,EAAE,CAAC;MAC1B7B,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEqK,qBAAqB,EAAE,CAAC;MACxB/B,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEsK,cAAc,EAAE,CAAC;MACjBhC,IAAI,EAAEjI;IACV,CAAC,CAAC;IAAE2K,gBAAgB,EAAE,CAAC;MACnB1C,IAAI,EAAEtI;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,SAAS8L,oBAAoBA,CAACD,OAAO,EAAE;EACnC,OAAO,CAAC,CAACA,OAAO,CAACgB,KAAK;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,CAAC;EACpBC,OAAOA,CAACrJ,KAAK,EAAEsJ,EAAE,EAAE;IACf,OAAOA,EAAE,CAAC,CAAC,CAACxC,IAAI,CAACpH,UAAU,CAAC,MAAMH,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD;EACA,OAAO2E,IAAI,YAAAqF,0BAAAnF,iBAAA;IAAA,YAAAA,iBAAA,IAA+FgF,iBAAiB;EAAA;EAC3H,OAAOI,KAAK,kBAvToF7N,EAAE,CAAA8N,kBAAA;IAAAC,KAAA,EAuTYN,iBAAiB;IAAAO,OAAA,EAAjBP,iBAAiB,CAAAlF,IAAA;IAAA0F,UAAA,EAAc;EAAM;AACvJ;AACA;EAAA,QAAAvH,SAAA,oBAAAA,SAAA,KAzToG1G,EAAE,CAAA8J,iBAAA,CAyTJ2D,iBAAiB,EAAc,CAAC;IACtHzE,IAAI,EAAE7H,UAAU;IAChB4I,IAAI,EAAE,CAAC;MAAEkE,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACfR,OAAOA,CAACrJ,KAAK,EAAEsJ,EAAE,EAAE;IACf,OAAO/J,EAAE,CAAC,IAAI,CAAC;EACnB;EACA,OAAO2E,IAAI,YAAA4F,qBAAA1F,iBAAA;IAAA,YAAAA,iBAAA,IAA+FyF,YAAY;EAAA;EACtH,OAAOL,KAAK,kBA3UoF7N,EAAE,CAAA8N,kBAAA;IAAAC,KAAA,EA2UYG,YAAY;IAAAF,OAAA,EAAZE,YAAY,CAAA3F,IAAA;IAAA0F,UAAA,EAAc;EAAM;AAClJ;AACA;EAAA,QAAAvH,SAAA,oBAAAA,SAAA,KA7UoG1G,EAAE,CAAA8J,iBAAA,CA6UJoE,YAAY,EAAc,CAAC;IACjHlF,IAAI,EAAE7H,UAAU;IAChB4I,IAAI,EAAE,CAAC;MAAEkE,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,eAAe,CAAC;EAClBhK,MAAM;EACNiK,QAAQ;EACRC,kBAAkB;EAClBC,MAAM;EACNpJ,YAAY;EACZG,WAAWA,CAAClB,MAAM,EAAEiK,QAAQ,EAAEC,kBAAkB,EAAEC,MAAM,EAAE;IACtD,IAAI,CAACnK,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACiK,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACrJ,YAAY,GAAG,IAAI,CAACf,MAAM,CAAC0B,MAAM,CACjCqF,IAAI,CAACnH,MAAM,CAAE4D,CAAC,IAAKA,CAAC,YAAY5F,aAAa,CAAC,EAAEiC,SAAS,CAAC,MAAM,IAAI,CAACyJ,OAAO,CAAC,CAAC,CAAC,CAAC,CAChF3H,SAAS,CAAC,MAAM,CAAE,CAAC,CAAC;EAC7B;EACA2H,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACe,aAAa,CAAC,IAAI,CAACJ,QAAQ,EAAE,IAAI,CAACjK,MAAM,CAACsK,MAAM,CAAC;EAChE;EACA;EACA7G,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC1C,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC2C,WAAW,CAAC,CAAC;IACnC;EACJ;EACA2G,aAAaA,CAACJ,QAAQ,EAAEM,MAAM,EAAE;IAC5B,MAAMC,GAAG,GAAG,EAAE;IACd,KAAK,MAAMvK,KAAK,IAAIsK,MAAM,EAAE;MACxB,IAAItK,KAAK,CAACwK,SAAS,IAAI,CAACxK,KAAK,CAACyK,SAAS,EAAE;QACrCzK,KAAK,CAACyK,SAAS,GAAG5N,yBAAyB,CAACmD,KAAK,CAACwK,SAAS,EAAER,QAAQ,EAAE,UAAUhK,KAAK,CAAC0K,IAAI,EAAE,CAAC;MAClG;MACA,MAAMC,uBAAuB,GAAG3K,KAAK,CAACyK,SAAS,IAAIT,QAAQ;MAC3D,MAAMY,mBAAmB,GAAG5K,KAAK,CAAC6K,eAAe,IAAIF,uBAAuB;MAC5E;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAK3K,KAAK,CAAC8K,YAAY,IAAI,CAAC9K,KAAK,CAAC+K,aAAa,IAAI/K,KAAK,CAACgL,OAAO,KAAKzI,SAAS,IACzEvC,KAAK,CAACiL,aAAa,IAAI,CAACjL,KAAK,CAACkL,gBAAiB,EAAE;QAClDX,GAAG,CAACY,IAAI,CAAC,IAAI,CAACC,aAAa,CAACT,uBAAuB,EAAE3K,KAAK,CAAC,CAAC;MAChE;MACA,IAAIA,KAAK,CAACqL,QAAQ,IAAIrL,KAAK,CAAC+K,aAAa,EAAE;QACvCR,GAAG,CAACY,IAAI,CAAC,IAAI,CAACf,aAAa,CAACQ,mBAAmB,EAAG5K,KAAK,CAACqL,QAAQ,IAAIrL,KAAK,CAAC+K,aAAc,CAAC,CAAC;MAC9F;IACJ;IACA,OAAOvL,IAAI,CAAC+K,GAAG,CAAC,CAACzD,IAAI,CAACrH,QAAQ,CAAC,CAAC,CAAC;EACrC;EACA2L,aAAaA,CAACpB,QAAQ,EAAEhK,KAAK,EAAE;IAC3B,OAAO,IAAI,CAACiK,kBAAkB,CAACZ,OAAO,CAACrJ,KAAK,EAAE,MAAM;MAChD,IAAIsL,eAAe;MACnB,IAAItL,KAAK,CAAC8K,YAAY,IAAI9K,KAAK,CAACgL,OAAO,KAAKzI,SAAS,EAAE;QACnD+I,eAAe,GAAG,IAAI,CAACpB,MAAM,CAACY,YAAY,CAACd,QAAQ,EAAEhK,KAAK,CAAC;MAC/D,CAAC,MACI;QACDsL,eAAe,GAAG/L,EAAE,CAAC,IAAI,CAAC;MAC9B;MACA,MAAMgM,sBAAsB,GAAGD,eAAe,CAACxE,IAAI,CAACjH,QAAQ,CAAEwK,MAAM,IAAK;QACrE,IAAIA,MAAM,KAAK,IAAI,EAAE;UACjB,OAAO9K,EAAE,CAAC,KAAK,CAAC,CAAC;QACrB;QACAS,KAAK,CAAC+K,aAAa,GAAGV,MAAM,CAACC,MAAM;QACnCtK,KAAK,CAAC6K,eAAe,GAAGR,MAAM,CAACL,QAAQ;QACvC;QACA;QACA,OAAO,IAAI,CAACI,aAAa,CAACC,MAAM,CAACL,QAAQ,IAAIA,QAAQ,EAAEK,MAAM,CAACC,MAAM,CAAC;MACzE,CAAC,CAAC,CAAC;MACH,IAAItK,KAAK,CAACiL,aAAa,IAAI,CAACjL,KAAK,CAACkL,gBAAgB,EAAE;QAChD,MAAMM,cAAc,GAAG,IAAI,CAACtB,MAAM,CAACe,aAAa,CAACjL,KAAK,CAAC;QACvD,OAAOR,IAAI,CAAC,CAAC+L,sBAAsB,EAAEC,cAAc,CAAC,CAAC,CAAC1E,IAAI,CAACrH,QAAQ,CAAC,CAAC,CAAC;MAC1E,CAAC,MACI;QACD,OAAO8L,sBAAsB;MACjC;IACJ,CAAC,CAAC;EACN;EACA,OAAOrH,IAAI,YAAAuH,wBAAArH,iBAAA;IAAA,YAAAA,iBAAA,IAA+F2F,eAAe,EA7azBpO,EAAE,CAAA+P,QAAA,CA6ayC7N,MAAM,GA7ajDlC,EAAE,CAAA+P,QAAA,CA6a4D/P,EAAE,CAACgQ,mBAAmB,GA7apFhQ,EAAE,CAAA+P,QAAA,CA6a+FvC,kBAAkB,GA7anHxN,EAAE,CAAA+P,QAAA,CA6a8H3N,kBAAkB;EAAA;EAClP,OAAOyL,KAAK,kBA9aoF7N,EAAE,CAAA8N,kBAAA;IAAAC,KAAA,EA8aYK,eAAe;IAAAJ,OAAA,EAAfI,eAAe,CAAA7F,IAAA;IAAA0F,UAAA,EAAc;EAAM;AACrJ;AACA;EAAA,QAAAvH,SAAA,oBAAAA,SAAA,KAhboG1G,EAAE,CAAA8J,iBAAA,CAgbJsE,eAAe,EAAc,CAAC;IACpHpF,IAAI,EAAE7H,UAAU;IAChB4I,IAAI,EAAE,CAAC;MAAEkE,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjF,IAAI,EAAE9G;EAAO,CAAC,EAAE;IAAE8G,IAAI,EAAEhJ,EAAE,CAACgQ;EAAoB,CAAC,EAAE;IAAEhH,IAAI,EAAEwE;EAAmB,CAAC,EAAE;IAAExE,IAAI,EAAE5G;EAAmB,CAAC,CAAC;AAAA;AAElJ,MAAM6N,eAAe,GAAG,IAAI7O,cAAc,CAAC,EAAE,CAAC;AAC9C,MAAM8O,cAAc,CAAC;EACjBC,aAAa;EACbC,WAAW;EACXC,gBAAgB;EAChBC,IAAI;EACJ/D,OAAO;EACP9B,wBAAwB;EACxB8F,wBAAwB;EACxBC,MAAM,GAAG,CAAC;EACVC,UAAU,GAAGpO,qBAAqB;EAClCqO,UAAU,GAAG,CAAC;EACdC,KAAK,GAAG,CAAC,CAAC;EACV;EACArL,WAAWA,CAAC6K,aAAa,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,IAAI,EAAE/D,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1E,IAAI,CAAC4D,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC/D,OAAO,GAAGA,OAAO;IACtB;IACAA,OAAO,CAACqE,yBAAyB,KAAK,UAAU;IAChDrE,OAAO,CAACsE,eAAe,KAAK,UAAU;EAC1C;EACAC,IAAIA,CAAA,EAAG;IACH;IACA;IACA;IACA,IAAI,IAAI,CAACvE,OAAO,CAACqE,yBAAyB,KAAK,UAAU,EAAE;MACvD,IAAI,CAACP,gBAAgB,CAACU,2BAA2B,CAAC,QAAQ,CAAC;IAC/D;IACA,IAAI,CAACtG,wBAAwB,GAAG,IAAI,CAACuG,kBAAkB,CAAC,CAAC;IACzD,IAAI,CAACT,wBAAwB,GAAG,IAAI,CAACU,mBAAmB,CAAC,CAAC;EAC9D;EACAD,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACZ,WAAW,CAACtK,MAAM,CAACC,SAAS,CAAE6B,CAAC,IAAK;MAC5C,IAAIA,CAAC,YAAYtF,eAAe,EAAE;QAC9B;QACA,IAAI,CAACqO,KAAK,CAAC,IAAI,CAACH,MAAM,CAAC,GAAG,IAAI,CAACH,gBAAgB,CAACa,iBAAiB,CAAC,CAAC;QACnE,IAAI,CAACT,UAAU,GAAG7I,CAAC,CAACuJ,iBAAiB;QACrC,IAAI,CAACT,UAAU,GAAG9I,CAAC,CAACwJ,aAAa,GAAGxJ,CAAC,CAACwJ,aAAa,CAACC,YAAY,GAAG,CAAC;MACxE,CAAC,MACI,IAAIzJ,CAAC,YAAY5F,aAAa,EAAE;QACjC,IAAI,CAACwO,MAAM,GAAG5I,CAAC,CAAC0J,EAAE;QAClB,IAAI,CAACC,mBAAmB,CAAC3J,CAAC,EAAE,IAAI,CAACuI,aAAa,CAACqB,KAAK,CAAC5J,CAAC,CAAC6J,iBAAiB,CAAC,CAAC5M,QAAQ,CAAC;MACvF,CAAC,MACI,IAAI+C,CAAC,YAAYrF,iBAAiB,IACnCqF,CAAC,CAAC8J,IAAI,KAAKlP,qBAAqB,CAACmP,wBAAwB,EAAE;QAC3D,IAAI,CAAClB,UAAU,GAAG7J,SAAS;QAC3B,IAAI,CAAC8J,UAAU,GAAG,CAAC;QACnB,IAAI,CAACa,mBAAmB,CAAC3J,CAAC,EAAE,IAAI,CAACuI,aAAa,CAACqB,KAAK,CAAC5J,CAAC,CAACgK,GAAG,CAAC,CAAC/M,QAAQ,CAAC;MACzE;IACJ,CAAC,CAAC;EACN;EACAoM,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACb,WAAW,CAACtK,MAAM,CAACC,SAAS,CAAE6B,CAAC,IAAK;MAC5C,IAAI,EAAEA,CAAC,YAAYnF,MAAM,CAAC,EACtB;MACJ;MACA,IAAImF,CAAC,CAACiK,QAAQ,EAAE;QACZ,IAAI,IAAI,CAACtF,OAAO,CAACqE,yBAAyB,KAAK,KAAK,EAAE;UAClD,IAAI,CAACP,gBAAgB,CAACyB,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClD,CAAC,MACI,IAAI,IAAI,CAACvF,OAAO,CAACqE,yBAAyB,KAAK,SAAS,EAAE;UAC3D,IAAI,CAACP,gBAAgB,CAACyB,gBAAgB,CAAClK,CAAC,CAACiK,QAAQ,CAAC;QACtD;QACA;MACJ,CAAC,MACI;QACD,IAAIjK,CAAC,CAACmK,MAAM,IAAI,IAAI,CAACxF,OAAO,CAACsE,eAAe,KAAK,SAAS,EAAE;UACxD,IAAI,CAACR,gBAAgB,CAAC2B,cAAc,CAACpK,CAAC,CAACmK,MAAM,CAAC;QAClD,CAAC,MACI,IAAI,IAAI,CAACxF,OAAO,CAACqE,yBAAyB,KAAK,UAAU,EAAE;UAC5D,IAAI,CAACP,gBAAgB,CAACyB,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClD;MACJ;IACJ,CAAC,CAAC;EACN;EACAP,mBAAmBA,CAACU,WAAW,EAAEF,MAAM,EAAE;IAAA,IAAAG,KAAA;IACrC,IAAI,CAAC5B,IAAI,CAAC6B,iBAAiB,cAAAC,iBAAA,CAAC,aAAY;MACpC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAM,IAAIC,OAAO,CAAEC,OAAO,IAAK;QAC3BC,UAAU,CAACD,OAAO,CAAC;QACnB,IAAI,OAAOE,qBAAqB,KAAK,WAAW,EAAE;UAC9CA,qBAAqB,CAACF,OAAO,CAAC;QAClC;MACJ,CAAC,CAAC;MACFJ,KAAI,CAAC5B,IAAI,CAACmC,GAAG,CAAC,MAAM;QAChBP,KAAI,CAAC9B,WAAW,CAACtK,MAAM,CAACe,IAAI,CAAC,IAAIpE,MAAM,CAACwP,WAAW,EAAEC,KAAI,CAACzB,UAAU,KAAK,UAAU,GAAGyB,KAAI,CAACvB,KAAK,CAACuB,KAAI,CAACxB,UAAU,CAAC,GAAG,IAAI,EAAEqB,MAAM,CAAC,CAAC;MACtI,CAAC,CAAC;IACN,CAAC,EAAC;EACN;EACA;EACAlK,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4C,wBAAwB,EAAE3C,WAAW,CAAC,CAAC;IAC5C,IAAI,CAACyI,wBAAwB,EAAEzI,WAAW,CAAC,CAAC;EAChD;EACA,OAAOS,IAAI,YAAAmK,uBAAAjK,iBAAA;IA7hBqFzI,EAAE,CAAA2S,gBAAA;EAAA;EA8hBlG,OAAO9E,KAAK,kBA9hBoF7N,EAAE,CAAA8N,kBAAA;IAAAC,KAAA,EA8hBYmC,cAAc;IAAAlC,OAAA,EAAdkC,cAAc,CAAA3H;EAAA;AAChI;AACA;EAAA,QAAA7B,SAAA,oBAAAA,SAAA,KAhiBoG1G,EAAE,CAAA8J,iBAAA,CAgiBJoG,cAAc,EAAc,CAAC;IACnHlH,IAAI,EAAE7H;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE6H,IAAI,EAAEtG;EAAc,CAAC,EAAE;IAAEsG,IAAI,EAAErG;EAAsB,CAAC,EAAE;IAAEqG,IAAI,EAAEvJ,EAAE,CAACI;EAAiB,CAAC,EAAE;IAAEmJ,IAAI,EAAEhJ,EAAE,CAAC6B;EAAO,CAAC,EAAE;IAAEmH,IAAI,EAAEpC;EAAU,CAAC,CAAC;AAAA;;AAErK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgM,aAAaA,CAACjE,MAAM,EAAE,GAAG/E,QAAQ,EAAE;EACxC,OAAOrI,wBAAwB,CAAC,CAC5B;IAAEsR,OAAO,EAAEjQ,MAAM;IAAEkQ,KAAK,EAAE,IAAI;IAAEC,QAAQ,EAAEpE;EAAO,CAAC,EAClD,OAAOjI,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvC;IAAEmM,OAAO,EAAEG,kBAAkB;IAAED,QAAQ,EAAE;EAAK,CAAC,GAC/C,EAAE,EACR;IAAEF,OAAO,EAAE1Q,cAAc;IAAE8Q,UAAU,EAAEC,SAAS;IAAEC,IAAI,EAAE,CAACjR,MAAM;EAAE,CAAC,EAClE;IAAE2Q,OAAO,EAAErR,sBAAsB;IAAEsR,KAAK,EAAE,IAAI;IAAEG,UAAU,EAAEG;EAAqB,CAAC,EAClFxJ,QAAQ,CAAC4B,GAAG,CAAE6H,OAAO,IAAKA,OAAO,CAACC,UAAU,CAAC,CAChD,CAAC;AACN;AACA,SAASJ,SAASA,CAAC9O,MAAM,EAAE;EACvB,OAAOA,MAAM,CAACmP,WAAW,CAACC,IAAI;AAClC;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,IAAI,EAAE7E,SAAS,EAAE;EACpC,OAAO;IAAE8E,KAAK,EAAED,IAAI;IAAEJ,UAAU,EAAEzE;EAAU,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,MAAMmE,kBAAkB,GAAG,IAAI5R,cAAc,CAAC,EAAE,EAAE;EAC9C6M,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAEA,CAAA,KAAM;AACnB,CAAC,CAAC;AACF,MAAM4F,4BAA4B,GAAG;EACjCf,OAAO,EAAEpR,uBAAuB;EAChCqR,KAAK,EAAE,IAAI;EACXG,UAAUA,CAAA,EAAG;IACT,OAAO,MAAM;MACT,IAAI,CAAChT,MAAM,CAAC+S,kBAAkB,CAAC,EAAE;QAC7Ba,OAAO,CAACC,IAAI,CAAC,gFAAgF,GACzF,2BAA2B,CAAC;MACpC;IACJ,CAAC;EACL;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACpF,MAAM,EAAE;EAC3B,OAAO,CACH;IAAEkE,OAAO,EAAEjQ,MAAM;IAAEkQ,KAAK,EAAE,IAAI;IAAEC,QAAQ,EAAEpE;EAAO,CAAC,EAClD,OAAOjI,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAGkN,4BAA4B,GAAG,EAAE,CACpF;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,qBAAqBA,CAACzH,OAAO,GAAG,CAAC,CAAC,EAAE;EACzC,MAAMsC,SAAS,GAAG,CACd;IACIgE,OAAO,EAAE5C,eAAe;IACxBgD,UAAU,EAAEA,CAAA,KAAM;MACd,MAAM5C,gBAAgB,GAAGpQ,MAAM,CAACJ,gBAAgB,CAAC;MACjD,MAAMyQ,IAAI,GAAGrQ,MAAM,CAAC4B,MAAM,CAAC;MAC3B,MAAMuO,WAAW,GAAGnQ,MAAM,CAAC0C,qBAAqB,CAAC;MACjD,MAAMwN,aAAa,GAAGlQ,MAAM,CAACyC,aAAa,CAAC;MAC3C,OAAO,IAAIwN,cAAc,CAACC,aAAa,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,IAAI,EAAE/D,OAAO,CAAC;IAC1F;EACJ,CAAC,CACJ;EACD,OAAOkH,aAAa,CAAC,CAAC,CAAC,kDAAkD5E,SAAS,CAAC;AACvF;AACA,SAASuE,oBAAoBA,CAAA,EAAG;EAC5B,MAAM/E,QAAQ,GAAGpO,MAAM,CAAC0B,QAAQ,CAAC;EACjC,OAAQsS,wBAAwB,IAAK;IACjC,MAAMC,GAAG,GAAG7F,QAAQ,CAAC1I,GAAG,CAAC/D,cAAc,CAAC;IACxC,IAAIqS,wBAAwB,KAAKC,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,EAAE;MAChD;IACJ;IACA,MAAM/P,MAAM,GAAGiK,QAAQ,CAAC1I,GAAG,CAACzD,MAAM,CAAC;IACnC,MAAMkS,aAAa,GAAG/F,QAAQ,CAAC1I,GAAG,CAAC0O,cAAc,CAAC;IAClD,IAAIhG,QAAQ,CAAC1I,GAAG,CAAC2O,kBAAkB,CAAC,KAAK,CAAC,CAAC,4CAA4C;MACnFlQ,MAAM,CAACmQ,iBAAiB,CAAC,CAAC;IAC9B;IACAlG,QAAQ,CAAC1I,GAAG,CAAC6O,gBAAgB,EAAE,IAAI,EAAE;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,EAAEjG,eAAe,CAAC,CAAC;IAC3EH,QAAQ,CAAC1I,GAAG,CAACsK,eAAe,EAAE,IAAI,EAAE;MAAEwE,QAAQ,EAAE;IAAK,CAAC,CAAC,EAAE3D,IAAI,CAAC,CAAC;IAC/D1M,MAAM,CAACsQ,sBAAsB,CAACR,GAAG,CAACS,cAAc,CAAC,CAAC,CAAC,CAAC;IACpD,IAAI,CAACP,aAAa,CAACQ,MAAM,EAAE;MACvBR,aAAa,CAACvN,IAAI,CAAC,CAAC;MACpBuN,aAAa,CAACS,QAAQ,CAAC,CAAC;MACxBT,aAAa,CAACtM,WAAW,CAAC,CAAC;IAC/B;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuM,cAAc,GAAG,IAAIjT,cAAc,CAAC,OAAOsF,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,0BAA0B,GAAG,EAAE,EAAE;EACvHsH,OAAO,EAAEA,CAAA,KAAM;IACX,OAAO,IAAIrK,OAAO,CAAC,CAAC;EACxB;AACJ,CAAC,CAAC;AACF,MAAM2Q,kBAAkB,GAAG,IAAIlT,cAAc,CAAC,OAAOsF,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,oBAAoB,GAAG,EAAE,EAAE;EAAEuH,UAAU,EAAE,MAAM;EAAED,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAC;AAA2C,CAAC,CAAC;AAC7M;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8G,oCAAoCA,CAAA,EAAG;EAC5C,MAAMjG,SAAS,GAAG,CACd;IAAEgE,OAAO,EAAEyB,kBAAkB;IAAEvB,QAAQ,EAAE,CAAC,CAAC;EAAwC,CAAC,EACpFrR,qBAAqB,CAAC,MAAM;IACxB,MAAM2M,QAAQ,GAAGpO,MAAM,CAAC0B,QAAQ,CAAC;IACjC,MAAMoT,mBAAmB,GAAG1G,QAAQ,CAAC1I,GAAG,CAACjG,oBAAoB,EAAE2S,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;IACjF,OAAOyC,mBAAmB,CAACC,IAAI,CAAC,MAAM;MAClC,OAAO,IAAI3C,OAAO,CAAEC,OAAO,IAAK;QAC5B,MAAMlO,MAAM,GAAGiK,QAAQ,CAAC1I,GAAG,CAACzD,MAAM,CAAC;QACnC,MAAMkS,aAAa,GAAG/F,QAAQ,CAAC1I,GAAG,CAAC0O,cAAc,CAAC;QAClDxR,mBAAmB,CAACuB,MAAM,EAAE,MAAM;UAC9B;UACA;UACAkO,OAAO,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC;QACFjE,QAAQ,CAAC1I,GAAG,CAAChD,qBAAqB,CAAC,CAACsS,kBAAkB,GAAG,MAAM;UAC3D;UACA;UACA;UACA3C,OAAO,CAAC,IAAI,CAAC;UACb,OAAO8B,aAAa,CAACQ,MAAM,GAAGhR,EAAE,CAAC,KAAK,CAAC,CAAC,GAAGwQ,aAAa;QAC5D,CAAC;QACDhQ,MAAM,CAACmQ,iBAAiB,CAAC,CAAC;MAC9B,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC,CAAC,CACL;EACD,OAAOd,aAAa,CAAC,CAAC,CAAC,iEAAiE5E,SAAS,CAAC;AACtG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqG,6BAA6BA,CAAA,EAAG;EACrC,MAAMrG,SAAS,GAAG,CACdnN,qBAAqB,CAAC,MAAM;IACxBzB,MAAM,CAACiC,MAAM,CAAC,CAACiT,2BAA2B,CAAC,CAAC;EAChD,CAAC,CAAC,EACF;IAAEtC,OAAO,EAAEyB,kBAAkB;IAAEvB,QAAQ,EAAE,CAAC,CAAC;EAAiC,CAAC,CAChF;EACD,OAAOU,aAAa,CAAC,CAAC,CAAC,0DAA0D5E,SAAS,CAAC;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuG,gBAAgBA,CAAA,EAAG;EACxB,IAAIvG,SAAS,GAAG,EAAE;EAClB,IAAI,OAAOnI,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;IAC/CmI,SAAS,GAAG,CACR;MACIgE,OAAO,EAAEpR,uBAAuB;MAChCqR,KAAK,EAAE,IAAI;MACXG,UAAU,EAAEA,CAAA,KAAM;QACd,MAAM7O,MAAM,GAAGnE,MAAM,CAACiC,MAAM,CAAC;QAC7B,OAAO,MAAMkC,MAAM,CAAC0B,MAAM,CAACC,SAAS,CAAE6B,CAAC,IAAK;UACxC;UACAiM,OAAO,CAACwB,KAAK,GAAG,iBAAiBzN,CAAC,CAACtC,WAAW,CAACgQ,IAAI,EAAE,CAAC;UACtDzB,OAAO,CAAC0B,GAAG,CAAClS,cAAc,CAACuE,CAAC,CAAC,CAAC;UAC9BiM,OAAO,CAAC0B,GAAG,CAAC3N,CAAC,CAAC;UACdiM,OAAO,CAAC2B,QAAQ,GAAG,CAAC;UACpB;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CACJ;EACL,CAAC,MACI;IACD3G,SAAS,GAAG,EAAE;EAClB;EACA,OAAO4E,aAAa,CAAC,CAAC,CAAC,6CAA6C5E,SAAS,CAAC;AAClF;AACA,MAAM2F,gBAAgB,GAAG,IAAIpT,cAAc,CAAC,OAAOsF,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,kBAAkB,GAAG,EAAE,CAAC;AACpH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+O,cAAcA,CAACnH,kBAAkB,EAAE;EACxC,MAAMO,SAAS,GAAG,CACd;IAAEgE,OAAO,EAAE2B,gBAAgB;IAAEkB,WAAW,EAAEtH;EAAgB,CAAC,EAC3D;IAAEyE,OAAO,EAAErF,kBAAkB;IAAEkI,WAAW,EAAEpH;EAAmB,CAAC,CACnE;EACD,OAAOmF,aAAa,CAAC,CAAC,CAAC,2CAA2C5E,SAAS,CAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8G,gBAAgBA,CAACpJ,OAAO,EAAE;EAC/B,MAAMsC,SAAS,GAAG,CAAC;IAAEgE,OAAO,EAAE/P,oBAAoB;IAAEiQ,QAAQ,EAAExG;EAAQ,CAAC,CAAC;EACxE,OAAOkH,aAAa,CAAC,CAAC,CAAC,oDAAoD5E,SAAS,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+G,gBAAgBA,CAAA,EAAG;EACxB,MAAM/G,SAAS,GAAG,CAAC;IAAEgE,OAAO,EAAEjT,gBAAgB;IAAEiW,QAAQ,EAAElW;EAAqB,CAAC,CAAC;EACjF,OAAO8T,aAAa,CAAC,CAAC,CAAC,mDAAmD5E,SAAS,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiH,0BAA0BA,CAACC,OAAO,EAAE;EACzC,MAAMlH,SAAS,GAAG,CACd;IACIgE,OAAO,EAAE9P,wBAAwB;IACjCgQ,QAAQ,EAAEgD;EACd,CAAC,CACJ;EACD,OAAOtC,aAAa,CAAC,CAAC,CAAC,uDAAuD5E,SAAS,CAAC;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmH,yBAAyBA,CAAA,EAAG;EACjC,MAAMnH,SAAS,GAAG,CACd7L,0BAA0B,EAC1B;IAAE6P,OAAO,EAAE5P,YAAY;IAAEyS,WAAW,EAAE1S;EAA2B,CAAC,CACrE;EACD,OAAOyQ,aAAa,CAAC,CAAC,CAAC,sDAAsD5E,SAAS,CAAC;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoH,mBAAmBA,CAAC1J,OAAO,EAAE;EAClCjL,uBAAuB,CAAC,yBAAyB,CAAC;EAClD,MAAMuN,SAAS,GAAG,CACd;IAAEgE,OAAO,EAAE1P,sBAAsB;IAAE4P,QAAQ,EAAE7P;EAAqB,CAAC,EACnE;IACI2P,OAAO,EAAEzP,uBAAuB;IAChC2P,QAAQ,EAAE;MAAEmD,kBAAkB,EAAE,CAAC,CAAC3J,OAAO,EAAE4J,qBAAqB;MAAE,GAAG5J;IAAQ;EACjF,CAAC,CACJ;EACD,OAAOkH,aAAa,CAAC,CAAC,CAAC,gDAAgD5E,SAAS,CAAC;AACrF;;AAEA;AACA;AACA;AACA,MAAMuH,iBAAiB,GAAG,CAAC5S,YAAY,EAAEW,UAAU,EAAEgG,gBAAgB,EAAEzG,qBAAqB,CAAC;AAC7F;AACA;AACA;AACA,MAAM2S,oBAAoB,GAAG,IAAIjV,cAAc,CAAC,OAAOsF,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,gCAAgC,GAAG,EAAE,CAAC;AACtI;AACA;AACA;AACA;AACA,MAAM4P,gBAAgB,GAAG,CACrBxW,QAAQ,EACR;EAAE+S,OAAO,EAAEnQ,aAAa;EAAEmT,QAAQ,EAAEvS;AAAqB,CAAC,EAC1DpB,MAAM,EACNqB,sBAAsB,EACtB;EAAEsP,OAAO,EAAE1Q,cAAc;EAAE8Q,UAAU,EAAEC,SAAS;EAAEC,IAAI,EAAE,CAACjR,MAAM;AAAE,CAAC,EAClEE,kBAAkB;AAClB;AACA;AACA,OAAOsE,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvC;EAAEmM,OAAO,EAAEG,kBAAkB;EAAED,QAAQ,EAAE;AAAK,CAAC,GAC/C,EAAE,CACX;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwD,YAAY,CAAC;EACfjR,WAAWA,CAAA,EAAG;IACV,IAAI,OAAOoB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/CzG,MAAM,CAACoW,oBAAoB,EAAE;QAAE5B,QAAQ,EAAE;MAAK,CAAC,CAAC;IACpD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAO+B,OAAOA,CAAC7H,MAAM,EAAED,MAAM,EAAE;IAC3B,OAAO;MACH+H,QAAQ,EAAEF,YAAY;MACtB1H,SAAS,EAAE,CACPyH,gBAAgB,EAChB,OAAO5P,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvCgI,MAAM,EAAEgI,aAAa,GACjBtB,gBAAgB,CAAC,CAAC,CAAC9B,UAAU,GAC7B,EAAE,GACN,EAAE,EACR;QAAET,OAAO,EAAEjQ,MAAM;QAAEkQ,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAEpE;MAAO,CAAC,EAClD,OAAOjI,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvC;QACEmM,OAAO,EAAEwD,oBAAoB;QAC7BpD,UAAU,EAAE0D,mBAAmB;QAC/BxD,IAAI,EAAE,CAAC,CAACjR,MAAM,EAAE,IAAIjB,QAAQ,CAAC,CAAC,EAAE,IAAIa,QAAQ,CAAC,CAAC,CAAC;MACnD,CAAC,GACC,EAAE,EACR4M,MAAM,EAAEkI,YAAY,GACd;QACE/D,OAAO,EAAE9P,wBAAwB;QACjCgQ,QAAQ,EAAErE,MAAM,CAACkI;MACrB,CAAC,GACC,EAAE,EACR;QAAE/D,OAAO,EAAE/P,oBAAoB;QAAEiQ,QAAQ,EAAErE,MAAM,GAAGA,MAAM,GAAG,CAAC;MAAE,CAAC,EACjEA,MAAM,EAAEmI,OAAO,GAAGC,2BAA2B,CAAC,CAAC,GAAGC,2BAA2B,CAAC,CAAC,EAC/EC,qBAAqB,CAAC,CAAC,EACvBtI,MAAM,EAAEJ,kBAAkB,GAAGmH,cAAc,CAAC/G,MAAM,CAACJ,kBAAkB,CAAC,CAACgF,UAAU,GAAG,EAAE,EACtF5E,MAAM,EAAE6F,iBAAiB,GAAG0C,wBAAwB,CAACvI,MAAM,CAAC,GAAG,EAAE,EACjEA,MAAM,EAAEwI,qBAAqB,GAAGlB,yBAAyB,CAAC,CAAC,CAAC1C,UAAU,GAAG,EAAE,EAC3E5E,MAAM,EAAEyI,qBAAqB,GAAGlB,mBAAmB,CAAC,CAAC,CAAC3C,UAAU,GAAG,EAAE,EACrE8D,wBAAwB,CAAC,CAAC;IAElC,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,QAAQA,CAAC1I,MAAM,EAAE;IACpB,OAAO;MACH8H,QAAQ,EAAEF,YAAY;MACtB1H,SAAS,EAAE,CAAC;QAAEgE,OAAO,EAAEjQ,MAAM;QAAEkQ,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAEpE;MAAO,CAAC;IAClE,CAAC;EACL;EACA,OAAOpG,IAAI,YAAA+O,qBAAA7O,iBAAA;IAAA,YAAAA,iBAAA,IAA+F8N,YAAY;EAAA;EACtH,OAAOgB,IAAI,kBAzrCqFvX,EAAE,CAAAwX,gBAAA;IAAAxO,IAAA,EAyrCSuN;EAAY;EACvH,OAAOkB,IAAI,kBA1rCqFzX,EAAE,CAAA0X,gBAAA;AA2rCtG;AACA;EAAA,QAAAhR,SAAA,oBAAAA,SAAA,KA5rCoG1G,EAAE,CAAA8J,iBAAA,CA4rCJyM,YAAY,EAAc,CAAC;IACjHvN,IAAI,EAAEjH,QAAQ;IACdgI,IAAI,EAAE,CAAC;MACC4N,OAAO,EAAEvB,iBAAiB;MAC1BwB,OAAO,EAAExB;IACb,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,SAASY,qBAAqBA,CAAA,EAAG;EAC7B,OAAO;IACHnE,OAAO,EAAE5C,eAAe;IACxBgD,UAAU,EAAEA,CAAA,KAAM;MACd,MAAM5C,gBAAgB,GAAGpQ,MAAM,CAACJ,gBAAgB,CAAC;MACjD,MAAMyQ,IAAI,GAAGrQ,MAAM,CAAC4B,MAAM,CAAC;MAC3B,MAAM6M,MAAM,GAAGzO,MAAM,CAAC6C,oBAAoB,CAAC;MAC3C,MAAMsN,WAAW,GAAGnQ,MAAM,CAAC0C,qBAAqB,CAAC;MACjD,MAAMwN,aAAa,GAAGlQ,MAAM,CAACyC,aAAa,CAAC;MAC3C,IAAIgM,MAAM,CAACmJ,YAAY,EAAE;QACrBxH,gBAAgB,CAACyH,SAAS,CAACpJ,MAAM,CAACmJ,YAAY,CAAC;MACnD;MACA,OAAO,IAAI3H,cAAc,CAACC,aAAa,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,IAAI,EAAE5B,MAAM,CAAC;IACzF;EACJ,CAAC;AACL;AACA;AACA;AACA,SAASoI,2BAA2BA,CAAA,EAAG;EACnC,OAAO;IAAEjE,OAAO,EAAEjT,gBAAgB;IAAEiW,QAAQ,EAAElW;EAAqB,CAAC;AACxE;AACA;AACA;AACA,SAASoX,2BAA2BA,CAAA,EAAG;EACnC,OAAO;IAAElE,OAAO,EAAEjT,gBAAgB;IAAEiW,QAAQ,EAAE9V;EAAqB,CAAC;AACxE;AACA,SAAS4W,mBAAmBA,CAACvS,MAAM,EAAE;EACjC,IAAIA,MAAM,EAAE;IACR,MAAM,IAAI/D,aAAa,CAAC,IAAI,CAAC,8CAA8C,4GAA4G,GACnL,kEAAkE,CAAC;EAC3E;EACA,OAAO,SAAS;AACpB;AACA;AACA;AACA,SAAS4W,wBAAwBA,CAACvI,MAAM,EAAE;EACtC,OAAO,CACHA,MAAM,CAAC6F,iBAAiB,KAAK,UAAU,GAAGW,6BAA6B,CAAC,CAAC,CAAC5B,UAAU,GAAG,EAAE,EACzF5E,MAAM,CAAC6F,iBAAiB,KAAK,iBAAiB,GACxCO,oCAAoC,CAAC,CAAC,CAACxB,UAAU,GACjD,EAAE,CACX;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyE,kBAAkB,GAAG,IAAI3W,cAAc,CAAC,OAAOsF,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,oBAAoB,GAAG,EAAE,CAAC;AACxH,SAAS0Q,wBAAwBA,CAAA,EAAG;EAChC,OAAO;EACH;EACA;EACA;IAAEvE,OAAO,EAAEkF,kBAAkB;IAAE9E,UAAU,EAAEG;EAAqB,CAAC,EACjE;IAAEP,OAAO,EAAErR,sBAAsB;IAAEsR,KAAK,EAAE,IAAI;IAAE4C,WAAW,EAAEqC;EAAmB,CAAC,CACpF;AACL;AAEA,SAAS7J,YAAY,EAAET,iBAAiB,EAAED,kBAAkB,EAAEuK,kBAAkB,EAAEzB,gBAAgB,EAAEnS,UAAU,EAAEgG,gBAAgB,EAAEoM,YAAY,EAAEnI,eAAe,EAAEwE,aAAa,EAAEmB,aAAa,EAAEiC,yBAAyB,EAAEZ,gBAAgB,EAAEF,6BAA6B,EAAEJ,oCAAoC,EAAEc,gBAAgB,EAAE5B,qBAAqB,EAAE8B,0BAA0B,EAAEL,cAAc,EAAEE,gBAAgB,EAAEM,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}