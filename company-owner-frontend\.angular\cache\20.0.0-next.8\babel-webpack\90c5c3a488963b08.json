{"ast": null, "code": "import { isFunction } from './isFunction';\nexport function isScheduler(value) {\n  return value && isFunction(value.schedule);\n}", "map": {"version": 3, "names": ["isFunction", "isScheduler", "value", "schedule"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/rxjs/dist/esm/internal/util/isScheduler.js"], "sourcesContent": ["import { isFunction } from './isFunction';\nexport function isScheduler(value) {\n    return value && isFunction(value.schedule);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC/B,OAAOA,KAAK,IAAIF,UAAU,CAACE,KAAK,CAACC,QAAQ,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}