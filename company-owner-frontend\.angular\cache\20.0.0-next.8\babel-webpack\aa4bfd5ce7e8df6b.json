{"ast": null, "code": "AmCharts.themes.light = {\n  themeName: 'light',\n  AmChart: {\n    color: '#000000',\n    backgroundColor: '#FFFFFF'\n  },\n  AmCoordinateChart: {\n    colors: ['#67b7dc', '#fdd400', '#84b761', '#cc4748', '#cd82ad', '#2f4074', '#448e4d', '#b7b83f', '#b9783f', '#b93e3d', '#913167']\n  },\n  AmStockChart: {\n    colors: ['#67b7dc', '#fdd400', '#84b761', '#cc4748', '#cd82ad', '#2f4074', '#448e4d', '#b7b83f', '#b9783f', '#b93e3d', '#913167']\n  },\n  AmSlicedChart: {\n    colors: ['#67b7dc', '#fdd400', '#84b761', '#cc4748', '#cd82ad', '#2f4074', '#448e4d', '#b7b83f', '#b9783f', '#b93e3d', '#913167'],\n    outlineAlpha: 1,\n    outlineThickness: 2,\n    labelTickColor: '#000000',\n    labelTickAlpha: 0.3\n  },\n  AmRectangularChart: {\n    zoomOutButtonColor: '#000000',\n    zoomOutButtonRollOverAlpha: 0.15,\n    zoomOutButtonImage: 'lens'\n  },\n  AxisBase: {\n    axisColor: '#000000',\n    axisAlpha: 0.3,\n    gridAlpha: 0.1,\n    gridColor: '#000000'\n  },\n  ChartScrollbar: {\n    backgroundColor: '#000000',\n    backgroundAlpha: 0.12,\n    graphFillAlpha: 0.5,\n    graphLineAlpha: 0,\n    selectedBackgroundColor: '#FFFFFF',\n    selectedBackgroundAlpha: 0.4,\n    gridAlpha: 0.15\n  },\n  ChartCursor: {\n    cursorColor: '#000000',\n    color: '#FFFFFF',\n    cursorAlpha: 0.5\n  },\n  AmLegend: {\n    color: '#000000'\n  },\n  AmGraph: {\n    lineAlpha: 0.9\n  },\n  GaugeArrow: {\n    color: '#000000',\n    alpha: 0.8,\n    nailAlpha: 0,\n    innerRadius: '40%',\n    nailRadius: 15,\n    startWidth: 15,\n    borderAlpha: 0.8,\n    nailBorderAlpha: 0\n  },\n  GaugeAxis: {\n    tickColor: '#000000',\n    tickAlpha: 1,\n    tickLength: 15,\n    minorTickLength: 8,\n    axisThickness: 3,\n    axisColor: '#000000',\n    axisAlpha: 1,\n    bandAlpha: 0.8\n  },\n  TrendLine: {\n    lineColor: '#c03246',\n    lineAlpha: 0.8\n  },\n  AreasSettings: {\n    alpha: 0.8,\n    color: '#67b7dc',\n    colorSolid: '#003767',\n    unlistedAreasAlpha: 0.4,\n    unlistedAreasColor: '#000000',\n    outlineColor: '#FFFFFF',\n    outlineAlpha: 0.5,\n    outlineThickness: 0.5,\n    rollOverColor: '#3c5bdc',\n    rollOverOutlineColor: '#FFFFFF',\n    selectedOutlineColor: '#FFFFFF',\n    selectedColor: '#f15135',\n    unlistedAreasOutlineColor: '#FFFFFF',\n    unlistedAreasOutlineAlpha: 0.5\n  },\n  LinesSettings: {\n    color: '#000000',\n    alpha: 0.8\n  },\n  ImagesSettings: {\n    alpha: 0.8,\n    labelColor: '#000000',\n    color: '#000000',\n    labelRollOverColor: '#3c5bdc'\n  },\n  ZoomControl: {\n    buttonFillAlpha: 0.7,\n    buttonIconColor: '#a7a7a7'\n  },\n  SmallMap: {\n    mapColor: '#000000',\n    rectangleColor: '#f15135',\n    backgroundColor: '#FFFFFF',\n    backgroundAlpha: 0.7,\n    borderThickness: 1,\n    borderAlpha: 0.8\n  },\n  PeriodSelector: {\n    color: '#000000'\n  },\n  PeriodButton: {\n    color: '#000000',\n    background: 'transparent',\n    opacity: 0.7,\n    border: '1px solid rgba(0, 0, 0, .3)',\n    MozBorderRadius: '5px',\n    borderRadius: '5px',\n    margin: '1px',\n    outline: 'none',\n    boxSizing: 'border-box'\n  },\n  PeriodButtonSelected: {\n    color: '#000000',\n    backgroundColor: '#b9cdf5',\n    border: '1px solid rgba(0, 0, 0, .3)',\n    MozBorderRadius: '5px',\n    borderRadius: '5px',\n    margin: '1px',\n    outline: 'none',\n    opacity: 1,\n    boxSizing: 'border-box'\n  },\n  PeriodInputField: {\n    color: '#000000',\n    background: 'transparent',\n    border: '1px solid rgba(0, 0, 0, .3)',\n    outline: 'none'\n  },\n  DataSetSelector: {\n    color: '#000000',\n    selectedBackgroundColor: '#b9cdf5',\n    rollOverBackgroundColor: '#a8b0e4'\n  },\n  DataSetCompareList: {\n    color: '#000000',\n    lineHeight: '100%',\n    boxSizing: 'initial',\n    webkitBoxSizing: 'initial',\n    border: '1px solid rgba(0, 0, 0, .3)'\n  },\n  DataSetSelect: {\n    border: '1px solid rgba(0, 0, 0, .3)',\n    outline: 'none'\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "themes", "light", "themeName", "AmChart", "color", "backgroundColor", "AmCoordinateChart", "colors", "AmStockChart", "AmSlicedChart", "outlineAlpha", "outlineThickness", "labelTickColor", "labelTickAlpha", "AmRectangularChart", "zoomOutButtonColor", "zoomOutButtonRollOverAlpha", "zoomOutButtonImage", "AxisBase", "axisColor", "axisAlpha", "gridAlpha", "gridColor", "ChartScrollbar", "backgroundAlpha", "graphFillAlpha", "graphLineAlpha", "selectedBackgroundColor", "selectedBackgroundAlpha", "ChartCursor", "cursorColor", "cursorAlpha", "AmLegend", "AmGraph", "lineAlpha", "GaugeArrow", "alpha", "nail<PERSON>l<PERSON>", "innerRadius", "nailRadius", "startWidth", "borderAlpha", "nailBorderAlpha", "GaugeAxis", "tickColor", "tickAlpha", "tick<PERSON><PERSON>th", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "axisThickness", "bandAlpha", "TrendLine", "lineColor", "AreasSettings", "colorSolid", "unlistedAreasAlpha", "unlistedAreasColor", "outlineColor", "rollOverColor", "rollOverOutlineColor", "selectedOutlineColor", "selectedColor", "unlistedAreasOutlineColor", "unlistedAreasOutlineAlpha", "LinesSettings", "ImagesSettings", "labelColor", "labelRollOverColor", "ZoomControl", "buttonFillAlpha", "buttonIconColor", "SmallMap", "mapColor", "rectangleColor", "borderThickness", "PeriodSelector", "PeriodButton", "background", "opacity", "border", "MozBorderRadius", "borderRadius", "margin", "outline", "boxSizing", "PeriodButtonSelected", "PeriodInputField", "DataSetSelector", "rollOverBackgroundColor", "DataSetCompareList", "lineHeight", "webkitBoxSizing", "DataSetSelect"], "sources": ["D:/employee-survey-app/company-owner-frontend/src/assets/charts/amchart/light.js"], "sourcesContent": ["AmCharts.themes.light = {\r\n  themeName: 'light',\r\n  AmChart: { color: '#000000', backgroundColor: '#FFFFFF' },\r\n  AmCoordinateChart: {\r\n    colors: ['#67b7dc', '#fdd400', '#84b761', '#cc4748', '#cd82ad', '#2f4074', '#448e4d', '#b7b83f', '#b9783f', '#b93e3d', '#913167']\r\n  },\r\n  AmStockChart: {\r\n    colors: ['#67b7dc', '#fdd400', '#84b761', '#cc4748', '#cd82ad', '#2f4074', '#448e4d', '#b7b83f', '#b9783f', '#b93e3d', '#913167']\r\n  },\r\n  AmSlicedChart: {\r\n    colors: ['#67b7dc', '#fdd400', '#84b761', '#cc4748', '#cd82ad', '#2f4074', '#448e4d', '#b7b83f', '#b9783f', '#b93e3d', '#913167'],\r\n    outlineAlpha: 1,\r\n    outlineThickness: 2,\r\n    labelTickColor: '#000000',\r\n    labelTickAlpha: 0.3\r\n  },\r\n  AmRectangularChart: {\r\n    zoomOutButtonColor: '#000000',\r\n    zoomOutButtonRollOverAlpha: 0.15,\r\n    zoomOutButtonImage: 'lens'\r\n  },\r\n  AxisBase: {\r\n    axisColor: '#000000',\r\n    axisAlpha: 0.3,\r\n    gridAlpha: 0.1,\r\n    gridColor: '#000000'\r\n  },\r\n  ChartScrollbar: {\r\n    backgroundColor: '#000000',\r\n    backgroundAlpha: 0.12,\r\n    graphFillAlpha: 0.5,\r\n    graphLineAlpha: 0,\r\n    selectedBackgroundColor: '#FFFFFF',\r\n    selectedBackgroundAlpha: 0.4,\r\n    gridAlpha: 0.15\r\n  },\r\n  ChartCursor: { cursorColor: '#000000', color: '#FFFFFF', cursorAlpha: 0.5 },\r\n  AmLegend: { color: '#000000' },\r\n  AmGraph: { lineAlpha: 0.9 },\r\n  GaugeArrow: {\r\n    color: '#000000',\r\n    alpha: 0.8,\r\n    nailAlpha: 0,\r\n    innerRadius: '40%',\r\n    nailRadius: 15,\r\n    startWidth: 15,\r\n    borderAlpha: 0.8,\r\n    nailBorderAlpha: 0\r\n  },\r\n  GaugeAxis: {\r\n    tickColor: '#000000',\r\n    tickAlpha: 1,\r\n    tickLength: 15,\r\n    minorTickLength: 8,\r\n    axisThickness: 3,\r\n    axisColor: '#000000',\r\n    axisAlpha: 1,\r\n    bandAlpha: 0.8\r\n  },\r\n  TrendLine: { lineColor: '#c03246', lineAlpha: 0.8 },\r\n  AreasSettings: {\r\n    alpha: 0.8,\r\n    color: '#67b7dc',\r\n    colorSolid: '#003767',\r\n    unlistedAreasAlpha: 0.4,\r\n    unlistedAreasColor: '#000000',\r\n    outlineColor: '#FFFFFF',\r\n    outlineAlpha: 0.5,\r\n    outlineThickness: 0.5,\r\n    rollOverColor: '#3c5bdc',\r\n    rollOverOutlineColor: '#FFFFFF',\r\n    selectedOutlineColor: '#FFFFFF',\r\n    selectedColor: '#f15135',\r\n    unlistedAreasOutlineColor: '#FFFFFF',\r\n    unlistedAreasOutlineAlpha: 0.5\r\n  },\r\n  LinesSettings: { color: '#000000', alpha: 0.8 },\r\n  ImagesSettings: {\r\n    alpha: 0.8,\r\n    labelColor: '#000000',\r\n    color: '#000000',\r\n    labelRollOverColor: '#3c5bdc'\r\n  },\r\n  ZoomControl: { buttonFillAlpha: 0.7, buttonIconColor: '#a7a7a7' },\r\n  SmallMap: {\r\n    mapColor: '#000000',\r\n    rectangleColor: '#f15135',\r\n    backgroundColor: '#FFFFFF',\r\n    backgroundAlpha: 0.7,\r\n    borderThickness: 1,\r\n    borderAlpha: 0.8\r\n  },\r\n  PeriodSelector: { color: '#000000' },\r\n  PeriodButton: {\r\n    color: '#000000',\r\n    background: 'transparent',\r\n    opacity: 0.7,\r\n    border: '1px solid rgba(0, 0, 0, .3)',\r\n    MozBorderRadius: '5px',\r\n    borderRadius: '5px',\r\n    margin: '1px',\r\n    outline: 'none',\r\n    boxSizing: 'border-box'\r\n  },\r\n  PeriodButtonSelected: {\r\n    color: '#000000',\r\n    backgroundColor: '#b9cdf5',\r\n    border: '1px solid rgba(0, 0, 0, .3)',\r\n    MozBorderRadius: '5px',\r\n    borderRadius: '5px',\r\n    margin: '1px',\r\n    outline: 'none',\r\n    opacity: 1,\r\n    boxSizing: 'border-box'\r\n  },\r\n  PeriodInputField: {\r\n    color: '#000000',\r\n    background: 'transparent',\r\n    border: '1px solid rgba(0, 0, 0, .3)',\r\n    outline: 'none'\r\n  },\r\n  DataSetSelector: {\r\n    color: '#000000',\r\n    selectedBackgroundColor: '#b9cdf5',\r\n    rollOverBackgroundColor: '#a8b0e4'\r\n  },\r\n  DataSetCompareList: {\r\n    color: '#000000',\r\n    lineHeight: '100%',\r\n    boxSizing: 'initial',\r\n    webkitBoxSizing: 'initial',\r\n    border: '1px solid rgba(0, 0, 0, .3)'\r\n  },\r\n  DataSetSelect: { border: '1px solid rgba(0, 0, 0, .3)', outline: 'none' }\r\n};\r\n"], "mappings": "AAAAA,QAAQ,CAACC,MAAM,CAACC,KAAK,GAAG;EACtBC,SAAS,EAAE,OAAO;EAClBC,OAAO,EAAE;IAAEC,KAAK,EAAE,SAAS;IAAEC,eAAe,EAAE;EAAU,CAAC;EACzDC,iBAAiB,EAAE;IACjBC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;EAClI,CAAC;EACDC,YAAY,EAAE;IACZD,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;EAClI,CAAC;EACDE,aAAa,EAAE;IACbF,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;IACjIG,YAAY,EAAE,CAAC;IACfC,gBAAgB,EAAE,CAAC;IACnBC,cAAc,EAAE,SAAS;IACzBC,cAAc,EAAE;EAClB,CAAC;EACDC,kBAAkB,EAAE;IAClBC,kBAAkB,EAAE,SAAS;IAC7BC,0BAA0B,EAAE,IAAI;IAChCC,kBAAkB,EAAE;EACtB,CAAC;EACDC,QAAQ,EAAE;IACRC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,GAAG;IACdC,SAAS,EAAE,GAAG;IACdC,SAAS,EAAE;EACb,CAAC;EACDC,cAAc,EAAE;IACdlB,eAAe,EAAE,SAAS;IAC1BmB,eAAe,EAAE,IAAI;IACrBC,cAAc,EAAE,GAAG;IACnBC,cAAc,EAAE,CAAC;IACjBC,uBAAuB,EAAE,SAAS;IAClCC,uBAAuB,EAAE,GAAG;IAC5BP,SAAS,EAAE;EACb,CAAC;EACDQ,WAAW,EAAE;IAAEC,WAAW,EAAE,SAAS;IAAE1B,KAAK,EAAE,SAAS;IAAE2B,WAAW,EAAE;EAAI,CAAC;EAC3EC,QAAQ,EAAE;IAAE5B,KAAK,EAAE;EAAU,CAAC;EAC9B6B,OAAO,EAAE;IAAEC,SAAS,EAAE;EAAI,CAAC;EAC3BC,UAAU,EAAE;IACV/B,KAAK,EAAE,SAAS;IAChBgC,KAAK,EAAE,GAAG;IACVC,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE,KAAK;IAClBC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,GAAG;IAChBC,eAAe,EAAE;EACnB,CAAC;EACDC,SAAS,EAAE;IACTC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,EAAE;IACdC,eAAe,EAAE,CAAC;IAClBC,aAAa,EAAE,CAAC;IAChB7B,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,CAAC;IACZ6B,SAAS,EAAE;EACb,CAAC;EACDC,SAAS,EAAE;IAAEC,SAAS,EAAE,SAAS;IAAEjB,SAAS,EAAE;EAAI,CAAC;EACnDkB,aAAa,EAAE;IACbhB,KAAK,EAAE,GAAG;IACVhC,KAAK,EAAE,SAAS;IAChBiD,UAAU,EAAE,SAAS;IACrBC,kBAAkB,EAAE,GAAG;IACvBC,kBAAkB,EAAE,SAAS;IAC7BC,YAAY,EAAE,SAAS;IACvB9C,YAAY,EAAE,GAAG;IACjBC,gBAAgB,EAAE,GAAG;IACrB8C,aAAa,EAAE,SAAS;IACxBC,oBAAoB,EAAE,SAAS;IAC/BC,oBAAoB,EAAE,SAAS;IAC/BC,aAAa,EAAE,SAAS;IACxBC,yBAAyB,EAAE,SAAS;IACpCC,yBAAyB,EAAE;EAC7B,CAAC;EACDC,aAAa,EAAE;IAAE3D,KAAK,EAAE,SAAS;IAAEgC,KAAK,EAAE;EAAI,CAAC;EAC/C4B,cAAc,EAAE;IACd5B,KAAK,EAAE,GAAG;IACV6B,UAAU,EAAE,SAAS;IACrB7D,KAAK,EAAE,SAAS;IAChB8D,kBAAkB,EAAE;EACtB,CAAC;EACDC,WAAW,EAAE;IAAEC,eAAe,EAAE,GAAG;IAAEC,eAAe,EAAE;EAAU,CAAC;EACjEC,QAAQ,EAAE;IACRC,QAAQ,EAAE,SAAS;IACnBC,cAAc,EAAE,SAAS;IACzBnE,eAAe,EAAE,SAAS;IAC1BmB,eAAe,EAAE,GAAG;IACpBiD,eAAe,EAAE,CAAC;IAClBhC,WAAW,EAAE;EACf,CAAC;EACDiC,cAAc,EAAE;IAAEtE,KAAK,EAAE;EAAU,CAAC;EACpCuE,YAAY,EAAE;IACZvE,KAAK,EAAE,SAAS;IAChBwE,UAAU,EAAE,aAAa;IACzBC,OAAO,EAAE,GAAG;IACZC,MAAM,EAAE,6BAA6B;IACrCC,eAAe,EAAE,KAAK;IACtBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE;EACb,CAAC;EACDC,oBAAoB,EAAE;IACpBhF,KAAK,EAAE,SAAS;IAChBC,eAAe,EAAE,SAAS;IAC1ByE,MAAM,EAAE,6BAA6B;IACrCC,eAAe,EAAE,KAAK;IACtBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,MAAM;IACfL,OAAO,EAAE,CAAC;IACVM,SAAS,EAAE;EACb,CAAC;EACDE,gBAAgB,EAAE;IAChBjF,KAAK,EAAE,SAAS;IAChBwE,UAAU,EAAE,aAAa;IACzBE,MAAM,EAAE,6BAA6B;IACrCI,OAAO,EAAE;EACX,CAAC;EACDI,eAAe,EAAE;IACflF,KAAK,EAAE,SAAS;IAChBuB,uBAAuB,EAAE,SAAS;IAClC4D,uBAAuB,EAAE;EAC3B,CAAC;EACDC,kBAAkB,EAAE;IAClBpF,KAAK,EAAE,SAAS;IAChBqF,UAAU,EAAE,MAAM;IAClBN,SAAS,EAAE,SAAS;IACpBO,eAAe,EAAE,SAAS;IAC1BZ,MAAM,EAAE;EACV,CAAC;EACDa,aAAa,EAAE;IAAEb,MAAM,EAAE,6BAA6B;IAAEI,OAAO,EAAE;EAAO;AAC1E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}