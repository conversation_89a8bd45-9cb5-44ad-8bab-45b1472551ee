{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class QuestionsComponent {\n  static {\n    this.ɵfac = function QuestionsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuestionsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QuestionsComponent,\n      selectors: [[\"app-questions\"]],\n      decls: 1,\n      vars: 0,\n      template: function QuestionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i1.RouterOutlet],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["QuestionsComponent", "selectors", "decls", "vars", "template", "QuestionsComponent_Template", "rf", "ctx", "i0", "ɵɵelement"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\questions\\questions.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-questions',\n  template: '<router-outlet></router-outlet>'\n})\nexport class QuestionsComponent {}"], "mappings": ";;AAMA,OAAM,MAAOA,kBAAkB;;;uCAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAFlBE,EAAA,CAAAC,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}