{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  children: [{\n    path: 'badges',\n    loadComponent: () => import('./basic-badge/basic-badge.component')\n  }, {\n    path: 'button',\n    loadComponent: () => import('./basic-button/basic-button.component')\n  }, {\n    path: 'breadcrumb-paging',\n    loadComponent: () => import('./breadcrumb-paging/breadcrumb-paging.component')\n  }, {\n    path: 'collapse',\n    loadComponent: () => import('./basic-collapse/basic-collapse.component')\n  }, {\n    path: 'tabs-pills',\n    loadComponent: () => import('./basic-tabs-pills/basic-tabs-pills.component')\n  }, {\n    path: 'typography',\n    loadComponent: () => import('./basic-typography/basic-typography.component')\n  }]\n}];\nexport class UiBasicRoutingModule {\n  static {\n    this.ɵfac = function UiBasicRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UiBasicRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: UiBasicRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(UiBasicRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "routes", "path", "children", "loadComponent", "UiBasicRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\ui-elements\\ui-basic\\ui-basic-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    children: [\r\n      {\r\n        path: 'badges',\r\n        loadComponent: () => import('./basic-badge/basic-badge.component')\r\n      },\r\n      {\r\n        path: 'button',\r\n        loadComponent: () => import('./basic-button/basic-button.component')\r\n      },\r\n      {\r\n        path: 'breadcrumb-paging',\r\n        loadComponent: () => import('./breadcrumb-paging/breadcrumb-paging.component')\r\n      },\r\n      {\r\n        path: 'collapse',\r\n        loadComponent: () => import('./basic-collapse/basic-collapse.component')\r\n      },\r\n      {\r\n        path: 'tabs-pills',\r\n        loadComponent: () => import('./basic-tabs-pills/basic-tabs-pills.component')\r\n      },\r\n      {\r\n        path: 'typography',\r\n        loadComponent: () => import('./basic-typography/basic-typography.component')\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class UiBasicRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;;;AAEtD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,QAAQ;IACdE,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC;GAClE,EACD;IACEF,IAAI,EAAE,QAAQ;IACdE,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC;GACpE,EACD;IACEF,IAAI,EAAE,mBAAmB;IACzBE,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,iDAAiD;GAC9E,EACD;IACEF,IAAI,EAAE,UAAU;IAChBE,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,2CAA2C;GACxE,EACD;IACEF,IAAI,EAAE,YAAY;IAClBE,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C;GAC5E,EACD;IACEF,IAAI,EAAE,YAAY;IAClBE,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C;GAC5E;CAEJ,CACF;AAMD,OAAM,MAAOC,oBAAoB;;;uCAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBL,YAAY,CAACM,QAAQ,CAACL,MAAM,CAAC,EAC7BD,YAAY;IAAA;EAAA;;;2EAEXK,oBAAoB;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAFrBT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}