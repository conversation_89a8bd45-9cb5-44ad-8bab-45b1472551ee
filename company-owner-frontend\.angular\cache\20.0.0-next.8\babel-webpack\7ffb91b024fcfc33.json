{"ast": null, "code": "import { concat } from '../observable/concat';\nimport { of } from '../observable/of';\nexport function endWith(...values) {\n  return source => concat(source, of(...values));\n}", "map": {"version": 3, "names": ["concat", "of", "endWith", "values", "source"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/rxjs/dist/esm/internal/operators/endWith.js"], "sourcesContent": ["import { concat } from '../observable/concat';\nimport { of } from '../observable/of';\nexport function endWith(...values) {\n    return (source) => concat(source, of(...values));\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,EAAE,QAAQ,kBAAkB;AACrC,OAAO,SAASC,OAAOA,CAAC,GAAGC,MAAM,EAAE;EAC/B,OAAQC,MAAM,IAAKJ,MAAM,CAACI,MAAM,EAAEH,EAAE,CAAC,GAAGE,MAAM,CAAC,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}