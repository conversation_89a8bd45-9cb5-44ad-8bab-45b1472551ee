{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class Footer {\n  static {\n    this.ɵfac = function Footer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Footer)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: Footer,\n      selectors: [[\"app-footer\"]],\n      decls: 19,\n      vars: 0,\n      consts: [[1, \"pc-footer\"], [1, \"footer-wrapper\", \"container-fuild\"], [1, \"row\"], [1, \"col\", \"my-1\"], [1, \"m-0\"], [\"href\", \"https://codedthemes.com/\", \"target\", \"_blank\"], [1, \"col-auto\", \"my-1\"], [1, \"list-inline\", \"footer-link\", \"mb-0\"], [1, \"list-inline-item\"], [\"href\", \"https://codedthemes.com\", \"target\", \"_blank\"], [\"href\", \"https://codedthemes.gitbook.io/datta-angular\", \"target\", \"_blank\"], [\"href\", \"https://codedthemes.support-hub.io\", \"target\", \"_blank\"]],\n      template: function Footer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"p\", 4);\n          i0.ɵɵtext(5, \" Datta able \\u2665 crafted by Team \");\n          i0.ɵɵelementStart(6, \"a\", 5);\n          i0.ɵɵtext(7, \"CodedThemes\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"ul\", 7)(10, \"li\", 8)(11, \"a\", 9);\n          i0.ɵɵtext(12, \"Home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"li\", 8)(14, \"a\", 10);\n          i0.ɵɵtext(15, \"Documentation\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"li\", 8)(17, \"a\", 11);\n          i0.ɵɵtext(18, \"Support\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Footer", "selectors", "decls", "vars", "consts", "template", "Footer_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\footer\\footer.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\footer\\footer.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-footer',\r\n  imports: [],\r\n  templateUrl: './footer.html',\r\n  styleUrl: './footer.scss'\r\n})\r\nexport class Footer {}\r\n", "<div class=\"pc-footer\">\r\n  <div class=\"footer-wrapper container-fuild\">\r\n    <div class=\"row\">\r\n      <div class=\"col my-1\">\r\n        <p class=\"m-0\">\r\n          Datta able ♥ crafted by Team\r\n          <a href=\"https://codedthemes.com/\" target=\"_blank\">CodedThemes</a>\r\n        </p>\r\n      </div>\r\n      <div class=\"col-auto my-1\">\r\n        <ul class=\"list-inline footer-link mb-0\">\r\n          <li class=\"list-inline-item\">\r\n            <a href=\"https://codedthemes.com\" target=\"_blank\">Home</a>\r\n          </li>\r\n          <li class=\"list-inline-item\">\r\n            <a href=\"https://codedthemes.gitbook.io/datta-angular\" target=\"_blank\">Documentation</a>\r\n          </li>\r\n          <li class=\"list-inline-item\">\r\n            <a href=\"https://codedthemes.support-hub.io\" target=\"_blank\">Support</a>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AAQA,OAAM,MAAOA,MAAM;;;uCAANA,MAAM;IAAA;EAAA;;;YAANA,MAAM;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCJXE,EAJR,CAAAC,cAAA,aAAuB,aACuB,aACzB,aACO,WACL;UACbD,EAAA,CAAAE,MAAA,0CACA;UAAAF,EAAA,CAAAC,cAAA,WAAmD;UAAAD,EAAA,CAAAE,MAAA,kBAAW;UAElEF,EAFkE,CAAAG,YAAA,EAAI,EAChE,EACA;UAIAH,EAHN,CAAAC,cAAA,aAA2B,YACgB,aACV,YACuB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UACxDF,EADwD,CAAAG,YAAA,EAAI,EACvD;UAEHH,EADF,CAAAC,cAAA,aAA6B,aAC4C;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UACtFF,EADsF,CAAAG,YAAA,EAAI,EACrF;UAEHH,EADF,CAAAC,cAAA,aAA6B,aACkC;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAMhFF,EANgF,CAAAG,YAAA,EAAI,EACrE,EACF,EACD,EACF,EACF,EACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}