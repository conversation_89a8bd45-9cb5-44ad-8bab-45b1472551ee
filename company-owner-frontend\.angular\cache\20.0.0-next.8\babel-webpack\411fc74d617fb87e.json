{"ast": null, "code": "(function () {\n  var d = window.AmCharts;\n  d.GaugeAxis = d.Class({\n    construct: function (a) {\n      this.cname = 'GaugeAxis';\n      this.radius = '95%';\n      this.createEvents('rollOverBand', 'rollOutBand', 'clickBand');\n      this.labelsEnabled = !0;\n      this.startAngle = -120;\n      this.endAngle = 120;\n      this.startValue = 0;\n      this.endValue = 200;\n      this.gridCount = 5;\n      this.tickLength = 10;\n      this.minorTickLength = 5;\n      this.tickColor = '#555555';\n      this.labelFrequency = this.tickThickness = this.tickAlpha = 1;\n      this.inside = !0;\n      this.labelOffset = 10;\n      this.showLastLabel = this.showFirstLabel = !0;\n      this.axisThickness = 1;\n      this.axisColor = '#000000';\n      this.axisAlpha = 1;\n      this.gridInside = !0;\n      this.topTextYOffset = 0;\n      this.topTextBold = !0;\n      this.bottomTextYOffset = 0;\n      this.bottomTextBold = !0;\n      this.centerY = this.centerX = '0%';\n      this.bandOutlineAlpha = this.bandOutlineThickness = 0;\n      this.bandOutlineColor = '#000000';\n      this.bandAlpha = 1;\n      this.bcn = 'gauge-axis';\n      d.applyTheme(this, a, 'GaugeAxis');\n    },\n    value2angle: function (a) {\n      return (a - this.startValue) / (this.endValue - this.startValue) * (this.endAngle - this.startAngle) + this.startAngle;\n    },\n    setTopText: function (a) {\n      if (void 0 !== a) {\n        this.topText = a;\n        var b = this.chart;\n        if (this.axisCreated) {\n          this.topTF && this.topTF.remove();\n          var c = this.topTextFontSize;\n          c || (c = b.fontSize);\n          var e = this.topTextColor;\n          e || (e = b.color);\n          a = d.text(b.container, a, e, b.fontFamily, c, void 0, this.topTextBold);\n          d.setCN(b, a, 'axis-top-label');\n          a.translate(this.centerXReal, this.centerYReal - this.radiusReal / 2 + this.topTextYOffset);\n          this.set.push(a);\n          this.topTF = a;\n        }\n      }\n    },\n    setBottomText: function (a) {\n      if (void 0 !== a) {\n        this.bottomText = a;\n        var b = this.chart;\n        if (this.axisCreated) {\n          this.bottomTF && this.bottomTF.remove();\n          var c = this.bottomTextFontSize;\n          c || (c = b.fontSize);\n          var e = this.bottomTextColor;\n          e || (e = b.color);\n          a = d.text(b.container, a, e, b.fontFamily, c, void 0, this.bottomTextBold);\n          d.setCN(b, a, 'axis-bottom-label');\n          a.translate(this.centerXReal, this.centerYReal + this.radiusReal / 2 + this.bottomTextYOffset);\n          this.bottomTF = a;\n          this.set.push(a);\n        }\n      }\n    },\n    draw: function () {\n      var a = this.chart,\n        b = a.container.set();\n      this.set = b;\n      d.setCN(a, b, this.bcn);\n      d.setCN(a, b, this.bcn + '-' + this.id);\n      a.graphsSet.push(b);\n      this.bandSet = a.container.set();\n      this.set.push(this.bandSet);\n      var c = this.startValue,\n        e = this.endValue,\n        g = this.valueInterval;\n      isNaN(g) && (g = (e - c) / this.gridCount);\n      var l = this.minorTickInterval;\n      isNaN(l) && (l = g / 5);\n      var n = this.startAngle,\n        h = this.endAngle,\n        k = this.tickLength,\n        p = (e - c) / g + 1,\n        f = (h - n) / (p - 1);\n      this.singleValueAngle = f / g;\n      var m = a.container,\n        w = this.tickColor,\n        z = this.tickAlpha,\n        J = this.tickThickness,\n        l = g / l,\n        K = f / l,\n        H = this.minorTickLength,\n        I = this.labelFrequency,\n        v = this.radiusReal;\n      this.inside || (v -= 15);\n      this.radiusRealReal = v;\n      var A = a.centerX + d.toCoordinate(this.centerX, a.realWidth),\n        B = a.centerY + d.toCoordinate(this.centerY, a.realHeight);\n      this.centerXReal = A;\n      this.centerYReal = B;\n      var t = {\n          fill: this.axisColor,\n          'fill-opacity': this.axisAlpha,\n          'stroke-width': 0,\n          'stroke-opacity': 0\n        },\n        r,\n        C;\n      this.gridInside ? C = r = v : (r = v - k, C = r + H);\n      this.minorTickRadius = C;\n      this.drawBands();\n      var q = this.axisThickness / 2,\n        h = d.wedge(m, A, B, n, h - n, r + q, r + q, r - q, 0, t);\n      d.setCN(a, h.wedge, 'axis-line');\n      b.push(h);\n      h = d.doNothing;\n      d.isModern || (h = Math.round);\n      t = d.getDecimals(c);\n      r = d.getDecimals(e);\n      e = d.getDecimals(g);\n      e = Math.max(e, t, r);\n      g = d.roundTo(g, e + 1);\n      for (t = 0; t < p; t++) {\n        q = d.roundTo(c + t * g, e);\n        r = n + t * f;\n        var u = h(A + v * Math.sin(r / 180 * Math.PI)),\n          F = h(B - v * Math.cos(r / 180 * Math.PI)),\n          x = h(A + (v - k) * Math.sin(r / 180 * Math.PI)),\n          y = h(B - (v - k) * Math.cos(r / 180 * Math.PI)),\n          u = d.line(m, [u, x], [F, y], w, z, J, 0, !1, !1, !0);\n        d.setCN(a, u, 'axis-tick');\n        b.push(u);\n        u = -1;\n        x = this.labelOffset;\n        this.inside || (x = -x - k, u = 1);\n        var F = A + (v - k - x) * Math.sin(r / 180 * Math.PI),\n          x = B - (v - k - x) * Math.cos(r / 180 * Math.PI),\n          D = this.fontSize;\n        isNaN(D) && (D = a.fontSize);\n        var y = Math.sin((r - 90) / 180 * Math.PI),\n          L = Math.cos((r - 90) / 180 * Math.PI);\n        if (0 < I && this.labelsEnabled && t / I == Math.round(t / I) && (this.showLastLabel || t != p - 1) && (this.showFirstLabel || 0 !== t)) {\n          var E;\n          E = this.usePrefixes ? d.addPrefix(q, a.prefixesOfBigNumbers, a.prefixesOfSmallNumbers, a.nf, !0) : d.formatNumber(q, a.nf, e);\n          var G = this.unit;\n          G && (E = 'left' == this.unitPosition ? G + E : E + G);\n          (G = this.labelFunction) && (E = G(q));\n          q = this.color;\n          void 0 === q && (q = a.color);\n          q = d.text(m, E, q, a.fontFamily, D);\n          d.setCN(a, q, 'axis-label');\n          D = q.getBBox();\n          q.translate(F + u * D.width / 2 * L, x + u * D.height / 2 * y);\n          b.push(q);\n        }\n        if (t < p - 1) for (q = 1; q < l; q++) y = r + K * q, u = h(A + C * Math.sin(y / 180 * Math.PI)), F = h(B - C * Math.cos(y / 180 * Math.PI)), x = h(A + (C - H) * Math.sin(y / 180 * Math.PI)), y = h(B - (C - H) * Math.cos(y / 180 * Math.PI)), u = d.line(m, [u, x], [F, y], w, z, J, 0, !1, !1, !0), d.setCN(a, u, 'axis-tick-minor'), b.push(u);\n      }\n      this.axisCreated = !0;\n      this.setTopText(this.topText);\n      this.setBottomText(this.bottomText);\n      a = a.graphsSet.getBBox();\n      this.width = a.width;\n      this.height = a.height;\n    },\n    drawBands: function () {\n      var a = this.bands;\n      if (a) for (var b = 0; b < a.length; b++) {\n        var c = a[b];\n        c && (c.axis = this, d.processObject(c, d.GaugeBand, this.theme), c.draw(c.startValue, c.endValue));\n      }\n    },\n    fireEvent: function (a, b, c) {\n      this.fire({\n        type: a,\n        dataItem: b,\n        chart: this,\n        event: c\n      });\n    },\n    addEventListeners: function (a, b) {\n      var c = this,\n        e = c.chart;\n      a.mouseover(function (a) {\n        e.showBalloon(b.balloonText, b.color, !0);\n        c.fireEvent('rollOverBand', b, a);\n      }).mouseout(function (a) {\n        e.hideBalloon();\n        c.fireEvent('rollOutBand', b, a);\n      }).click(function (a) {\n        c.fireEvent('clickBand', b, a);\n        d.getURL(b.url, e.urlTarget);\n      }).touchend(function (a) {\n        c.fireEvent('clickBand', b, a);\n        d.getURL(b.url, e.urlTarget);\n      });\n    }\n  });\n})();\n(function () {\n  var d = window.AmCharts;\n  d.GaugeArrow = d.Class({\n    construct: function (a) {\n      this.cname = 'GaugeArrow';\n      this.color = '#000000';\n      this.nailAlpha = this.alpha = 1;\n      this.startWidth = this.nailRadius = 8;\n      this.endWidth = 0;\n      this.borderAlpha = 1;\n      this.radius = '90%';\n      this.nailBorderAlpha = this.innerRadius = 0;\n      this.nailBorderThickness = 1;\n      this.frame = 0;\n      d.applyTheme(this, a, 'GaugeArrow');\n    },\n    setValue: function (a) {\n      var b = this.chart;\n      b ? b.setValue ? b.setValue(this, a) : this.previousValue = this.value = a : this.previousValue = this.value = a;\n    }\n  });\n  d.GaugeBand = d.Class({\n    construct: function () {\n      this.cname = 'GaugeBand';\n      this.frame = 0;\n    },\n    draw: function (a, b) {\n      var c = this.axis;\n      this.bandGraphics && this.bandGraphics.remove();\n      var e = c.chart,\n        g = c.startAngle,\n        l = c.radiusRealReal,\n        n = c.singleValueAngle,\n        h = e.container,\n        k = c.minorTickLength,\n        p = d.toCoordinate(this.radius, l);\n      isNaN(p) && (p = c.minorTickRadius);\n      l = d.toCoordinate(this.innerRadius, l);\n      isNaN(l) && (l = p - k);\n      var g = g + n * (a - c.startValue),\n        k = n * (b - a),\n        f = this.outlineColor;\n      void 0 === f && (f = c.bandOutlineColor);\n      var m = this.outlineThickness;\n      isNaN(m) && (m = c.bandOutlineThickness);\n      var w = this.outlineAlpha;\n      isNaN(w) && (w = c.bandOutlineAlpha);\n      n = this.alpha;\n      isNaN(n) && (n = c.bandAlpha);\n      f = {\n        fill: this.color,\n        stroke: f,\n        'stroke-width': m,\n        'stroke-opacity': w\n      };\n      this.url && (f.cursor = 'pointer');\n      m = this.gradientRatio;\n      m || (m = c.bandGradientRatio);\n      h = d.wedge(h, c.centerXReal, c.centerYReal, g, k, p, p, l, 0, f, m, void 0, void 0, 'radial');\n      d.setCN(e, h.wedge, 'axis-band');\n      void 0 !== this.id && d.setCN(e, h.wedge, 'axis-band-' + this.id);\n      h.setAttr('opacity', n);\n      c.bandSet.push(h);\n      this.bandGraphics = h;\n      this.currentStartValue = a;\n      this.currentEndValue = b;\n      c.addEventListeners(h, this);\n    },\n    update: function () {\n      var a = this.axis,\n        b = a.chart;\n      if (a && a.value2angle) {\n        if (this.frame >= b.totalFrames) b = this.endValue, a = this.startValue;else {\n          this.frame++;\n          var c = d.getEffect(b.startEffect),\n            a = d[c](0, this.frame, this.previousStartValue, this.startValue - this.previousStartValue, b.totalFrames),\n            b = d[c](0, this.frame, this.previousEndValue, this.endValue - this.previousEndValue, b.totalFrames);\n          isNaN(a) && (a = this.startValue);\n          isNaN(b) && (b = this.endValue);\n        }\n        a == this.currentStartValue && b == this.currentEndValue || this.draw(a, b);\n      }\n    },\n    setStartValue: function (a) {\n      this.previousStartValue = this.startValue;\n      this.startValue = a;\n      this.frame = 0;\n    },\n    setEndValue: function (a) {\n      this.previousEndValue = this.endValue;\n      this.endValue = a;\n      this.frame = 0;\n    }\n  });\n})();\n(function () {\n  var d = window.AmCharts;\n  d.AmAngularGauge = d.Class({\n    inherits: d.AmChart,\n    construct: function (a) {\n      this.cname = 'AmAngularGauge';\n      d.AmAngularGauge.base.construct.call(this, a);\n      this.theme = a;\n      this.type = 'gauge';\n      this.minRadius = this.marginRight = this.marginBottom = this.marginTop = this.marginLeft = 10;\n      this.faceColor = '#FAFAFA';\n      this.faceAlpha = 0;\n      this.faceBorderWidth = 1;\n      this.faceBorderColor = '#555555';\n      this.faceBorderAlpha = 0;\n      this.arrows = [];\n      this.axes = [];\n      this.startDuration = 1;\n      this.startEffect = 'easeOutSine';\n      this.adjustSize = !0;\n      this.extraHeight = this.extraWidth = 0;\n      d.applyTheme(this, a, this.cname);\n    },\n    addAxis: function (a) {\n      a.chart = this;\n      this.axes.push(a);\n    },\n    formatString: function (a, b) {\n      return a = d.formatValue(a, b, ['value'], this.nf, '', this.usePrefixes, this.prefixesOfSmallNumbers, this.prefixesOfBigNumbers);\n    },\n    initChart: function () {\n      d.AmAngularGauge.base.initChart.call(this);\n      var a;\n      0 === this.axes.length && (a = new d.GaugeAxis(this.theme), this.addAxis(a));\n      var b;\n      for (b = 0; b < this.axes.length; b++) a = this.axes[b], a = d.processObject(a, d.GaugeAxis, this.theme), a.id || (a.id = 'axisAuto' + b + '_' + new Date().getTime()), a.chart = this, this.axes[b] = a;\n      var c = this.arrows;\n      for (b = 0; b < c.length; b++) {\n        a = c[b];\n        a = d.processObject(a, d.GaugeArrow, this.theme);\n        a.id || (a.id = 'arrowAuto' + b + '_' + new Date().getTime());\n        a.chart = this;\n        c[b] = a;\n        var e = a.axis;\n        d.isString(e) && (a.axis = d.getObjById(this.axes, e));\n        a.axis || (a.axis = this.axes[0]);\n        isNaN(a.value) && a.setValue(a.axis.startValue);\n        isNaN(a.previousValue) && (a.previousValue = a.axis.startValue);\n      }\n      this.setLegendData(c);\n      this.drawChart();\n      this.totalFrames = this.startDuration * d.updateRate;\n    },\n    drawChart: function () {\n      d.AmAngularGauge.base.drawChart.call(this);\n      var a = this.container,\n        b = this.updateWidth();\n      this.realWidth = b;\n      var c = this.updateHeight();\n      this.realHeight = c;\n      var e = d.toCoordinate,\n        g = e(this.marginLeft, b),\n        l = e(this.marginRight, b),\n        n = e(this.marginTop, c) + this.getTitleHeight(),\n        h = e(this.marginBottom, c),\n        k = e(this.radius, b, c),\n        e = b - g - l,\n        p = c - n - h + this.extraHeight;\n      k || (k = Math.min(e, p) / 2);\n      k < this.minRadius && (k = this.minRadius);\n      this.radiusReal = k;\n      this.centerX = (b - g - l) / 2 + g;\n      this.centerY = (c - n - h) / 2 + n + this.extraHeight / 2;\n      isNaN(this.gaugeX) || (this.centerX = this.gaugeX);\n      isNaN(this.gaugeY) || (this.centerY = this.gaugeY);\n      var b = this.faceAlpha,\n        c = this.faceBorderAlpha,\n        f;\n      if (0 < b || 0 < c) f = d.circle(a, k, this.faceColor, b, this.faceBorderWidth, this.faceBorderColor, c, !1), f.translate(this.centerX, this.centerY), f.toBack(), (a = this.facePattern) && f.pattern(a, NaN, this.path);\n      for (b = k = a = 0; b < this.axes.length; b++) c = this.axes[b], g = c.radius, c.radiusReal = d.toCoordinate(g, this.radiusReal), c.draw(), l = 1, -1 !== String(g).indexOf('%') && (l = 1 + (100 - Number(g.substr(0, g.length - 1))) / 100), c.width * l > a && (a = c.width * l), c.height * l > k && (k = c.height * l);\n      (b = this.legend) && b.invalidateSize();\n      if (this.adjustSize && !this.sizeAdjusted) {\n        f && (f = f.getBBox(), f.width > a && (a = f.width), f.height > k && (k = f.height));\n        f = 0;\n        if (p > k || e > a) f = Math.min(p - k, e - a);\n        5 < f && (this.extraHeight = f, this.sizeAdjusted = !0, this.validateNow());\n      }\n      e = this.arrows.length;\n      for (b = 0; b < e; b++) p = this.arrows[b], p.drawnAngle = NaN;\n      this.dispDUpd();\n    },\n    validateSize: function () {\n      this.extraHeight = this.extraWidth = 0;\n      this.chartCreated = this.sizeAdjusted = !1;\n      d.AmAngularGauge.base.validateSize.call(this);\n    },\n    addArrow: function (a) {\n      this.arrows.push(a);\n    },\n    removeArrow: function (a) {\n      d.removeFromArray(this.arrows, a);\n      this.validateNow();\n    },\n    removeAxis: function (a) {\n      d.removeFromArray(this.axes, a);\n      this.validateNow();\n    },\n    drawArrow: function (a, b) {\n      a.set && a.set.remove();\n      var c = this.container;\n      a.set = c.set();\n      d.setCN(this, a.set, 'gauge-arrow');\n      d.setCN(this, a.set, 'gauge-arrow-' + a.id);\n      var e = a.axis,\n        g = e.radiusReal,\n        l = e.centerXReal,\n        n = e.centerYReal,\n        h = a.startWidth,\n        k = a.endWidth,\n        p = d.toCoordinate(a.innerRadius, e.radiusReal),\n        f = d.toCoordinate(a.radius, e.radiusReal);\n      e.inside || (f -= 15);\n      var m = a.nailColor;\n      m || (m = a.color);\n      var w = a.nailColor;\n      w || (w = a.color);\n      0 < a.nailRadius && (m = d.circle(c, a.nailRadius, m, a.nailAlpha, a.nailBorderThickness, m, a.nailBorderAlpha), d.setCN(this, m, 'gauge-arrow-nail'), a.set.push(m), m.translate(l, n));\n      isNaN(f) && (f = g - e.tickLength);\n      var e = Math.sin(b / 180 * Math.PI),\n        g = Math.cos(b / 180 * Math.PI),\n        m = Math.sin((b + 90) / 180 * Math.PI),\n        z = Math.cos((b + 90) / 180 * Math.PI),\n        c = d.polygon(c, [l - h / 2 * m + p * e, l + f * e - k / 2 * m, l + f * e + k / 2 * m, l + h / 2 * m + p * e], [n + h / 2 * z - p * g, n - f * g + k / 2 * z, n - f * g - k / 2 * z, n - h / 2 * z - p * g], a.color, a.alpha, 1, w, a.borderAlpha, void 0, !0);\n      d.setCN(this, c, 'gauge-arrow');\n      a.set.push(c);\n      this.graphsSet.push(a.set);\n      a.hidden && this.hideArrow(a);\n    },\n    setValue: function (a, b) {\n      a.axis && a.axis.value2angle && (a.frame = 0, a.previousValue = a.value);\n      a.value = b;\n      var c = this.legend;\n      c && c.updateValues();\n      this.accessible && this.background && this.makeAccessible(this.background, b);\n    },\n    handleLegendEvent: function (a) {\n      var b = a.type;\n      a = a.dataItem;\n      if (!this.legend.data && a) switch (b) {\n        case 'hideItem':\n          this.hideArrow(a);\n          break;\n        case 'showItem':\n          this.showArrow(a);\n      }\n    },\n    hideArrow: function (a) {\n      a.set.hide();\n      a.hidden = !0;\n      this.legend && this.legend.invalidateSize();\n    },\n    showArrow: function (a) {\n      a.set.show();\n      a.hidden = !1;\n      this.legend && this.legend.invalidateSize();\n    },\n    updateAnimations: function () {\n      d.AmAngularGauge.base.updateAnimations.call(this);\n      for (var a = this.arrows.length, b, c, e = 0; e < a; e++) b = this.arrows[e], b.axis && b.axis.value2angle && (b.frame >= this.totalFrames ? c = b.value : (b.frame++, b.clockWiseOnly && b.value < b.previousValue && (c = b.axis, b.previousValue -= c.endValue - c.startValue), c = d.getEffect(this.startEffect), c = d[c](0, b.frame, b.previousValue, b.value - b.previousValue, this.totalFrames), isNaN(c) && (c = b.value)), c = b.axis.value2angle(c), b.drawnAngle != c && (this.drawArrow(b, c), b.drawnAngle = c));\n      a = this.axes;\n      for (b = a.length - 1; 0 <= b; b--) if (c = a[b], c.bands) for (e = c.bands.length - 1; 0 <= e; e--) {\n        var g = c.bands[e];\n        g.update && g.update();\n      }\n    }\n  });\n})();", "map": {"version": 3, "names": ["d", "window", "<PERSON><PERSON><PERSON><PERSON>", "GaugeAxis", "Class", "construct", "a", "cname", "radius", "createEvents", "labelsEnabled", "startAngle", "endAngle", "startValue", "endValue", "gridCount", "tick<PERSON><PERSON>th", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tickColor", "labelFrequency", "tickThickness", "tickAlpha", "inside", "labelOffset", "showLastLabel", "showFirstLabel", "axisThickness", "axisColor", "axisAlpha", "gridInside", "topTextYOffset", "topTextBold", "bottomTextYOffset", "bottomTextBold", "centerY", "centerX", "bandOutlineAlpha", "bandOutlineThickness", "bandOutlineColor", "bandAlpha", "bcn", "applyTheme", "value2angle", "setTopText", "topText", "b", "chart", "axisCreated", "topTF", "remove", "c", "topTextFontSize", "fontSize", "e", "topTextColor", "color", "text", "container", "fontFamily", "setCN", "translate", "centerXReal", "centerYReal", "radiusReal", "set", "push", "setBottomText", "bottomText", "bottomTF", "bottomTextFontSize", "bottomTextColor", "draw", "id", "graphsSet", "bandSet", "g", "valueInterval", "isNaN", "l", "minorTickInterval", "n", "h", "k", "p", "f", "singleValueAngle", "m", "w", "z", "J", "K", "H", "I", "v", "radiusRealReal", "A", "toCoordinate", "realWidth", "B", "realHeight", "t", "fill", "r", "C", "minorTickRadius", "drawBands", "q", "wedge", "doNothing", "isModern", "Math", "round", "getDecimals", "max", "roundTo", "u", "sin", "PI", "F", "cos", "x", "y", "line", "D", "L", "E", "usePrefixes", "addPrefix", "prefixesOfBigNumbers", "prefixesOfSmallNumbers", "nf", "formatNumber", "G", "unit", "unitPosition", "labelFunction", "getBBox", "width", "height", "bands", "length", "axis", "processObject", "GaugeBand", "theme", "fireEvent", "fire", "type", "dataItem", "event", "addEventListeners", "mouseover", "showBalloon", "balloonText", "mouseout", "hideBalloon", "click", "getURL", "url", "url<PERSON>arget", "touchend", "GaugeArrow", "nail<PERSON>l<PERSON>", "alpha", "startWidth", "nailRadius", "endWidth", "borderAlpha", "nailBorderAlpha", "innerRadius", "nailBorderThickness", "frame", "setValue", "previousValue", "value", "bandGraphics", "outlineColor", "outlineThickness", "outlineAlpha", "stroke", "cursor", "gradientRatio", "bandGradientRatio", "setAttr", "currentStartValue", "currentEndValue", "update", "totalFrames", "getEffect", "startEffect", "previousStartValue", "previousEndValue", "setStartValue", "setEndValue", "AmAngularGauge", "inherits", "AmChart", "base", "call", "minRadius", "marginRight", "marginBottom", "marginTop", "marginLeft", "faceColor", "faceAlpha", "faceBorderWidth", "faceBorderColor", "faceBorderAlpha", "arrows", "axes", "startDuration", "adjustSize", "extraHeight", "extraWidth", "addAxis", "formatString", "formatValue", "initChart", "Date", "getTime", "isString", "getObjById", "setLegendData", "<PERSON><PERSON><PERSON>", "updateRate", "updateWidth", "updateHeight", "getTitleHeight", "min", "gaugeX", "gaugeY", "circle", "toBack", "facePattern", "pattern", "NaN", "path", "String", "indexOf", "Number", "substr", "legend", "invalidateSize", "sizeAdjusted", "validateNow", "drawnAngle", "dispDUpd", "validateSize", "chartCreated", "addArrow", "removeArrow", "removeFromArray", "removeAxis", "drawArrow", "nailColor", "polygon", "hidden", "hideArrow", "updateValues", "accessible", "background", "makeAccessible", "handleLegendEvent", "data", "showArrow", "hide", "show", "updateAnimations", "clockWiseOnly"], "sources": ["D:/employee-survey-app/company-owner-frontend/src/assets/charts/amchart/gauge.js"], "sourcesContent": ["(function () {\r\n  var d = window.AmCharts;\r\n  d.GaugeAxis = d.Class({\r\n    construct: function (a) {\r\n      this.cname = 'GaugeAxis';\r\n      this.radius = '95%';\r\n      this.createEvents('rollOverBand', 'rollOutBand', 'clickBand');\r\n      this.labelsEnabled = !0;\r\n      this.startAngle = -120;\r\n      this.endAngle = 120;\r\n      this.startValue = 0;\r\n      this.endValue = 200;\r\n      this.gridCount = 5;\r\n      this.tickLength = 10;\r\n      this.minorTickLength = 5;\r\n      this.tickColor = '#555555';\r\n      this.labelFrequency = this.tickThickness = this.tickAlpha = 1;\r\n      this.inside = !0;\r\n      this.labelOffset = 10;\r\n      this.showLastLabel = this.showFirstLabel = !0;\r\n      this.axisThickness = 1;\r\n      this.axisColor = '#000000';\r\n      this.axisAlpha = 1;\r\n      this.gridInside = !0;\r\n      this.topTextYOffset = 0;\r\n      this.topTextBold = !0;\r\n      this.bottomTextYOffset = 0;\r\n      this.bottomTextBold = !0;\r\n      this.centerY = this.centerX = '0%';\r\n      this.bandOutlineAlpha = this.bandOutlineThickness = 0;\r\n      this.bandOutlineColor = '#000000';\r\n      this.bandAlpha = 1;\r\n      this.bcn = 'gauge-axis';\r\n      d.applyTheme(this, a, 'GaugeAxis');\r\n    },\r\n    value2angle: function (a) {\r\n      return ((a - this.startValue) / (this.endValue - this.startValue)) * (this.endAngle - this.startAngle) + this.startAngle;\r\n    },\r\n    setTopText: function (a) {\r\n      if (void 0 !== a) {\r\n        this.topText = a;\r\n        var b = this.chart;\r\n        if (this.axisCreated) {\r\n          this.topTF && this.topTF.remove();\r\n          var c = this.topTextFontSize;\r\n          c || (c = b.fontSize);\r\n          var e = this.topTextColor;\r\n          e || (e = b.color);\r\n          a = d.text(b.container, a, e, b.fontFamily, c, void 0, this.topTextBold);\r\n          d.setCN(b, a, 'axis-top-label');\r\n          a.translate(this.centerXReal, this.centerYReal - this.radiusReal / 2 + this.topTextYOffset);\r\n          this.set.push(a);\r\n          this.topTF = a;\r\n        }\r\n      }\r\n    },\r\n    setBottomText: function (a) {\r\n      if (void 0 !== a) {\r\n        this.bottomText = a;\r\n        var b = this.chart;\r\n        if (this.axisCreated) {\r\n          this.bottomTF && this.bottomTF.remove();\r\n          var c = this.bottomTextFontSize;\r\n          c || (c = b.fontSize);\r\n          var e = this.bottomTextColor;\r\n          e || (e = b.color);\r\n          a = d.text(b.container, a, e, b.fontFamily, c, void 0, this.bottomTextBold);\r\n          d.setCN(b, a, 'axis-bottom-label');\r\n          a.translate(this.centerXReal, this.centerYReal + this.radiusReal / 2 + this.bottomTextYOffset);\r\n          this.bottomTF = a;\r\n          this.set.push(a);\r\n        }\r\n      }\r\n    },\r\n    draw: function () {\r\n      var a = this.chart,\r\n        b = a.container.set();\r\n      this.set = b;\r\n      d.setCN(a, b, this.bcn);\r\n      d.setCN(a, b, this.bcn + '-' + this.id);\r\n      a.graphsSet.push(b);\r\n      this.bandSet = a.container.set();\r\n      this.set.push(this.bandSet);\r\n      var c = this.startValue,\r\n        e = this.endValue,\r\n        g = this.valueInterval;\r\n      isNaN(g) && (g = (e - c) / this.gridCount);\r\n      var l = this.minorTickInterval;\r\n      isNaN(l) && (l = g / 5);\r\n      var n = this.startAngle,\r\n        h = this.endAngle,\r\n        k = this.tickLength,\r\n        p = (e - c) / g + 1,\r\n        f = (h - n) / (p - 1);\r\n      this.singleValueAngle = f / g;\r\n      var m = a.container,\r\n        w = this.tickColor,\r\n        z = this.tickAlpha,\r\n        J = this.tickThickness,\r\n        l = g / l,\r\n        K = f / l,\r\n        H = this.minorTickLength,\r\n        I = this.labelFrequency,\r\n        v = this.radiusReal;\r\n      this.inside || (v -= 15);\r\n      this.radiusRealReal = v;\r\n      var A = a.centerX + d.toCoordinate(this.centerX, a.realWidth),\r\n        B = a.centerY + d.toCoordinate(this.centerY, a.realHeight);\r\n      this.centerXReal = A;\r\n      this.centerYReal = B;\r\n      var t = {\r\n          fill: this.axisColor,\r\n          'fill-opacity': this.axisAlpha,\r\n          'stroke-width': 0,\r\n          'stroke-opacity': 0\r\n        },\r\n        r,\r\n        C;\r\n      this.gridInside ? (C = r = v) : ((r = v - k), (C = r + H));\r\n      this.minorTickRadius = C;\r\n      this.drawBands();\r\n      var q = this.axisThickness / 2,\r\n        h = d.wedge(m, A, B, n, h - n, r + q, r + q, r - q, 0, t);\r\n      d.setCN(a, h.wedge, 'axis-line');\r\n      b.push(h);\r\n      h = d.doNothing;\r\n      d.isModern || (h = Math.round);\r\n      t = d.getDecimals(c);\r\n      r = d.getDecimals(e);\r\n      e = d.getDecimals(g);\r\n      e = Math.max(e, t, r);\r\n      g = d.roundTo(g, e + 1);\r\n      for (t = 0; t < p; t++) {\r\n        q = d.roundTo(c + t * g, e);\r\n        r = n + t * f;\r\n        var u = h(A + v * Math.sin((r / 180) * Math.PI)),\r\n          F = h(B - v * Math.cos((r / 180) * Math.PI)),\r\n          x = h(A + (v - k) * Math.sin((r / 180) * Math.PI)),\r\n          y = h(B - (v - k) * Math.cos((r / 180) * Math.PI)),\r\n          u = d.line(m, [u, x], [F, y], w, z, J, 0, !1, !1, !0);\r\n        d.setCN(a, u, 'axis-tick');\r\n        b.push(u);\r\n        u = -1;\r\n        x = this.labelOffset;\r\n        this.inside || ((x = -x - k), (u = 1));\r\n        var F = A + (v - k - x) * Math.sin((r / 180) * Math.PI),\r\n          x = B - (v - k - x) * Math.cos((r / 180) * Math.PI),\r\n          D = this.fontSize;\r\n        isNaN(D) && (D = a.fontSize);\r\n        var y = Math.sin(((r - 90) / 180) * Math.PI),\r\n          L = Math.cos(((r - 90) / 180) * Math.PI);\r\n        if (\r\n          0 < I &&\r\n          this.labelsEnabled &&\r\n          t / I == Math.round(t / I) &&\r\n          (this.showLastLabel || t != p - 1) &&\r\n          (this.showFirstLabel || 0 !== t)\r\n        ) {\r\n          var E;\r\n          E = this.usePrefixes ? d.addPrefix(q, a.prefixesOfBigNumbers, a.prefixesOfSmallNumbers, a.nf, !0) : d.formatNumber(q, a.nf, e);\r\n          var G = this.unit;\r\n          G && (E = 'left' == this.unitPosition ? G + E : E + G);\r\n          (G = this.labelFunction) && (E = G(q));\r\n          q = this.color;\r\n          void 0 === q && (q = a.color);\r\n          q = d.text(m, E, q, a.fontFamily, D);\r\n          d.setCN(a, q, 'axis-label');\r\n          D = q.getBBox();\r\n          q.translate(F + ((u * D.width) / 2) * L, x + ((u * D.height) / 2) * y);\r\n          b.push(q);\r\n        }\r\n        if (t < p - 1)\r\n          for (q = 1; q < l; q++)\r\n            (y = r + K * q),\r\n              (u = h(A + C * Math.sin((y / 180) * Math.PI))),\r\n              (F = h(B - C * Math.cos((y / 180) * Math.PI))),\r\n              (x = h(A + (C - H) * Math.sin((y / 180) * Math.PI))),\r\n              (y = h(B - (C - H) * Math.cos((y / 180) * Math.PI))),\r\n              (u = d.line(m, [u, x], [F, y], w, z, J, 0, !1, !1, !0)),\r\n              d.setCN(a, u, 'axis-tick-minor'),\r\n              b.push(u);\r\n      }\r\n      this.axisCreated = !0;\r\n      this.setTopText(this.topText);\r\n      this.setBottomText(this.bottomText);\r\n      a = a.graphsSet.getBBox();\r\n      this.width = a.width;\r\n      this.height = a.height;\r\n    },\r\n    drawBands: function () {\r\n      var a = this.bands;\r\n      if (a)\r\n        for (var b = 0; b < a.length; b++) {\r\n          var c = a[b];\r\n          c && ((c.axis = this), d.processObject(c, d.GaugeBand, this.theme), c.draw(c.startValue, c.endValue));\r\n        }\r\n    },\r\n    fireEvent: function (a, b, c) {\r\n      this.fire({ type: a, dataItem: b, chart: this, event: c });\r\n    },\r\n    addEventListeners: function (a, b) {\r\n      var c = this,\r\n        e = c.chart;\r\n      a.mouseover(function (a) {\r\n        e.showBalloon(b.balloonText, b.color, !0);\r\n        c.fireEvent('rollOverBand', b, a);\r\n      })\r\n        .mouseout(function (a) {\r\n          e.hideBalloon();\r\n          c.fireEvent('rollOutBand', b, a);\r\n        })\r\n        .click(function (a) {\r\n          c.fireEvent('clickBand', b, a);\r\n          d.getURL(b.url, e.urlTarget);\r\n        })\r\n        .touchend(function (a) {\r\n          c.fireEvent('clickBand', b, a);\r\n          d.getURL(b.url, e.urlTarget);\r\n        });\r\n    }\r\n  });\r\n})();\r\n(function () {\r\n  var d = window.AmCharts;\r\n  d.GaugeArrow = d.Class({\r\n    construct: function (a) {\r\n      this.cname = 'GaugeArrow';\r\n      this.color = '#000000';\r\n      this.nailAlpha = this.alpha = 1;\r\n      this.startWidth = this.nailRadius = 8;\r\n      this.endWidth = 0;\r\n      this.borderAlpha = 1;\r\n      this.radius = '90%';\r\n      this.nailBorderAlpha = this.innerRadius = 0;\r\n      this.nailBorderThickness = 1;\r\n      this.frame = 0;\r\n      d.applyTheme(this, a, 'GaugeArrow');\r\n    },\r\n    setValue: function (a) {\r\n      var b = this.chart;\r\n      b ? (b.setValue ? b.setValue(this, a) : (this.previousValue = this.value = a)) : (this.previousValue = this.value = a);\r\n    }\r\n  });\r\n  d.GaugeBand = d.Class({\r\n    construct: function () {\r\n      this.cname = 'GaugeBand';\r\n      this.frame = 0;\r\n    },\r\n    draw: function (a, b) {\r\n      var c = this.axis;\r\n      this.bandGraphics && this.bandGraphics.remove();\r\n      var e = c.chart,\r\n        g = c.startAngle,\r\n        l = c.radiusRealReal,\r\n        n = c.singleValueAngle,\r\n        h = e.container,\r\n        k = c.minorTickLength,\r\n        p = d.toCoordinate(this.radius, l);\r\n      isNaN(p) && (p = c.minorTickRadius);\r\n      l = d.toCoordinate(this.innerRadius, l);\r\n      isNaN(l) && (l = p - k);\r\n      var g = g + n * (a - c.startValue),\r\n        k = n * (b - a),\r\n        f = this.outlineColor;\r\n      void 0 === f && (f = c.bandOutlineColor);\r\n      var m = this.outlineThickness;\r\n      isNaN(m) && (m = c.bandOutlineThickness);\r\n      var w = this.outlineAlpha;\r\n      isNaN(w) && (w = c.bandOutlineAlpha);\r\n      n = this.alpha;\r\n      isNaN(n) && (n = c.bandAlpha);\r\n      f = {\r\n        fill: this.color,\r\n        stroke: f,\r\n        'stroke-width': m,\r\n        'stroke-opacity': w\r\n      };\r\n      this.url && (f.cursor = 'pointer');\r\n      m = this.gradientRatio;\r\n      m || (m = c.bandGradientRatio);\r\n      h = d.wedge(h, c.centerXReal, c.centerYReal, g, k, p, p, l, 0, f, m, void 0, void 0, 'radial');\r\n      d.setCN(e, h.wedge, 'axis-band');\r\n      void 0 !== this.id && d.setCN(e, h.wedge, 'axis-band-' + this.id);\r\n      h.setAttr('opacity', n);\r\n      c.bandSet.push(h);\r\n      this.bandGraphics = h;\r\n      this.currentStartValue = a;\r\n      this.currentEndValue = b;\r\n      c.addEventListeners(h, this);\r\n    },\r\n    update: function () {\r\n      var a = this.axis,\r\n        b = a.chart;\r\n      if (a && a.value2angle) {\r\n        if (this.frame >= b.totalFrames) (b = this.endValue), (a = this.startValue);\r\n        else {\r\n          this.frame++;\r\n          var c = d.getEffect(b.startEffect),\r\n            a = d[c](0, this.frame, this.previousStartValue, this.startValue - this.previousStartValue, b.totalFrames),\r\n            b = d[c](0, this.frame, this.previousEndValue, this.endValue - this.previousEndValue, b.totalFrames);\r\n          isNaN(a) && (a = this.startValue);\r\n          isNaN(b) && (b = this.endValue);\r\n        }\r\n        (a == this.currentStartValue && b == this.currentEndValue) || this.draw(a, b);\r\n      }\r\n    },\r\n    setStartValue: function (a) {\r\n      this.previousStartValue = this.startValue;\r\n      this.startValue = a;\r\n      this.frame = 0;\r\n    },\r\n    setEndValue: function (a) {\r\n      this.previousEndValue = this.endValue;\r\n      this.endValue = a;\r\n      this.frame = 0;\r\n    }\r\n  });\r\n})();\r\n(function () {\r\n  var d = window.AmCharts;\r\n  d.AmAngularGauge = d.Class({\r\n    inherits: d.AmChart,\r\n    construct: function (a) {\r\n      this.cname = 'AmAngularGauge';\r\n      d.AmAngularGauge.base.construct.call(this, a);\r\n      this.theme = a;\r\n      this.type = 'gauge';\r\n      this.minRadius = this.marginRight = this.marginBottom = this.marginTop = this.marginLeft = 10;\r\n      this.faceColor = '#FAFAFA';\r\n      this.faceAlpha = 0;\r\n      this.faceBorderWidth = 1;\r\n      this.faceBorderColor = '#555555';\r\n      this.faceBorderAlpha = 0;\r\n      this.arrows = [];\r\n      this.axes = [];\r\n      this.startDuration = 1;\r\n      this.startEffect = 'easeOutSine';\r\n      this.adjustSize = !0;\r\n      this.extraHeight = this.extraWidth = 0;\r\n      d.applyTheme(this, a, this.cname);\r\n    },\r\n    addAxis: function (a) {\r\n      a.chart = this;\r\n      this.axes.push(a);\r\n    },\r\n    formatString: function (a, b) {\r\n      return (a = d.formatValue(a, b, ['value'], this.nf, '', this.usePrefixes, this.prefixesOfSmallNumbers, this.prefixesOfBigNumbers));\r\n    },\r\n    initChart: function () {\r\n      d.AmAngularGauge.base.initChart.call(this);\r\n      var a;\r\n      0 === this.axes.length && ((a = new d.GaugeAxis(this.theme)), this.addAxis(a));\r\n      var b;\r\n      for (b = 0; b < this.axes.length; b++)\r\n        (a = this.axes[b]),\r\n          (a = d.processObject(a, d.GaugeAxis, this.theme)),\r\n          a.id || (a.id = 'axisAuto' + b + '_' + new Date().getTime()),\r\n          (a.chart = this),\r\n          (this.axes[b] = a);\r\n      var c = this.arrows;\r\n      for (b = 0; b < c.length; b++) {\r\n        a = c[b];\r\n        a = d.processObject(a, d.GaugeArrow, this.theme);\r\n        a.id || (a.id = 'arrowAuto' + b + '_' + new Date().getTime());\r\n        a.chart = this;\r\n        c[b] = a;\r\n        var e = a.axis;\r\n        d.isString(e) && (a.axis = d.getObjById(this.axes, e));\r\n        a.axis || (a.axis = this.axes[0]);\r\n        isNaN(a.value) && a.setValue(a.axis.startValue);\r\n        isNaN(a.previousValue) && (a.previousValue = a.axis.startValue);\r\n      }\r\n      this.setLegendData(c);\r\n      this.drawChart();\r\n      this.totalFrames = this.startDuration * d.updateRate;\r\n    },\r\n    drawChart: function () {\r\n      d.AmAngularGauge.base.drawChart.call(this);\r\n      var a = this.container,\r\n        b = this.updateWidth();\r\n      this.realWidth = b;\r\n      var c = this.updateHeight();\r\n      this.realHeight = c;\r\n      var e = d.toCoordinate,\r\n        g = e(this.marginLeft, b),\r\n        l = e(this.marginRight, b),\r\n        n = e(this.marginTop, c) + this.getTitleHeight(),\r\n        h = e(this.marginBottom, c),\r\n        k = e(this.radius, b, c),\r\n        e = b - g - l,\r\n        p = c - n - h + this.extraHeight;\r\n      k || (k = Math.min(e, p) / 2);\r\n      k < this.minRadius && (k = this.minRadius);\r\n      this.radiusReal = k;\r\n      this.centerX = (b - g - l) / 2 + g;\r\n      this.centerY = (c - n - h) / 2 + n + this.extraHeight / 2;\r\n      isNaN(this.gaugeX) || (this.centerX = this.gaugeX);\r\n      isNaN(this.gaugeY) || (this.centerY = this.gaugeY);\r\n      var b = this.faceAlpha,\r\n        c = this.faceBorderAlpha,\r\n        f;\r\n      if (0 < b || 0 < c)\r\n        (f = d.circle(a, k, this.faceColor, b, this.faceBorderWidth, this.faceBorderColor, c, !1)),\r\n          f.translate(this.centerX, this.centerY),\r\n          f.toBack(),\r\n          (a = this.facePattern) && f.pattern(a, NaN, this.path);\r\n      for (b = k = a = 0; b < this.axes.length; b++)\r\n        (c = this.axes[b]),\r\n          (g = c.radius),\r\n          (c.radiusReal = d.toCoordinate(g, this.radiusReal)),\r\n          c.draw(),\r\n          (l = 1),\r\n          -1 !== String(g).indexOf('%') && (l = 1 + (100 - Number(g.substr(0, g.length - 1))) / 100),\r\n          c.width * l > a && (a = c.width * l),\r\n          c.height * l > k && (k = c.height * l);\r\n      (b = this.legend) && b.invalidateSize();\r\n      if (this.adjustSize && !this.sizeAdjusted) {\r\n        f && ((f = f.getBBox()), f.width > a && (a = f.width), f.height > k && (k = f.height));\r\n        f = 0;\r\n        if (p > k || e > a) f = Math.min(p - k, e - a);\r\n        5 < f && ((this.extraHeight = f), (this.sizeAdjusted = !0), this.validateNow());\r\n      }\r\n      e = this.arrows.length;\r\n      for (b = 0; b < e; b++) (p = this.arrows[b]), (p.drawnAngle = NaN);\r\n      this.dispDUpd();\r\n    },\r\n    validateSize: function () {\r\n      this.extraHeight = this.extraWidth = 0;\r\n      this.chartCreated = this.sizeAdjusted = !1;\r\n      d.AmAngularGauge.base.validateSize.call(this);\r\n    },\r\n    addArrow: function (a) {\r\n      this.arrows.push(a);\r\n    },\r\n    removeArrow: function (a) {\r\n      d.removeFromArray(this.arrows, a);\r\n      this.validateNow();\r\n    },\r\n    removeAxis: function (a) {\r\n      d.removeFromArray(this.axes, a);\r\n      this.validateNow();\r\n    },\r\n    drawArrow: function (a, b) {\r\n      a.set && a.set.remove();\r\n      var c = this.container;\r\n      a.set = c.set();\r\n      d.setCN(this, a.set, 'gauge-arrow');\r\n      d.setCN(this, a.set, 'gauge-arrow-' + a.id);\r\n      var e = a.axis,\r\n        g = e.radiusReal,\r\n        l = e.centerXReal,\r\n        n = e.centerYReal,\r\n        h = a.startWidth,\r\n        k = a.endWidth,\r\n        p = d.toCoordinate(a.innerRadius, e.radiusReal),\r\n        f = d.toCoordinate(a.radius, e.radiusReal);\r\n      e.inside || (f -= 15);\r\n      var m = a.nailColor;\r\n      m || (m = a.color);\r\n      var w = a.nailColor;\r\n      w || (w = a.color);\r\n      0 < a.nailRadius &&\r\n        ((m = d.circle(c, a.nailRadius, m, a.nailAlpha, a.nailBorderThickness, m, a.nailBorderAlpha)),\r\n        d.setCN(this, m, 'gauge-arrow-nail'),\r\n        a.set.push(m),\r\n        m.translate(l, n));\r\n      isNaN(f) && (f = g - e.tickLength);\r\n      var e = Math.sin((b / 180) * Math.PI),\r\n        g = Math.cos((b / 180) * Math.PI),\r\n        m = Math.sin(((b + 90) / 180) * Math.PI),\r\n        z = Math.cos(((b + 90) / 180) * Math.PI),\r\n        c = d.polygon(\r\n          c,\r\n          [l - (h / 2) * m + p * e, l + f * e - (k / 2) * m, l + f * e + (k / 2) * m, l + (h / 2) * m + p * e],\r\n          [n + (h / 2) * z - p * g, n - f * g + (k / 2) * z, n - f * g - (k / 2) * z, n - (h / 2) * z - p * g],\r\n          a.color,\r\n          a.alpha,\r\n          1,\r\n          w,\r\n          a.borderAlpha,\r\n          void 0,\r\n          !0\r\n        );\r\n      d.setCN(this, c, 'gauge-arrow');\r\n      a.set.push(c);\r\n      this.graphsSet.push(a.set);\r\n      a.hidden && this.hideArrow(a);\r\n    },\r\n    setValue: function (a, b) {\r\n      a.axis && a.axis.value2angle && ((a.frame = 0), (a.previousValue = a.value));\r\n      a.value = b;\r\n      var c = this.legend;\r\n      c && c.updateValues();\r\n      this.accessible && this.background && this.makeAccessible(this.background, b);\r\n    },\r\n    handleLegendEvent: function (a) {\r\n      var b = a.type;\r\n      a = a.dataItem;\r\n      if (!this.legend.data && a)\r\n        switch (b) {\r\n          case 'hideItem':\r\n            this.hideArrow(a);\r\n            break;\r\n          case 'showItem':\r\n            this.showArrow(a);\r\n        }\r\n    },\r\n    hideArrow: function (a) {\r\n      a.set.hide();\r\n      a.hidden = !0;\r\n      this.legend && this.legend.invalidateSize();\r\n    },\r\n    showArrow: function (a) {\r\n      a.set.show();\r\n      a.hidden = !1;\r\n      this.legend && this.legend.invalidateSize();\r\n    },\r\n    updateAnimations: function () {\r\n      d.AmAngularGauge.base.updateAnimations.call(this);\r\n      for (var a = this.arrows.length, b, c, e = 0; e < a; e++)\r\n        (b = this.arrows[e]),\r\n          b.axis &&\r\n            b.axis.value2angle &&\r\n            (b.frame >= this.totalFrames\r\n              ? (c = b.value)\r\n              : (b.frame++,\r\n                b.clockWiseOnly && b.value < b.previousValue && ((c = b.axis), (b.previousValue -= c.endValue - c.startValue)),\r\n                (c = d.getEffect(this.startEffect)),\r\n                (c = d[c](0, b.frame, b.previousValue, b.value - b.previousValue, this.totalFrames)),\r\n                isNaN(c) && (c = b.value)),\r\n            (c = b.axis.value2angle(c)),\r\n            b.drawnAngle != c && (this.drawArrow(b, c), (b.drawnAngle = c)));\r\n      a = this.axes;\r\n      for (b = a.length - 1; 0 <= b; b--)\r\n        if (((c = a[b]), c.bands))\r\n          for (e = c.bands.length - 1; 0 <= e; e--) {\r\n            var g = c.bands[e];\r\n            g.update && g.update();\r\n          }\r\n    }\r\n  });\r\n})();\r\n"], "mappings": "AAAA,CAAC,YAAY;EACX,IAAIA,CAAC,GAAGC,MAAM,CAACC,QAAQ;EACvBF,CAAC,CAACG,SAAS,GAAGH,CAAC,CAACI,KAAK,CAAC;IACpBC,SAAS,EAAE,SAAAA,CAAUC,CAAC,EAAE;MACtB,IAAI,CAACC,KAAK,GAAG,WAAW;MACxB,IAAI,CAACC,MAAM,GAAG,KAAK;MACnB,IAAI,CAACC,YAAY,CAAC,cAAc,EAAE,aAAa,EAAE,WAAW,CAAC;MAC7D,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;MACvB,IAAI,CAACC,UAAU,GAAG,CAAC,GAAG;MACtB,IAAI,CAACC,QAAQ,GAAG,GAAG;MACnB,IAAI,CAACC,UAAU,GAAG,CAAC;MACnB,IAAI,CAACC,QAAQ,GAAG,GAAG;MACnB,IAAI,CAACC,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,eAAe,GAAG,CAAC;MACxB,IAAI,CAACC,SAAS,GAAG,SAAS;MAC1B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,SAAS,GAAG,CAAC;MAC7D,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;MAChB,IAAI,CAACC,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;MAC7C,IAAI,CAACC,aAAa,GAAG,CAAC;MACtB,IAAI,CAACC,SAAS,GAAG,SAAS;MAC1B,IAAI,CAACC,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;MACpB,IAAI,CAACC,cAAc,GAAG,CAAC;MACvB,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;MACrB,IAAI,CAACC,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;MACxB,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,OAAO,GAAG,IAAI;MAClC,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,GAAG,CAAC;MACrD,IAAI,CAACC,gBAAgB,GAAG,SAAS;MACjC,IAAI,CAACC,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,GAAG,GAAG,YAAY;MACvBxC,CAAC,CAACyC,UAAU,CAAC,IAAI,EAAEnC,CAAC,EAAE,WAAW,CAAC;IACpC,CAAC;IACDoC,WAAW,EAAE,SAAAA,CAAUpC,CAAC,EAAE;MACxB,OAAQ,CAACA,CAAC,GAAG,IAAI,CAACO,UAAU,KAAK,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACD,UAAU,CAAC,IAAK,IAAI,CAACD,QAAQ,GAAG,IAAI,CAACD,UAAU,CAAC,GAAG,IAAI,CAACA,UAAU;IAC1H,CAAC;IACDgC,UAAU,EAAE,SAAAA,CAAUrC,CAAC,EAAE;MACvB,IAAI,KAAK,CAAC,KAAKA,CAAC,EAAE;QAChB,IAAI,CAACsC,OAAO,GAAGtC,CAAC;QAChB,IAAIuC,CAAC,GAAG,IAAI,CAACC,KAAK;QAClB,IAAI,IAAI,CAACC,WAAW,EAAE;UACpB,IAAI,CAACC,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,MAAM,CAAC,CAAC;UACjC,IAAIC,CAAC,GAAG,IAAI,CAACC,eAAe;UAC5BD,CAAC,KAAKA,CAAC,GAAGL,CAAC,CAACO,QAAQ,CAAC;UACrB,IAAIC,CAAC,GAAG,IAAI,CAACC,YAAY;UACzBD,CAAC,KAAKA,CAAC,GAAGR,CAAC,CAACU,KAAK,CAAC;UAClBjD,CAAC,GAAGN,CAAC,CAACwD,IAAI,CAACX,CAAC,CAACY,SAAS,EAAEnD,CAAC,EAAE+C,CAAC,EAAER,CAAC,CAACa,UAAU,EAAER,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,CAACnB,WAAW,CAAC;UACxE/B,CAAC,CAAC2D,KAAK,CAACd,CAAC,EAAEvC,CAAC,EAAE,gBAAgB,CAAC;UAC/BA,CAAC,CAACsD,SAAS,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,UAAU,GAAG,CAAC,GAAG,IAAI,CAACjC,cAAc,CAAC;UAC3F,IAAI,CAACkC,GAAG,CAACC,IAAI,CAAC3D,CAAC,CAAC;UAChB,IAAI,CAAC0C,KAAK,GAAG1C,CAAC;QAChB;MACF;IACF,CAAC;IACD4D,aAAa,EAAE,SAAAA,CAAU5D,CAAC,EAAE;MAC1B,IAAI,KAAK,CAAC,KAAKA,CAAC,EAAE;QAChB,IAAI,CAAC6D,UAAU,GAAG7D,CAAC;QACnB,IAAIuC,CAAC,GAAG,IAAI,CAACC,KAAK;QAClB,IAAI,IAAI,CAACC,WAAW,EAAE;UACpB,IAAI,CAACqB,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACnB,MAAM,CAAC,CAAC;UACvC,IAAIC,CAAC,GAAG,IAAI,CAACmB,kBAAkB;UAC/BnB,CAAC,KAAKA,CAAC,GAAGL,CAAC,CAACO,QAAQ,CAAC;UACrB,IAAIC,CAAC,GAAG,IAAI,CAACiB,eAAe;UAC5BjB,CAAC,KAAKA,CAAC,GAAGR,CAAC,CAACU,KAAK,CAAC;UAClBjD,CAAC,GAAGN,CAAC,CAACwD,IAAI,CAACX,CAAC,CAACY,SAAS,EAAEnD,CAAC,EAAE+C,CAAC,EAAER,CAAC,CAACa,UAAU,EAAER,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,CAACjB,cAAc,CAAC;UAC3EjC,CAAC,CAAC2D,KAAK,CAACd,CAAC,EAAEvC,CAAC,EAAE,mBAAmB,CAAC;UAClCA,CAAC,CAACsD,SAAS,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC/B,iBAAiB,CAAC;UAC9F,IAAI,CAACoC,QAAQ,GAAG9D,CAAC;UACjB,IAAI,CAAC0D,GAAG,CAACC,IAAI,CAAC3D,CAAC,CAAC;QAClB;MACF;IACF,CAAC;IACDiE,IAAI,EAAE,SAAAA,CAAA,EAAY;MAChB,IAAIjE,CAAC,GAAG,IAAI,CAACwC,KAAK;QAChBD,CAAC,GAAGvC,CAAC,CAACmD,SAAS,CAACO,GAAG,CAAC,CAAC;MACvB,IAAI,CAACA,GAAG,GAAGnB,CAAC;MACZ7C,CAAC,CAAC2D,KAAK,CAACrD,CAAC,EAAEuC,CAAC,EAAE,IAAI,CAACL,GAAG,CAAC;MACvBxC,CAAC,CAAC2D,KAAK,CAACrD,CAAC,EAAEuC,CAAC,EAAE,IAAI,CAACL,GAAG,GAAG,GAAG,GAAG,IAAI,CAACgC,EAAE,CAAC;MACvClE,CAAC,CAACmE,SAAS,CAACR,IAAI,CAACpB,CAAC,CAAC;MACnB,IAAI,CAAC6B,OAAO,GAAGpE,CAAC,CAACmD,SAAS,CAACO,GAAG,CAAC,CAAC;MAChC,IAAI,CAACA,GAAG,CAACC,IAAI,CAAC,IAAI,CAACS,OAAO,CAAC;MAC3B,IAAIxB,CAAC,GAAG,IAAI,CAACrC,UAAU;QACrBwC,CAAC,GAAG,IAAI,CAACvC,QAAQ;QACjB6D,CAAC,GAAG,IAAI,CAACC,aAAa;MACxBC,KAAK,CAACF,CAAC,CAAC,KAAKA,CAAC,GAAG,CAACtB,CAAC,GAAGH,CAAC,IAAI,IAAI,CAACnC,SAAS,CAAC;MAC1C,IAAI+D,CAAC,GAAG,IAAI,CAACC,iBAAiB;MAC9BF,KAAK,CAACC,CAAC,CAAC,KAAKA,CAAC,GAAGH,CAAC,GAAG,CAAC,CAAC;MACvB,IAAIK,CAAC,GAAG,IAAI,CAACrE,UAAU;QACrBsE,CAAC,GAAG,IAAI,CAACrE,QAAQ;QACjBsE,CAAC,GAAG,IAAI,CAAClE,UAAU;QACnBmE,CAAC,GAAG,CAAC9B,CAAC,GAAGH,CAAC,IAAIyB,CAAC,GAAG,CAAC;QACnBS,CAAC,GAAG,CAACH,CAAC,GAAGD,CAAC,KAAKG,CAAC,GAAG,CAAC,CAAC;MACvB,IAAI,CAACE,gBAAgB,GAAGD,CAAC,GAAGT,CAAC;MAC7B,IAAIW,CAAC,GAAGhF,CAAC,CAACmD,SAAS;QACjB8B,CAAC,GAAG,IAAI,CAACrE,SAAS;QAClBsE,CAAC,GAAG,IAAI,CAACnE,SAAS;QAClBoE,CAAC,GAAG,IAAI,CAACrE,aAAa;QACtB0D,CAAC,GAAGH,CAAC,GAAGG,CAAC;QACTY,CAAC,GAAGN,CAAC,GAAGN,CAAC;QACTa,CAAC,GAAG,IAAI,CAAC1E,eAAe;QACxB2E,CAAC,GAAG,IAAI,CAACzE,cAAc;QACvB0E,CAAC,GAAG,IAAI,CAAC9B,UAAU;MACrB,IAAI,CAACzC,MAAM,KAAKuE,CAAC,IAAI,EAAE,CAAC;MACxB,IAAI,CAACC,cAAc,GAAGD,CAAC;MACvB,IAAIE,CAAC,GAAGzF,CAAC,CAAC6B,OAAO,GAAGnC,CAAC,CAACgG,YAAY,CAAC,IAAI,CAAC7D,OAAO,EAAE7B,CAAC,CAAC2F,SAAS,CAAC;QAC3DC,CAAC,GAAG5F,CAAC,CAAC4B,OAAO,GAAGlC,CAAC,CAACgG,YAAY,CAAC,IAAI,CAAC9D,OAAO,EAAE5B,CAAC,CAAC6F,UAAU,CAAC;MAC5D,IAAI,CAACtC,WAAW,GAAGkC,CAAC;MACpB,IAAI,CAACjC,WAAW,GAAGoC,CAAC;MACpB,IAAIE,CAAC,GAAG;UACJC,IAAI,EAAE,IAAI,CAAC1E,SAAS;UACpB,cAAc,EAAE,IAAI,CAACC,SAAS;UAC9B,cAAc,EAAE,CAAC;UACjB,gBAAgB,EAAE;QACpB,CAAC;QACD0E,CAAC;QACDC,CAAC;MACH,IAAI,CAAC1E,UAAU,GAAI0E,CAAC,GAAGD,CAAC,GAAGT,CAAC,IAAMS,CAAC,GAAGT,CAAC,GAAGX,CAAC,EAAIqB,CAAC,GAAGD,CAAC,GAAGX,CAAE,CAAC;MAC1D,IAAI,CAACa,eAAe,GAAGD,CAAC;MACxB,IAAI,CAACE,SAAS,CAAC,CAAC;MAChB,IAAIC,CAAC,GAAG,IAAI,CAAChF,aAAa,GAAG,CAAC;QAC5BuD,CAAC,GAAGjF,CAAC,CAAC2G,KAAK,CAACrB,CAAC,EAAES,CAAC,EAAEG,CAAC,EAAElB,CAAC,EAAEC,CAAC,GAAGD,CAAC,EAAEsB,CAAC,GAAGI,CAAC,EAAEJ,CAAC,GAAGI,CAAC,EAAEJ,CAAC,GAAGI,CAAC,EAAE,CAAC,EAAEN,CAAC,CAAC;MAC3DpG,CAAC,CAAC2D,KAAK,CAACrD,CAAC,EAAE2E,CAAC,CAAC0B,KAAK,EAAE,WAAW,CAAC;MAChC9D,CAAC,CAACoB,IAAI,CAACgB,CAAC,CAAC;MACTA,CAAC,GAAGjF,CAAC,CAAC4G,SAAS;MACf5G,CAAC,CAAC6G,QAAQ,KAAK5B,CAAC,GAAG6B,IAAI,CAACC,KAAK,CAAC;MAC9BX,CAAC,GAAGpG,CAAC,CAACgH,WAAW,CAAC9D,CAAC,CAAC;MACpBoD,CAAC,GAAGtG,CAAC,CAACgH,WAAW,CAAC3D,CAAC,CAAC;MACpBA,CAAC,GAAGrD,CAAC,CAACgH,WAAW,CAACrC,CAAC,CAAC;MACpBtB,CAAC,GAAGyD,IAAI,CAACG,GAAG,CAAC5D,CAAC,EAAE+C,CAAC,EAAEE,CAAC,CAAC;MACrB3B,CAAC,GAAG3E,CAAC,CAACkH,OAAO,CAACvC,CAAC,EAAEtB,CAAC,GAAG,CAAC,CAAC;MACvB,KAAK+C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,CAAC,EAAEiB,CAAC,EAAE,EAAE;QACtBM,CAAC,GAAG1G,CAAC,CAACkH,OAAO,CAAChE,CAAC,GAAGkD,CAAC,GAAGzB,CAAC,EAAEtB,CAAC,CAAC;QAC3BiD,CAAC,GAAGtB,CAAC,GAAGoB,CAAC,GAAGhB,CAAC;QACb,IAAI+B,CAAC,GAAGlC,CAAC,CAACc,CAAC,GAAGF,CAAC,GAAGiB,IAAI,CAACM,GAAG,CAAEd,CAAC,GAAG,GAAG,GAAIQ,IAAI,CAACO,EAAE,CAAC,CAAC;UAC9CC,CAAC,GAAGrC,CAAC,CAACiB,CAAC,GAAGL,CAAC,GAAGiB,IAAI,CAACS,GAAG,CAAEjB,CAAC,GAAG,GAAG,GAAIQ,IAAI,CAACO,EAAE,CAAC,CAAC;UAC5CG,CAAC,GAAGvC,CAAC,CAACc,CAAC,GAAG,CAACF,CAAC,GAAGX,CAAC,IAAI4B,IAAI,CAACM,GAAG,CAAEd,CAAC,GAAG,GAAG,GAAIQ,IAAI,CAACO,EAAE,CAAC,CAAC;UAClDI,CAAC,GAAGxC,CAAC,CAACiB,CAAC,GAAG,CAACL,CAAC,GAAGX,CAAC,IAAI4B,IAAI,CAACS,GAAG,CAAEjB,CAAC,GAAG,GAAG,GAAIQ,IAAI,CAACO,EAAE,CAAC,CAAC;UAClDF,CAAC,GAAGnH,CAAC,CAAC0H,IAAI,CAACpC,CAAC,EAAE,CAAC6B,CAAC,EAAEK,CAAC,CAAC,EAAE,CAACF,CAAC,EAAEG,CAAC,CAAC,EAAElC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACvDzF,CAAC,CAAC2D,KAAK,CAACrD,CAAC,EAAE6G,CAAC,EAAE,WAAW,CAAC;QAC1BtE,CAAC,CAACoB,IAAI,CAACkD,CAAC,CAAC;QACTA,CAAC,GAAG,CAAC,CAAC;QACNK,CAAC,GAAG,IAAI,CAACjG,WAAW;QACpB,IAAI,CAACD,MAAM,KAAMkG,CAAC,GAAG,CAACA,CAAC,GAAGtC,CAAC,EAAIiC,CAAC,GAAG,CAAE,CAAC;QACtC,IAAIG,CAAC,GAAGvB,CAAC,GAAG,CAACF,CAAC,GAAGX,CAAC,GAAGsC,CAAC,IAAIV,IAAI,CAACM,GAAG,CAAEd,CAAC,GAAG,GAAG,GAAIQ,IAAI,CAACO,EAAE,CAAC;UACrDG,CAAC,GAAGtB,CAAC,GAAG,CAACL,CAAC,GAAGX,CAAC,GAAGsC,CAAC,IAAIV,IAAI,CAACS,GAAG,CAAEjB,CAAC,GAAG,GAAG,GAAIQ,IAAI,CAACO,EAAE,CAAC;UACnDM,CAAC,GAAG,IAAI,CAACvE,QAAQ;QACnByB,KAAK,CAAC8C,CAAC,CAAC,KAAKA,CAAC,GAAGrH,CAAC,CAAC8C,QAAQ,CAAC;QAC5B,IAAIqE,CAAC,GAAGX,IAAI,CAACM,GAAG,CAAE,CAACd,CAAC,GAAG,EAAE,IAAI,GAAG,GAAIQ,IAAI,CAACO,EAAE,CAAC;UAC1CO,CAAC,GAAGd,IAAI,CAACS,GAAG,CAAE,CAACjB,CAAC,GAAG,EAAE,IAAI,GAAG,GAAIQ,IAAI,CAACO,EAAE,CAAC;QAC1C,IACE,CAAC,GAAGzB,CAAC,IACL,IAAI,CAAClF,aAAa,IAClB0F,CAAC,GAAGR,CAAC,IAAIkB,IAAI,CAACC,KAAK,CAACX,CAAC,GAAGR,CAAC,CAAC,KACzB,IAAI,CAACpE,aAAa,IAAI4E,CAAC,IAAIjB,CAAC,GAAG,CAAC,CAAC,KACjC,IAAI,CAAC1D,cAAc,IAAI,CAAC,KAAK2E,CAAC,CAAC,EAChC;UACA,IAAIyB,CAAC;UACLA,CAAC,GAAG,IAAI,CAACC,WAAW,GAAG9H,CAAC,CAAC+H,SAAS,CAACrB,CAAC,EAAEpG,CAAC,CAAC0H,oBAAoB,EAAE1H,CAAC,CAAC2H,sBAAsB,EAAE3H,CAAC,CAAC4H,EAAE,EAAE,CAAC,CAAC,CAAC,GAAGlI,CAAC,CAACmI,YAAY,CAACzB,CAAC,EAAEpG,CAAC,CAAC4H,EAAE,EAAE7E,CAAC,CAAC;UAC9H,IAAI+E,CAAC,GAAG,IAAI,CAACC,IAAI;UACjBD,CAAC,KAAKP,CAAC,GAAG,MAAM,IAAI,IAAI,CAACS,YAAY,GAAGF,CAAC,GAAGP,CAAC,GAAGA,CAAC,GAAGO,CAAC,CAAC;UACtD,CAACA,CAAC,GAAG,IAAI,CAACG,aAAa,MAAMV,CAAC,GAAGO,CAAC,CAAC1B,CAAC,CAAC,CAAC;UACtCA,CAAC,GAAG,IAAI,CAACnD,KAAK;UACd,KAAK,CAAC,KAAKmD,CAAC,KAAKA,CAAC,GAAGpG,CAAC,CAACiD,KAAK,CAAC;UAC7BmD,CAAC,GAAG1G,CAAC,CAACwD,IAAI,CAAC8B,CAAC,EAAEuC,CAAC,EAAEnB,CAAC,EAAEpG,CAAC,CAACoD,UAAU,EAAEiE,CAAC,CAAC;UACpC3H,CAAC,CAAC2D,KAAK,CAACrD,CAAC,EAAEoG,CAAC,EAAE,YAAY,CAAC;UAC3BiB,CAAC,GAAGjB,CAAC,CAAC8B,OAAO,CAAC,CAAC;UACf9B,CAAC,CAAC9C,SAAS,CAAC0D,CAAC,GAAKH,CAAC,GAAGQ,CAAC,CAACc,KAAK,GAAI,CAAC,GAAIb,CAAC,EAAEJ,CAAC,GAAKL,CAAC,GAAGQ,CAAC,CAACe,MAAM,GAAI,CAAC,GAAIjB,CAAC,CAAC;UACtE5E,CAAC,CAACoB,IAAI,CAACyC,CAAC,CAAC;QACX;QACA,IAAIN,CAAC,GAAGjB,CAAC,GAAG,CAAC,EACX,KAAKuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,CAAC,EAAE4B,CAAC,EAAE,EACnBe,CAAC,GAAGnB,CAAC,GAAGZ,CAAC,GAAGgB,CAAC,EACXS,CAAC,GAAGlC,CAAC,CAACc,CAAC,GAAGQ,CAAC,GAAGO,IAAI,CAACM,GAAG,CAAEK,CAAC,GAAG,GAAG,GAAIX,IAAI,CAACO,EAAE,CAAC,CAAC,EAC5CC,CAAC,GAAGrC,CAAC,CAACiB,CAAC,GAAGK,CAAC,GAAGO,IAAI,CAACS,GAAG,CAAEE,CAAC,GAAG,GAAG,GAAIX,IAAI,CAACO,EAAE,CAAC,CAAC,EAC5CG,CAAC,GAAGvC,CAAC,CAACc,CAAC,GAAG,CAACQ,CAAC,GAAGZ,CAAC,IAAImB,IAAI,CAACM,GAAG,CAAEK,CAAC,GAAG,GAAG,GAAIX,IAAI,CAACO,EAAE,CAAC,CAAC,EAClDI,CAAC,GAAGxC,CAAC,CAACiB,CAAC,GAAG,CAACK,CAAC,GAAGZ,CAAC,IAAImB,IAAI,CAACS,GAAG,CAAEE,CAAC,GAAG,GAAG,GAAIX,IAAI,CAACO,EAAE,CAAC,CAAC,EAClDF,CAAC,GAAGnH,CAAC,CAAC0H,IAAI,CAACpC,CAAC,EAAE,CAAC6B,CAAC,EAAEK,CAAC,CAAC,EAAE,CAACF,CAAC,EAAEG,CAAC,CAAC,EAAElC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACtDzF,CAAC,CAAC2D,KAAK,CAACrD,CAAC,EAAE6G,CAAC,EAAE,iBAAiB,CAAC,EAChCtE,CAAC,CAACoB,IAAI,CAACkD,CAAC,CAAC;MACjB;MACA,IAAI,CAACpE,WAAW,GAAG,CAAC,CAAC;MACrB,IAAI,CAACJ,UAAU,CAAC,IAAI,CAACC,OAAO,CAAC;MAC7B,IAAI,CAACsB,aAAa,CAAC,IAAI,CAACC,UAAU,CAAC;MACnC7D,CAAC,GAAGA,CAAC,CAACmE,SAAS,CAAC+D,OAAO,CAAC,CAAC;MACzB,IAAI,CAACC,KAAK,GAAGnI,CAAC,CAACmI,KAAK;MACpB,IAAI,CAACC,MAAM,GAAGpI,CAAC,CAACoI,MAAM;IACxB,CAAC;IACDjC,SAAS,EAAE,SAAAA,CAAA,EAAY;MACrB,IAAInG,CAAC,GAAG,IAAI,CAACqI,KAAK;MAClB,IAAIrI,CAAC,EACH,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvC,CAAC,CAACsI,MAAM,EAAE/F,CAAC,EAAE,EAAE;QACjC,IAAIK,CAAC,GAAG5C,CAAC,CAACuC,CAAC,CAAC;QACZK,CAAC,KAAMA,CAAC,CAAC2F,IAAI,GAAG,IAAI,EAAG7I,CAAC,CAAC8I,aAAa,CAAC5F,CAAC,EAAElD,CAAC,CAAC+I,SAAS,EAAE,IAAI,CAACC,KAAK,CAAC,EAAE9F,CAAC,CAACqB,IAAI,CAACrB,CAAC,CAACrC,UAAU,EAAEqC,CAAC,CAACpC,QAAQ,CAAC,CAAC;MACvG;IACJ,CAAC;IACDmI,SAAS,EAAE,SAAAA,CAAU3I,CAAC,EAAEuC,CAAC,EAAEK,CAAC,EAAE;MAC5B,IAAI,CAACgG,IAAI,CAAC;QAAEC,IAAI,EAAE7I,CAAC;QAAE8I,QAAQ,EAAEvG,CAAC;QAAEC,KAAK,EAAE,IAAI;QAAEuG,KAAK,EAAEnG;MAAE,CAAC,CAAC;IAC5D,CAAC;IACDoG,iBAAiB,EAAE,SAAAA,CAAUhJ,CAAC,EAAEuC,CAAC,EAAE;MACjC,IAAIK,CAAC,GAAG,IAAI;QACVG,CAAC,GAAGH,CAAC,CAACJ,KAAK;MACbxC,CAAC,CAACiJ,SAAS,CAAC,UAAUjJ,CAAC,EAAE;QACvB+C,CAAC,CAACmG,WAAW,CAAC3G,CAAC,CAAC4G,WAAW,EAAE5G,CAAC,CAACU,KAAK,EAAE,CAAC,CAAC,CAAC;QACzCL,CAAC,CAAC+F,SAAS,CAAC,cAAc,EAAEpG,CAAC,EAAEvC,CAAC,CAAC;MACnC,CAAC,CAAC,CACCoJ,QAAQ,CAAC,UAAUpJ,CAAC,EAAE;QACrB+C,CAAC,CAACsG,WAAW,CAAC,CAAC;QACfzG,CAAC,CAAC+F,SAAS,CAAC,aAAa,EAAEpG,CAAC,EAAEvC,CAAC,CAAC;MAClC,CAAC,CAAC,CACDsJ,KAAK,CAAC,UAAUtJ,CAAC,EAAE;QAClB4C,CAAC,CAAC+F,SAAS,CAAC,WAAW,EAAEpG,CAAC,EAAEvC,CAAC,CAAC;QAC9BN,CAAC,CAAC6J,MAAM,CAAChH,CAAC,CAACiH,GAAG,EAAEzG,CAAC,CAAC0G,SAAS,CAAC;MAC9B,CAAC,CAAC,CACDC,QAAQ,CAAC,UAAU1J,CAAC,EAAE;QACrB4C,CAAC,CAAC+F,SAAS,CAAC,WAAW,EAAEpG,CAAC,EAAEvC,CAAC,CAAC;QAC9BN,CAAC,CAAC6J,MAAM,CAAChH,CAAC,CAACiH,GAAG,EAAEzG,CAAC,CAAC0G,SAAS,CAAC;MAC9B,CAAC,CAAC;IACN;EACF,CAAC,CAAC;AACJ,CAAC,EAAE,CAAC;AACJ,CAAC,YAAY;EACX,IAAI/J,CAAC,GAAGC,MAAM,CAACC,QAAQ;EACvBF,CAAC,CAACiK,UAAU,GAAGjK,CAAC,CAACI,KAAK,CAAC;IACrBC,SAAS,EAAE,SAAAA,CAAUC,CAAC,EAAE;MACtB,IAAI,CAACC,KAAK,GAAG,YAAY;MACzB,IAAI,CAACgD,KAAK,GAAG,SAAS;MACtB,IAAI,CAAC2G,SAAS,GAAG,IAAI,CAACC,KAAK,GAAG,CAAC;MAC/B,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,UAAU,GAAG,CAAC;MACrC,IAAI,CAACC,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACC,WAAW,GAAG,CAAC;MACpB,IAAI,CAAC/J,MAAM,GAAG,KAAK;MACnB,IAAI,CAACgK,eAAe,GAAG,IAAI,CAACC,WAAW,GAAG,CAAC;MAC3C,IAAI,CAACC,mBAAmB,GAAG,CAAC;MAC5B,IAAI,CAACC,KAAK,GAAG,CAAC;MACd3K,CAAC,CAACyC,UAAU,CAAC,IAAI,EAAEnC,CAAC,EAAE,YAAY,CAAC;IACrC,CAAC;IACDsK,QAAQ,EAAE,SAAAA,CAAUtK,CAAC,EAAE;MACrB,IAAIuC,CAAC,GAAG,IAAI,CAACC,KAAK;MAClBD,CAAC,GAAIA,CAAC,CAAC+H,QAAQ,GAAG/H,CAAC,CAAC+H,QAAQ,CAAC,IAAI,EAAEtK,CAAC,CAAC,GAAI,IAAI,CAACuK,aAAa,GAAG,IAAI,CAACC,KAAK,GAAGxK,CAAE,GAAK,IAAI,CAACuK,aAAa,GAAG,IAAI,CAACC,KAAK,GAAGxK,CAAE;IACxH;EACF,CAAC,CAAC;EACFN,CAAC,CAAC+I,SAAS,GAAG/I,CAAC,CAACI,KAAK,CAAC;IACpBC,SAAS,EAAE,SAAAA,CAAA,EAAY;MACrB,IAAI,CAACE,KAAK,GAAG,WAAW;MACxB,IAAI,CAACoK,KAAK,GAAG,CAAC;IAChB,CAAC;IACDpG,IAAI,EAAE,SAAAA,CAAUjE,CAAC,EAAEuC,CAAC,EAAE;MACpB,IAAIK,CAAC,GAAG,IAAI,CAAC2F,IAAI;MACjB,IAAI,CAACkC,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC9H,MAAM,CAAC,CAAC;MAC/C,IAAII,CAAC,GAAGH,CAAC,CAACJ,KAAK;QACb6B,CAAC,GAAGzB,CAAC,CAACvC,UAAU;QAChBmE,CAAC,GAAG5B,CAAC,CAAC4C,cAAc;QACpBd,CAAC,GAAG9B,CAAC,CAACmC,gBAAgB;QACtBJ,CAAC,GAAG5B,CAAC,CAACI,SAAS;QACfyB,CAAC,GAAGhC,CAAC,CAACjC,eAAe;QACrBkE,CAAC,GAAGnF,CAAC,CAACgG,YAAY,CAAC,IAAI,CAACxF,MAAM,EAAEsE,CAAC,CAAC;MACpCD,KAAK,CAACM,CAAC,CAAC,KAAKA,CAAC,GAAGjC,CAAC,CAACsD,eAAe,CAAC;MACnC1B,CAAC,GAAG9E,CAAC,CAACgG,YAAY,CAAC,IAAI,CAACyE,WAAW,EAAE3F,CAAC,CAAC;MACvCD,KAAK,CAACC,CAAC,CAAC,KAAKA,CAAC,GAAGK,CAAC,GAAGD,CAAC,CAAC;MACvB,IAAIP,CAAC,GAAGA,CAAC,GAAGK,CAAC,IAAI1E,CAAC,GAAG4C,CAAC,CAACrC,UAAU,CAAC;QAChCqE,CAAC,GAAGF,CAAC,IAAInC,CAAC,GAAGvC,CAAC,CAAC;QACf8E,CAAC,GAAG,IAAI,CAAC4F,YAAY;MACvB,KAAK,CAAC,KAAK5F,CAAC,KAAKA,CAAC,GAAGlC,CAAC,CAACZ,gBAAgB,CAAC;MACxC,IAAIgD,CAAC,GAAG,IAAI,CAAC2F,gBAAgB;MAC7BpG,KAAK,CAACS,CAAC,CAAC,KAAKA,CAAC,GAAGpC,CAAC,CAACb,oBAAoB,CAAC;MACxC,IAAIkD,CAAC,GAAG,IAAI,CAAC2F,YAAY;MACzBrG,KAAK,CAACU,CAAC,CAAC,KAAKA,CAAC,GAAGrC,CAAC,CAACd,gBAAgB,CAAC;MACpC4C,CAAC,GAAG,IAAI,CAACmF,KAAK;MACdtF,KAAK,CAACG,CAAC,CAAC,KAAKA,CAAC,GAAG9B,CAAC,CAACX,SAAS,CAAC;MAC7B6C,CAAC,GAAG;QACFiB,IAAI,EAAE,IAAI,CAAC9C,KAAK;QAChB4H,MAAM,EAAE/F,CAAC;QACT,cAAc,EAAEE,CAAC;QACjB,gBAAgB,EAAEC;MACpB,CAAC;MACD,IAAI,CAACuE,GAAG,KAAK1E,CAAC,CAACgG,MAAM,GAAG,SAAS,CAAC;MAClC9F,CAAC,GAAG,IAAI,CAAC+F,aAAa;MACtB/F,CAAC,KAAKA,CAAC,GAAGpC,CAAC,CAACoI,iBAAiB,CAAC;MAC9BrG,CAAC,GAAGjF,CAAC,CAAC2G,KAAK,CAAC1B,CAAC,EAAE/B,CAAC,CAACW,WAAW,EAAEX,CAAC,CAACY,WAAW,EAAEa,CAAC,EAAEO,CAAC,EAAEC,CAAC,EAAEA,CAAC,EAAEL,CAAC,EAAE,CAAC,EAAEM,CAAC,EAAEE,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,CAAC;MAC9FtF,CAAC,CAAC2D,KAAK,CAACN,CAAC,EAAE4B,CAAC,CAAC0B,KAAK,EAAE,WAAW,CAAC;MAChC,KAAK,CAAC,KAAK,IAAI,CAACnC,EAAE,IAAIxE,CAAC,CAAC2D,KAAK,CAACN,CAAC,EAAE4B,CAAC,CAAC0B,KAAK,EAAE,YAAY,GAAG,IAAI,CAACnC,EAAE,CAAC;MACjES,CAAC,CAACsG,OAAO,CAAC,SAAS,EAAEvG,CAAC,CAAC;MACvB9B,CAAC,CAACwB,OAAO,CAACT,IAAI,CAACgB,CAAC,CAAC;MACjB,IAAI,CAAC8F,YAAY,GAAG9F,CAAC;MACrB,IAAI,CAACuG,iBAAiB,GAAGlL,CAAC;MAC1B,IAAI,CAACmL,eAAe,GAAG5I,CAAC;MACxBK,CAAC,CAACoG,iBAAiB,CAACrE,CAAC,EAAE,IAAI,CAAC;IAC9B,CAAC;IACDyG,MAAM,EAAE,SAAAA,CAAA,EAAY;MAClB,IAAIpL,CAAC,GAAG,IAAI,CAACuI,IAAI;QACfhG,CAAC,GAAGvC,CAAC,CAACwC,KAAK;MACb,IAAIxC,CAAC,IAAIA,CAAC,CAACoC,WAAW,EAAE;QACtB,IAAI,IAAI,CAACiI,KAAK,IAAI9H,CAAC,CAAC8I,WAAW,EAAG9I,CAAC,GAAG,IAAI,CAAC/B,QAAQ,EAAIR,CAAC,GAAG,IAAI,CAACO,UAAW,CAAC,KACvE;UACH,IAAI,CAAC8J,KAAK,EAAE;UACZ,IAAIzH,CAAC,GAAGlD,CAAC,CAAC4L,SAAS,CAAC/I,CAAC,CAACgJ,WAAW,CAAC;YAChCvL,CAAC,GAAGN,CAAC,CAACkD,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyH,KAAK,EAAE,IAAI,CAACmB,kBAAkB,EAAE,IAAI,CAACjL,UAAU,GAAG,IAAI,CAACiL,kBAAkB,EAAEjJ,CAAC,CAAC8I,WAAW,CAAC;YAC1G9I,CAAC,GAAG7C,CAAC,CAACkD,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyH,KAAK,EAAE,IAAI,CAACoB,gBAAgB,EAAE,IAAI,CAACjL,QAAQ,GAAG,IAAI,CAACiL,gBAAgB,EAAElJ,CAAC,CAAC8I,WAAW,CAAC;UACtG9G,KAAK,CAACvE,CAAC,CAAC,KAAKA,CAAC,GAAG,IAAI,CAACO,UAAU,CAAC;UACjCgE,KAAK,CAAChC,CAAC,CAAC,KAAKA,CAAC,GAAG,IAAI,CAAC/B,QAAQ,CAAC;QACjC;QACCR,CAAC,IAAI,IAAI,CAACkL,iBAAiB,IAAI3I,CAAC,IAAI,IAAI,CAAC4I,eAAe,IAAK,IAAI,CAAClH,IAAI,CAACjE,CAAC,EAAEuC,CAAC,CAAC;MAC/E;IACF,CAAC;IACDmJ,aAAa,EAAE,SAAAA,CAAU1L,CAAC,EAAE;MAC1B,IAAI,CAACwL,kBAAkB,GAAG,IAAI,CAACjL,UAAU;MACzC,IAAI,CAACA,UAAU,GAAGP,CAAC;MACnB,IAAI,CAACqK,KAAK,GAAG,CAAC;IAChB,CAAC;IACDsB,WAAW,EAAE,SAAAA,CAAU3L,CAAC,EAAE;MACxB,IAAI,CAACyL,gBAAgB,GAAG,IAAI,CAACjL,QAAQ;MACrC,IAAI,CAACA,QAAQ,GAAGR,CAAC;MACjB,IAAI,CAACqK,KAAK,GAAG,CAAC;IAChB;EACF,CAAC,CAAC;AACJ,CAAC,EAAE,CAAC;AACJ,CAAC,YAAY;EACX,IAAI3K,CAAC,GAAGC,MAAM,CAACC,QAAQ;EACvBF,CAAC,CAACkM,cAAc,GAAGlM,CAAC,CAACI,KAAK,CAAC;IACzB+L,QAAQ,EAAEnM,CAAC,CAACoM,OAAO;IACnB/L,SAAS,EAAE,SAAAA,CAAUC,CAAC,EAAE;MACtB,IAAI,CAACC,KAAK,GAAG,gBAAgB;MAC7BP,CAAC,CAACkM,cAAc,CAACG,IAAI,CAAChM,SAAS,CAACiM,IAAI,CAAC,IAAI,EAAEhM,CAAC,CAAC;MAC7C,IAAI,CAAC0I,KAAK,GAAG1I,CAAC;MACd,IAAI,CAAC6I,IAAI,GAAG,OAAO;MACnB,IAAI,CAACoD,SAAS,GAAG,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,YAAY,GAAG,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,UAAU,GAAG,EAAE;MAC7F,IAAI,CAACC,SAAS,GAAG,SAAS;MAC1B,IAAI,CAACC,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,eAAe,GAAG,CAAC;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACC,eAAe,GAAG,CAAC;MACxB,IAAI,CAACC,MAAM,GAAG,EAAE;MAChB,IAAI,CAACC,IAAI,GAAG,EAAE;MACd,IAAI,CAACC,aAAa,GAAG,CAAC;MACtB,IAAI,CAACtB,WAAW,GAAG,aAAa;MAChC,IAAI,CAACuB,UAAU,GAAG,CAAC,CAAC;MACpB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,UAAU,GAAG,CAAC;MACtCtN,CAAC,CAACyC,UAAU,CAAC,IAAI,EAAEnC,CAAC,EAAE,IAAI,CAACC,KAAK,CAAC;IACnC,CAAC;IACDgN,OAAO,EAAE,SAAAA,CAAUjN,CAAC,EAAE;MACpBA,CAAC,CAACwC,KAAK,GAAG,IAAI;MACd,IAAI,CAACoK,IAAI,CAACjJ,IAAI,CAAC3D,CAAC,CAAC;IACnB,CAAC;IACDkN,YAAY,EAAE,SAAAA,CAAUlN,CAAC,EAAEuC,CAAC,EAAE;MAC5B,OAAQvC,CAAC,GAAGN,CAAC,CAACyN,WAAW,CAACnN,CAAC,EAAEuC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,IAAI,CAACqF,EAAE,EAAE,EAAE,EAAE,IAAI,CAACJ,WAAW,EAAE,IAAI,CAACG,sBAAsB,EAAE,IAAI,CAACD,oBAAoB,CAAC;IACnI,CAAC;IACD0F,SAAS,EAAE,SAAAA,CAAA,EAAY;MACrB1N,CAAC,CAACkM,cAAc,CAACG,IAAI,CAACqB,SAAS,CAACpB,IAAI,CAAC,IAAI,CAAC;MAC1C,IAAIhM,CAAC;MACL,CAAC,KAAK,IAAI,CAAC4M,IAAI,CAACtE,MAAM,KAAMtI,CAAC,GAAG,IAAIN,CAAC,CAACG,SAAS,CAAC,IAAI,CAAC6I,KAAK,CAAC,EAAG,IAAI,CAACuE,OAAO,CAACjN,CAAC,CAAC,CAAC;MAC9E,IAAIuC,CAAC;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACqK,IAAI,CAACtE,MAAM,EAAE/F,CAAC,EAAE,EAClCvC,CAAC,GAAG,IAAI,CAAC4M,IAAI,CAACrK,CAAC,CAAC,EACdvC,CAAC,GAAGN,CAAC,CAAC8I,aAAa,CAACxI,CAAC,EAAEN,CAAC,CAACG,SAAS,EAAE,IAAI,CAAC6I,KAAK,CAAC,EAChD1I,CAAC,CAACkE,EAAE,KAAKlE,CAAC,CAACkE,EAAE,GAAG,UAAU,GAAG3B,CAAC,GAAG,GAAG,GAAG,IAAI8K,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAC3DtN,CAAC,CAACwC,KAAK,GAAG,IAAI,EACd,IAAI,CAACoK,IAAI,CAACrK,CAAC,CAAC,GAAGvC,CAAE;MACtB,IAAI4C,CAAC,GAAG,IAAI,CAAC+J,MAAM;MACnB,KAAKpK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAAC0F,MAAM,EAAE/F,CAAC,EAAE,EAAE;QAC7BvC,CAAC,GAAG4C,CAAC,CAACL,CAAC,CAAC;QACRvC,CAAC,GAAGN,CAAC,CAAC8I,aAAa,CAACxI,CAAC,EAAEN,CAAC,CAACiK,UAAU,EAAE,IAAI,CAACjB,KAAK,CAAC;QAChD1I,CAAC,CAACkE,EAAE,KAAKlE,CAAC,CAACkE,EAAE,GAAG,WAAW,GAAG3B,CAAC,GAAG,GAAG,GAAG,IAAI8K,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;QAC7DtN,CAAC,CAACwC,KAAK,GAAG,IAAI;QACdI,CAAC,CAACL,CAAC,CAAC,GAAGvC,CAAC;QACR,IAAI+C,CAAC,GAAG/C,CAAC,CAACuI,IAAI;QACd7I,CAAC,CAAC6N,QAAQ,CAACxK,CAAC,CAAC,KAAK/C,CAAC,CAACuI,IAAI,GAAG7I,CAAC,CAAC8N,UAAU,CAAC,IAAI,CAACZ,IAAI,EAAE7J,CAAC,CAAC,CAAC;QACtD/C,CAAC,CAACuI,IAAI,KAAKvI,CAAC,CAACuI,IAAI,GAAG,IAAI,CAACqE,IAAI,CAAC,CAAC,CAAC,CAAC;QACjCrI,KAAK,CAACvE,CAAC,CAACwK,KAAK,CAAC,IAAIxK,CAAC,CAACsK,QAAQ,CAACtK,CAAC,CAACuI,IAAI,CAAChI,UAAU,CAAC;QAC/CgE,KAAK,CAACvE,CAAC,CAACuK,aAAa,CAAC,KAAKvK,CAAC,CAACuK,aAAa,GAAGvK,CAAC,CAACuI,IAAI,CAAChI,UAAU,CAAC;MACjE;MACA,IAAI,CAACkN,aAAa,CAAC7K,CAAC,CAAC;MACrB,IAAI,CAAC8K,SAAS,CAAC,CAAC;MAChB,IAAI,CAACrC,WAAW,GAAG,IAAI,CAACwB,aAAa,GAAGnN,CAAC,CAACiO,UAAU;IACtD,CAAC;IACDD,SAAS,EAAE,SAAAA,CAAA,EAAY;MACrBhO,CAAC,CAACkM,cAAc,CAACG,IAAI,CAAC2B,SAAS,CAAC1B,IAAI,CAAC,IAAI,CAAC;MAC1C,IAAIhM,CAAC,GAAG,IAAI,CAACmD,SAAS;QACpBZ,CAAC,GAAG,IAAI,CAACqL,WAAW,CAAC,CAAC;MACxB,IAAI,CAACjI,SAAS,GAAGpD,CAAC;MAClB,IAAIK,CAAC,GAAG,IAAI,CAACiL,YAAY,CAAC,CAAC;MAC3B,IAAI,CAAChI,UAAU,GAAGjD,CAAC;MACnB,IAAIG,CAAC,GAAGrD,CAAC,CAACgG,YAAY;QACpBrB,CAAC,GAAGtB,CAAC,CAAC,IAAI,CAACsJ,UAAU,EAAE9J,CAAC,CAAC;QACzBiC,CAAC,GAAGzB,CAAC,CAAC,IAAI,CAACmJ,WAAW,EAAE3J,CAAC,CAAC;QAC1BmC,CAAC,GAAG3B,CAAC,CAAC,IAAI,CAACqJ,SAAS,EAAExJ,CAAC,CAAC,GAAG,IAAI,CAACkL,cAAc,CAAC,CAAC;QAChDnJ,CAAC,GAAG5B,CAAC,CAAC,IAAI,CAACoJ,YAAY,EAAEvJ,CAAC,CAAC;QAC3BgC,CAAC,GAAG7B,CAAC,CAAC,IAAI,CAAC7C,MAAM,EAAEqC,CAAC,EAAEK,CAAC,CAAC;QACxBG,CAAC,GAAGR,CAAC,GAAG8B,CAAC,GAAGG,CAAC;QACbK,CAAC,GAAGjC,CAAC,GAAG8B,CAAC,GAAGC,CAAC,GAAG,IAAI,CAACoI,WAAW;MAClCnI,CAAC,KAAKA,CAAC,GAAG4B,IAAI,CAACuH,GAAG,CAAChL,CAAC,EAAE8B,CAAC,CAAC,GAAG,CAAC,CAAC;MAC7BD,CAAC,GAAG,IAAI,CAACqH,SAAS,KAAKrH,CAAC,GAAG,IAAI,CAACqH,SAAS,CAAC;MAC1C,IAAI,CAACxI,UAAU,GAAGmB,CAAC;MACnB,IAAI,CAAC/C,OAAO,GAAG,CAACU,CAAC,GAAG8B,CAAC,GAAGG,CAAC,IAAI,CAAC,GAAGH,CAAC;MAClC,IAAI,CAACzC,OAAO,GAAG,CAACgB,CAAC,GAAG8B,CAAC,GAAGC,CAAC,IAAI,CAAC,GAAGD,CAAC,GAAG,IAAI,CAACqI,WAAW,GAAG,CAAC;MACzDxI,KAAK,CAAC,IAAI,CAACyJ,MAAM,CAAC,KAAK,IAAI,CAACnM,OAAO,GAAG,IAAI,CAACmM,MAAM,CAAC;MAClDzJ,KAAK,CAAC,IAAI,CAAC0J,MAAM,CAAC,KAAK,IAAI,CAACrM,OAAO,GAAG,IAAI,CAACqM,MAAM,CAAC;MAClD,IAAI1L,CAAC,GAAG,IAAI,CAACgK,SAAS;QACpB3J,CAAC,GAAG,IAAI,CAAC8J,eAAe;QACxB5H,CAAC;MACH,IAAI,CAAC,GAAGvC,CAAC,IAAI,CAAC,GAAGK,CAAC,EACfkC,CAAC,GAAGpF,CAAC,CAACwO,MAAM,CAAClO,CAAC,EAAE4E,CAAC,EAAE,IAAI,CAAC0H,SAAS,EAAE/J,CAAC,EAAE,IAAI,CAACiK,eAAe,EAAE,IAAI,CAACC,eAAe,EAAE7J,CAAC,EAAE,CAAC,CAAC,CAAC,EACvFkC,CAAC,CAACxB,SAAS,CAAC,IAAI,CAACzB,OAAO,EAAE,IAAI,CAACD,OAAO,CAAC,EACvCkD,CAAC,CAACqJ,MAAM,CAAC,CAAC,EACV,CAACnO,CAAC,GAAG,IAAI,CAACoO,WAAW,KAAKtJ,CAAC,CAACuJ,OAAO,CAACrO,CAAC,EAAEsO,GAAG,EAAE,IAAI,CAACC,IAAI,CAAC;MAC1D,KAAKhM,CAAC,GAAGqC,CAAC,GAAG5E,CAAC,GAAG,CAAC,EAAEuC,CAAC,GAAG,IAAI,CAACqK,IAAI,CAACtE,MAAM,EAAE/F,CAAC,EAAE,EAC1CK,CAAC,GAAG,IAAI,CAACgK,IAAI,CAACrK,CAAC,CAAC,EACd8B,CAAC,GAAGzB,CAAC,CAAC1C,MAAM,EACZ0C,CAAC,CAACa,UAAU,GAAG/D,CAAC,CAACgG,YAAY,CAACrB,CAAC,EAAE,IAAI,CAACZ,UAAU,CAAC,EAClDb,CAAC,CAACqB,IAAI,CAAC,CAAC,EACPO,CAAC,GAAG,CAAC,EACN,CAAC,CAAC,KAAKgK,MAAM,CAACnK,CAAC,CAAC,CAACoK,OAAO,CAAC,GAAG,CAAC,KAAKjK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAGkK,MAAM,CAACrK,CAAC,CAACsK,MAAM,CAAC,CAAC,EAAEtK,CAAC,CAACiE,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,EAC1F1F,CAAC,CAACuF,KAAK,GAAG3D,CAAC,GAAGxE,CAAC,KAAKA,CAAC,GAAG4C,CAAC,CAACuF,KAAK,GAAG3D,CAAC,CAAC,EACpC5B,CAAC,CAACwF,MAAM,GAAG5D,CAAC,GAAGI,CAAC,KAAKA,CAAC,GAAGhC,CAAC,CAACwF,MAAM,GAAG5D,CAAC,CAAC;MAC1C,CAACjC,CAAC,GAAG,IAAI,CAACqM,MAAM,KAAKrM,CAAC,CAACsM,cAAc,CAAC,CAAC;MACvC,IAAI,IAAI,CAAC/B,UAAU,IAAI,CAAC,IAAI,CAACgC,YAAY,EAAE;QACzChK,CAAC,KAAMA,CAAC,GAAGA,CAAC,CAACoD,OAAO,CAAC,CAAC,EAAGpD,CAAC,CAACqD,KAAK,GAAGnI,CAAC,KAAKA,CAAC,GAAG8E,CAAC,CAACqD,KAAK,CAAC,EAAErD,CAAC,CAACsD,MAAM,GAAGxD,CAAC,KAAKA,CAAC,GAAGE,CAAC,CAACsD,MAAM,CAAC,CAAC;QACtFtD,CAAC,GAAG,CAAC;QACL,IAAID,CAAC,GAAGD,CAAC,IAAI7B,CAAC,GAAG/C,CAAC,EAAE8E,CAAC,GAAG0B,IAAI,CAACuH,GAAG,CAAClJ,CAAC,GAAGD,CAAC,EAAE7B,CAAC,GAAG/C,CAAC,CAAC;QAC9C,CAAC,GAAG8E,CAAC,KAAM,IAAI,CAACiI,WAAW,GAAGjI,CAAC,EAAI,IAAI,CAACgK,YAAY,GAAG,CAAC,CAAC,EAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;MACjF;MACAhM,CAAC,GAAG,IAAI,CAAC4J,MAAM,CAACrE,MAAM;MACtB,KAAK/F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,CAAC,EAAER,CAAC,EAAE,EAAGsC,CAAC,GAAG,IAAI,CAAC8H,MAAM,CAACpK,CAAC,CAAC,EAAIsC,CAAC,CAACmK,UAAU,GAAGV,GAAI;MAClE,IAAI,CAACW,QAAQ,CAAC,CAAC;IACjB,CAAC;IACDC,YAAY,EAAE,SAAAA,CAAA,EAAY;MACxB,IAAI,CAACnC,WAAW,GAAG,IAAI,CAACC,UAAU,GAAG,CAAC;MACtC,IAAI,CAACmC,YAAY,GAAG,IAAI,CAACL,YAAY,GAAG,CAAC,CAAC;MAC1CpP,CAAC,CAACkM,cAAc,CAACG,IAAI,CAACmD,YAAY,CAAClD,IAAI,CAAC,IAAI,CAAC;IAC/C,CAAC;IACDoD,QAAQ,EAAE,SAAAA,CAAUpP,CAAC,EAAE;MACrB,IAAI,CAAC2M,MAAM,CAAChJ,IAAI,CAAC3D,CAAC,CAAC;IACrB,CAAC;IACDqP,WAAW,EAAE,SAAAA,CAAUrP,CAAC,EAAE;MACxBN,CAAC,CAAC4P,eAAe,CAAC,IAAI,CAAC3C,MAAM,EAAE3M,CAAC,CAAC;MACjC,IAAI,CAAC+O,WAAW,CAAC,CAAC;IACpB,CAAC;IACDQ,UAAU,EAAE,SAAAA,CAAUvP,CAAC,EAAE;MACvBN,CAAC,CAAC4P,eAAe,CAAC,IAAI,CAAC1C,IAAI,EAAE5M,CAAC,CAAC;MAC/B,IAAI,CAAC+O,WAAW,CAAC,CAAC;IACpB,CAAC;IACDS,SAAS,EAAE,SAAAA,CAAUxP,CAAC,EAAEuC,CAAC,EAAE;MACzBvC,CAAC,CAAC0D,GAAG,IAAI1D,CAAC,CAAC0D,GAAG,CAACf,MAAM,CAAC,CAAC;MACvB,IAAIC,CAAC,GAAG,IAAI,CAACO,SAAS;MACtBnD,CAAC,CAAC0D,GAAG,GAAGd,CAAC,CAACc,GAAG,CAAC,CAAC;MACfhE,CAAC,CAAC2D,KAAK,CAAC,IAAI,EAAErD,CAAC,CAAC0D,GAAG,EAAE,aAAa,CAAC;MACnChE,CAAC,CAAC2D,KAAK,CAAC,IAAI,EAAErD,CAAC,CAAC0D,GAAG,EAAE,cAAc,GAAG1D,CAAC,CAACkE,EAAE,CAAC;MAC3C,IAAInB,CAAC,GAAG/C,CAAC,CAACuI,IAAI;QACZlE,CAAC,GAAGtB,CAAC,CAACU,UAAU;QAChBe,CAAC,GAAGzB,CAAC,CAACQ,WAAW;QACjBmB,CAAC,GAAG3B,CAAC,CAACS,WAAW;QACjBmB,CAAC,GAAG3E,CAAC,CAAC8J,UAAU;QAChBlF,CAAC,GAAG5E,CAAC,CAACgK,QAAQ;QACdnF,CAAC,GAAGnF,CAAC,CAACgG,YAAY,CAAC1F,CAAC,CAACmK,WAAW,EAAEpH,CAAC,CAACU,UAAU,CAAC;QAC/CqB,CAAC,GAAGpF,CAAC,CAACgG,YAAY,CAAC1F,CAAC,CAACE,MAAM,EAAE6C,CAAC,CAACU,UAAU,CAAC;MAC5CV,CAAC,CAAC/B,MAAM,KAAK8D,CAAC,IAAI,EAAE,CAAC;MACrB,IAAIE,CAAC,GAAGhF,CAAC,CAACyP,SAAS;MACnBzK,CAAC,KAAKA,CAAC,GAAGhF,CAAC,CAACiD,KAAK,CAAC;MAClB,IAAIgC,CAAC,GAAGjF,CAAC,CAACyP,SAAS;MACnBxK,CAAC,KAAKA,CAAC,GAAGjF,CAAC,CAACiD,KAAK,CAAC;MAClB,CAAC,GAAGjD,CAAC,CAAC+J,UAAU,KACZ/E,CAAC,GAAGtF,CAAC,CAACwO,MAAM,CAACtL,CAAC,EAAE5C,CAAC,CAAC+J,UAAU,EAAE/E,CAAC,EAAEhF,CAAC,CAAC4J,SAAS,EAAE5J,CAAC,CAACoK,mBAAmB,EAAEpF,CAAC,EAAEhF,CAAC,CAACkK,eAAe,CAAC,EAC5FxK,CAAC,CAAC2D,KAAK,CAAC,IAAI,EAAE2B,CAAC,EAAE,kBAAkB,CAAC,EACpChF,CAAC,CAAC0D,GAAG,CAACC,IAAI,CAACqB,CAAC,CAAC,EACbA,CAAC,CAAC1B,SAAS,CAACkB,CAAC,EAAEE,CAAC,CAAC,CAAC;MACpBH,KAAK,CAACO,CAAC,CAAC,KAAKA,CAAC,GAAGT,CAAC,GAAGtB,CAAC,CAACrC,UAAU,CAAC;MAClC,IAAIqC,CAAC,GAAGyD,IAAI,CAACM,GAAG,CAAEvE,CAAC,GAAG,GAAG,GAAIiE,IAAI,CAACO,EAAE,CAAC;QACnC1C,CAAC,GAAGmC,IAAI,CAACS,GAAG,CAAE1E,CAAC,GAAG,GAAG,GAAIiE,IAAI,CAACO,EAAE,CAAC;QACjC/B,CAAC,GAAGwB,IAAI,CAACM,GAAG,CAAE,CAACvE,CAAC,GAAG,EAAE,IAAI,GAAG,GAAIiE,IAAI,CAACO,EAAE,CAAC;QACxC7B,CAAC,GAAGsB,IAAI,CAACS,GAAG,CAAE,CAAC1E,CAAC,GAAG,EAAE,IAAI,GAAG,GAAIiE,IAAI,CAACO,EAAE,CAAC;QACxCnE,CAAC,GAAGlD,CAAC,CAACgQ,OAAO,CACX9M,CAAC,EACD,CAAC4B,CAAC,GAAIG,CAAC,GAAG,CAAC,GAAIK,CAAC,GAAGH,CAAC,GAAG9B,CAAC,EAAEyB,CAAC,GAAGM,CAAC,GAAG/B,CAAC,GAAI6B,CAAC,GAAG,CAAC,GAAII,CAAC,EAAER,CAAC,GAAGM,CAAC,GAAG/B,CAAC,GAAI6B,CAAC,GAAG,CAAC,GAAII,CAAC,EAAER,CAAC,GAAIG,CAAC,GAAG,CAAC,GAAIK,CAAC,GAAGH,CAAC,GAAG9B,CAAC,CAAC,EACpG,CAAC2B,CAAC,GAAIC,CAAC,GAAG,CAAC,GAAIO,CAAC,GAAGL,CAAC,GAAGR,CAAC,EAAEK,CAAC,GAAGI,CAAC,GAAGT,CAAC,GAAIO,CAAC,GAAG,CAAC,GAAIM,CAAC,EAAER,CAAC,GAAGI,CAAC,GAAGT,CAAC,GAAIO,CAAC,GAAG,CAAC,GAAIM,CAAC,EAAER,CAAC,GAAIC,CAAC,GAAG,CAAC,GAAIO,CAAC,GAAGL,CAAC,GAAGR,CAAC,CAAC,EACpGrE,CAAC,CAACiD,KAAK,EACPjD,CAAC,CAAC6J,KAAK,EACP,CAAC,EACD5E,CAAC,EACDjF,CAAC,CAACiK,WAAW,EACb,KAAK,CAAC,EACN,CAAC,CACH,CAAC;MACHvK,CAAC,CAAC2D,KAAK,CAAC,IAAI,EAAET,CAAC,EAAE,aAAa,CAAC;MAC/B5C,CAAC,CAAC0D,GAAG,CAACC,IAAI,CAACf,CAAC,CAAC;MACb,IAAI,CAACuB,SAAS,CAACR,IAAI,CAAC3D,CAAC,CAAC0D,GAAG,CAAC;MAC1B1D,CAAC,CAAC2P,MAAM,IAAI,IAAI,CAACC,SAAS,CAAC5P,CAAC,CAAC;IAC/B,CAAC;IACDsK,QAAQ,EAAE,SAAAA,CAAUtK,CAAC,EAAEuC,CAAC,EAAE;MACxBvC,CAAC,CAACuI,IAAI,IAAIvI,CAAC,CAACuI,IAAI,CAACnG,WAAW,KAAMpC,CAAC,CAACqK,KAAK,GAAG,CAAC,EAAIrK,CAAC,CAACuK,aAAa,GAAGvK,CAAC,CAACwK,KAAM,CAAC;MAC5ExK,CAAC,CAACwK,KAAK,GAAGjI,CAAC;MACX,IAAIK,CAAC,GAAG,IAAI,CAACgM,MAAM;MACnBhM,CAAC,IAAIA,CAAC,CAACiN,YAAY,CAAC,CAAC;MACrB,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,cAAc,CAAC,IAAI,CAACD,UAAU,EAAExN,CAAC,CAAC;IAC/E,CAAC;IACD0N,iBAAiB,EAAE,SAAAA,CAAUjQ,CAAC,EAAE;MAC9B,IAAIuC,CAAC,GAAGvC,CAAC,CAAC6I,IAAI;MACd7I,CAAC,GAAGA,CAAC,CAAC8I,QAAQ;MACd,IAAI,CAAC,IAAI,CAAC8F,MAAM,CAACsB,IAAI,IAAIlQ,CAAC,EACxB,QAAQuC,CAAC;QACP,KAAK,UAAU;UACb,IAAI,CAACqN,SAAS,CAAC5P,CAAC,CAAC;UACjB;QACF,KAAK,UAAU;UACb,IAAI,CAACmQ,SAAS,CAACnQ,CAAC,CAAC;MACrB;IACJ,CAAC;IACD4P,SAAS,EAAE,SAAAA,CAAU5P,CAAC,EAAE;MACtBA,CAAC,CAAC0D,GAAG,CAAC0M,IAAI,CAAC,CAAC;MACZpQ,CAAC,CAAC2P,MAAM,GAAG,CAAC,CAAC;MACb,IAAI,CAACf,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,cAAc,CAAC,CAAC;IAC7C,CAAC;IACDsB,SAAS,EAAE,SAAAA,CAAUnQ,CAAC,EAAE;MACtBA,CAAC,CAAC0D,GAAG,CAAC2M,IAAI,CAAC,CAAC;MACZrQ,CAAC,CAAC2P,MAAM,GAAG,CAAC,CAAC;MACb,IAAI,CAACf,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,cAAc,CAAC,CAAC;IAC7C,CAAC;IACDyB,gBAAgB,EAAE,SAAAA,CAAA,EAAY;MAC5B5Q,CAAC,CAACkM,cAAc,CAACG,IAAI,CAACuE,gBAAgB,CAACtE,IAAI,CAAC,IAAI,CAAC;MACjD,KAAK,IAAIhM,CAAC,GAAG,IAAI,CAAC2M,MAAM,CAACrE,MAAM,EAAE/F,CAAC,EAAEK,CAAC,EAAEG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/C,CAAC,EAAE+C,CAAC,EAAE,EACrDR,CAAC,GAAG,IAAI,CAACoK,MAAM,CAAC5J,CAAC,CAAC,EACjBR,CAAC,CAACgG,IAAI,IACJhG,CAAC,CAACgG,IAAI,CAACnG,WAAW,KACjBG,CAAC,CAAC8H,KAAK,IAAI,IAAI,CAACgB,WAAW,GACvBzI,CAAC,GAAGL,CAAC,CAACiI,KAAK,IACXjI,CAAC,CAAC8H,KAAK,EAAE,EACV9H,CAAC,CAACgO,aAAa,IAAIhO,CAAC,CAACiI,KAAK,GAAGjI,CAAC,CAACgI,aAAa,KAAM3H,CAAC,GAAGL,CAAC,CAACgG,IAAI,EAAIhG,CAAC,CAACgI,aAAa,IAAI3H,CAAC,CAACpC,QAAQ,GAAGoC,CAAC,CAACrC,UAAW,CAAC,EAC7GqC,CAAC,GAAGlD,CAAC,CAAC4L,SAAS,CAAC,IAAI,CAACC,WAAW,CAAC,EACjC3I,CAAC,GAAGlD,CAAC,CAACkD,CAAC,CAAC,CAAC,CAAC,EAAEL,CAAC,CAAC8H,KAAK,EAAE9H,CAAC,CAACgI,aAAa,EAAEhI,CAAC,CAACiI,KAAK,GAAGjI,CAAC,CAACgI,aAAa,EAAE,IAAI,CAACc,WAAW,CAAC,EACnF9G,KAAK,CAAC3B,CAAC,CAAC,KAAKA,CAAC,GAAGL,CAAC,CAACiI,KAAK,CAAC,CAAC,EAC7B5H,CAAC,GAAGL,CAAC,CAACgG,IAAI,CAACnG,WAAW,CAACQ,CAAC,CAAC,EAC1BL,CAAC,CAACyM,UAAU,IAAIpM,CAAC,KAAK,IAAI,CAAC4M,SAAS,CAACjN,CAAC,EAAEK,CAAC,CAAC,EAAGL,CAAC,CAACyM,UAAU,GAAGpM,CAAE,CAAC,CAAC;MACtE5C,CAAC,GAAG,IAAI,CAAC4M,IAAI;MACb,KAAKrK,CAAC,GAAGvC,CAAC,CAACsI,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI/F,CAAC,EAAEA,CAAC,EAAE,EAChC,IAAMK,CAAC,GAAG5C,CAAC,CAACuC,CAAC,CAAC,EAAGK,CAAC,CAACyF,KAAK,EACtB,KAAKtF,CAAC,GAAGH,CAAC,CAACyF,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAIvF,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxC,IAAIsB,CAAC,GAAGzB,CAAC,CAACyF,KAAK,CAACtF,CAAC,CAAC;QAClBsB,CAAC,CAAC+G,MAAM,IAAI/G,CAAC,CAAC+G,MAAM,CAAC,CAAC;MACxB;IACN;EACF,CAAC,CAAC;AACJ,CAAC,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}