{"ast": null, "code": "import { inject, input } from '@angular/core';\nimport { Spinkit } from './spinkits';\nimport { Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nfunction SpinnerComponent_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"colored\", !ctx_r0.backgroundColor());\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r0.backgroundColor());\n  }\n}\nfunction SpinnerComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n    i0.ɵɵconditionalCreate(2, SpinnerComponent_Conditional_0_Conditional_2_Template, 2, 4, \"div\", 2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r0.spinner() === ctx_r0.Spinkit.skLine ? 2 : -1);\n  }\n}\nexport class SpinnerComponent {\n  constructor() {\n    this.router = inject(Router);\n    this.isSpinnerVisible = true;\n    this.Spinkit = Spinkit;\n    this.backgroundColor = input('#1dc4e9');\n    this.spinner = input(Spinkit.skLine);\n    this.router.events.subscribe(event => {\n      if (event instanceof NavigationStart) {\n        this.isSpinnerVisible = true;\n      } else if (event instanceof NavigationEnd || event instanceof NavigationCancel || event instanceof NavigationError) {\n        this.isSpinnerVisible = false;\n      }\n    }, () => {\n      this.isSpinnerVisible = false;\n    });\n  }\n  ngOnDestroy() {\n    this.isSpinnerVisible = false;\n  }\n  static {\n    this.ɵfac = function SpinnerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpinnerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpinnerComponent,\n      selectors: [[\"app-spinner\"]],\n      inputs: {\n        backgroundColor: [1, \"backgroundColor\"],\n        spinner: [1, \"spinner\"]\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"id\", \"http-loader\"], [1, \"loader-bg\"], [1, \"sk-line-material\", 3, \"colored\"], [1, \"sk-line-material\"], [1, \"sk-child\", \"sk-bounce1\"]],\n      template: function SpinnerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵconditionalCreate(0, SpinnerComponent_Conditional_0_Template, 3, 1, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.isSpinnerVisible ? 0 : -1);\n        }\n      },\n      styles: [\"#http-loader {\\n  top: 0;\\n  left: 0;\\n  height: 100%;\\n  width: 100%;\\n  position: fixed;\\n  z-index: 9999;\\n}\\n\\n.loader-bg {\\n  height: 100%;\\n  width: 100%;\\n  position: absolute;\\n  filter: alpha(opacity=70);\\n  opacity: 0.7;\\n  background-color: #f1f1f1;\\n}\\n\\n.colored-parent,\\n.colored > div {\\n  background-color: #333;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdGhlbWUvc2hhcmVkL2NvbXBvbmVudHMvc3Bpbm5lci9zcGlubmVyLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsTUFBQTtFQUNBLE9BQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtFQUNBLGVBQUE7RUFDQSxhQUFBO0FBQ0Y7O0FBRUE7RUFDRSxZQUFBO0VBQ0EsV0FBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSxZQUFBO0VBQ0EseUJBQUE7QUFDRjs7QUFFQTs7RUFFRSxzQkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiI2h0dHAtbG9hZGVyIHtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogMDtcclxuICBoZWlnaHQ6IDEwMCU7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgcG9zaXRpb246IGZpeGVkO1xyXG4gIHotaW5kZXg6IDk5OTk7XHJcbn1cclxuXHJcbi5sb2FkZXItYmcge1xyXG4gIGhlaWdodDogMTAwJTtcclxuICB3aWR0aDogMTAwJTtcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgZmlsdGVyOiBhbHBoYShvcGFjaXR5PTcwKTtcclxuICBvcGFjaXR5OiAwLjc7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2YxZjFmMTtcclxufVxyXG5cclxuLmNvbG9yZWQtcGFyZW50LFxyXG4uY29sb3JlZCA+IGRpdiB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzMzMztcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\", \".sk-line-material {\\n  top: 0;\\n  position: relative;\\n  margin: auto;\\n  width: 100%;\\n}\\n\\n.sk-line-material .sk-child {\\n  width: 100%;\\n  height: 4px;\\n  position: absolute;\\n  top: 0;\\n  display: inline-block;\\n  transform-origin: 0% 0%;\\n  animation: sk-line-material 2s ease-in-out 0s infinite both;\\n}\\n@keyframes sk-line-material {\\n  0% {\\n    transform: scaleX(0);\\n  }\\n  100% {\\n    transform: scaleX(1);\\n  }\\n}\\n#http-loader {\\n  top: 0;\\n  left: 0;\\n  height: 100%;\\n  width: 100%;\\n  position: fixed;\\n  z-index: 9999;\\n}\\n\\n.loader-bg {\\n  height: 100%;\\n  width: 100%;\\n  position: absolute;\\n  filter: alpha(opacity=70);\\n  opacity: 1;\\n  background-color: rgba(0, 0, 0, 0);\\n}\\n\\n.colored-parent,\\n.colored > div {\\n  background-color: rgba(26, 188, 156, 0.8);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["inject", "input", "Spinkit", "Router", "NavigationStart", "NavigationEnd", "NavigationCancel", "NavigationError", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵclassProp", "ctx_r0", "backgroundColor", "ɵɵadvance", "ɵɵstyleProp", "ɵɵconditionalCreate", "SpinnerComponent_Conditional_0_Conditional_2_Template", "ɵɵconditional", "spinner", "skLine", "SpinnerComponent", "constructor", "router", "isSpinnerVisible", "events", "subscribe", "event", "ngOnDestroy", "selectors", "inputs", "decls", "vars", "consts", "template", "SpinnerComponent_Template", "rf", "ctx", "SpinnerComponent_Conditional_0_Template"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\shared\\components\\spinner\\spinner.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\shared\\components\\spinner\\spinner.component.html"], "sourcesContent": ["import { Component, OnDestroy, ViewEncapsulation, inject, input } from '@angular/core';\r\nimport { Spinkit } from './spinkits';\r\nimport { Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-spinner',\r\n  templateUrl: './spinner.component.html',\r\n  styleUrls: ['./spinner.component.scss', './spinkit-css/sk-line-material.scss'],\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport class SpinnerComponent implements OnDestroy {\r\n  private router = inject(Router);\r\n\r\n  isSpinnerVisible = true;\r\n  Spinkit = Spinkit;\r\n  readonly backgroundColor = input('#1dc4e9');\r\n  readonly spinner = input(Spinkit.skLine);\r\n  constructor() {\r\n    this.router.events.subscribe(\r\n      (event) => {\r\n        if (event instanceof NavigationStart) {\r\n          this.isSpinnerVisible = true;\r\n        } else if (event instanceof NavigationEnd || event instanceof NavigationCancel || event instanceof NavigationError) {\r\n          this.isSpinnerVisible = false;\r\n        }\r\n      },\r\n      () => {\r\n        this.isSpinnerVisible = false;\r\n      }\r\n    );\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.isSpinnerVisible = false;\r\n  }\r\n}\r\n", "@if (isSpinnerVisible) {\r\n  <div id=\"http-loader\">\r\n    <div class=\"loader-bg\">\r\n      @if (spinner() === Spinkit.skLine) {\r\n        <div class=\"sk-line-material\" [class.colored]=\"!backgroundColor()\">\r\n          <div class=\"sk-child sk-bounce1\" [style.background-color]=\"backgroundColor()\"></div>\r\n        </div>\r\n      }\r\n    </div>\r\n  </div>\r\n}\r\n"], "mappings": "AAAA,SAAkDA,MAAM,EAAEC,KAAK,QAAQ,eAAe;AACtF,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,MAAM,EAAEC,eAAe,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,iBAAiB;;;;ICEnGC,EAAA,CAAAC,cAAA,aAAmE;IACjED,EAAA,CAAAE,SAAA,aAAoF;IACtFF,EAAA,CAAAG,YAAA,EAAM;;;;IAFwBH,EAAA,CAAAI,WAAA,aAAAC,MAAA,CAAAC,eAAA,GAAoC;IAC/BN,EAAA,CAAAO,SAAA,EAA4C;IAA5CP,EAAA,CAAAQ,WAAA,qBAAAH,MAAA,CAAAC,eAAA,GAA4C;;;;;IAHnFN,EADF,CAAAC,cAAA,aAAsB,aACG;IACrBD,EAAA,CAAAS,mBAAA,IAAAC,qDAAA,iBAAoC;IAMxCV,EADE,CAAAG,YAAA,EAAM,EACF;;;;IANFH,EAAA,CAAAO,SAAA,GAIC;IAJDP,EAAA,CAAAW,aAAA,CAAAN,MAAA,CAAAO,OAAA,OAAAP,MAAA,CAAAX,OAAA,CAAAmB,MAAA,UAIC;;;ADGP,OAAM,MAAOC,gBAAgB;EAO3BC,YAAA;IANQ,KAAAC,MAAM,GAAGxB,MAAM,CAACG,MAAM,CAAC;IAE/B,KAAAsB,gBAAgB,GAAG,IAAI;IACvB,KAAAvB,OAAO,GAAGA,OAAO;IACR,KAAAY,eAAe,GAAGb,KAAK,CAAC,SAAS,CAAC;IAClC,KAAAmB,OAAO,GAAGnB,KAAK,CAACC,OAAO,CAACmB,MAAM,CAAC;IAEtC,IAAI,CAACG,MAAM,CAACE,MAAM,CAACC,SAAS,CACzBC,KAAK,IAAI;MACR,IAAIA,KAAK,YAAYxB,eAAe,EAAE;QACpC,IAAI,CAACqB,gBAAgB,GAAG,IAAI;MAC9B,CAAC,MAAM,IAAIG,KAAK,YAAYvB,aAAa,IAAIuB,KAAK,YAAYtB,gBAAgB,IAAIsB,KAAK,YAAYrB,eAAe,EAAE;QAClH,IAAI,CAACkB,gBAAgB,GAAG,KAAK;MAC/B;IACF,CAAC,EACD,MAAK;MACH,IAAI,CAACA,gBAAgB,GAAG,KAAK;IAC/B,CAAC,CACF;EACH;EAEAI,WAAWA,CAAA;IACT,IAAI,CAACJ,gBAAgB,GAAG,KAAK;EAC/B;;;uCAxBWH,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAQ,SAAA;MAAAC,MAAA;QAAAjB,eAAA;QAAAM,OAAA;MAAA;MAAAY,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV7B7B,EAAA,CAAAS,mBAAA,IAAAsB,uCAAA,iBAAwB;;;UAAxB/B,EAAA,CAAAW,aAAA,CAAAmB,GAAA,CAAAb,gBAAA,UAUC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}