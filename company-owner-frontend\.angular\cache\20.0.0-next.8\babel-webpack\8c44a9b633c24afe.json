{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = () => [\"/auth/signin\"];\nexport default class AuthSignupComponent {\n  static {\n    this.ɵfac = function AuthSignupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthSignupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AuthSignupComponent,\n      selectors: [[\"app-auth-signup\"]],\n      decls: 38,\n      vars: 2,\n      consts: [[1, \"auth-wrapper\"], [1, \"auth-content\"], [1, \"auth-bg\"], [1, \"r\"], [1, \"r\", \"s\"], [1, \"card\"], [1, \"card-body\", \"text-center\"], [1, \"mb-4\"], [1, \"feather\", \"icon-user-plus\", \"auth-icon\"], [1, \"input-group\", \"mb-3\"], [\"type\", \"text\", \"placeholder\", \"Username\", 1, \"form-control\"], [\"type\", \"email\", \"placeholder\", \"Email\", 1, \"form-control\"], [1, \"input-group\", \"mb-4\"], [\"type\", \"password\", \"placeholder\", \"Password\", 1, \"form-control\"], [1, \"form-group\", \"text-start\", \"mb-4\"], [1, \"checkbox\", \"checkbox-fill\", \"d-inline\"], [\"type\", \"checkbox\", \"name\", \"checkbox-fill-1\", \"id\", \"checkbox-fill-1\", \"checked\", \"\"], [\"for\", \"checkbox-fill-1\", 1, \"cr\"], [1, \"form-group\", \"text-start\", \"mb-3\"], [\"type\", \"checkbox\", \"name\", \"checkbox-fill-2\", \"id\", \"checkbox-fill-2\"], [\"for\", \"checkbox-fill-2\", 1, \"cr\"], [\"href\", \"javascript:\"], [1, \"btn\", \"btn-primary\", \"shadow-2\", \"mb-4\"], [1, \"mb-0\", \"text-muted\"], [3, \"routerLink\"]],\n      template: function AuthSignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"span\", 3)(4, \"span\", 4)(5, \"span\", 4)(6, \"span\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"div\", 7);\n          i0.ɵɵelement(10, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"h3\", 7);\n          i0.ɵɵtext(12, \"Sign up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 9);\n          i0.ɵɵelement(14, \"input\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 9);\n          i0.ɵɵelement(16, \"input\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 12);\n          i0.ɵɵelement(18, \"input\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 14)(20, \"div\", 15);\n          i0.ɵɵelement(21, \"input\", 16);\n          i0.ɵɵelementStart(22, \"label\", 17);\n          i0.ɵɵtext(23, \"Save Details\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"div\", 18)(25, \"div\", 15);\n          i0.ɵɵelement(26, \"input\", 19);\n          i0.ɵɵelementStart(27, \"label\", 20);\n          i0.ɵɵtext(28, \" Send me the \");\n          i0.ɵɵelementStart(29, \"a\", 21);\n          i0.ɵɵtext(30, \"Newsletter\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(31, \" weekly. \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"button\", 22);\n          i0.ɵɵtext(33, \"Sign up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"p\", 23);\n          i0.ɵɵtext(35, \" Already have an account? \");\n          i0.ɵɵelementStart(36, \"a\", 24);\n          i0.ɵɵtext(37, \"Log in\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(36);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c0));\n        }\n      },\n      dependencies: [RouterModule, i1.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterModule", "AuthSignupComponent", "selectors", "decls", "vars", "consts", "template", "AuthSignupComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "i1", "RouterLink", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\authentication\\auth-signup\\auth-signup.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\authentication\\auth-signup\\auth-signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-auth-signup',\r\n  standalone: true,\r\n  imports: [RouterModule],\r\n  templateUrl: './auth-signup.component.html',\r\n  styleUrls: ['./auth-signup.component.scss']\r\n})\r\nexport default class AuthSignupComponent {}\r\n", "<div class=\"auth-wrapper\">\r\n  <div class=\"auth-content\">\r\n    <div class=\"auth-bg\">\r\n      <span class=\"r\"></span>\r\n      <span class=\"r s\"></span>\r\n      <span class=\"r s\"></span>\r\n      <span class=\"r\"></span>\r\n    </div>\r\n    <div class=\"card\">\r\n      <div class=\"card-body text-center\">\r\n        <div class=\"mb-4\">\r\n          <i class=\"feather icon-user-plus auth-icon\"></i>\r\n        </div>\r\n        <h3 class=\"mb-4\">Sign up</h3>\r\n        <div class=\"input-group mb-3\">\r\n          <input type=\"text\" class=\"form-control\" placeholder=\"Username\" />\r\n        </div>\r\n        <div class=\"input-group mb-3\">\r\n          <input type=\"email\" class=\"form-control\" placeholder=\"Email\" />\r\n        </div>\r\n        <div class=\"input-group mb-4\">\r\n          <input type=\"password\" class=\"form-control\" placeholder=\"Password\" />\r\n        </div>\r\n        <div class=\"form-group text-start mb-4\">\r\n          <div class=\"checkbox checkbox-fill d-inline\">\r\n            <input type=\"checkbox\" name=\"checkbox-fill-1\" id=\"checkbox-fill-1\" checked=\"\" />\r\n            <label for=\"checkbox-fill-1\" class=\"cr\">Save Details</label>\r\n          </div>\r\n        </div>\r\n        <div class=\"form-group text-start mb-3\">\r\n          <div class=\"checkbox checkbox-fill d-inline\">\r\n            <input type=\"checkbox\" name=\"checkbox-fill-2\" id=\"checkbox-fill-2\" />\r\n            <label for=\"checkbox-fill-2\" class=\"cr\">\r\n              Send me the\r\n              <a href=\"javascript:\">Newsletter</a>\r\n              weekly.\r\n            </label>\r\n          </div>\r\n        </div>\r\n        <button class=\"btn btn-primary shadow-2 mb-4\">Sign up</button>\r\n        <p class=\"mb-0 text-muted\">\r\n          Already have an account?\r\n          <a [routerLink]=\"['/auth/signin']\">Log in</a>\r\n        </p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;AAS9C,eAAc,MAAOC,mBAAmB;;;uCAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRpCE,EAFJ,CAAAC,cAAA,aAA0B,aACE,aACH;UAInBD,EAHA,CAAAE,SAAA,cAAuB,cACE,cACA,cACF;UACzBF,EAAA,CAAAG,YAAA,EAAM;UAGFH,EAFJ,CAAAC,cAAA,aAAkB,aACmB,aACf;UAChBD,EAAA,CAAAE,SAAA,YAAgD;UAClDF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAAiB;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,SAAA,iBAAiE;UACnEF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,SAAA,iBAA+D;UACjEF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA8B;UAC5BD,EAAA,CAAAE,SAAA,iBAAqE;UACvEF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAwC,eACO;UAC3CD,EAAA,CAAAE,SAAA,iBAAgF;UAChFF,EAAA,CAAAC,cAAA,iBAAwC;UAAAD,EAAA,CAAAI,MAAA,oBAAY;UAExDJ,EAFwD,CAAAG,YAAA,EAAQ,EACxD,EACF;UAEJH,EADF,CAAAC,cAAA,eAAwC,eACO;UAC3CD,EAAA,CAAAE,SAAA,iBAAqE;UACrEF,EAAA,CAAAC,cAAA,iBAAwC;UACtCD,EAAA,CAAAI,MAAA,qBACA;UAAAJ,EAAA,CAAAC,cAAA,aAAsB;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACpCH,EAAA,CAAAI,MAAA,iBACF;UAEJJ,EAFI,CAAAG,YAAA,EAAQ,EACJ,EACF;UACNH,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAC9DH,EAAA,CAAAC,cAAA,aAA2B;UACzBD,EAAA,CAAAI,MAAA,kCACA;UAAAJ,EAAA,CAAAC,cAAA,aAAmC;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAKnDJ,EALmD,CAAAG,YAAA,EAAI,EAC3C,EACA,EACF,EACF,EACF;;;UALOH,EAAA,CAAAK,SAAA,IAA+B;UAA/BL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAA+B;;;qBDpChClB,YAAY,EAAAmB,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}