{"ast": null, "code": "// angular import\nimport { output } from '@angular/core';\n// project import\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\nimport { NavLogoComponent } from './nav-logo/nav-logo.component';\nimport { NavContentComponent } from './nav-content/nav-content.component';\nimport * as i0 from \"@angular/core\";\nexport class NavigationComponent {\n  // constructor\n  constructor() {\n    // public props\n    this.NavCollapse = output();\n    this.NavCollapsedMob = output();\n    this.windowWidth = window.innerWidth;\n    this.navCollapsedMob = false;\n  }\n  // public method\n  navCollapse() {\n    if (this.windowWidth >= 992) {\n      this.navCollapsed = !this.navCollapsed;\n      this.NavCollapse.emit();\n    }\n  }\n  navCollapseMob() {\n    if (this.windowWidth < 992) {\n      this.NavCollapsedMob.emit();\n    }\n  }\n  static {\n    this.ɵfac = function NavigationComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NavigationComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavigationComponent,\n      selectors: [[\"app-navigation\"]],\n      outputs: {\n        NavCollapse: \"NavCollapse\",\n        NavCollapsedMob: \"NavCollapsedMob\"\n      },\n      decls: 4,\n      vars: 1,\n      consts: [[1, \"navbar-wrapper\"], [1, \"navbar-brand\", \"header-logo\", 3, \"NavCollapse\", \"navCollapsed\"], [1, \"scroll-div\", \"w-100\", 3, \"NavCollapsedMob\"]],\n      template: function NavigationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\")(1, \"div\", 0)(2, \"app-nav-logo\", 1);\n          i0.ɵɵlistener(\"NavCollapse\", function NavigationComponent_Template_app_nav_logo_NavCollapse_2_listener() {\n            return ctx.navCollapse();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"app-nav-content\", 2);\n          i0.ɵɵlistener(\"NavCollapsedMob\", function NavigationComponent_Template_app_nav_content_NavCollapsedMob_3_listener() {\n            return ctx.navCollapseMob();\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"navCollapsed\", ctx.navCollapsed);\n        }\n      },\n      dependencies: [SharedModule, NavLogoComponent, NavContentComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["output", "SharedModule", "NavLogoComponent", "NavContentComponent", "NavigationComponent", "constructor", "NavCollapse", "NavCollapsedMob", "windowWidth", "window", "innerWidth", "navCollapsedMob", "navCollapse", "navCollapsed", "emit", "navCollapseMob", "selectors", "outputs", "decls", "vars", "consts", "template", "NavigationComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵlistener", "NavigationComponent_Template_app_nav_logo_NavCollapse_2_listener", "ɵɵelementEnd", "NavigationComponent_Template_app_nav_content_NavCollapsedMob_3_listener", "ɵɵadvance", "ɵɵproperty", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\navigation\\navigation.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\navigation\\navigation.component.html"], "sourcesContent": ["// angular import\r\nimport { Component, output } from '@angular/core';\r\n\r\n// project import\r\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\r\nimport { NavLogoComponent } from './nav-logo/nav-logo.component';\r\nimport { NavContentComponent } from './nav-content/nav-content.component';\r\n\r\n@Component({\r\n  selector: 'app-navigation',\r\n  imports: [SharedModule, NavLogoComponent, NavContentComponent],\r\n  templateUrl: './navigation.component.html',\r\n  styleUrls: ['./navigation.component.scss']\r\n})\r\nexport class NavigationComponent {\r\n  // public props\r\n  NavCollapse = output();\r\n  NavCollapsedMob = output();\r\n  navCollapsed: boolean;\r\n  navCollapsedMob: boolean;\r\n  windowWidth: number;\r\n\r\n  // constructor\r\n  constructor() {\r\n    this.windowWidth = window.innerWidth;\r\n    this.navCollapsedMob = false;\r\n  }\r\n\r\n  // public method\r\n  navCollapse() {\r\n    if (this.windowWidth >= 992) {\r\n      this.navCollapsed = !this.navCollapsed;\r\n      this.NavCollapse.emit();\r\n    }\r\n  }\r\n\r\n  navCollapseMob() {\r\n    if (this.windowWidth < 992) {\r\n      this.NavCollapsedMob.emit();\r\n    }\r\n  }\r\n}\r\n", "<nav>\r\n  <div class=\"navbar-wrapper\">\r\n    <app-nav-logo [navCollapsed]=\"navCollapsed\" (NavCollapse)=\"navCollapse()\" class=\"navbar-brand header-logo\" />\r\n    <app-nav-content (NavCollapsedMob)=\"navCollapseMob()\" class=\"scroll-div w-100\" />\r\n  </div>\r\n</nav>\r\n"], "mappings": "AAAA;AACA,SAAoBA,MAAM,QAAQ,eAAe;AAEjD;AACA,SAASC,YAAY,QAAQ,oCAAoC;AACjE,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,mBAAmB,QAAQ,qCAAqC;;AAQzE,OAAM,MAAOC,mBAAmB;EAQ9B;EACAC,YAAA;IARA;IACA,KAAAC,WAAW,GAAGN,MAAM,EAAE;IACtB,KAAAO,eAAe,GAAGP,MAAM,EAAE;IAOxB,IAAI,CAACQ,WAAW,GAAGC,MAAM,CAACC,UAAU;IACpC,IAAI,CAACC,eAAe,GAAG,KAAK;EAC9B;EAEA;EACAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACJ,WAAW,IAAI,GAAG,EAAE;MAC3B,IAAI,CAACK,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;MACtC,IAAI,CAACP,WAAW,CAACQ,IAAI,EAAE;IACzB;EACF;EAEAC,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACP,WAAW,GAAG,GAAG,EAAE;MAC1B,IAAI,CAACD,eAAe,CAACO,IAAI,EAAE;IAC7B;EACF;;;uCA1BWV,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAY,SAAA;MAAAC,OAAA;QAAAX,WAAA;QAAAC,eAAA;MAAA;MAAAW,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ5BE,EAFJ,CAAAC,cAAA,UAAK,aACyB,sBACmF;UAAjED,EAAA,CAAAE,UAAA,yBAAAC,iEAAA;YAAA,OAAeJ,GAAA,CAAAZ,WAAA,EAAa;UAAA,EAAC;UAAzEa,EAAA,CAAAI,YAAA,EAA6G;UAC7GJ,EAAA,CAAAC,cAAA,yBAAiF;UAAhED,EAAA,CAAAE,UAAA,6BAAAG,wEAAA;YAAA,OAAmBN,GAAA,CAAAT,cAAA,EAAgB;UAAA,EAAC;UAEzDU,EAFI,CAAAI,YAAA,EAAiF,EAC7E,EACF;;;UAHYJ,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAO,UAAA,iBAAAR,GAAA,CAAAX,YAAA,CAA6B;;;qBDQnCZ,YAAY,EAAEC,gBAAgB,EAAEC,mBAAmB;MAAA8B,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}