{"ast": null, "code": "export const Spinkit = {\n  skChasingDots: 'sk-chasing-dots',\n  skCubeGrid: 'sk-cube-grid',\n  skDoubleBounce: 'sk-double-bounce',\n  skRotatingPlane: 'sk-rotationg-plane',\n  skSpinnerPulse: 'sk-spinner-pulse',\n  skThreeBounce: 'sk-three-bounce',\n  skWanderingCubes: 'sk-wandering-cubes',\n  skWave: 'sk-wave',\n  skLine: 'sk-line-material'\n};", "map": {"version": 3, "names": ["Spinkit", "skChasingDots", "skCubeGrid", "skDoubleBounce", "skRotatingPlane", "skSpinnerPulse", "skThreeBounce", "skWanderingCubes", "skWave", "skLine"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\shared\\components\\spinner\\spinkits.ts"], "sourcesContent": ["export const Spinkit = {\r\n  skChasingDots: 'sk-chasing-dots',\r\n  skCubeGrid: 'sk-cube-grid',\r\n  skDoubleBounce: 'sk-double-bounce',\r\n  skRotatingPlane: 'sk-rotationg-plane',\r\n  skSpinnerPulse: 'sk-spinner-pulse',\r\n  skThreeBounce: 'sk-three-bounce',\r\n  skWanderingCubes: 'sk-wandering-cubes',\r\n  skWave: 'sk-wave',\r\n  skLine: 'sk-line-material'\r\n};\r\n"], "mappings": "AAAA,OAAO,MAAMA,OAAO,GAAG;EACrBC,aAAa,EAAE,iBAAiB;EAChCC,UAAU,EAAE,cAAc;EAC1BC,cAAc,EAAE,kBAAkB;EAClCC,eAAe,EAAE,oBAAoB;EACrCC,cAAc,EAAE,kBAAkB;EAClCC,aAAa,EAAE,iBAAiB;EAChCC,gBAAgB,EAAE,oBAAoB;EACtCC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE;CACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}