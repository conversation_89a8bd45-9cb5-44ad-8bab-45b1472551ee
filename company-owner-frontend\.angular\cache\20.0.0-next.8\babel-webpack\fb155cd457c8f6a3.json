{"ast": null, "code": "import { SharedModule } from 'src/app/theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nfunction BasicTabsPillsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 21);\n    i0.ɵɵtext(1, \" Raw denim you probably haven't heard of them jean shorts Austin. Nesciunt tofu stumptown aliqua, retro synth master cleanse. Mustache cliche tempor, williamsburg carles vegan helvetica. Reprehenderit butcher retro keffiyeh dreamcatcher synth. Cosby sweater eu banh mi, qui irure terry richardson ex squid. Aliquip placeat salvia cillum iphone. Seitan aliquip quis cardigan american apparel, butcher voluptate nisi qui. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BasicTabsPillsComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 21);\n    i0.ɵɵtext(1, \" Food truck fixie locavore, accusamus m<PERSON><PERSON><PERSON>y's marfa nulla single-origin coffee squid. Exercitation +1 labore velit, blog sartorial PBR leggings next level wes anderson artisan four loko farm-to-table craft beer twee. Qui photo booth letterpress, commodo enim craft beer mlkshk aliquip jean shorts ullamco ad vinyl cillum PBR. Homo nostrud organic, assumenda labore aesthetic magna delectus mollit. Keytar helvetica VHS salvia yr, vero magna velit sapiente labore stumptown. Vegan fanny pack odio cillum wes anderson 8-bit, sustainable jean shorts beard ut DIY ethical culpa terry richardson biodiesel. Art party scenester stumptown, tumblr butcher vero sint qui sapiente accusamus tattooed echo park. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BasicTabsPillsComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 21);\n    i0.ɵɵtext(1, \" Etsy mixtape wayfarers, ethical wes anderson tofu before they sold out mcsweeney's organic lomo retro fanny pack lo-fi farm-to-table readymade. Messenger bag gentrify pitchfork tattooed craft beer, iphone skateboard locavore carles etsy salvia banksy hoodie helvetica. DIY synth PBR banksy irony. Leggings gentrify squid 8-bit cred pitchfork. Williamsburg banh mi whatever gluten-free, carles pitchfork biodiesel fixie etsy retro mlkshk vice blog. Scenester cred you probably haven't heard of them, vinyl craft beer blog stumptown. Pitchfork sustainable tofu synth chambray yr. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BasicTabsPillsComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 21);\n    i0.ɵɵtext(1, \" Consequat occaecat ullamco amet non eiusmod nostrud dolore irure incididunt est duis anim sunt officia. Fugiat velit proident aliquip nisi incididunt nostrud exercitation proident est nisi. Irure magna elit commodo anim ex veniam culpa eiusmod id nostrud sit cupidatat in veniam ad. Eiusmod consequat eu adipisicing minim anim aliquip cupidatat culpa excepteur quis. Occaecat sit eu exercitation irure Lorem incididunt nostrud. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BasicTabsPillsComponent_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 21);\n    i0.ɵɵtext(1, \" Ad pariatur nostrud pariatur exercitation ipsum ipsum culpa mollit commodo mollit ex. Aute sunt incididunt amet commodo est sint nisi deserunt pariatur do. Aliquip ex eiusmod voluptate exercitation cillum id incididunt elit sunt. Qui minim sit magna Lorem id et dolore velit Lorem amet exercitation duis deserunt. Anim id labore elit adipisicing ut in id occaecat pariatur ut ullamco ea tempor duis. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BasicTabsPillsComponent_ng_template_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 21);\n    i0.ɵɵtext(1, \" Est quis nulla laborum officia ad nisi ex nostrud culpa Lorem excepteur aliquip dolor aliqua irure ex. Nulla ut duis ipsum nisi elit fugiat commodo sunt reprehenderit laborum veniam eu veniam. Eiusmod minim exercitation fugiat irure ex labore incididunt do fugiat commodo aliquip sit id deserunt reprehenderit aliquip nostrud. Amet ex cupidatat excepteur aute veniam incididunt mollit cupidatat esse irure officia elit do ipsum ullamco Lorem. Ullamco ut ad minim do mollit labore ipsum laboris ipsum commodo sunt tempor enim incididunt. Commodo quis sunt dolore aliquip aute tempor irure magna enim minim reprehenderit. Ullamco consectetur culpa veniam sint cillum aliqua incididunt velit ullamco sunt ullamco quis quis commodo voluptate. Mollit nulla nostrud adipisicing aliqua cupidatat aliqua pariatur mollit voluptate voluptate consequat non. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BasicTabsPillsComponent_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 21);\n    i0.ɵɵtext(1, \" Cillum ad ut irure tempor velit nostrud occaecat ullamco aliqua anim Lorem sint. Veniam sint duis incididunt do esse magna mollit excepteur laborum qui. Id id reprehenderit sit est eu aliqua occaecat quis et velit excepteur laborum mollit dolore eiusmod. Ipsum dolor in occaecat commodo et voluptate minim reprehenderit mollit pariatur. Deserunt non laborum enim et cillum eu deserunt excepteur ea incididunt minim occaecat. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BasicTabsPillsComponent_ng_template_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 21);\n    i0.ɵɵtext(1, \" Culpa dolor voluptate do laboris laboris irure reprehenderit id incididunt duis pariatur mollit aute magna pariatur consectetur. Eu veniam duis non ut dolor deserunt commodo et minim in quis laboris ipsum velit id veniam. Quis ut consectetur adipisicing officia excepteur non sit. Ut et elit aliquip labore Lorem enim eu. Ullamco mollit occaecat dolore ipsum id officia mollit qui esse anim eiusmod do sint minim consectetur qui. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BasicTabsPillsComponent_ng_template_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 21);\n    i0.ɵɵtext(1, \" Fugiat id quis dolor culpa eiusmod anim velit excepteur proident dolor aute qui magna. Ad proident laboris ullamco esse anim Lorem Lorem veniam quis Lorem irure occaecat velit nostrud magna nulla. Velit et et proident Lorem do ea tempor officia dolor. Reprehenderit Lorem aliquip labore est magna commodo est ea veniam consectetur. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BasicTabsPillsComponent_ng_template_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 21);\n    i0.ɵɵtext(1, \" Eu dolore ea ullamco dolore Lorem id cupidatat excepteur reprehenderit consectetur elit id dolor proident in cupidatat officia. Voluptate excepteur commodo labore nisi cillum duis aliqua do. Aliqua amet qui mollit consectetur nulla mollit velit aliqua veniam nisi id do Lorem deserunt amet. Culpa ullamco sit adipisicing labore officia magna elit nisi in aute tempor commodo eiusmod. \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport default class BasicTabsPillsComponent {\n  static {\n    this.ɵfac = function BasicTabsPillsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BasicTabsPillsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BasicTabsPillsComponent,\n      selectors: [[\"app-basic-tabs-pills\"]],\n      decls: 68,\n      vars: 13,\n      consts: [[\"nav\", \"ngbNav\"], [\"nav1\", \"ngbNav\"], [\"nav3\", \"ngbNav\"], [1, \"row\"], [1, \"col-sm-12\"], [1, \"card\"], [1, \"card-body\"], [1, \"mb-3\"], [\"ngbNav\", \"\", 1, \"nav\", \"nav-tabs\", \"mb-3\"], [1, \"nav-item\", 3, \"ngbNavItem\"], [\"aria-controls\", \"home\", \"aria-selected\", \"true\", \"ngbNavLink\", \"\", 1, \"nav-link\", \"text-uppercase\"], [\"ngbNavContent\", \"\"], [3, \"ngbNavOutlet\"], [\"ngbNav\", \"\", 1, \"nav\", \"nav-pills\", \"mb-3\"], [\"ngbNavLink\", \"\", \"aria-controls\", \"pills-home\", \"aria-selected\", \"true\", 1, \"nav-link\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"], [1, \"col-md-3\", \"col-sm-12\"], [\"ngbNav\", \"\", \"orientation\", \"vertical\", \"y\", \"\", 1, \"nav\", \"flex-column\", \"nav-pills\"], [\"ngbNavLink\", \"\", 1, \"nav-link\"], [1, \"col-md-9\", \"col-sm-12\"], [1, \"ms-4\", 3, \"ngbNavOutlet\"], [1, \"mb-0\"]],\n      template: function BasicTabsPillsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"h5\", 7);\n          i0.ɵɵtext(5, \"Basic Tabs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"ul\", 8, 0)(8, \"li\", 9)(9, \"a\", 10);\n          i0.ɵɵtext(10, \"Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, BasicTabsPillsComponent_ng_template_11_Template, 2, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"li\", 9)(13, \"a\", 10);\n          i0.ɵɵtext(14, \"Profile\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(15, BasicTabsPillsComponent_ng_template_15_Template, 2, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"li\", 9)(17, \"a\", 10);\n          i0.ɵɵtext(18, \"Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, BasicTabsPillsComponent_ng_template_19_Template, 2, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(20, \"div\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 4)(22, \"div\", 5)(23, \"div\", 6)(24, \"h5\", 7);\n          i0.ɵɵtext(25, \"Basic Pills\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"ul\", 13, 1)(28, \"li\", 9)(29, \"a\", 14);\n          i0.ɵɵtext(30, \"Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, BasicTabsPillsComponent_ng_template_31_Template, 2, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"li\", 9)(33, \"a\", 14);\n          i0.ɵɵtext(34, \"Profile\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(35, BasicTabsPillsComponent_ng_template_35_Template, 2, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"li\", 9)(37, \"a\", 14);\n          i0.ɵɵtext(38, \"Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(39, BasicTabsPillsComponent_ng_template_39_Template, 2, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(40, \"div\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 4)(42, \"div\", 5)(43, \"div\", 6)(44, \"h5\", 7);\n          i0.ɵɵtext(45, \"Vertical Pills\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 3)(47, \"div\", 16)(48, \"ul\", 17, 2)(50, \"li\", 9)(51, \"a\", 18);\n          i0.ɵɵtext(52, \"Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(53, BasicTabsPillsComponent_ng_template_53_Template, 2, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"li\", 9)(55, \"a\", 18);\n          i0.ɵɵtext(56, \"Profile\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(57, BasicTabsPillsComponent_ng_template_57_Template, 2, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"li\", 9)(59, \"a\", 18);\n          i0.ɵɵtext(60, \"Messages\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(61, BasicTabsPillsComponent_ng_template_61_Template, 2, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"li\", 9)(63, \"a\", 18);\n          i0.ɵɵtext(64, \"Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(65, BasicTabsPillsComponent_ng_template_65_Template, 2, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(66, \"div\", 19);\n          i0.ɵɵelement(67, \"div\", 20);\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          const nav_r1 = i0.ɵɵreference(7);\n          const nav1_r2 = i0.ɵɵreference(27);\n          const nav3_r3 = i0.ɵɵreference(49);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngbNavItem\", 1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngbNavItem\", 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngbNavItem\", 3);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngbNavOutlet\", nav_r1);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngbNavItem\", 1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngbNavItem\", 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngbNavItem\", 3);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngbNavOutlet\", nav1_r2);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngbNavItem\", 1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngbNavItem\", 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngbNavItem\", 3);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngbNavItem\", 4);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngbNavOutlet\", nav3_r3);\n        }\n      },\n      dependencies: [SharedModule, i1.NgbNavContent, i1.NgbNav, i1.NgbNavItem, i1.NgbNavItemRole, i1.NgbNavLink, i1.NgbNavLinkBase, i1.NgbNavOutlet],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "BasicTabsPillsComponent", "selectors", "decls", "vars", "consts", "template", "BasicTabsPillsComponent_Template", "rf", "ctx", "ɵɵtemplate", "BasicTabsPillsComponent_ng_template_11_Template", "BasicTabsPillsComponent_ng_template_15_Template", "BasicTabsPillsComponent_ng_template_19_Template", "ɵɵelement", "BasicTabsPillsComponent_ng_template_31_Template", "BasicTabsPillsComponent_ng_template_35_Template", "BasicTabsPillsComponent_ng_template_39_Template", "BasicTabsPillsComponent_ng_template_53_Template", "BasicTabsPillsComponent_ng_template_57_Template", "BasicTabsPillsComponent_ng_template_61_Template", "BasicTabsPillsComponent_ng_template_65_Template", "ɵɵadvance", "ɵɵproperty", "nav_r1", "nav1_r2", "nav3_r3", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NgbNav", "NgbNavItem", "NgbNavItemRole", "NgbNavLink", "NgbNavLinkBase", "NgbNavOutlet", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\ui-elements\\ui-basic\\basic-tabs-pills\\basic-tabs-pills.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\ui-elements\\ui-basic\\basic-tabs-pills\\basic-tabs-pills.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\r\n\r\n@Component({\r\n  selector: 'app-basic-tabs-pills',\r\n  standalone: true,\r\n  imports: [SharedModule],\r\n  templateUrl: './basic-tabs-pills.component.html',\r\n  styleUrls: ['./basic-tabs-pills.component.scss']\r\n})\r\nexport default class BasicTabsPillsComponent {}\r\n", "<div class=\"row\">\r\n  <!-- [ tabs ] start -->\r\n  <div class=\"col-sm-12\">\r\n    <div class=\"card\">\r\n      <div class=\"card-body\">\r\n        <h5 class=\"mb-3\">Basic Tabs</h5>\r\n        <ul class=\"nav nav-tabs mb-3\" ngbNav #nav=\"ngbNav\">\r\n          <li class=\"nav-item\" [ngbNavItem]=\"1\">\r\n            <a class=\"nav-link text-uppercase\" aria-controls=\"home\" aria-selected=\"true\" ngbNavLink>Home</a>\r\n            <ng-template ngbNavContent>\r\n              <p class=\"mb-0\">\r\n                Raw denim you probably haven't heard of them jean shorts Austin. Nesciunt tofu stumptown aliqua, retro synth master cleanse.\r\n                Mustache cliche tempor, williamsburg carles vegan helvetica. Reprehenderit butcher retro keffiyeh dreamcatcher synth. Cosby\r\n                sweater eu banh mi, qui irure terry richardson ex squid. Aliquip placeat salvia cillum iphone. Seitan aliquip quis cardigan\r\n                american apparel, butcher voluptate nisi qui.\r\n              </p>\r\n            </ng-template>\r\n          </li>\r\n          <li class=\"nav-item\" [ngbNavItem]=\"2\">\r\n            <a class=\"nav-link text-uppercase\" aria-controls=\"home\" aria-selected=\"true\" ngbNavLink>Profile</a>\r\n            <ng-template ngbNavContent>\r\n              <p class=\"mb-0\">\r\n                Food truck fixie locavore, accusamus mcsweeney's marfa nulla single-origin coffee squid. Exercitation +1 labore velit, blog\r\n                sartorial PBR leggings next level wes anderson artisan four loko farm-to-table craft beer twee. Qui photo booth letterpress,\r\n                commodo enim craft beer mlkshk aliquip jean shorts ullamco ad vinyl cillum PBR. Homo nostrud organic, assumenda labore\r\n                aesthetic magna delectus mollit. Keytar helvetica VHS salvia yr, vero magna velit sapiente labore stumptown. Vegan fanny\r\n                pack odio cillum wes anderson 8-bit, sustainable jean shorts beard ut DIY ethical culpa terry richardson biodiesel. Art\r\n                party scenester stumptown, tumblr butcher vero sint qui sapiente accusamus tattooed echo park.\r\n              </p>\r\n            </ng-template>\r\n          </li>\r\n          <li class=\"nav-item\" [ngbNavItem]=\"3\">\r\n            <a class=\"nav-link text-uppercase\" aria-controls=\"home\" aria-selected=\"true\" ngbNavLink>Contact</a>\r\n            <ng-template ngbNavContent>\r\n              <p class=\"mb-0\">\r\n                Etsy mixtape wayfarers, ethical wes anderson tofu before they sold out mcsweeney's organic lomo retro fanny pack lo-fi\r\n                farm-to-table readymade. Messenger bag gentrify pitchfork tattooed craft beer, iphone skateboard locavore carles etsy salvia\r\n                banksy hoodie helvetica. DIY synth PBR banksy irony. Leggings gentrify squid 8-bit cred pitchfork. Williamsburg banh mi\r\n                whatever gluten-free, carles pitchfork biodiesel fixie etsy retro mlkshk vice blog. Scenester cred you probably haven't\r\n                heard of them, vinyl craft beer blog stumptown. Pitchfork sustainable tofu synth chambray yr.\r\n              </p>\r\n            </ng-template>\r\n          </li>\r\n        </ul>\r\n        <div [ngbNavOutlet]=\"nav\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"col-sm-12\">\r\n    <div class=\"card\">\r\n      <div class=\"card-body\">\r\n        <h5 class=\"mb-3\">Basic Pills</h5>\r\n        <ul class=\"nav nav-pills mb-3\" ngbNav #nav1=\"ngbNav\">\r\n          <li class=\"nav-item\" [ngbNavItem]=\"1\">\r\n            <a class=\"nav-link\" ngbNavLink aria-controls=\"pills-home\" aria-selected=\"true\">Home</a>\r\n            <ng-template ngbNavContent>\r\n              <p class=\"mb-0\">\r\n                Consequat occaecat ullamco amet non eiusmod nostrud dolore irure incididunt est duis anim sunt officia. Fugiat velit\r\n                proident aliquip nisi incididunt nostrud exercitation proident est nisi. Irure magna elit commodo anim ex veniam culpa\r\n                eiusmod id nostrud sit cupidatat in veniam ad. Eiusmod consequat eu adipisicing minim anim aliquip cupidatat culpa excepteur\r\n                quis. Occaecat sit eu exercitation irure Lorem incididunt nostrud.\r\n              </p>\r\n            </ng-template>\r\n          </li>\r\n          <li class=\"nav-item\" [ngbNavItem]=\"2\">\r\n            <a class=\"nav-link\" ngbNavLink aria-controls=\"pills-home\" aria-selected=\"true\">Profile</a>\r\n            <ng-template ngbNavContent>\r\n              <p class=\"mb-0\">\r\n                Ad pariatur nostrud pariatur exercitation ipsum ipsum culpa mollit commodo mollit ex. Aute sunt incididunt amet commodo est\r\n                sint nisi deserunt pariatur do. Aliquip ex eiusmod voluptate exercitation cillum id incididunt elit sunt. Qui minim sit\r\n                magna Lorem id et dolore velit Lorem amet exercitation duis deserunt. Anim id labore elit adipisicing ut in id occaecat\r\n                pariatur ut ullamco ea tempor duis.\r\n              </p>\r\n            </ng-template>\r\n          </li>\r\n          <li class=\"nav-item\" [ngbNavItem]=\"3\">\r\n            <a class=\"nav-link\" ngbNavLink aria-controls=\"pills-home\" aria-selected=\"true\">Contact</a>\r\n            <ng-template ngbNavContent>\r\n              <p class=\"mb-0\">\r\n                Est quis nulla laborum officia ad nisi ex nostrud culpa Lorem excepteur aliquip dolor aliqua irure ex. Nulla ut duis ipsum\r\n                nisi elit fugiat commodo sunt reprehenderit laborum veniam eu veniam. Eiusmod minim exercitation fugiat irure ex labore\r\n                incididunt do fugiat commodo aliquip sit id deserunt reprehenderit aliquip nostrud. Amet ex cupidatat excepteur aute veniam\r\n                incididunt mollit cupidatat esse irure officia elit do ipsum ullamco Lorem. Ullamco ut ad minim do mollit labore ipsum\r\n                laboris ipsum commodo sunt tempor enim incididunt. Commodo quis sunt dolore aliquip aute tempor irure magna enim minim\r\n                reprehenderit. Ullamco consectetur culpa veniam sint cillum aliqua incididunt velit ullamco sunt ullamco quis quis commodo\r\n                voluptate. Mollit nulla nostrud adipisicing aliqua cupidatat aliqua pariatur mollit voluptate voluptate consequat non.\r\n              </p>\r\n            </ng-template>\r\n          </li>\r\n        </ul>\r\n        <div [ngbNavOutlet]=\"nav1\" class=\"mt-2\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"col-sm-12\">\r\n    <div class=\"card\">\r\n      <div class=\"card-body\">\r\n        <h5 class=\"mb-3\">Vertical Pills</h5>\r\n        <div class=\"row\">\r\n          <div class=\"col-md-3 col-sm-12\">\r\n            <ul class=\"nav flex-column nav-pills\" ngbNav #nav3=\"ngbNav\" orientation=\"vertical\" y>\r\n              <li [ngbNavItem]=\"1\" class=\"nav-item\">\r\n                <a class=\"nav-link\" ngbNavLink>Home</a>\r\n                <ng-template ngbNavContent>\r\n                  <p class=\"mb-0\">\r\n                    Cillum ad ut irure tempor velit nostrud occaecat ullamco aliqua anim Lorem sint. Veniam sint duis incididunt do esse\r\n                    magna mollit excepteur laborum qui. Id id reprehenderit sit est eu aliqua occaecat quis et velit excepteur laborum\r\n                    mollit dolore eiusmod. Ipsum dolor in occaecat commodo et voluptate minim reprehenderit mollit pariatur. Deserunt non\r\n                    laborum enim et cillum eu deserunt excepteur ea incididunt minim occaecat.\r\n                  </p>\r\n                </ng-template>\r\n              </li>\r\n              <li [ngbNavItem]=\"2\" class=\"nav-item\">\r\n                <a class=\"nav-link\" ngbNavLink>Profile</a>\r\n                <ng-template ngbNavContent>\r\n                  <p class=\"mb-0\">\r\n                    Culpa dolor voluptate do laboris laboris irure reprehenderit id incididunt duis pariatur mollit aute magna pariatur\r\n                    consectetur. Eu veniam duis non ut dolor deserunt commodo et minim in quis laboris ipsum velit id veniam. Quis ut\r\n                    consectetur adipisicing officia excepteur non sit. Ut et elit aliquip labore Lorem enim eu. Ullamco mollit occaecat\r\n                    dolore ipsum id officia mollit qui esse anim eiusmod do sint minim consectetur qui.\r\n                  </p>\r\n                </ng-template>\r\n              </li>\r\n              <li [ngbNavItem]=\"3\" class=\"nav-item\">\r\n                <a class=\"nav-link\" ngbNavLink>Messages</a>\r\n                <ng-template ngbNavContent>\r\n                  <p class=\"mb-0\">\r\n                    Fugiat id quis dolor culpa eiusmod anim velit excepteur proident dolor aute qui magna. Ad proident laboris ullamco esse\r\n                    anim Lorem Lorem veniam quis Lorem irure occaecat velit nostrud magna nulla. Velit et et proident Lorem do ea tempor\r\n                    officia dolor. Reprehenderit Lorem aliquip labore est magna commodo est ea veniam consectetur.\r\n                  </p>\r\n                </ng-template>\r\n              </li>\r\n              <li [ngbNavItem]=\"4\" class=\"nav-item\">\r\n                <a class=\"nav-link\" ngbNavLink>Settings</a>\r\n                <ng-template ngbNavContent>\r\n                  <p class=\"mb-0\">\r\n                    Eu dolore ea ullamco dolore Lorem id cupidatat excepteur reprehenderit consectetur elit id dolor proident in cupidatat\r\n                    officia. Voluptate excepteur commodo labore nisi cillum duis aliqua do. Aliqua amet qui mollit consectetur nulla mollit\r\n                    velit aliqua veniam nisi id do Lorem deserunt amet. Culpa ullamco sit adipisicing labore officia magna elit nisi in aute\r\n                    tempor commodo eiusmod.\r\n                  </p>\r\n                </ng-template>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div class=\"col-md-9 col-sm-12\">\r\n            <div [ngbNavOutlet]=\"nav3\" class=\"ms-4\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,oCAAoC;;;;;ICSnDC,EAAA,CAAAC,cAAA,YAAgB;IACdD,EAAA,CAAAE,MAAA,2aAIF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMJH,EAAA,CAAAC,cAAA,YAAgB;IACdD,EAAA,CAAAE,MAAA,wsBAMF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMJH,EAAA,CAAAC,cAAA,YAAgB;IACdD,EAAA,CAAAE,MAAA,0kBAKF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAgBJH,EAAA,CAAAC,cAAA,YAAgB;IACdD,EAAA,CAAAE,MAAA,obAIF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMJH,EAAA,CAAAC,cAAA,YAAgB;IACdD,EAAA,CAAAE,MAAA,wZAIF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMJH,EAAA,CAAAC,cAAA,YAAgB;IACdD,EAAA,CAAAE,MAAA,u1BAOF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAkBAH,EAAA,CAAAC,cAAA,YAAgB;IACdD,EAAA,CAAAE,MAAA,ibAIF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMJH,EAAA,CAAAC,cAAA,YAAgB;IACdD,EAAA,CAAAE,MAAA,sbAIF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMJH,EAAA,CAAAC,cAAA,YAAgB;IACdD,EAAA,CAAAE,MAAA,oVAGF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMJH,EAAA,CAAAC,cAAA,YAAgB;IACdD,EAAA,CAAAE,MAAA,wYAIF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADnItB,eAAc,MAAOC,uBAAuB;;;uCAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLpCX,EALR,CAAAC,cAAA,aAAiB,aAEQ,aACH,aACO,YACJ;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG5BH,EAFJ,CAAAC,cAAA,eAAmD,YACX,YACoD;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAChGH,EAAA,CAAAa,UAAA,KAAAC,+CAAA,0BAA2B;UAQ7Bd,EAAA,CAAAG,YAAA,EAAK;UAEHH,EADF,CAAAC,cAAA,aAAsC,aACoD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnGH,EAAA,CAAAa,UAAA,KAAAE,+CAAA,0BAA2B;UAU7Bf,EAAA,CAAAG,YAAA,EAAK;UAEHH,EADF,CAAAC,cAAA,aAAsC,aACoD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnGH,EAAA,CAAAa,UAAA,KAAAG,+CAAA,0BAA2B;UAU/BhB,EADE,CAAAG,YAAA,EAAK,EACF;UACLH,EAAA,CAAAiB,SAAA,eAAgC;UAGtCjB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAIAH,EAHN,CAAAC,cAAA,cAAuB,cACH,cACO,aACJ;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG7BH,EAFJ,CAAAC,cAAA,iBAAqD,aACb,aAC2C;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvFH,EAAA,CAAAa,UAAA,KAAAK,+CAAA,0BAA2B;UAQ7BlB,EAAA,CAAAG,YAAA,EAAK;UAEHH,EADF,CAAAC,cAAA,aAAsC,aAC2C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC1FH,EAAA,CAAAa,UAAA,KAAAM,+CAAA,0BAA2B;UAQ7BnB,EAAA,CAAAG,YAAA,EAAK;UAEHH,EADF,CAAAC,cAAA,aAAsC,aAC2C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC1FH,EAAA,CAAAa,UAAA,KAAAO,+CAAA,0BAA2B;UAY/BpB,EADE,CAAAG,YAAA,EAAK,EACF;UACLH,EAAA,CAAAiB,SAAA,eAA8C;UAGpDjB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAIAH,EAHN,CAAAC,cAAA,cAAuB,cACH,cACO,aACJ;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAK5BH,EAJR,CAAAC,cAAA,cAAiB,eACiB,iBACuD,aAC7C,aACL;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvCH,EAAA,CAAAa,UAAA,KAAAQ,+CAAA,0BAA2B;UAQ7BrB,EAAA,CAAAG,YAAA,EAAK;UAEHH,EADF,CAAAC,cAAA,aAAsC,aACL;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC1CH,EAAA,CAAAa,UAAA,KAAAS,+CAAA,0BAA2B;UAQ7BtB,EAAA,CAAAG,YAAA,EAAK;UAEHH,EADF,CAAAC,cAAA,aAAsC,aACL;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC3CH,EAAA,CAAAa,UAAA,KAAAU,+CAAA,0BAA2B;UAO7BvB,EAAA,CAAAG,YAAA,EAAK;UAEHH,EADF,CAAAC,cAAA,aAAsC,aACL;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC3CH,EAAA,CAAAa,UAAA,KAAAW,+CAAA,0BAA2B;UAUjCxB,EAFI,CAAAG,YAAA,EAAK,EACF,EACD;UACNH,EAAA,CAAAC,cAAA,eAAgC;UAC9BD,EAAA,CAAAiB,SAAA,eAA8C;UAM1DjB,EALU,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF,EACF;;;;;;UAlJyBH,EAAA,CAAAyB,SAAA,GAAgB;UAAhBzB,EAAA,CAAA0B,UAAA,iBAAgB;UAWhB1B,EAAA,CAAAyB,SAAA,GAAgB;UAAhBzB,EAAA,CAAA0B,UAAA,iBAAgB;UAahB1B,EAAA,CAAAyB,SAAA,GAAgB;UAAhBzB,EAAA,CAAA0B,UAAA,iBAAgB;UAalC1B,EAAA,CAAAyB,SAAA,GAAoB;UAApBzB,EAAA,CAAA0B,UAAA,iBAAAC,MAAA,CAAoB;UASF3B,EAAA,CAAAyB,SAAA,GAAgB;UAAhBzB,EAAA,CAAA0B,UAAA,iBAAgB;UAWhB1B,EAAA,CAAAyB,SAAA,GAAgB;UAAhBzB,EAAA,CAAA0B,UAAA,iBAAgB;UAWhB1B,EAAA,CAAAyB,SAAA,GAAgB;UAAhBzB,EAAA,CAAA0B,UAAA,iBAAgB;UAelC1B,EAAA,CAAAyB,SAAA,GAAqB;UAArBzB,EAAA,CAAA0B,UAAA,iBAAAE,OAAA,CAAqB;UAWhB5B,EAAA,CAAAyB,SAAA,IAAgB;UAAhBzB,EAAA,CAAA0B,UAAA,iBAAgB;UAWhB1B,EAAA,CAAAyB,SAAA,GAAgB;UAAhBzB,EAAA,CAAA0B,UAAA,iBAAgB;UAWhB1B,EAAA,CAAAyB,SAAA,GAAgB;UAAhBzB,EAAA,CAAA0B,UAAA,iBAAgB;UAUhB1B,EAAA,CAAAyB,SAAA,GAAgB;UAAhBzB,EAAA,CAAA0B,UAAA,iBAAgB;UAcjB1B,EAAA,CAAAyB,SAAA,GAAqB;UAArBzB,EAAA,CAAA0B,UAAA,iBAAAG,OAAA,CAAqB;;;qBD7I1B9B,YAAY,EAAA+B,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,MAAA,EAAAF,EAAA,CAAAG,UAAA,EAAAH,EAAA,CAAAI,cAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,cAAA,EAAAN,EAAA,CAAAO,YAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}