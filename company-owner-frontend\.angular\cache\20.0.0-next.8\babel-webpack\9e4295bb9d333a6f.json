{"ast": null, "code": "// Angular import\nimport { inject } from '@angular/core';\nimport { NavigationEnd, Router, RouterModule } from '@angular/router';\n// project import\nimport { SpinnerComponent } from './theme/shared/components/spinner/spinner.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppComponent {\n  constructor() {\n    this.router = inject(Router);\n    this.title = 'datta-able';\n  }\n  // life cycle hook\n  ngOnInit() {\n    this.router.events.subscribe(evt => {\n      if (!(evt instanceof NavigationEnd)) {\n        return;\n      }\n      window.scrollTo(0, 0);\n    });\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 2,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"router-outlet\");\n          i0.ɵɵelement(1, \"app-spinner\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [SpinnerComponent, RouterModule, i1.RouterOutlet],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["inject", "NavigationEnd", "Router", "RouterModule", "SpinnerComponent", "AppComponent", "constructor", "router", "title", "ngOnInit", "events", "subscribe", "evt", "window", "scrollTo", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "i1", "RouterOutlet", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\app.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\app.component.html"], "sourcesContent": ["// Angular import\r\nimport { Component, OnInit, inject } from '@angular/core';\r\nimport { NavigationEnd, Router, RouterModule } from '@angular/router';\r\n\r\n// project import\r\nimport { SpinnerComponent } from './theme/shared/components/spinner/spinner.component';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  imports: [SpinnerComponent, RouterModule],\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.scss']\r\n})\r\nexport class AppComponent implements OnInit {\r\n  private router = inject(Router);\r\n\r\n  title = 'datta-able';\r\n\r\n  // life cycle hook\r\n  ngOnInit() {\r\n    this.router.events.subscribe((evt) => {\r\n      if (!(evt instanceof NavigationEnd)) {\r\n        return;\r\n      }\r\n      window.scrollTo(0, 0);\r\n    });\r\n  }\r\n}\r\n", "<router-outlet><app-spinner></app-spinner></router-outlet>\r\n"], "mappings": "AAAA;AACA,SAA4BA,MAAM,QAAQ,eAAe;AACzD,SAASC,aAAa,EAAEC,MAAM,EAAEC,YAAY,QAAQ,iBAAiB;AAErE;AACA,SAASC,gBAAgB,QAAQ,qDAAqD;;;AAQtF,OAAM,MAAOC,YAAY;EANzBC,YAAA;IAOU,KAAAC,MAAM,GAAGP,MAAM,CAACE,MAAM,CAAC;IAE/B,KAAAM,KAAK,GAAG,YAAY;;EAEpB;EACAC,QAAQA,CAAA;IACN,IAAI,CAACF,MAAM,CAACG,MAAM,CAACC,SAAS,CAAEC,GAAG,IAAI;MACnC,IAAI,EAAEA,GAAG,YAAYX,aAAa,CAAC,EAAE;QACnC;MACF;MACAY,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACvB,CAAC,CAAC;EACJ;;;uCAbWT,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAU,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbzBE,EAAA,CAAAC,cAAA,oBAAe;UAAAD,EAAA,CAAAE,SAAA,kBAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAgB;;;qBDS9CrB,gBAAgB,EAAED,YAAY,EAAAuB,EAAA,CAAAC,YAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}