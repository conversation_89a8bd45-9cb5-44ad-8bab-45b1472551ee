{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { QuestionsListComponent } from './questions-list/questions-list.component';\nimport { QuestionFormComponent } from './questions-form/questions-form.component';\nimport { QuestionsComponent } from './questions.component';\nconst routes = [{\n  path: '',\n  component: QuestionsComponent,\n  children: [{\n    path: '',\n    component: QuestionsListComponent\n  }, {\n    path: 'new',\n    component: QuestionFormComponent\n  }, {\n    path: 'edit/:id',\n    component: QuestionFormComponent\n  }]\n}];\nlet QuestionModule = class QuestionModule {};\nQuestionModule = __decorate([NgModule({\n  declarations: [QuestionsComponent, QuestionsListComponent, QuestionFormComponent],\n  imports: [CommonModule, ReactiveFormsModule, RouterModule.forChild(routes)]\n})], QuestionModule);\nexport { QuestionModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "ReactiveFormsModule", "QuestionsListComponent", "QuestionFormComponent", "QuestionsComponent", "routes", "path", "component", "children", "QuestionModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\questions\\question.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { QuestionsListComponent } from './questions-list/questions-list.component';\nimport { QuestionFormComponent } from './questions-form/questions-form.component';\nimport { QuestionsComponent } from './questions.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: QuestionsComponent,\n    children: [\n      {\n        path: '',\n        component: QuestionsListComponent\n      },\n      {\n        path: 'new',\n        component: QuestionFormComponent\n      },\n      {\n        path: 'edit/:id',\n        component: QuestionFormComponent\n      }\n    ]\n  }\n];\n\n@NgModule({\n  declarations: [\n    QuestionsComponent,\n    QuestionsListComponent,\n    QuestionFormComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class QuestionModule { }"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,kBAAkB,QAAQ,uBAAuB;AAE1D,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,kBAAkB;EAC7BI,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEL;GACZ,EACD;IACEI,IAAI,EAAE,KAAK;IACXC,SAAS,EAAEJ;GACZ,EACD;IACEG,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEJ;GACZ;CAEJ,CACF;AAcM,IAAMM,cAAc,GAApB,MAAMA,cAAc,GAAI;AAAlBA,cAAc,GAAAC,UAAA,EAZ1BZ,QAAQ,CAAC;EACRa,YAAY,EAAE,CACZP,kBAAkB,EAClBF,sBAAsB,EACtBC,qBAAqB,CACtB;EACDS,OAAO,EAAE,CACPb,YAAY,EACZE,mBAAmB,EACnBD,YAAY,CAACa,QAAQ,CAACR,MAAM,CAAC;CAEhC,CAAC,C,EACWI,cAAc,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}