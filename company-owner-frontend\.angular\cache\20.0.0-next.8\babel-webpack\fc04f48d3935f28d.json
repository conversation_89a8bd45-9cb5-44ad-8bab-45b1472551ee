{"ast": null, "code": "/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The character used to mark the start and end of a \"block\" in a `$localize` tagged string.\n * A block can indicate metadata about the message or specify a name of a placeholder for a\n * substitution expressions.\n *\n * For example:\n *\n * ```ts\n * $localize`Hello, ${title}:title:!`;\n * $localize`:meaning|description@@id:source message text`;\n * ```\n */\nconst BLOCK_MARKER$1 = ':';\n/**\n * The marker used to separate a message's \"meaning\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:correct|Indicates that the user got the answer correct: Right!`;\n * $localize `:movement|Button label for moving to the right: Right!`;\n * ```\n */\nconst MEANING_SEPARATOR = '|';\n/**\n * The marker used to separate a message's custom \"id\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:A welcome message on the home page@@myApp-homepage-welcome: Welcome!`;\n * ```\n */\nconst ID_SEPARATOR = '@@';\n/**\n * The marker used to separate legacy message ids from the rest of a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:@@custom-id␟2df64767cd895a8fabe3e18b94b5b6b6f9e2e3f0: Welcome!`;\n * ```\n *\n * Note that this character is the \"symbol for the unit separator\" (␟) not the \"unit separator\n * character\" itself, since that has no visual representation. See https://graphemica.com/%E2%90%9F.\n *\n * Here is some background for the original \"unit separator character\":\n * https://stackoverflow.com/questions/8695118/whats-the-file-group-record-unit-separator-control-characters-and-its-usage\n */\nconst LEGACY_ID_INDICATOR = '\\u241F';\n\n/**\n * A lazily created TextEncoder instance for converting strings into UTF-8 bytes\n */\nlet textEncoder;\n/**\n * Compute the fingerprint of the given string\n *\n * The output is 64 bit number encoded as a decimal string\n *\n * based on:\n * https://github.com/google/closure-compiler/blob/master/src/com/google/javascript/jscomp/GoogleJsMessageIdGenerator.java\n */\nfunction fingerprint(str) {\n  textEncoder ??= new TextEncoder();\n  const utf8 = textEncoder.encode(str);\n  const view = new DataView(utf8.buffer, utf8.byteOffset, utf8.byteLength);\n  let hi = hash32(view, utf8.length, 0);\n  let lo = hash32(view, utf8.length, 102072);\n  if (hi == 0 && (lo == 0 || lo == 1)) {\n    hi = hi ^ 0x130f9bef;\n    lo = lo ^ -1801410264;\n  }\n  return BigInt.asUintN(32, BigInt(hi)) << BigInt(32) | BigInt.asUintN(32, BigInt(lo));\n}\nfunction computeMsgId(msg, meaning = '') {\n  let msgFingerprint = fingerprint(msg);\n  if (meaning) {\n    // Rotate the 64-bit message fingerprint one bit to the left and then add the meaning\n    // fingerprint.\n    msgFingerprint = BigInt.asUintN(64, msgFingerprint << BigInt(1)) | msgFingerprint >> BigInt(63) & BigInt(1);\n    msgFingerprint += fingerprint(meaning);\n  }\n  return BigInt.asUintN(63, msgFingerprint).toString();\n}\nfunction hash32(view, length, c) {\n  let a = 0x9e3779b9,\n    b = 0x9e3779b9;\n  let index = 0;\n  const end = length - 12;\n  for (; index <= end; index += 12) {\n    a += view.getUint32(index, true);\n    b += view.getUint32(index + 4, true);\n    c += view.getUint32(index + 8, true);\n    const res = mix(a, b, c);\n    a = res[0], b = res[1], c = res[2];\n  }\n  const remainder = length - index;\n  // the first byte of c is reserved for the length\n  c += length;\n  if (remainder >= 4) {\n    a += view.getUint32(index, true);\n    index += 4;\n    if (remainder >= 8) {\n      b += view.getUint32(index, true);\n      index += 4;\n      // Partial 32-bit word for c\n      if (remainder >= 9) {\n        c += view.getUint8(index++) << 8;\n      }\n      if (remainder >= 10) {\n        c += view.getUint8(index++) << 16;\n      }\n      if (remainder === 11) {\n        c += view.getUint8(index++) << 24;\n      }\n    } else {\n      // Partial 32-bit word for b\n      if (remainder >= 5) {\n        b += view.getUint8(index++);\n      }\n      if (remainder >= 6) {\n        b += view.getUint8(index++) << 8;\n      }\n      if (remainder === 7) {\n        b += view.getUint8(index++) << 16;\n      }\n    }\n  } else {\n    // Partial 32-bit word for a\n    if (remainder >= 1) {\n      a += view.getUint8(index++);\n    }\n    if (remainder >= 2) {\n      a += view.getUint8(index++) << 8;\n    }\n    if (remainder === 3) {\n      a += view.getUint8(index++) << 16;\n    }\n  }\n  return mix(a, b, c)[2];\n}\nfunction mix(a, b, c) {\n  a -= b;\n  a -= c;\n  a ^= c >>> 13;\n  b -= c;\n  b -= a;\n  b ^= a << 8;\n  c -= a;\n  c -= b;\n  c ^= b >>> 13;\n  a -= b;\n  a -= c;\n  a ^= c >>> 12;\n  b -= c;\n  b -= a;\n  b ^= a << 16;\n  c -= a;\n  c -= b;\n  c ^= b >>> 5;\n  a -= b;\n  a -= c;\n  a ^= c >>> 3;\n  b -= c;\n  b -= a;\n  b ^= a << 10;\n  c -= a;\n  c -= b;\n  c ^= b >>> 15;\n  return [a, b, c];\n}\n// Utils\nvar Endian;\n(function (Endian) {\n  Endian[Endian[\"Little\"] = 0] = \"Little\";\n  Endian[Endian[\"Big\"] = 1] = \"Big\";\n})(Endian || (Endian = {}));\n\n// This module specifier is intentionally a relative path to allow bundling the code directly\n// into the package.\n// @ng_package: ignore-cross-repo-import\n/**\n * Parse a `$localize` tagged string into a structure that can be used for translation or\n * extraction.\n *\n * See `ParsedMessage` for an example.\n */\nfunction parseMessage(messageParts, expressions, location, messagePartLocations, expressionLocations = []) {\n  const substitutions = {};\n  const substitutionLocations = {};\n  const associatedMessageIds = {};\n  const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);\n  const cleanedMessageParts = [metadata.text];\n  const placeholderNames = [];\n  let messageString = metadata.text;\n  for (let i = 1; i < messageParts.length; i++) {\n    const {\n      messagePart,\n      placeholderName = computePlaceholderName(i),\n      associatedMessageId\n    } = parsePlaceholder(messageParts[i], messageParts.raw[i]);\n    messageString += `{$${placeholderName}}${messagePart}`;\n    if (expressions !== undefined) {\n      substitutions[placeholderName] = expressions[i - 1];\n      substitutionLocations[placeholderName] = expressionLocations[i - 1];\n    }\n    placeholderNames.push(placeholderName);\n    if (associatedMessageId !== undefined) {\n      associatedMessageIds[placeholderName] = associatedMessageId;\n    }\n    cleanedMessageParts.push(messagePart);\n  }\n  const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');\n  const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter(id => id !== messageId) : [];\n  return {\n    id: messageId,\n    legacyIds,\n    substitutions,\n    substitutionLocations,\n    text: messageString,\n    customId: metadata.customId,\n    meaning: metadata.meaning || '',\n    description: metadata.description || '',\n    messageParts: cleanedMessageParts,\n    messagePartLocations,\n    placeholderNames,\n    associatedMessageIds,\n    location\n  };\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.\n *\n * If the message part has a metadata block this function will extract the `meaning`,\n * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties\n * are serialized in the string delimited by `|`, `@@` and `␟` respectively.\n *\n * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)\n *\n * For example:\n *\n * ```ts\n * `:meaning|description@@custom-id:`\n * `:meaning|@@custom-id:`\n * `:meaning|description:`\n * `:description@@custom-id:`\n * `:meaning|:`\n * `:description:`\n * `:@@custom-id:`\n * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing any metadata that was parsed from the message part.\n */\nfunction parseMetadata(cooked, raw) {\n  const {\n    text: messageString,\n    block\n  } = splitBlock(cooked, raw);\n  if (block === undefined) {\n    return {\n      text: messageString\n    };\n  } else {\n    const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);\n    const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);\n    let [meaning, description] = meaningAndDesc.split(MEANING_SEPARATOR, 2);\n    if (description === undefined) {\n      description = meaning;\n      meaning = undefined;\n    }\n    if (description === '') {\n      description = undefined;\n    }\n    return {\n      text: messageString,\n      meaning,\n      description,\n      customId,\n      legacyIds\n    };\n  }\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the\n * text.\n *\n * If the message part has a metadata block this function will extract the `placeholderName` and\n * `associatedMessageId` (if provided) from the block.\n *\n * These metadata properties are serialized in the string delimited by `@@`.\n *\n * For example:\n *\n * ```ts\n * `:placeholder-name@@associated-id:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the\n *     preceding placeholder, along with the static text that follows.\n */\nfunction parsePlaceholder(cooked, raw) {\n  const {\n    text: messagePart,\n    block\n  } = splitBlock(cooked, raw);\n  if (block === undefined) {\n    return {\n      messagePart\n    };\n  } else {\n    const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);\n    return {\n      messagePart,\n      placeholderName,\n      associatedMessageId\n    };\n  }\n}\n/**\n * Split a message part (`cooked` + `raw`) into an optional delimited \"block\" off the front and the\n * rest of the text of the message part.\n *\n * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the\n * start and end of the block.\n *\n * If the block is in the first message part then it will be metadata about the whole message:\n * meaning, description, id.  Otherwise it will be metadata about the immediately preceding\n * substitution: placeholder name.\n *\n * Since blocks are optional, it is possible that the content of a message block actually starts\n * with a block marker. In this case the marker must be escaped `\\:`.\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns An object containing the `text` of the message part and the text of the `block`, if it\n * exists.\n * @throws an error if the `block` is unterminated\n */\nfunction splitBlock(cooked, raw) {\n  if (raw.charAt(0) !== BLOCK_MARKER$1) {\n    return {\n      text: cooked\n    };\n  } else {\n    const endOfBlock = findEndOfBlock(cooked, raw);\n    return {\n      block: cooked.substring(1, endOfBlock),\n      text: cooked.substring(endOfBlock + 1)\n    };\n  }\n}\nfunction computePlaceholderName(index) {\n  return index === 1 ? 'PH' : `PH_${index - 1}`;\n}\n/**\n * Find the end of a \"marked block\" indicated by the first non-escaped colon.\n *\n * @param cooked The cooked string (where escaped chars have been processed)\n * @param raw The raw string (where escape sequences are still in place)\n *\n * @returns the index of the end of block marker\n * @throws an error if the block is unterminated\n */\nfunction findEndOfBlock(cooked, raw) {\n  for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {\n    if (raw[rawIndex] === '\\\\') {\n      rawIndex++;\n    } else if (cooked[cookedIndex] === BLOCK_MARKER$1) {\n      return cookedIndex;\n    }\n  }\n  throw new Error(`Unterminated $localize metadata block in \"${raw}\".`);\n}\n\n/**\n * Tag a template literal string for localization.\n *\n * For example:\n *\n * ```ts\n * $localize `some string to localize`\n * ```\n *\n * **Providing meaning, description and id**\n *\n * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n * string by pre-pending it with a colon delimited block of the form:\n *\n * ```ts\n * $localize`:meaning|description@@id:source message text`;\n *\n * $localize`:meaning|:source message text`;\n * $localize`:description:source message text`;\n * $localize`:@@id:source message text`;\n * ```\n *\n * This format is the same as that used for `i18n` markers in Angular templates. See the\n * [Angular i18n guide](guide/i18n/prepare#mark-text-in-component-template).\n *\n * **Naming placeholders**\n *\n * If the template literal string contains expressions, then the expressions will be automatically\n * associated with placeholder names for you.\n *\n * For example:\n *\n * ```ts\n * $localize `Hi ${name}! There are ${items.length} items.`;\n * ```\n *\n * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n *\n * The recommended practice is to name the placeholder associated with each expression though.\n *\n * Do this by providing the placeholder name wrapped in `:` characters directly after the\n * expression. These placeholder names are stripped out of the rendered localized string.\n *\n * For example, to name the `items.length` expression placeholder `itemCount` you write:\n *\n * ```ts\n * $localize `There are ${items.length}:itemCount: items`;\n * ```\n *\n * **Escaping colon markers**\n *\n * If you need to use a `:` character directly at the start of a tagged string that has no\n * metadata block, or directly after a substitution expression that has no name you must escape\n * the `:` by preceding it with a backslash:\n *\n * For example:\n *\n * ```ts\n * // message has a metadata block so no need to escape colon\n * $localize `:some description::this message starts with a colon (:)`;\n * // no metadata block so the colon must be escaped\n * $localize `\\:this message starts with a colon (:)`;\n * ```\n *\n * ```ts\n * // named substitution so no need to escape colon\n * $localize `${label}:label:: ${}`\n * // anonymous substitution so colon must be escaped\n * $localize `${label}\\: ${}`\n * ```\n *\n * **Processing localized strings:**\n *\n * There are three scenarios:\n *\n * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n * transpiler, removing the tag and replacing the template literal string with a translated\n * literal string from a collection of translations provided to the transpilation tool.\n *\n * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n * reorders the parts (static strings and expressions) of the template literal string with strings\n * from a collection of translations loaded at run-time.\n *\n * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n * the original template literal string without applying any translations to the parts. This\n * version is used during development or where there is no need to translate the localized\n * template literals.\n *\n * @param messageParts a collection of the static parts of the template string.\n * @param expressions a collection of the values of each placeholder in the template string.\n * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n *\n * @publicApi\n */\nconst $localize = function (messageParts, ...expressions) {\n  if ($localize.translate) {\n    // Don't use array expansion here to avoid the compiler adding `__read()` helper unnecessarily.\n    const translation = $localize.translate(messageParts, expressions);\n    messageParts = translation[0];\n    expressions = translation[1];\n  }\n  let message = stripBlock(messageParts[0], messageParts.raw[0]);\n  for (let i = 1; i < messageParts.length; i++) {\n    message += expressions[i - 1] + stripBlock(messageParts[i], messageParts.raw[i]);\n  }\n  return message;\n};\nconst BLOCK_MARKER = ':';\n/**\n * Strip a delimited \"block\" from the start of the `messagePart`, if it is found.\n *\n * If a marker character (:) actually appears in the content at the start of a tagged string or\n * after a substitution expression, where a block has not been provided the character must be\n * escaped with a backslash, `\\:`. This function checks for this by looking at the `raw`\n * messagePart, which should still contain the backslash.\n *\n * @param messagePart The cooked message part to process.\n * @param rawMessagePart The raw message part to check.\n * @returns the message part with the placeholder name stripped, if found.\n * @throws an error if the block is unterminated\n */\nfunction stripBlock(messagePart, rawMessagePart) {\n  return rawMessagePart.charAt(0) === BLOCK_MARKER ? messagePart.substring(findEndOfBlock(messagePart, rawMessagePart) + 1) : messagePart;\n}\nexport { $localize, BLOCK_MARKER$1 as BLOCK_MARKER, computeMsgId, findEndOfBlock, parseMessage, parseMetadata, splitBlock };", "map": {"version": 3, "names": ["BLOCK_MARKER$1", "MEANING_SEPARATOR", "ID_SEPARATOR", "LEGACY_ID_INDICATOR", "textEncoder", "fingerprint", "str", "TextEncoder", "utf8", "encode", "view", "DataView", "buffer", "byteOffset", "byteLength", "hi", "hash32", "length", "lo", "BigInt", "asUintN", "computeMsgId", "msg", "meaning", "msgFingerprint", "toString", "c", "a", "b", "index", "end", "getUint32", "res", "mix", "remainder", "getUint8", "<PERSON><PERSON>", "parseMessage", "messageParts", "expressions", "location", "messagePartLocations", "expressionLocations", "substitutions", "substitutionLocations", "associatedMessageIds", "metadata", "parseMetadata", "raw", "cleanedMessageParts", "text", "placeholder<PERSON><PERSON><PERSON>", "messageString", "i", "messagePart", "placeholder<PERSON><PERSON>", "computePlaceholderName", "associatedMessageId", "parsePlaceholder", "undefined", "push", "messageId", "customId", "legacyIds", "filter", "id", "description", "cooked", "block", "splitBlock", "meaningDescAndId", "split", "meaningAndDesc", "char<PERSON>t", "endOfBlock", "findEndOfBlock", "substring", "cookedIndex", "rawIndex", "Error", "$localize", "translate", "translation", "message", "stripBlock", "BLOCK_MARKER", "rawMessagePart"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/@angular/localize/fesm2022/localize-CajB9YLv.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The character used to mark the start and end of a \"block\" in a `$localize` tagged string.\n * A block can indicate metadata about the message or specify a name of a placeholder for a\n * substitution expressions.\n *\n * For example:\n *\n * ```ts\n * $localize`Hello, ${title}:title:!`;\n * $localize`:meaning|description@@id:source message text`;\n * ```\n */\nconst BLOCK_MARKER$1 = ':';\n/**\n * The marker used to separate a message's \"meaning\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:correct|Indicates that the user got the answer correct: Right!`;\n * $localize `:movement|Button label for moving to the right: Right!`;\n * ```\n */\nconst MEANING_SEPARATOR = '|';\n/**\n * The marker used to separate a message's custom \"id\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:A welcome message on the home page@@myApp-homepage-welcome: Welcome!`;\n * ```\n */\nconst ID_SEPARATOR = '@@';\n/**\n * The marker used to separate legacy message ids from the rest of a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:@@custom-id␟2df64767cd895a8fabe3e18b94b5b6b6f9e2e3f0: Welcome!`;\n * ```\n *\n * Note that this character is the \"symbol for the unit separator\" (␟) not the \"unit separator\n * character\" itself, since that has no visual representation. See https://graphemica.com/%E2%90%9F.\n *\n * Here is some background for the original \"unit separator character\":\n * https://stackoverflow.com/questions/8695118/whats-the-file-group-record-unit-separator-control-characters-and-its-usage\n */\nconst LEGACY_ID_INDICATOR = '\\u241F';\n\n/**\n * A lazily created TextEncoder instance for converting strings into UTF-8 bytes\n */\nlet textEncoder;\n/**\n * Compute the fingerprint of the given string\n *\n * The output is 64 bit number encoded as a decimal string\n *\n * based on:\n * https://github.com/google/closure-compiler/blob/master/src/com/google/javascript/jscomp/GoogleJsMessageIdGenerator.java\n */\nfunction fingerprint(str) {\n    textEncoder ??= new TextEncoder();\n    const utf8 = textEncoder.encode(str);\n    const view = new DataView(utf8.buffer, utf8.byteOffset, utf8.byteLength);\n    let hi = hash32(view, utf8.length, 0);\n    let lo = hash32(view, utf8.length, 102072);\n    if (hi == 0 && (lo == 0 || lo == 1)) {\n        hi = hi ^ 0x130f9bef;\n        lo = lo ^ -1801410264;\n    }\n    return (BigInt.asUintN(32, BigInt(hi)) << BigInt(32)) | BigInt.asUintN(32, BigInt(lo));\n}\nfunction computeMsgId(msg, meaning = '') {\n    let msgFingerprint = fingerprint(msg);\n    if (meaning) {\n        // Rotate the 64-bit message fingerprint one bit to the left and then add the meaning\n        // fingerprint.\n        msgFingerprint =\n            BigInt.asUintN(64, msgFingerprint << BigInt(1)) |\n                ((msgFingerprint >> BigInt(63)) & BigInt(1));\n        msgFingerprint += fingerprint(meaning);\n    }\n    return BigInt.asUintN(63, msgFingerprint).toString();\n}\nfunction hash32(view, length, c) {\n    let a = 0x9e3779b9, b = 0x9e3779b9;\n    let index = 0;\n    const end = length - 12;\n    for (; index <= end; index += 12) {\n        a += view.getUint32(index, true);\n        b += view.getUint32(index + 4, true);\n        c += view.getUint32(index + 8, true);\n        const res = mix(a, b, c);\n        (a = res[0]), (b = res[1]), (c = res[2]);\n    }\n    const remainder = length - index;\n    // the first byte of c is reserved for the length\n    c += length;\n    if (remainder >= 4) {\n        a += view.getUint32(index, true);\n        index += 4;\n        if (remainder >= 8) {\n            b += view.getUint32(index, true);\n            index += 4;\n            // Partial 32-bit word for c\n            if (remainder >= 9) {\n                c += view.getUint8(index++) << 8;\n            }\n            if (remainder >= 10) {\n                c += view.getUint8(index++) << 16;\n            }\n            if (remainder === 11) {\n                c += view.getUint8(index++) << 24;\n            }\n        }\n        else {\n            // Partial 32-bit word for b\n            if (remainder >= 5) {\n                b += view.getUint8(index++);\n            }\n            if (remainder >= 6) {\n                b += view.getUint8(index++) << 8;\n            }\n            if (remainder === 7) {\n                b += view.getUint8(index++) << 16;\n            }\n        }\n    }\n    else {\n        // Partial 32-bit word for a\n        if (remainder >= 1) {\n            a += view.getUint8(index++);\n        }\n        if (remainder >= 2) {\n            a += view.getUint8(index++) << 8;\n        }\n        if (remainder === 3) {\n            a += view.getUint8(index++) << 16;\n        }\n    }\n    return mix(a, b, c)[2];\n}\nfunction mix(a, b, c) {\n    a -= b;\n    a -= c;\n    a ^= c >>> 13;\n    b -= c;\n    b -= a;\n    b ^= a << 8;\n    c -= a;\n    c -= b;\n    c ^= b >>> 13;\n    a -= b;\n    a -= c;\n    a ^= c >>> 12;\n    b -= c;\n    b -= a;\n    b ^= a << 16;\n    c -= a;\n    c -= b;\n    c ^= b >>> 5;\n    a -= b;\n    a -= c;\n    a ^= c >>> 3;\n    b -= c;\n    b -= a;\n    b ^= a << 10;\n    c -= a;\n    c -= b;\n    c ^= b >>> 15;\n    return [a, b, c];\n}\n// Utils\nvar Endian;\n(function (Endian) {\n    Endian[Endian[\"Little\"] = 0] = \"Little\";\n    Endian[Endian[\"Big\"] = 1] = \"Big\";\n})(Endian || (Endian = {}));\n\n// This module specifier is intentionally a relative path to allow bundling the code directly\n// into the package.\n// @ng_package: ignore-cross-repo-import\n/**\n * Parse a `$localize` tagged string into a structure that can be used for translation or\n * extraction.\n *\n * See `ParsedMessage` for an example.\n */\nfunction parseMessage(messageParts, expressions, location, messagePartLocations, expressionLocations = []) {\n    const substitutions = {};\n    const substitutionLocations = {};\n    const associatedMessageIds = {};\n    const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);\n    const cleanedMessageParts = [metadata.text];\n    const placeholderNames = [];\n    let messageString = metadata.text;\n    for (let i = 1; i < messageParts.length; i++) {\n        const { messagePart, placeholderName = computePlaceholderName(i), associatedMessageId, } = parsePlaceholder(messageParts[i], messageParts.raw[i]);\n        messageString += `{$${placeholderName}}${messagePart}`;\n        if (expressions !== undefined) {\n            substitutions[placeholderName] = expressions[i - 1];\n            substitutionLocations[placeholderName] = expressionLocations[i - 1];\n        }\n        placeholderNames.push(placeholderName);\n        if (associatedMessageId !== undefined) {\n            associatedMessageIds[placeholderName] = associatedMessageId;\n        }\n        cleanedMessageParts.push(messagePart);\n    }\n    const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');\n    const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter((id) => id !== messageId) : [];\n    return {\n        id: messageId,\n        legacyIds,\n        substitutions,\n        substitutionLocations,\n        text: messageString,\n        customId: metadata.customId,\n        meaning: metadata.meaning || '',\n        description: metadata.description || '',\n        messageParts: cleanedMessageParts,\n        messagePartLocations,\n        placeholderNames,\n        associatedMessageIds,\n        location,\n    };\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.\n *\n * If the message part has a metadata block this function will extract the `meaning`,\n * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties\n * are serialized in the string delimited by `|`, `@@` and `␟` respectively.\n *\n * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)\n *\n * For example:\n *\n * ```ts\n * `:meaning|description@@custom-id:`\n * `:meaning|@@custom-id:`\n * `:meaning|description:`\n * `:description@@custom-id:`\n * `:meaning|:`\n * `:description:`\n * `:@@custom-id:`\n * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing any metadata that was parsed from the message part.\n */\nfunction parseMetadata(cooked, raw) {\n    const { text: messageString, block } = splitBlock(cooked, raw);\n    if (block === undefined) {\n        return { text: messageString };\n    }\n    else {\n        const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);\n        const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);\n        let [meaning, description] = meaningAndDesc.split(MEANING_SEPARATOR, 2);\n        if (description === undefined) {\n            description = meaning;\n            meaning = undefined;\n        }\n        if (description === '') {\n            description = undefined;\n        }\n        return { text: messageString, meaning, description, customId, legacyIds };\n    }\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the\n * text.\n *\n * If the message part has a metadata block this function will extract the `placeholderName` and\n * `associatedMessageId` (if provided) from the block.\n *\n * These metadata properties are serialized in the string delimited by `@@`.\n *\n * For example:\n *\n * ```ts\n * `:placeholder-name@@associated-id:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the\n *     preceding placeholder, along with the static text that follows.\n */\nfunction parsePlaceholder(cooked, raw) {\n    const { text: messagePart, block } = splitBlock(cooked, raw);\n    if (block === undefined) {\n        return { messagePart };\n    }\n    else {\n        const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);\n        return { messagePart, placeholderName, associatedMessageId };\n    }\n}\n/**\n * Split a message part (`cooked` + `raw`) into an optional delimited \"block\" off the front and the\n * rest of the text of the message part.\n *\n * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the\n * start and end of the block.\n *\n * If the block is in the first message part then it will be metadata about the whole message:\n * meaning, description, id.  Otherwise it will be metadata about the immediately preceding\n * substitution: placeholder name.\n *\n * Since blocks are optional, it is possible that the content of a message block actually starts\n * with a block marker. In this case the marker must be escaped `\\:`.\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns An object containing the `text` of the message part and the text of the `block`, if it\n * exists.\n * @throws an error if the `block` is unterminated\n */\nfunction splitBlock(cooked, raw) {\n    if (raw.charAt(0) !== BLOCK_MARKER$1) {\n        return { text: cooked };\n    }\n    else {\n        const endOfBlock = findEndOfBlock(cooked, raw);\n        return {\n            block: cooked.substring(1, endOfBlock),\n            text: cooked.substring(endOfBlock + 1),\n        };\n    }\n}\nfunction computePlaceholderName(index) {\n    return index === 1 ? 'PH' : `PH_${index - 1}`;\n}\n/**\n * Find the end of a \"marked block\" indicated by the first non-escaped colon.\n *\n * @param cooked The cooked string (where escaped chars have been processed)\n * @param raw The raw string (where escape sequences are still in place)\n *\n * @returns the index of the end of block marker\n * @throws an error if the block is unterminated\n */\nfunction findEndOfBlock(cooked, raw) {\n    for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {\n        if (raw[rawIndex] === '\\\\') {\n            rawIndex++;\n        }\n        else if (cooked[cookedIndex] === BLOCK_MARKER$1) {\n            return cookedIndex;\n        }\n    }\n    throw new Error(`Unterminated $localize metadata block in \"${raw}\".`);\n}\n\n/**\n * Tag a template literal string for localization.\n *\n * For example:\n *\n * ```ts\n * $localize `some string to localize`\n * ```\n *\n * **Providing meaning, description and id**\n *\n * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n * string by pre-pending it with a colon delimited block of the form:\n *\n * ```ts\n * $localize`:meaning|description@@id:source message text`;\n *\n * $localize`:meaning|:source message text`;\n * $localize`:description:source message text`;\n * $localize`:@@id:source message text`;\n * ```\n *\n * This format is the same as that used for `i18n` markers in Angular templates. See the\n * [Angular i18n guide](guide/i18n/prepare#mark-text-in-component-template).\n *\n * **Naming placeholders**\n *\n * If the template literal string contains expressions, then the expressions will be automatically\n * associated with placeholder names for you.\n *\n * For example:\n *\n * ```ts\n * $localize `Hi ${name}! There are ${items.length} items.`;\n * ```\n *\n * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n *\n * The recommended practice is to name the placeholder associated with each expression though.\n *\n * Do this by providing the placeholder name wrapped in `:` characters directly after the\n * expression. These placeholder names are stripped out of the rendered localized string.\n *\n * For example, to name the `items.length` expression placeholder `itemCount` you write:\n *\n * ```ts\n * $localize `There are ${items.length}:itemCount: items`;\n * ```\n *\n * **Escaping colon markers**\n *\n * If you need to use a `:` character directly at the start of a tagged string that has no\n * metadata block, or directly after a substitution expression that has no name you must escape\n * the `:` by preceding it with a backslash:\n *\n * For example:\n *\n * ```ts\n * // message has a metadata block so no need to escape colon\n * $localize `:some description::this message starts with a colon (:)`;\n * // no metadata block so the colon must be escaped\n * $localize `\\:this message starts with a colon (:)`;\n * ```\n *\n * ```ts\n * // named substitution so no need to escape colon\n * $localize `${label}:label:: ${}`\n * // anonymous substitution so colon must be escaped\n * $localize `${label}\\: ${}`\n * ```\n *\n * **Processing localized strings:**\n *\n * There are three scenarios:\n *\n * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n * transpiler, removing the tag and replacing the template literal string with a translated\n * literal string from a collection of translations provided to the transpilation tool.\n *\n * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n * reorders the parts (static strings and expressions) of the template literal string with strings\n * from a collection of translations loaded at run-time.\n *\n * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n * the original template literal string without applying any translations to the parts. This\n * version is used during development or where there is no need to translate the localized\n * template literals.\n *\n * @param messageParts a collection of the static parts of the template string.\n * @param expressions a collection of the values of each placeholder in the template string.\n * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n *\n * @publicApi\n */\nconst $localize = function (messageParts, ...expressions) {\n    if ($localize.translate) {\n        // Don't use array expansion here to avoid the compiler adding `__read()` helper unnecessarily.\n        const translation = $localize.translate(messageParts, expressions);\n        messageParts = translation[0];\n        expressions = translation[1];\n    }\n    let message = stripBlock(messageParts[0], messageParts.raw[0]);\n    for (let i = 1; i < messageParts.length; i++) {\n        message += expressions[i - 1] + stripBlock(messageParts[i], messageParts.raw[i]);\n    }\n    return message;\n};\nconst BLOCK_MARKER = ':';\n/**\n * Strip a delimited \"block\" from the start of the `messagePart`, if it is found.\n *\n * If a marker character (:) actually appears in the content at the start of a tagged string or\n * after a substitution expression, where a block has not been provided the character must be\n * escaped with a backslash, `\\:`. This function checks for this by looking at the `raw`\n * messagePart, which should still contain the backslash.\n *\n * @param messagePart The cooked message part to process.\n * @param rawMessagePart The raw message part to check.\n * @returns the message part with the placeholder name stripped, if found.\n * @throws an error if the block is unterminated\n */\nfunction stripBlock(messagePart, rawMessagePart) {\n    return rawMessagePart.charAt(0) === BLOCK_MARKER\n        ? messagePart.substring(findEndOfBlock(messagePart, rawMessagePart) + 1)\n        : messagePart;\n}\n\nexport { $localize, BLOCK_MARKER$1 as BLOCK_MARKER, computeMsgId, findEndOfBlock, parseMessage, parseMetadata, splitBlock };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,cAAc,GAAG,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,IAAI;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,QAAQ;;AAEpC;AACA;AACA;AACA,IAAIC,WAAW;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,GAAG,EAAE;EACtBF,WAAW,KAAK,IAAIG,WAAW,CAAC,CAAC;EACjC,MAAMC,IAAI,GAAGJ,WAAW,CAACK,MAAM,CAACH,GAAG,CAAC;EACpC,MAAMI,IAAI,GAAG,IAAIC,QAAQ,CAACH,IAAI,CAACI,MAAM,EAAEJ,IAAI,CAACK,UAAU,EAAEL,IAAI,CAACM,UAAU,CAAC;EACxE,IAAIC,EAAE,GAAGC,MAAM,CAACN,IAAI,EAAEF,IAAI,CAACS,MAAM,EAAE,CAAC,CAAC;EACrC,IAAIC,EAAE,GAAGF,MAAM,CAACN,IAAI,EAAEF,IAAI,CAACS,MAAM,EAAE,MAAM,CAAC;EAC1C,IAAIF,EAAE,IAAI,CAAC,KAAKG,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,CAAC,EAAE;IACjCH,EAAE,GAAGA,EAAE,GAAG,UAAU;IACpBG,EAAE,GAAGA,EAAE,GAAG,CAAC,UAAU;EACzB;EACA,OAAQC,MAAM,CAACC,OAAO,CAAC,EAAE,EAAED,MAAM,CAACJ,EAAE,CAAC,CAAC,IAAII,MAAM,CAAC,EAAE,CAAC,GAAIA,MAAM,CAACC,OAAO,CAAC,EAAE,EAAED,MAAM,CAACD,EAAE,CAAC,CAAC;AAC1F;AACA,SAASG,YAAYA,CAACC,GAAG,EAAEC,OAAO,GAAG,EAAE,EAAE;EACrC,IAAIC,cAAc,GAAGnB,WAAW,CAACiB,GAAG,CAAC;EACrC,IAAIC,OAAO,EAAE;IACT;IACA;IACAC,cAAc,GACVL,MAAM,CAACC,OAAO,CAAC,EAAE,EAAEI,cAAc,IAAIL,MAAM,CAAC,CAAC,CAAC,CAAC,GACzCK,cAAc,IAAIL,MAAM,CAAC,EAAE,CAAC,GAAIA,MAAM,CAAC,CAAC,CAAE;IACpDK,cAAc,IAAInB,WAAW,CAACkB,OAAO,CAAC;EAC1C;EACA,OAAOJ,MAAM,CAACC,OAAO,CAAC,EAAE,EAAEI,cAAc,CAAC,CAACC,QAAQ,CAAC,CAAC;AACxD;AACA,SAAST,MAAMA,CAACN,IAAI,EAAEO,MAAM,EAAES,CAAC,EAAE;EAC7B,IAAIC,CAAC,GAAG,UAAU;IAAEC,CAAC,GAAG,UAAU;EAClC,IAAIC,KAAK,GAAG,CAAC;EACb,MAAMC,GAAG,GAAGb,MAAM,GAAG,EAAE;EACvB,OAAOY,KAAK,IAAIC,GAAG,EAAED,KAAK,IAAI,EAAE,EAAE;IAC9BF,CAAC,IAAIjB,IAAI,CAACqB,SAAS,CAACF,KAAK,EAAE,IAAI,CAAC;IAChCD,CAAC,IAAIlB,IAAI,CAACqB,SAAS,CAACF,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC;IACpCH,CAAC,IAAIhB,IAAI,CAACqB,SAAS,CAACF,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC;IACpC,MAAMG,GAAG,GAAGC,GAAG,CAACN,CAAC,EAAEC,CAAC,EAAEF,CAAC,CAAC;IACvBC,CAAC,GAAGK,GAAG,CAAC,CAAC,CAAC,EAAIJ,CAAC,GAAGI,GAAG,CAAC,CAAC,CAAC,EAAIN,CAAC,GAAGM,GAAG,CAAC,CAAC,CAAE;EAC5C;EACA,MAAME,SAAS,GAAGjB,MAAM,GAAGY,KAAK;EAChC;EACAH,CAAC,IAAIT,MAAM;EACX,IAAIiB,SAAS,IAAI,CAAC,EAAE;IAChBP,CAAC,IAAIjB,IAAI,CAACqB,SAAS,CAACF,KAAK,EAAE,IAAI,CAAC;IAChCA,KAAK,IAAI,CAAC;IACV,IAAIK,SAAS,IAAI,CAAC,EAAE;MAChBN,CAAC,IAAIlB,IAAI,CAACqB,SAAS,CAACF,KAAK,EAAE,IAAI,CAAC;MAChCA,KAAK,IAAI,CAAC;MACV;MACA,IAAIK,SAAS,IAAI,CAAC,EAAE;QAChBR,CAAC,IAAIhB,IAAI,CAACyB,QAAQ,CAACN,KAAK,EAAE,CAAC,IAAI,CAAC;MACpC;MACA,IAAIK,SAAS,IAAI,EAAE,EAAE;QACjBR,CAAC,IAAIhB,IAAI,CAACyB,QAAQ,CAACN,KAAK,EAAE,CAAC,IAAI,EAAE;MACrC;MACA,IAAIK,SAAS,KAAK,EAAE,EAAE;QAClBR,CAAC,IAAIhB,IAAI,CAACyB,QAAQ,CAACN,KAAK,EAAE,CAAC,IAAI,EAAE;MACrC;IACJ,CAAC,MACI;MACD;MACA,IAAIK,SAAS,IAAI,CAAC,EAAE;QAChBN,CAAC,IAAIlB,IAAI,CAACyB,QAAQ,CAACN,KAAK,EAAE,CAAC;MAC/B;MACA,IAAIK,SAAS,IAAI,CAAC,EAAE;QAChBN,CAAC,IAAIlB,IAAI,CAACyB,QAAQ,CAACN,KAAK,EAAE,CAAC,IAAI,CAAC;MACpC;MACA,IAAIK,SAAS,KAAK,CAAC,EAAE;QACjBN,CAAC,IAAIlB,IAAI,CAACyB,QAAQ,CAACN,KAAK,EAAE,CAAC,IAAI,EAAE;MACrC;IACJ;EACJ,CAAC,MACI;IACD;IACA,IAAIK,SAAS,IAAI,CAAC,EAAE;MAChBP,CAAC,IAAIjB,IAAI,CAACyB,QAAQ,CAACN,KAAK,EAAE,CAAC;IAC/B;IACA,IAAIK,SAAS,IAAI,CAAC,EAAE;MAChBP,CAAC,IAAIjB,IAAI,CAACyB,QAAQ,CAACN,KAAK,EAAE,CAAC,IAAI,CAAC;IACpC;IACA,IAAIK,SAAS,KAAK,CAAC,EAAE;MACjBP,CAAC,IAAIjB,IAAI,CAACyB,QAAQ,CAACN,KAAK,EAAE,CAAC,IAAI,EAAE;IACrC;EACJ;EACA,OAAOI,GAAG,CAACN,CAAC,EAAEC,CAAC,EAAEF,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B;AACA,SAASO,GAAGA,CAACN,CAAC,EAAEC,CAAC,EAAEF,CAAC,EAAE;EAClBC,CAAC,IAAIC,CAAC;EACND,CAAC,IAAID,CAAC;EACNC,CAAC,IAAID,CAAC,KAAK,EAAE;EACbE,CAAC,IAAIF,CAAC;EACNE,CAAC,IAAID,CAAC;EACNC,CAAC,IAAID,CAAC,IAAI,CAAC;EACXD,CAAC,IAAIC,CAAC;EACND,CAAC,IAAIE,CAAC;EACNF,CAAC,IAAIE,CAAC,KAAK,EAAE;EACbD,CAAC,IAAIC,CAAC;EACND,CAAC,IAAID,CAAC;EACNC,CAAC,IAAID,CAAC,KAAK,EAAE;EACbE,CAAC,IAAIF,CAAC;EACNE,CAAC,IAAID,CAAC;EACNC,CAAC,IAAID,CAAC,IAAI,EAAE;EACZD,CAAC,IAAIC,CAAC;EACND,CAAC,IAAIE,CAAC;EACNF,CAAC,IAAIE,CAAC,KAAK,CAAC;EACZD,CAAC,IAAIC,CAAC;EACND,CAAC,IAAID,CAAC;EACNC,CAAC,IAAID,CAAC,KAAK,CAAC;EACZE,CAAC,IAAIF,CAAC;EACNE,CAAC,IAAID,CAAC;EACNC,CAAC,IAAID,CAAC,IAAI,EAAE;EACZD,CAAC,IAAIC,CAAC;EACND,CAAC,IAAIE,CAAC;EACNF,CAAC,IAAIE,CAAC,KAAK,EAAE;EACb,OAAO,CAACD,CAAC,EAAEC,CAAC,EAAEF,CAAC,CAAC;AACpB;AACA;AACA,IAAIU,MAAM;AACV,CAAC,UAAUA,MAAM,EAAE;EACfA,MAAM,CAACA,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACvCA,MAAM,CAACA,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;AACrC,CAAC,EAAEA,MAAM,KAAKA,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,YAAY,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,oBAAoB,EAAEC,mBAAmB,GAAG,EAAE,EAAE;EACvG,MAAMC,aAAa,GAAG,CAAC,CAAC;EACxB,MAAMC,qBAAqB,GAAG,CAAC,CAAC;EAChC,MAAMC,oBAAoB,GAAG,CAAC,CAAC;EAC/B,MAAMC,QAAQ,GAAGC,aAAa,CAACT,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC;EACpE,MAAMC,mBAAmB,GAAG,CAACH,QAAQ,CAACI,IAAI,CAAC;EAC3C,MAAMC,gBAAgB,GAAG,EAAE;EAC3B,IAAIC,aAAa,GAAGN,QAAQ,CAACI,IAAI;EACjC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,YAAY,CAACrB,MAAM,EAAEoC,CAAC,EAAE,EAAE;IAC1C,MAAM;MAAEC,WAAW;MAAEC,eAAe,GAAGC,sBAAsB,CAACH,CAAC,CAAC;MAAEI;IAAqB,CAAC,GAAGC,gBAAgB,CAACpB,YAAY,CAACe,CAAC,CAAC,EAAEf,YAAY,CAACU,GAAG,CAACK,CAAC,CAAC,CAAC;IACjJD,aAAa,IAAI,KAAKG,eAAe,IAAID,WAAW,EAAE;IACtD,IAAIf,WAAW,KAAKoB,SAAS,EAAE;MAC3BhB,aAAa,CAACY,eAAe,CAAC,GAAGhB,WAAW,CAACc,CAAC,GAAG,CAAC,CAAC;MACnDT,qBAAqB,CAACW,eAAe,CAAC,GAAGb,mBAAmB,CAACW,CAAC,GAAG,CAAC,CAAC;IACvE;IACAF,gBAAgB,CAACS,IAAI,CAACL,eAAe,CAAC;IACtC,IAAIE,mBAAmB,KAAKE,SAAS,EAAE;MACnCd,oBAAoB,CAACU,eAAe,CAAC,GAAGE,mBAAmB;IAC/D;IACAR,mBAAmB,CAACW,IAAI,CAACN,WAAW,CAAC;EACzC;EACA,MAAMO,SAAS,GAAGf,QAAQ,CAACgB,QAAQ,IAAIzC,YAAY,CAAC+B,aAAa,EAAEN,QAAQ,CAACvB,OAAO,IAAI,EAAE,CAAC;EAC1F,MAAMwC,SAAS,GAAGjB,QAAQ,CAACiB,SAAS,GAAGjB,QAAQ,CAACiB,SAAS,CAACC,MAAM,CAAEC,EAAE,IAAKA,EAAE,KAAKJ,SAAS,CAAC,GAAG,EAAE;EAC/F,OAAO;IACHI,EAAE,EAAEJ,SAAS;IACbE,SAAS;IACTpB,aAAa;IACbC,qBAAqB;IACrBM,IAAI,EAAEE,aAAa;IACnBU,QAAQ,EAAEhB,QAAQ,CAACgB,QAAQ;IAC3BvC,OAAO,EAAEuB,QAAQ,CAACvB,OAAO,IAAI,EAAE;IAC/B2C,WAAW,EAAEpB,QAAQ,CAACoB,WAAW,IAAI,EAAE;IACvC5B,YAAY,EAAEW,mBAAmB;IACjCR,oBAAoB;IACpBU,gBAAgB;IAChBN,oBAAoB;IACpBL;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,aAAaA,CAACoB,MAAM,EAAEnB,GAAG,EAAE;EAChC,MAAM;IAAEE,IAAI,EAAEE,aAAa;IAAEgB;EAAM,CAAC,GAAGC,UAAU,CAACF,MAAM,EAAEnB,GAAG,CAAC;EAC9D,IAAIoB,KAAK,KAAKT,SAAS,EAAE;IACrB,OAAO;MAAET,IAAI,EAAEE;IAAc,CAAC;EAClC,CAAC,MACI;IACD,MAAM,CAACkB,gBAAgB,EAAE,GAAGP,SAAS,CAAC,GAAGK,KAAK,CAACG,KAAK,CAACpE,mBAAmB,CAAC;IACzE,MAAM,CAACqE,cAAc,EAAEV,QAAQ,CAAC,GAAGQ,gBAAgB,CAACC,KAAK,CAACrE,YAAY,EAAE,CAAC,CAAC;IAC1E,IAAI,CAACqB,OAAO,EAAE2C,WAAW,CAAC,GAAGM,cAAc,CAACD,KAAK,CAACtE,iBAAiB,EAAE,CAAC,CAAC;IACvE,IAAIiE,WAAW,KAAKP,SAAS,EAAE;MAC3BO,WAAW,GAAG3C,OAAO;MACrBA,OAAO,GAAGoC,SAAS;IACvB;IACA,IAAIO,WAAW,KAAK,EAAE,EAAE;MACpBA,WAAW,GAAGP,SAAS;IAC3B;IACA,OAAO;MAAET,IAAI,EAAEE,aAAa;MAAE7B,OAAO;MAAE2C,WAAW;MAAEJ,QAAQ;MAAEC;IAAU,CAAC;EAC7E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,gBAAgBA,CAACS,MAAM,EAAEnB,GAAG,EAAE;EACnC,MAAM;IAAEE,IAAI,EAAEI,WAAW;IAAEc;EAAM,CAAC,GAAGC,UAAU,CAACF,MAAM,EAAEnB,GAAG,CAAC;EAC5D,IAAIoB,KAAK,KAAKT,SAAS,EAAE;IACrB,OAAO;MAAEL;IAAY,CAAC;EAC1B,CAAC,MACI;IACD,MAAM,CAACC,eAAe,EAAEE,mBAAmB,CAAC,GAAGW,KAAK,CAACG,KAAK,CAACrE,YAAY,CAAC;IACxE,OAAO;MAAEoD,WAAW;MAAEC,eAAe;MAAEE;IAAoB,CAAC;EAChE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,UAAUA,CAACF,MAAM,EAAEnB,GAAG,EAAE;EAC7B,IAAIA,GAAG,CAACyB,MAAM,CAAC,CAAC,CAAC,KAAKzE,cAAc,EAAE;IAClC,OAAO;MAAEkD,IAAI,EAAEiB;IAAO,CAAC;EAC3B,CAAC,MACI;IACD,MAAMO,UAAU,GAAGC,cAAc,CAACR,MAAM,EAAEnB,GAAG,CAAC;IAC9C,OAAO;MACHoB,KAAK,EAAED,MAAM,CAACS,SAAS,CAAC,CAAC,EAAEF,UAAU,CAAC;MACtCxB,IAAI,EAAEiB,MAAM,CAACS,SAAS,CAACF,UAAU,GAAG,CAAC;IACzC,CAAC;EACL;AACJ;AACA,SAASlB,sBAAsBA,CAAC3B,KAAK,EAAE;EACnC,OAAOA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,MAAMA,KAAK,GAAG,CAAC,EAAE;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8C,cAAcA,CAACR,MAAM,EAAEnB,GAAG,EAAE;EACjC,KAAK,IAAI6B,WAAW,GAAG,CAAC,EAAEC,QAAQ,GAAG,CAAC,EAAED,WAAW,GAAGV,MAAM,CAAClD,MAAM,EAAE4D,WAAW,EAAE,EAAEC,QAAQ,EAAE,EAAE;IAC5F,IAAI9B,GAAG,CAAC8B,QAAQ,CAAC,KAAK,IAAI,EAAE;MACxBA,QAAQ,EAAE;IACd,CAAC,MACI,IAAIX,MAAM,CAACU,WAAW,CAAC,KAAK7E,cAAc,EAAE;MAC7C,OAAO6E,WAAW;IACtB;EACJ;EACA,MAAM,IAAIE,KAAK,CAAC,6CAA6C/B,GAAG,IAAI,CAAC;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgC,SAAS,GAAG,SAAAA,CAAU1C,YAAY,EAAE,GAAGC,WAAW,EAAE;EACtD,IAAIyC,SAAS,CAACC,SAAS,EAAE;IACrB;IACA,MAAMC,WAAW,GAAGF,SAAS,CAACC,SAAS,CAAC3C,YAAY,EAAEC,WAAW,CAAC;IAClED,YAAY,GAAG4C,WAAW,CAAC,CAAC,CAAC;IAC7B3C,WAAW,GAAG2C,WAAW,CAAC,CAAC,CAAC;EAChC;EACA,IAAIC,OAAO,GAAGC,UAAU,CAAC9C,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9D,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,YAAY,CAACrB,MAAM,EAAEoC,CAAC,EAAE,EAAE;IAC1C8B,OAAO,IAAI5C,WAAW,CAACc,CAAC,GAAG,CAAC,CAAC,GAAG+B,UAAU,CAAC9C,YAAY,CAACe,CAAC,CAAC,EAAEf,YAAY,CAACU,GAAG,CAACK,CAAC,CAAC,CAAC;EACpF;EACA,OAAO8B,OAAO;AAClB,CAAC;AACD,MAAME,YAAY,GAAG,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,UAAUA,CAAC9B,WAAW,EAAEgC,cAAc,EAAE;EAC7C,OAAOA,cAAc,CAACb,MAAM,CAAC,CAAC,CAAC,KAAKY,YAAY,GAC1C/B,WAAW,CAACsB,SAAS,CAACD,cAAc,CAACrB,WAAW,EAAEgC,cAAc,CAAC,GAAG,CAAC,CAAC,GACtEhC,WAAW;AACrB;AAEA,SAAS0B,SAAS,EAAEhF,cAAc,IAAIqF,YAAY,EAAEhE,YAAY,EAAEsD,cAAc,EAAEtC,YAAY,EAAEU,aAAa,EAAEsB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}