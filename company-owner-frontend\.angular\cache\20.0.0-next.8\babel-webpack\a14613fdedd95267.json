{"ast": null, "code": "const latlong = {};\nlatlong['AU'] = {\n  latitude: -27,\n  longitude: 133\n};\nlatlong['BR'] = {\n  latitude: -10,\n  longitude: -55\n};\nlatlong['BW'] = {\n  latitude: -22,\n  longitude: 24\n};\nlatlong['IN'] = {\n  latitude: 20,\n  longitude: 77\n};\nlatlong['KE'] = {\n  latitude: 1,\n  longitude: 38\n};\nlatlong['MX'] = {\n  latitude: 23,\n  longitude: -102\n};\nlatlong['MY'] = {\n  latitude: 2.5,\n  longitude: 112.5\n};\nlatlong['NI'] = {\n  latitude: 13,\n  longitude: -85\n};\nlatlong['NZ'] = {\n  latitude: -41,\n  longitude: 174\n};\nlatlong['PH'] = {\n  latitude: 13,\n  longitude: 122\n};\nlatlong['PL'] = {\n  latitude: 52,\n  longitude: 20\n};\nlatlong['RU'] = {\n  latitude: 60,\n  longitude: 100\n};\nlatlong['TH'] = {\n  latitude: 15,\n  longitude: 100\n};\nlatlong['ZA'] = {\n  latitude: -29,\n  longitude: 24\n};\nexport default latlong;", "map": {"version": 3, "names": ["latlong", "latitude", "longitude"], "sources": ["D:/employee-survey-app/company-owner-frontend/src/fake-data/map_data.js"], "sourcesContent": ["const latlong = {};\r\nlatlong['AU'] = {\r\n  latitude: -27,\r\n  longitude: 133\r\n};\r\nlatlong['BR'] = {\r\n  latitude: -10,\r\n  longitude: -55\r\n};\r\nlatlong['BW'] = {\r\n  latitude: -22,\r\n  longitude: 24\r\n};\r\nlatlong['IN'] = {\r\n  latitude: 20,\r\n  longitude: 77\r\n};\r\nlatlong['KE'] = {\r\n  latitude: 1,\r\n  longitude: 38\r\n};\r\nlatlong['MX'] = {\r\n  latitude: 23,\r\n  longitude: -102\r\n};\r\nlatlong['MY'] = {\r\n  latitude: 2.5,\r\n  longitude: 112.5\r\n};\r\nlatlong['NI'] = {\r\n  latitude: 13,\r\n  longitude: -85\r\n};\r\nlatlong['NZ'] = {\r\n  latitude: -41,\r\n  longitude: 174\r\n};\r\nlatlong['PH'] = {\r\n  latitude: 13,\r\n  longitude: 122\r\n};\r\nlatlong['PL'] = {\r\n  latitude: 52,\r\n  longitude: 20\r\n};\r\nlatlong['RU'] = {\r\n  latitude: 60,\r\n  longitude: 100\r\n};\r\nlatlong['TH'] = {\r\n  latitude: 15,\r\n  longitude: 100\r\n};\r\nlatlong['ZA'] = {\r\n  latitude: -29,\r\n  longitude: 24\r\n};\r\n\r\nexport default latlong;\r\n"], "mappings": "AAAA,MAAMA,OAAO,GAAG,CAAC,CAAC;AAClBA,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,CAAC,EAAE;EACbC,SAAS,EAAE;AACb,CAAC;AACDF,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,CAAC,EAAE;EACbC,SAAS,EAAE,CAAC;AACd,CAAC;AACDF,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,CAAC,EAAE;EACbC,SAAS,EAAE;AACb,CAAC;AACDF,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE;AACb,CAAC;AACDF,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,CAAC;EACXC,SAAS,EAAE;AACb,CAAC;AACDF,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE,CAAC;AACd,CAAC;AACDF,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,GAAG;EACbC,SAAS,EAAE;AACb,CAAC;AACDF,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE,CAAC;AACd,CAAC;AACDF,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,CAAC,EAAE;EACbC,SAAS,EAAE;AACb,CAAC;AACDF,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE;AACb,CAAC;AACDF,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE;AACb,CAAC;AACDF,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE;AACb,CAAC;AACDF,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE;AACb,CAAC;AACDF,OAAO,CAAC,IAAI,CAAC,GAAG;EACdC,QAAQ,EAAE,CAAC,EAAE;EACbC,SAAS,EAAE;AACb,CAAC;AAED,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}