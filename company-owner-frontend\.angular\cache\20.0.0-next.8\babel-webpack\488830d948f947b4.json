{"ast": null, "code": "// project import\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../theme/shared/components/card/card.component\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nfunction BasicCollapseComponent_ng_template_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.loremText, \" \");\n  }\n}\nfunction BasicCollapseComponent_ng_template_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.loremText, \" \");\n  }\n}\nfunction BasicCollapseComponent_ng_template_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.loremText, \" \");\n  }\n}\nfunction BasicCollapseComponent_ng_template_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.loremText, \" \");\n  }\n}\nfunction BasicCollapseComponent_ng_template_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.loremText, \" \");\n  }\n}\nexport default class BasicCollapseComponent {\n  constructor() {\n    // private props\n    this.isCollapsed = true;\n    this.multiCollapsed1 = true;\n    this.multiCollapsed2 = true;\n    this.loremText = \"Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident. Ad vegan excepteur butcher vice lomo. Leggings occaecat craft beer farm-to-table, raw denim aesthetic synth nesciunt you probably haven't heard of them accusamus labore sustainable VHS.\";\n  }\n  static {\n    this.ɵfac = function BasicCollapseComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BasicCollapseComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BasicCollapseComponent,\n      selectors: [[\"app-basic-collapse\"]],\n      decls: 95,\n      vars: 9,\n      consts: [[\"collapse\", \"ngbCollapse\"], [\"accordion\", \"ngbAccordion\"], [1, \"row\"], [1, \"col-sm-12\"], [1, \"mb-3\"], [1, \"card\"], [1, \"card-header\"], [\"type\", \"button\", \"title\", \"Toggle Collapse\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"keydown\", \"keyup\"], [\"type\", \"button\", \"aria-controls\", \"collapseExample\", 1, \"btn\", \"btn-primary\", \"ms-2\", 3, \"click\"], [3, \"ngbCollapseChange\", \"ngbCollapse\"], [1, \"card-body\"], [1, \"col-sm-12\", \"mb-3\"], [1, \"card\", \"mb-0\"], [\"aria-controls\", \"multiCollapseExample1\", 1, \"btn\", \"btn-primary\", \"text-white\", \"me-2\", \"mb-2\", 3, \"click\"], [\"type\", \"button\", \"aria-controls\", \"multiCollapseExample2\", 1, \"btn\", \"btn-primary\", \"me-2\", \"mb-2\", 3, \"click\"], [\"type\", \"button\", \"aria-controls\", \"multiCollapseExample1 multiCollapseExample2\", 1, \"btn\", \"btn-primary\", \"mb-2\", 3, \"click\"], [1, \"col-sm-6\"], [\"id\", \"multiCollapseExample1\", 1, \"multi-collapse\", \"mt-2\", 3, \"ngbCollapse\"], [1, \"mb-0\"], [\"id\", \"multiCollapseExample2\", 1, \"multi-collapse\", \"mt-2\", 3, \"ngbCollapse\"], [\"cardTitle\", \"Accordion\"], [\"ngbAccordion\", \"\"], [\"ngbAccordionItem\", \"\", 3, \"collapsed\"], [\"ngbAccordionHeader\", \"\"], [\"ngbAccordionButton\", \"\"], [\"ngbAccordionCollapse\", \"\"], [\"ngbAccordionBody\", \"\"], [\"ngbAccordionItem\", \"\"], [\"ngbAccordionItem\", \"\", 3, \"disabled\"], [\"cardTitle\", \"Toggle Accordion\"], [\"ngbAccordionItem\", \"first\"], [\"ngbAccordionItem\", \"second\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", \"me-2\", 3, \"click\"]],\n      template: function BasicCollapseComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"h5\", 4);\n          i0.ɵɵtext(3, \"Basic Collapse\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"hr\");\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function BasicCollapseComponent_Template_button_click_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const collapse_r2 = i0.ɵɵreference(12);\n            return i0.ɵɵresetView(collapse_r2.toggle());\n          })(\"keydown\", function BasicCollapseComponent_Template_button_keydown_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const collapse_r2 = i0.ɵɵreference(12);\n            return i0.ɵɵresetView(collapse_r2.toggle());\n          })(\"keyup\", function BasicCollapseComponent_Template_button_keyup_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const collapse_r2 = i0.ɵɵreference(12);\n            return i0.ɵɵresetView(collapse_r2.toggle());\n          });\n          i0.ɵɵtext(8, \" Toggle with Function \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function BasicCollapseComponent_Template_button_click_9_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.isCollapsed = !ctx.isCollapsed);\n          });\n          i0.ɵɵtext(10, \" Toggle with Two Way Binding \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 9, 0);\n          i0.ɵɵtwoWayListener(\"ngbCollapseChange\", function BasicCollapseComponent_Template_div_ngbCollapseChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.isCollapsed, $event) || (ctx.isCollapsed = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\");\n          i0.ɵɵtext(15, \"You can collapse this card by clicking one Toggle button\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(16, \"div\", 11)(17, \"h5\", 4);\n          i0.ɵɵtext(18, \"Multiple Targets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"hr\");\n          i0.ɵɵelementStart(20, \"div\", 12)(21, \"div\", 6)(22, \"a\", 13);\n          i0.ɵɵlistener(\"click\", function BasicCollapseComponent_Template_a_click_22_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.multiCollapsed1 = !ctx.multiCollapsed1);\n          });\n          i0.ɵɵtext(23, \" Toggle first element \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function BasicCollapseComponent_Template_button_click_24_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.multiCollapsed2 = !ctx.multiCollapsed2);\n          });\n          i0.ɵɵtext(25, \" Toggle second element \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function BasicCollapseComponent_Template_button_click_26_listener() {\n            i0.ɵɵrestoreView(_r1);\n            ctx.multiCollapsed1 = !ctx.multiCollapsed1;\n            return i0.ɵɵresetView(ctx.multiCollapsed2 = !ctx.multiCollapsed2);\n          });\n          i0.ɵɵtext(27, \" Toggle both elements \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 2)(29, \"div\", 16)(30, \"div\", 17)(31, \"div\", 5)(32, \"div\", 10)(33, \"p\", 18);\n          i0.ɵɵtext(34, \" Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident. \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(35, \"div\", 16)(36, \"div\", 19)(37, \"div\", 5)(38, \"div\", 10)(39, \"p\", 18);\n          i0.ɵɵtext(40, \" Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident. \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(41, \"div\", 3)(42, \"app-card\", 20)(43, \"div\", 21)(44, \"div\", 22)(45, \"h2\", 23)(46, \"button\", 24);\n          i0.ɵɵtext(47, \"Simple\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 25)(49, \"div\", 26);\n          i0.ɵɵtemplate(50, BasicCollapseComponent_ng_template_50_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(51, \"div\", 27)(52, \"h2\", 23)(53, \"button\", 24)(54, \"span\");\n          i0.ɵɵtext(55, \" \\u2605 \");\n          i0.ɵɵelementStart(56, \"b\");\n          i0.ɵɵtext(57, \"Fancy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(58, \" title \\u2605 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"div\", 25)(60, \"div\", 26);\n          i0.ɵɵtemplate(61, BasicCollapseComponent_ng_template_61_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(62, \"div\", 28)(63, \"h2\", 23)(64, \"button\", 24);\n          i0.ɵɵtext(65, \"Disabled\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"div\", 25)(67, \"div\", 26);\n          i0.ɵɵtemplate(68, BasicCollapseComponent_ng_template_68_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(69, \"app-card\", 29)(70, \"div\", 21, 1)(72, \"div\", 30)(73, \"h2\", 23)(74, \"button\", 24);\n          i0.ɵɵtext(75, \"First panel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"div\", 25)(77, \"div\", 26);\n          i0.ɵɵtemplate(78, BasicCollapseComponent_ng_template_78_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(79, \"div\", 31)(80, \"h2\", 23)(81, \"button\", 24);\n          i0.ɵɵtext(82, \"Second panel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(83, \"div\", 25)(84, \"div\", 26);\n          i0.ɵɵtemplate(85, BasicCollapseComponent_ng_template_85_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(86, \"hr\");\n          i0.ɵɵelementStart(87, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function BasicCollapseComponent_Template_button_click_87_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const accordion_r4 = i0.ɵɵreference(71);\n            return i0.ɵɵresetView(accordion_r4.toggle(\"first\"));\n          });\n          i0.ɵɵtext(88, \"Toggle first\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function BasicCollapseComponent_Template_button_click_89_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const accordion_r4 = i0.ɵɵreference(71);\n            return i0.ɵɵresetView(accordion_r4.toggle(\"second\"));\n          });\n          i0.ɵɵtext(90, \"Toggle second\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function BasicCollapseComponent_Template_button_click_91_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const accordion_r4 = i0.ɵɵreference(71);\n            return i0.ɵɵresetView(accordion_r4.expandAll());\n          });\n          i0.ɵɵtext(92, \"Expand all\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function BasicCollapseComponent_Template_button_click_93_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const accordion_r4 = i0.ɵɵreference(71);\n            return i0.ɵɵresetView(accordion_r4.collapseAll());\n          });\n          i0.ɵɵtext(94, \"Collapse all\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵattribute(\"aria-expanded\", !ctx.isCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵattribute(\"aria-expanded\", !ctx.isCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngbCollapse\", ctx.isCollapsed);\n          i0.ɵɵadvance(11);\n          i0.ɵɵattribute(\"aria-expanded\", !ctx.multiCollapsed1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵattribute(\"aria-expanded\", !ctx.multiCollapsed2);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngbCollapse\", ctx.multiCollapsed1);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngbCollapse\", ctx.multiCollapsed2);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"collapsed\", false);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"disabled\", true);\n        }\n      },\n      dependencies: [SharedModule, i1.CardComponent, i2.NgbAccordionButton, i2.NgbAccordionDirective, i2.NgbAccordionItem, i2.NgbAccordionHeader, i2.NgbAccordionBody, i2.NgbAccordionCollapse, i2.NgbCollapse],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "i0", "ɵɵtext", "ɵɵtextInterpolate1", "ctx_r2", "loremText", "BasicCollapseComponent", "constructor", "isCollapsed", "multiCollapsed1", "multiCollapsed2", "selectors", "decls", "vars", "consts", "template", "BasicCollapseComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "BasicCollapseComponent_Template_button_click_7_listener", "ɵɵrestoreView", "_r1", "collapse_r2", "ɵɵreference", "ɵɵresetView", "toggle", "BasicCollapseComponent_Template_button_keydown_7_listener", "BasicCollapseComponent_Template_button_keyup_7_listener", "BasicCollapseComponent_Template_button_click_9_listener", "ɵɵtwoWayListener", "BasicCollapseComponent_Template_div_ngbCollapseChange_11_listener", "$event", "ɵɵtwoWayBindingSet", "BasicCollapseComponent_Template_a_click_22_listener", "BasicCollapseComponent_Template_button_click_24_listener", "BasicCollapseComponent_Template_button_click_26_listener", "ɵɵtemplate", "BasicCollapseComponent_ng_template_50_Template", "BasicCollapseComponent_ng_template_61_Template", "BasicCollapseComponent_ng_template_68_Template", "BasicCollapseComponent_ng_template_78_Template", "BasicCollapseComponent_ng_template_85_Template", "BasicCollapseComponent_Template_button_click_87_listener", "accordion_r4", "BasicCollapseComponent_Template_button_click_89_listener", "BasicCollapseComponent_Template_button_click_91_listener", "expandAll", "BasicCollapseComponent_Template_button_click_93_listener", "collapseAll", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵproperty", "i1", "CardComponent", "i2", "NgbAccordion<PERSON><PERSON>on", "NgbAccordionDirective", "NgbAccordionItem", "NgbA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NgbAccordionBody", "NgbAccordionCollapse", "NgbCollapse", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\ui-elements\\ui-basic\\basic-collapse\\basic-collapse.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\ui-elements\\ui-basic\\basic-collapse\\basic-collapse.component.html"], "sourcesContent": ["// angular import\r\nimport { Component } from '@angular/core';\r\n\r\n// project import\r\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\r\n\r\n@Component({\r\n  selector: 'app-basic-collapse',\r\n  standalone: true,\r\n  imports: [SharedModule],\r\n  templateUrl: './basic-collapse.component.html',\r\n  styleUrls: ['./basic-collapse.component.scss']\r\n})\r\nexport default class BasicCollapseComponent {\r\n  // private props\r\n  isCollapsed = true;\r\n  multiCollapsed1 = true;\r\n  multiCollapsed2 = true;\r\n  loremText =\r\n    \"Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident. Ad vegan excepteur butcher vice lomo. Leggings occaecat craft beer farm-to-table, raw denim aesthetic synth nesciunt you probably haven't heard of them accusamus labore sustainable VHS.\";\r\n}\r\n", "<div class=\"row\">\r\n  <!-- [ basic-collapse ] start -->\r\n  <div class=\"col-sm-12\">\r\n    <h5 class=\"mb-3\">Basic Collapse</h5>\r\n    <hr />\r\n    <div class=\"card\">\r\n      <div class=\"card-header\">\r\n        <button\r\n          type=\"button\"\r\n          class=\"btn btn-primary\"\r\n          (click)=\"collapse.toggle()\"\r\n          (keydown)=\"collapse.toggle()\"\r\n          (keyup)=\"collapse.toggle()\"\r\n          [attr.aria-expanded]=\"!isCollapsed\"\r\n          title=\"Toggle Collapse\"\r\n        >\r\n          Toggle with Function\r\n        </button>\r\n        <button\r\n          type=\"button\"\r\n          class=\"btn btn-primary ms-2\"\r\n          (click)=\"isCollapsed = !isCollapsed\"\r\n          [attr.aria-expanded]=\"!isCollapsed\"\r\n          aria-controls=\"collapseExample\"\r\n        >\r\n          Toggle with Two Way Binding\r\n        </button>\r\n      </div>\r\n      <div #collapse=\"ngbCollapse\" [(ngbCollapse)]=\"isCollapsed\">\r\n        <div class=\"card-body\">\r\n          <div>You can collapse this card by clicking one Toggle button</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <!-- [ basic-collapse ] end -->\r\n  <!-- [ multiple-collapse ] start -->\r\n  <div class=\"col-sm-12 mb-3\">\r\n    <h5 class=\"mb-3\">Multiple Targets</h5>\r\n    <hr />\r\n    <div class=\"card mb-0\">\r\n      <div class=\"card-header\">\r\n        <a\r\n          class=\"btn btn-primary text-white me-2 mb-2\"\r\n          (click)=\"multiCollapsed1 = !multiCollapsed1\"\r\n          [attr.aria-expanded]=\"!multiCollapsed1\"\r\n          aria-controls=\"multiCollapseExample1\"\r\n        >\r\n          Toggle first element\r\n        </a>\r\n        <button\r\n          class=\"btn btn-primary me-2 mb-2\"\r\n          (click)=\"multiCollapsed2 = !multiCollapsed2\"\r\n          [attr.aria-expanded]=\"!multiCollapsed2\"\r\n          type=\"button\"\r\n          aria-controls=\"multiCollapseExample2\"\r\n        >\r\n          Toggle second element\r\n        </button>\r\n        <button\r\n          class=\"btn btn-primary mb-2\"\r\n          type=\"button\"\r\n          (click)=\"multiCollapsed1 = !multiCollapsed1; multiCollapsed2 = !multiCollapsed2\"\r\n          aria-controls=\"multiCollapseExample1 multiCollapseExample2\"\r\n        >\r\n          Toggle both elements\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"row\">\r\n      <div class=\"col-sm-6\">\r\n        <div class=\"multi-collapse mt-2\" id=\"multiCollapseExample1\" [ngbCollapse]=\"multiCollapsed1\">\r\n          <div class=\"card\">\r\n            <div class=\"card-body\">\r\n              <p class=\"mb-0\">\r\n                Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. Nihil anim keffiyeh\r\n                helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-sm-6\">\r\n        <div class=\"multi-collapse mt-2\" id=\"multiCollapseExample2\" [ngbCollapse]=\"multiCollapsed2\">\r\n          <div class=\"card\">\r\n            <div class=\"card-body\">\r\n              <p class=\"mb-0\">\r\n                Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. Nihil anim keffiyeh\r\n                helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <!-- [ multiple-collapse ] end -->\r\n  <!-- [ accordion-collapse ] start -->\r\n  <div class=\"col-sm-12\">\r\n    <app-card cardTitle=\"Accordion\">\r\n      <div ngbAccordion>\r\n        <div ngbAccordionItem [collapsed]=\"false\">\r\n          <h2 ngbAccordionHeader>\r\n            <button ngbAccordionButton>Simple</button>\r\n          </h2>\r\n          <div ngbAccordionCollapse>\r\n            <div ngbAccordionBody>\r\n              <ng-template>\r\n                {{ loremText }}\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div ngbAccordionItem>\r\n          <h2 ngbAccordionHeader>\r\n            <button ngbAccordionButton>\r\n              <span>\r\n                &#9733;\r\n                <b>Fancy</b>\r\n                title &#9733;\r\n              </span>\r\n            </button>\r\n          </h2>\r\n          <div ngbAccordionCollapse>\r\n            <div ngbAccordionBody>\r\n              <ng-template>\r\n                {{ loremText }}\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div ngbAccordionItem [disabled]=\"true\">\r\n          <h2 ngbAccordionHeader>\r\n            <button ngbAccordionButton>Disabled</button>\r\n          </h2>\r\n          <div ngbAccordionCollapse>\r\n            <div ngbAccordionBody>\r\n              <ng-template>\r\n                {{ loremText }}\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n\r\n    <app-card cardTitle=\"Toggle Accordion\">\r\n      <div ngbAccordion #accordion=\"ngbAccordion\">\r\n        <div ngbAccordionItem=\"first\">\r\n          <h2 ngbAccordionHeader>\r\n            <button ngbAccordionButton>First panel</button>\r\n          </h2>\r\n          <div ngbAccordionCollapse>\r\n            <div ngbAccordionBody>\r\n              <ng-template>\r\n                {{ loremText }}\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div ngbAccordionItem=\"second\">\r\n          <h2 ngbAccordionHeader>\r\n            <button ngbAccordionButton>Second panel</button>\r\n          </h2>\r\n          <div ngbAccordionCollapse>\r\n            <div ngbAccordionBody>\r\n              <ng-template>\r\n                {{ loremText }}\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <hr />\r\n\r\n      <button class=\"btn btn-sm btn-outline-primary me-2\" (click)=\"accordion.toggle('first')\">Toggle first</button>\r\n      <button class=\"btn btn-sm btn-outline-primary me-2\" (click)=\"accordion.toggle('second')\">Toggle second</button>\r\n      <button class=\"btn btn-sm btn-outline-primary me-2\" (click)=\"accordion.expandAll()\">Expand all</button>\r\n      <button class=\"btn btn-sm btn-outline-primary me-2\" (click)=\"accordion.collapseAll()\">Collapse all</button>\r\n    </app-card>\r\n  </div>\r\n  <!-- [ accordion-collapse ] end -->\r\n</div>\r\n"], "mappings": "AAGA;AACA,SAASA,YAAY,QAAQ,oCAAoC;;;;;;ICyGjDC,EAAA,CAAAC,MAAA,GACF;;;;IADED,EAAA,CAAAE,kBAAA,MAAAC,MAAA,CAAAC,SAAA,MACF;;;;;IAiBEJ,EAAA,CAAAC,MAAA,GACF;;;;IADED,EAAA,CAAAE,kBAAA,MAAAC,MAAA,CAAAC,SAAA,MACF;;;;;IAWEJ,EAAA,CAAAC,MAAA,GACF;;;;IADED,EAAA,CAAAE,kBAAA,MAAAC,MAAA,CAAAC,SAAA,MACF;;;;;IAgBEJ,EAAA,CAAAC,MAAA,GACF;;;;IADED,EAAA,CAAAE,kBAAA,MAAAC,MAAA,CAAAC,SAAA,MACF;;;;;IAWEJ,EAAA,CAAAC,MAAA,GACF;;;;IADED,EAAA,CAAAE,kBAAA,MAAAC,MAAA,CAAAC,SAAA,MACF;;;AD5Jd,eAAc,MAAOC,sBAAsB;EAP3CC,YAAA;IAQE;IACA,KAAAC,WAAW,GAAG,IAAI;IAClB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAL,SAAS,GACP,4lBAA4lB;;;;uCAN3kBC,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAK,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCVvChB,EAHJ,CAAAkB,cAAA,aAAiB,aAEQ,YACJ;UAAAlB,EAAA,CAAAC,MAAA,qBAAc;UAAAD,EAAA,CAAAmB,YAAA,EAAK;UACpCnB,EAAA,CAAAoB,SAAA,SAAM;UAGFpB,EAFJ,CAAAkB,cAAA,aAAkB,aACS,gBAStB;UAHClB,EAFA,CAAAqB,UAAA,mBAAAC,wDAAA;YAAAtB,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,MAAAC,WAAA,GAAAzB,EAAA,CAAA0B,WAAA;YAAA,OAAA1B,EAAA,CAAA2B,WAAA,CAASF,WAAA,CAAAG,MAAA,EAAiB;UAAA,EAAC,qBAAAC,0DAAA;YAAA7B,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,MAAAC,WAAA,GAAAzB,EAAA,CAAA0B,WAAA;YAAA,OAAA1B,EAAA,CAAA2B,WAAA,CAChBF,WAAA,CAAAG,MAAA,EAAiB;UAAA,EAAC,mBAAAE,wDAAA;YAAA9B,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,MAAAC,WAAA,GAAAzB,EAAA,CAAA0B,WAAA;YAAA,OAAA1B,EAAA,CAAA2B,WAAA,CACpBF,WAAA,CAAAG,MAAA,EAAiB;UAAA,EAAC;UAI3B5B,EAAA,CAAAC,MAAA,6BACF;UAAAD,EAAA,CAAAmB,YAAA,EAAS;UACTnB,EAAA,CAAAkB,cAAA,gBAMC;UAHClB,EAAA,CAAAqB,UAAA,mBAAAU,wDAAA;YAAA/B,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAAV,GAAA,CAAAV,WAAA,IAAAU,GAAA,CAAAV,WAAA;UAAA,EAAoC;UAIpCP,EAAA,CAAAC,MAAA,qCACF;UACFD,EADE,CAAAmB,YAAA,EAAS,EACL;UACNnB,EAAA,CAAAkB,cAAA,iBAA2D;UAA9BlB,EAAA,CAAAgC,gBAAA,+BAAAC,kEAAAC,MAAA;YAAAlC,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAAxB,EAAA,CAAAmC,kBAAA,CAAAlB,GAAA,CAAAV,WAAA,EAAA2B,MAAA,MAAAjB,GAAA,CAAAV,WAAA,GAAA2B,MAAA;YAAA,OAAAlC,EAAA,CAAA2B,WAAA,CAAAO,MAAA;UAAA,EAA6B;UAEtDlC,EADF,CAAAkB,cAAA,eAAuB,WAChB;UAAAlB,EAAA,CAAAC,MAAA,gEAAwD;UAIrED,EAJqE,CAAAmB,YAAA,EAAM,EAC/D,EACF,EACF,EACF;UAIJnB,EADF,CAAAkB,cAAA,eAA4B,aACT;UAAAlB,EAAA,CAAAC,MAAA,wBAAgB;UAAAD,EAAA,CAAAmB,YAAA,EAAK;UACtCnB,EAAA,CAAAoB,SAAA,UAAM;UAGFpB,EAFJ,CAAAkB,cAAA,eAAuB,cACI,aAMtB;UAHClB,EAAA,CAAAqB,UAAA,mBAAAe,oDAAA;YAAApC,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAAV,GAAA,CAAAT,eAAA,IAAAS,GAAA,CAAAT,eAAA;UAAA,EAA4C;UAI5CR,EAAA,CAAAC,MAAA,8BACF;UAAAD,EAAA,CAAAmB,YAAA,EAAI;UACJnB,EAAA,CAAAkB,cAAA,kBAMC;UAJClB,EAAA,CAAAqB,UAAA,mBAAAgB,yDAAA;YAAArC,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAAV,GAAA,CAAAR,eAAA,IAAAQ,GAAA,CAAAR,eAAA;UAAA,EAA4C;UAK5CT,EAAA,CAAAC,MAAA,+BACF;UAAAD,EAAA,CAAAmB,YAAA,EAAS;UACTnB,EAAA,CAAAkB,cAAA,kBAKC;UAFClB,EAAA,CAAAqB,UAAA,mBAAAiB,yDAAA;YAAAtC,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAAP,GAAA,CAAAT,eAAA,IAAAS,GAAA,CAAAT,eAAA;YAAA,OAAAR,EAAA,CAAA2B,WAAA,CAAAV,GAAA,CAAAR,eAAA,IAAAQ,GAAA,CAAAR,eAAA;UAAA,EAAgF;UAGhFT,EAAA,CAAAC,MAAA,8BACF;UAEJD,EAFI,CAAAmB,YAAA,EAAS,EACL,EACF;UAOInB,EALV,CAAAkB,cAAA,cAAiB,eACO,eACwE,cACxE,eACO,aACL;UACdlB,EAAA,CAAAC,MAAA,2MAEF;UAIRD,EAJQ,CAAAmB,YAAA,EAAI,EACA,EACF,EACF,EACF;UAKEnB,EAJR,CAAAkB,cAAA,eAAsB,eACwE,cACxE,eACO,aACL;UACdlB,EAAA,CAAAC,MAAA,2MAEF;UAMZD,EANY,CAAAmB,YAAA,EAAI,EACA,EACF,EACF,EACF,EACF,EACF;UAQInB,EALV,CAAAkB,cAAA,cAAuB,oBACW,eACZ,eAC0B,cACjB,kBACM;UAAAlB,EAAA,CAAAC,MAAA,cAAM;UACnCD,EADmC,CAAAmB,YAAA,EAAS,EACvC;UAEHnB,EADF,CAAAkB,cAAA,eAA0B,eACF;UACpBlB,EAAA,CAAAuC,UAAA,KAAAC,8CAAA,sBAAa;UAKnBxC,EAFI,CAAAmB,YAAA,EAAM,EACF,EACF;UAIAnB,EAHN,CAAAkB,cAAA,eAAsB,cACG,kBACM,YACnB;UACJlB,EAAA,CAAAC,MAAA,gBACA;UAAAD,EAAA,CAAAkB,cAAA,SAAG;UAAAlB,EAAA,CAAAC,MAAA,aAAK;UAAAD,EAAA,CAAAmB,YAAA,EAAI;UACZnB,EAAA,CAAAC,MAAA,sBACF;UAEJD,EAFI,CAAAmB,YAAA,EAAO,EACA,EACN;UAEHnB,EADF,CAAAkB,cAAA,eAA0B,eACF;UACpBlB,EAAA,CAAAuC,UAAA,KAAAE,8CAAA,sBAAa;UAKnBzC,EAFI,CAAAmB,YAAA,EAAM,EACF,EACF;UAGFnB,EAFJ,CAAAkB,cAAA,eAAwC,cACf,kBACM;UAAAlB,EAAA,CAAAC,MAAA,gBAAQ;UACrCD,EADqC,CAAAmB,YAAA,EAAS,EACzC;UAEHnB,EADF,CAAAkB,cAAA,eAA0B,eACF;UACpBlB,EAAA,CAAAuC,UAAA,KAAAG,8CAAA,sBAAa;UAOvB1C,EAJQ,CAAAmB,YAAA,EAAM,EACF,EACF,EACF,EACG;UAMHnB,EAJR,CAAAkB,cAAA,oBAAuC,kBACO,eACZ,cACL,kBACM;UAAAlB,EAAA,CAAAC,MAAA,mBAAW;UACxCD,EADwC,CAAAmB,YAAA,EAAS,EAC5C;UAEHnB,EADF,CAAAkB,cAAA,eAA0B,eACF;UACpBlB,EAAA,CAAAuC,UAAA,KAAAI,8CAAA,sBAAa;UAKnB3C,EAFI,CAAAmB,YAAA,EAAM,EACF,EACF;UAGFnB,EAFJ,CAAAkB,cAAA,eAA+B,cACN,kBACM;UAAAlB,EAAA,CAAAC,MAAA,oBAAY;UACzCD,EADyC,CAAAmB,YAAA,EAAS,EAC7C;UAEHnB,EADF,CAAAkB,cAAA,eAA0B,eACF;UACpBlB,EAAA,CAAAuC,UAAA,KAAAK,8CAAA,sBAAa;UAMrB5C,EAHM,CAAAmB,YAAA,EAAM,EACF,EACF,EACF;UAENnB,EAAA,CAAAoB,SAAA,UAAM;UAENpB,EAAA,CAAAkB,cAAA,kBAAwF;UAApClB,EAAA,CAAAqB,UAAA,mBAAAwB,yDAAA;YAAA7C,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,MAAAsB,YAAA,GAAA9C,EAAA,CAAA0B,WAAA;YAAA,OAAA1B,EAAA,CAAA2B,WAAA,CAASmB,YAAA,CAAAlB,MAAA,CAAiB,OAAO,CAAC;UAAA,EAAC;UAAC5B,EAAA,CAAAC,MAAA,oBAAY;UAAAD,EAAA,CAAAmB,YAAA,EAAS;UAC7GnB,EAAA,CAAAkB,cAAA,kBAAyF;UAArClB,EAAA,CAAAqB,UAAA,mBAAA0B,yDAAA;YAAA/C,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,MAAAsB,YAAA,GAAA9C,EAAA,CAAA0B,WAAA;YAAA,OAAA1B,EAAA,CAAA2B,WAAA,CAASmB,YAAA,CAAAlB,MAAA,CAAiB,QAAQ,CAAC;UAAA,EAAC;UAAC5B,EAAA,CAAAC,MAAA,qBAAa;UAAAD,EAAA,CAAAmB,YAAA,EAAS;UAC/GnB,EAAA,CAAAkB,cAAA,kBAAoF;UAAhClB,EAAA,CAAAqB,UAAA,mBAAA2B,yDAAA;YAAAhD,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,MAAAsB,YAAA,GAAA9C,EAAA,CAAA0B,WAAA;YAAA,OAAA1B,EAAA,CAAA2B,WAAA,CAASmB,YAAA,CAAAG,SAAA,EAAqB;UAAA,EAAC;UAACjD,EAAA,CAAAC,MAAA,kBAAU;UAAAD,EAAA,CAAAmB,YAAA,EAAS;UACvGnB,EAAA,CAAAkB,cAAA,kBAAsF;UAAlClB,EAAA,CAAAqB,UAAA,mBAAA6B,yDAAA;YAAAlD,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,MAAAsB,YAAA,GAAA9C,EAAA,CAAA0B,WAAA;YAAA,OAAA1B,EAAA,CAAA2B,WAAA,CAASmB,YAAA,CAAAK,WAAA,EAAuB;UAAA,EAAC;UAACnD,EAAA,CAAAC,MAAA,oBAAY;UAIxGD,EAJwG,CAAAmB,YAAA,EAAS,EAClG,EACP,EAEF;;;UA3KInB,EAAA,CAAAoD,SAAA,GAAmC;;UASnCpD,EAAA,CAAAoD,SAAA,GAAmC;;UAMVpD,EAAA,CAAAoD,SAAA,GAA6B;UAA7BpD,EAAA,CAAAqD,gBAAA,gBAAApC,GAAA,CAAAV,WAAA,CAA6B;UAiBtDP,EAAA,CAAAoD,SAAA,IAAuC;;UAQvCpD,EAAA,CAAAoD,SAAA,GAAuC;;UAmBmBpD,EAAA,CAAAoD,SAAA,GAA+B;UAA/BpD,EAAA,CAAAsD,UAAA,gBAAArC,GAAA,CAAAT,eAAA,CAA+B;UAY/BR,EAAA,CAAAoD,SAAA,GAA+B;UAA/BpD,EAAA,CAAAsD,UAAA,gBAAArC,GAAA,CAAAR,eAAA,CAA+B;UAkBrET,EAAA,CAAAoD,SAAA,GAAmB;UAAnBpD,EAAA,CAAAsD,UAAA,oBAAmB;UA8BnBtD,EAAA,CAAAoD,SAAA,IAAiB;UAAjBpD,EAAA,CAAAsD,UAAA,kBAAiB;;;qBD3HnCvD,YAAY,EAAAwD,EAAA,CAAAC,aAAA,EAAAC,EAAA,CAAAC,kBAAA,EAAAD,EAAA,CAAAE,qBAAA,EAAAF,EAAA,CAAAG,gBAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAJ,EAAA,CAAAK,gBAAA,EAAAL,EAAA,CAAAM,oBAAA,EAAAN,EAAA,CAAAO,WAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}