{"ast": null, "code": "export const NavigationItems = [{\n  id: 'navigation',\n  title: 'Navigation',\n  type: 'group',\n  icon: 'icon-navigation',\n  children: [{\n    id: 'dashboard',\n    title: 'Dashboard',\n    type: 'item',\n    url: '/dashboard',\n    icon: 'feather icon-home',\n    classes: 'nav-item'\n  }]\n}, {\n  id: 'ui-element',\n  title: 'UI ELEMENT',\n  type: 'group',\n  icon: 'icon-ui',\n  children: [{\n    id: 'basic',\n    title: 'Component',\n    type: 'collapse',\n    icon: 'feather icon-box',\n    children: [{\n      id: 'button',\n      title: 'Button',\n      type: 'item',\n      url: '/basic/button'\n    }, {\n      id: 'badges',\n      title: 'Badges',\n      type: 'item',\n      url: '/basic/badges'\n    }, {\n      id: 'breadcrumb-pagination',\n      title: 'Breadcrumb & Pagination',\n      type: 'item',\n      url: '/basic/breadcrumb-paging'\n    }, {\n      id: 'collapse',\n      title: 'Collapse',\n      type: 'item',\n      url: '/basic/collapse'\n    }, {\n      id: 'tabs-pills',\n      title: 'Tabs & Pills',\n      type: 'item',\n      url: '/basic/tabs-pills'\n    }, {\n      id: 'typography',\n      title: 'Typography',\n      type: 'item',\n      url: '/basic/typography'\n    }]\n  }]\n}, {\n  id: 'forms',\n  title: 'Forms & Tables',\n  type: 'group',\n  icon: 'icon-group',\n  children: [{\n    id: 'forms-element',\n    title: 'Form Elements',\n    type: 'item',\n    url: '/forms/basic',\n    classes: 'nav-item',\n    icon: 'feather icon-file-text'\n  }, {\n    id: 'tables',\n    title: 'Tables',\n    type: 'item',\n    url: '/tables/bootstrap',\n    classes: 'nav-item',\n    icon: 'feather icon-server'\n  }]\n}, {\n  id: 'chart-maps',\n  title: 'Chart',\n  type: 'group',\n  icon: 'icon-charts',\n  children: [{\n    id: 'apexChart',\n    title: 'ApexChart',\n    type: 'item',\n    url: 'apexchart',\n    classes: 'nav-item',\n    icon: 'feather icon-pie-chart'\n  }]\n}, {\n  id: 'pages',\n  title: 'Pages',\n  type: 'group',\n  icon: 'icon-pages',\n  children: [{\n    id: 'auth',\n    title: 'Authentication',\n    type: 'collapse',\n    icon: 'feather icon-lock',\n    children: [{\n      id: 'signup',\n      title: 'Sign up',\n      type: 'item',\n      url: '/auth/signup',\n      target: true,\n      breadcrumbs: false\n    }, {\n      id: 'signin',\n      title: 'Sign in',\n      type: 'item',\n      url: '/auth/signin',\n      target: true,\n      breadcrumbs: false\n    }]\n  }, {\n    id: 'sample-page',\n    title: 'Sample Page',\n    type: 'item',\n    url: '/sample-page',\n    classes: 'nav-item',\n    icon: 'feather icon-sidebar'\n  }, {\n    id: 'disabled-menu',\n    title: 'Disabled Menu',\n    type: 'item',\n    url: 'javascript:',\n    classes: 'nav-item disabled',\n    icon: 'feather icon-power',\n    external: true\n  }, {\n    id: 'buy_now',\n    title: 'Buy Now',\n    type: 'item',\n    icon: 'feather icon-book',\n    classes: 'nav-item',\n    url: 'https://codedthemes.com/item/datta-able-angular/',\n    target: true,\n    external: true\n  }]\n}];", "map": {"version": 3, "names": ["NavigationItems", "id", "title", "type", "icon", "children", "url", "classes", "target", "breadcrumbs", "external"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\navigation\\navigation.ts"], "sourcesContent": ["export interface NavigationItem {\r\n  id: string;\r\n  title: string;\r\n  type: 'item' | 'collapse' | 'group';\r\n  translate?: string;\r\n  icon?: string;\r\n  hidden?: boolean;\r\n  url?: string;\r\n  classes?: string;\r\n  exactMatch?: boolean;\r\n  external?: boolean;\r\n  target?: boolean;\r\n  breadcrumbs?: boolean;\r\n\r\n  children?: NavigationItem[];\r\n}\r\nexport const NavigationItems: NavigationItem[] = [\r\n  {\r\n    id: 'navigation',\r\n    title: 'Navigation',\r\n    type: 'group',\r\n    icon: 'icon-navigation',\r\n    children: [\r\n      {\r\n        id: 'dashboard',\r\n        title: 'Dashboard',\r\n        type: 'item',\r\n        url: '/dashboard',\r\n        icon: 'feather icon-home',\r\n        classes: 'nav-item'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: 'ui-element',\r\n    title: 'UI ELEMENT',\r\n    type: 'group',\r\n    icon: 'icon-ui',\r\n    children: [\r\n      {\r\n        id: 'basic',\r\n        title: 'Component',\r\n        type: 'collapse',\r\n        icon: 'feather icon-box',\r\n        children: [\r\n          {\r\n            id: 'button',\r\n            title: 'Button',\r\n            type: 'item',\r\n            url: '/basic/button'\r\n          },\r\n          {\r\n            id: 'badges',\r\n            title: 'Badges',\r\n            type: 'item',\r\n            url: '/basic/badges'\r\n          },\r\n          {\r\n            id: 'breadcrumb-pagination',\r\n            title: 'Breadcrumb & Pagination',\r\n            type: 'item',\r\n            url: '/basic/breadcrumb-paging'\r\n          },\r\n          {\r\n            id: 'collapse',\r\n            title: 'Collapse',\r\n            type: 'item',\r\n            url: '/basic/collapse'\r\n          },\r\n          {\r\n            id: 'tabs-pills',\r\n            title: 'Tabs & Pills',\r\n            type: 'item',\r\n            url: '/basic/tabs-pills'\r\n          },\r\n          {\r\n            id: 'typography',\r\n            title: 'Typography',\r\n            type: 'item',\r\n            url: '/basic/typography'\r\n          }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: 'forms',\r\n    title: 'Forms & Tables',\r\n    type: 'group',\r\n    icon: 'icon-group',\r\n    children: [\r\n      {\r\n        id: 'forms-element',\r\n        title: 'Form Elements',\r\n        type: 'item',\r\n        url: '/forms/basic',\r\n        classes: 'nav-item',\r\n        icon: 'feather icon-file-text'\r\n      },\r\n      {\r\n        id: 'tables',\r\n        title: 'Tables',\r\n        type: 'item',\r\n        url: '/tables/bootstrap',\r\n        classes: 'nav-item',\r\n        icon: 'feather icon-server'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: 'chart-maps',\r\n    title: 'Chart',\r\n    type: 'group',\r\n    icon: 'icon-charts',\r\n    children: [\r\n      {\r\n        id: 'apexChart',\r\n        title: 'ApexChart',\r\n        type: 'item',\r\n        url: 'apexchart',\r\n        classes: 'nav-item',\r\n        icon: 'feather icon-pie-chart'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: 'pages',\r\n    title: 'Pages',\r\n    type: 'group',\r\n    icon: 'icon-pages',\r\n    children: [\r\n      {\r\n        id: 'auth',\r\n        title: 'Authentication',\r\n        type: 'collapse',\r\n        icon: 'feather icon-lock',\r\n        children: [\r\n          {\r\n            id: 'signup',\r\n            title: 'Sign up',\r\n            type: 'item',\r\n            url: '/auth/signup',\r\n            target: true,\r\n            breadcrumbs: false\r\n          },\r\n          {\r\n            id: 'signin',\r\n            title: 'Sign in',\r\n            type: 'item',\r\n            url: '/auth/signin',\r\n            target: true,\r\n            breadcrumbs: false\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        id: 'sample-page',\r\n        title: 'Sample Page',\r\n        type: 'item',\r\n        url: '/sample-page',\r\n        classes: 'nav-item',\r\n        icon: 'feather icon-sidebar'\r\n      },\r\n      {\r\n        id: 'disabled-menu',\r\n        title: 'Disabled Menu',\r\n        type: 'item',\r\n        url: 'javascript:',\r\n        classes: 'nav-item disabled',\r\n        icon: 'feather icon-power',\r\n        external: true\r\n      },\r\n      {\r\n        id: 'buy_now',\r\n        title: 'Buy Now',\r\n        type: 'item',\r\n        icon: 'feather icon-book',\r\n        classes: 'nav-item',\r\n        url: 'https://codedthemes.com/item/datta-able-angular/',\r\n        target: true,\r\n        external: true\r\n      }\r\n    ]\r\n  }\r\n];\r\n"], "mappings": "AAgBA,OAAO,MAAMA,eAAe,GAAqB,CAC/C;EACEC,EAAE,EAAE,YAAY;EAChBC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,iBAAiB;EACvBC,QAAQ,EAAE,CACR;IACEJ,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,MAAM;IACZG,GAAG,EAAE,YAAY;IACjBF,IAAI,EAAE,mBAAmB;IACzBG,OAAO,EAAE;GACV;CAEJ,EACD;EACEN,EAAE,EAAE,YAAY;EAChBC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,CACR;IACEJ,EAAE,EAAE,OAAO;IACXC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,kBAAkB;IACxBC,QAAQ,EAAE,CACR;MACEJ,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,MAAM;MACZG,GAAG,EAAE;KACN,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,MAAM;MACZG,GAAG,EAAE;KACN,EACD;MACEL,EAAE,EAAE,uBAAuB;MAC3BC,KAAK,EAAE,yBAAyB;MAChCC,IAAI,EAAE,MAAM;MACZG,GAAG,EAAE;KACN,EACD;MACEL,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAE,MAAM;MACZG,GAAG,EAAE;KACN,EACD;MACEL,EAAE,EAAE,YAAY;MAChBC,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE,MAAM;MACZG,GAAG,EAAE;KACN,EACD;MACEL,EAAE,EAAE,YAAY;MAChBC,KAAK,EAAE,YAAY;MACnBC,IAAI,EAAE,MAAM;MACZG,GAAG,EAAE;KACN;GAEJ;CAEJ,EACD;EACEL,EAAE,EAAE,OAAO;EACXC,KAAK,EAAE,gBAAgB;EACvBC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE,CACR;IACEJ,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE,MAAM;IACZG,GAAG,EAAE,cAAc;IACnBC,OAAO,EAAE,UAAU;IACnBH,IAAI,EAAE;GACP,EACD;IACEH,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE,MAAM;IACZG,GAAG,EAAE,mBAAmB;IACxBC,OAAO,EAAE,UAAU;IACnBH,IAAI,EAAE;GACP;CAEJ,EACD;EACEH,EAAE,EAAE,YAAY;EAChBC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,CACR;IACEJ,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,MAAM;IACZG,GAAG,EAAE,WAAW;IAChBC,OAAO,EAAE,UAAU;IACnBH,IAAI,EAAE;GACP;CAEJ,EACD;EACEH,EAAE,EAAE,OAAO;EACXC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE,CACR;IACEJ,EAAE,EAAE,MAAM;IACVC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,mBAAmB;IACzBC,QAAQ,EAAE,CACR;MACEJ,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,MAAM;MACZG,GAAG,EAAE,cAAc;MACnBE,MAAM,EAAE,IAAI;MACZC,WAAW,EAAE;KACd,EACD;MACER,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,MAAM;MACZG,GAAG,EAAE,cAAc;MACnBE,MAAM,EAAE,IAAI;MACZC,WAAW,EAAE;KACd;GAEJ,EACD;IACER,EAAE,EAAE,aAAa;IACjBC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,MAAM;IACZG,GAAG,EAAE,cAAc;IACnBC,OAAO,EAAE,UAAU;IACnBH,IAAI,EAAE;GACP,EACD;IACEH,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE,MAAM;IACZG,GAAG,EAAE,aAAa;IAClBC,OAAO,EAAE,mBAAmB;IAC5BH,IAAI,EAAE,oBAAoB;IAC1BM,QAAQ,EAAE;GACX,EACD;IACET,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,mBAAmB;IACzBG,OAAO,EAAE,UAAU;IACnBD,GAAG,EAAE,kDAAkD;IACvDE,MAAM,EAAE,IAAI;IACZE,QAAQ,EAAE;GACX;CAEJ,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}