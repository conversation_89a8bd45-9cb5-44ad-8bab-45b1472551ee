{"ast": null, "code": "import _asyncToGenerator from \"D:/employee-survey-app/company-owner-frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { inject, ElementRef, Directive, signal, Injectable, InjectionToken, NgZone, input, booleanAttribute, numberAttribute, computed, output, afterRenderEffect, untracked, effect, PLATFORM_ID, Component, ChangeDetectionStrategy, viewChild, Renderer2, ApplicationRef, linkedSignal, contentChild, createComponent, Injector, NgModule, makeEnvironmentProviders } from '@angular/core';\nimport { tap, throttleTime, combineLatest, fromEvent, map, merge, startWith, switchMap, takeUntil, delay, EMPTY, takeWhile, from, of, interval, animationFrameScheduler } from 'rxjs';\nimport { Platform } from '@angular/cdk/platform';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { SmoothScrollManager } from 'ngx-scrollbar/smooth-scroll';\nimport { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { ContentObserver } from '@angular/cdk/observers';\nconst _c0 = [\"scrollbarButton\", \"\"];\nfunction ScrollbarY_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"button\", 4)(1, \"button\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"ng-scrollbar-button \", ctx_r0.cmp.buttonClass());\n    i0.ɵɵadvance();\n    i0.ɵɵclassMapInterpolate1(\"ng-scrollbar-button \", ctx_r0.cmp.buttonClass());\n  }\n}\nfunction ScrollbarX_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"button\", 4)(1, \"button\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"ng-scrollbar-button \", ctx_r0.cmp.buttonClass());\n    i0.ɵɵadvance();\n    i0.ɵɵclassMapInterpolate1(\"ng-scrollbar-button \", ctx_r0.cmp.buttonClass());\n  }\n}\nconst _c1 = \"[_nghost-%COMP%]{position:absolute;inset:0;pointer-events:none;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}.ng-scrollbar-sticky[_ngcontent-%COMP%]{top:calc(var(--_scrollbar-wrapper-top) * 1px);left:calc(var(--_scrollbar-wrapper-left) * 1px);right:calc(var(--_scrollbar-wrapper-right) * 1px);height:calc(var(--_scrollbar-wrapper-height) * 1px);width:calc(var(--_scrollbar-wrapper-width) * 1px);position:sticky;z-index:100;opacity:var(--_scrollbar-hover-opacity);transition:var(--_scrollbar-opacity-transition);pointer-events:var(--_scrollbar-pointer-events)}.ng-scrollbar-track-wrapper[_ngcontent-%COMP%]{touch-action:none;-webkit-user-select:none;user-select:none;top:var(--_scrollbar-track-top);bottom:var(--_scrollbar-track-bottom);right:var(--_scrollbar-track-right);left:var(--_scrollbar-track-left);transition:var(--INTERNAL-scrollbar-track-wrapper-transition);position:absolute;overflow:hidden;display:flex;place-items:center}.ng-scrollbar-track[_ngcontent-%COMP%]{position:relative;width:100%;height:100%;background-color:var(--INTERNAL-scrollbar-track-color);border-radius:var(--INTERNAL-scrollbar-border-radius);cursor:default;z-index:1;order:2}.ng-scrollbar-thumb[_ngcontent-%COMP%]{box-sizing:border-box;position:absolute;transition:var(--INTERNAL-scrollbar-thumb-transition);border-radius:var(--INTERNAL-scrollbar-border-radius);height:var(--_thumb-height);width:var(--_thumb-width);animation-name:_ngcontent-%COMP%_scrollbarThumbAnimation;animation-duration:1ms;animation-timing-function:linear}@keyframes _ngcontent-%COMP%_scrollbarThumbAnimation{0%{translate:var(--_scrollbar-thumb-transform-from)}to{translate:var(--_scrollbar-thumb-transform-to)}}\";\nfunction Scrollbars_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"scrollbar-y\");\n  }\n}\nfunction Scrollbars_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"scrollbar-x\");\n  }\n}\nconst _c2 = [\"contentWrapper\"];\nconst _c3 = [\"*\"];\nconst _c4 = [\"externalViewport\", \"\"];\nconst _c5 = \"[_nghost-%COMP%]{display:block;position:relative;max-height:100%;max-width:100%;--INTERNAL-scrollbar-border-radius: var(--scrollbar-border-radius, 0px);--INTERNAL-scrollbar-thickness: var(--scrollbar-thickness, 5);--INTERNAL-scrollbar-offset: var(--scrollbar-offset, 0);--INTERNAL-scrollbar-track-wrapper-transition: var(--scrollbar-track-wrapper-transition, width 60ms linear, height 60ms linear);--INTERNAL-scrollbar-track-color: var(--scrollbar-track-color, transparent);--INTERNAL-scrollbar-thumb-color: var(--scrollbar-thumb-color, rgb(0 0 0 / 20%));--INTERNAL-scrollbar-thumb-hover-color: var(--scrollbar-thumb-hover-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-hover-thickness: var(--scrollbar-hover-thickness, var(--INTERNAL-scrollbar-thickness));--INTERNAL-scrollbar-thumb-transition: var(--scrollbar-thumb-transition, none);--INTERNAL-scrollbar-thumb-min-size: var(--scrollbar-thumb-min-size, 20);--INTERNAL-scrollbar-button-color: var(--scrollbar-button-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-button-hover-color: var(--scrollbar-button-hover-color, var(--INTERNAL-scrollbar-button-color));--INTERNAL-scrollbar-button-active-color: var(--scrollbar-button-active-color, var(--INTERNAL-scrollbar-button-hover-color));--INTERNAL-scrollbar-button-fill: var(--scrollbar-button-fill, white);--INTERNAL-scrollbar-button-hover-fill: var(--scrollbar-button-hover-fill, var(--INTERNAL-scrollbar-button-fill));--INTERNAL-scrollbar-button-active-fill: var(--scrollbar-button-active-fill, var(--INTERNAL-scrollbar-button-hover-fill));--INTERNAL-scrollbar-button-size: var(--scrollbar-button-size, 20px);--INTERNAL-scrollbar-hover-opacity-transition-enter-duration: var(--scrollbar-hover-opacity-transition-enter-duration, 0);--INTERNAL-scrollbar-hover-opacity-transition-leave-duration: var(--scrollbar-hover-opacity-transition-leave-duration, .4s);--INTERNAL-scrollbar-hover-opacity-transition-leave-delay: var(--scrollbar-hover-opacity-transition-leave-delay, 1s);--INTERNAL-scrollbar-overscroll-behavior: var(--scrollbar-overscroll-behavior, initial);--INTERNAL-scrollbar-mobile-overscroll-behavior: var(--scrollbar-mobile-overscroll-behavior, none);--_scrollbar-thickness: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 2);--_scrollbar-pointer-events: auto;--_scrollbar-offset-px: calc(var(--INTERNAL-scrollbar-offset) * 1px);--_scrollbar-thickness-px: calc(var(--INTERNAL-scrollbar-thickness) * 1px);--_scrollbar-hover-thickness-px: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_viewport-padding-top: 0;--_viewport-padding-bottom: 0;--_viewport-padding-left: 0;--_viewport-padding-right: 0;--_horizontal-thumb-display: block;--_vertical-thumb-display: block;--_viewport-overflow: auto;--_viewport-pointer-events: auto;--_thumb-x-color: var(--INTERNAL-scrollbar-thumb-color);--_thumb-y-color: var(--INTERNAL-scrollbar-thumb-color);--_track-y-thickness: var(--_scrollbar-thickness-px);--_track-x-thickness: var(--_scrollbar-thickness-px);--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-overscroll-behavior);--_scrollbar-content-width: fit-content}[_nghost-%COMP%]{--_spacer-width: var(--spacer-width);--_spacer-height: var(--spacer-height);--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-offset-px);--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-offset-px);--_horizontal-top: initial;--_horizontal-bottom: var(--_scrollbar-offset-px);--_scrollbar-wrapper-x-top: calc(var(--viewport-height) - var(--_scrollbar-thickness));--reached-offset: 1px;--reached-offset-top: var(--reached-offset);--reached-offset-bottom: var(--reached-offset);--reached-offset-start: var(--reached-offset);--reached-offset-end: var(--reached-offset);--dropped-offset: 1px;--dropped-offset-top: var(--dropped-offset);--dropped-offset-bottom: var(--dropped-offset);--dropped-offset-start: var(--dropped-offset);--dropped-offset-end: var(--dropped-offset);--_viewport_scroll-timeline: unset;--_animation-timeline-y: unset;--_scrollbar-y-thumb-transform-to-value: unset;--_scrollbar-x-thumb-transform-to-value: unset;--_scrollbar-thumb-transform-from: unset;--_scrollbar-thumb-transform-to: unset}.ng-scrollbar-external-viewport[_nghost-%COMP%]     .ng-scroll-viewport{min-height:100%;min-width:100%;height:100%;max-height:100%;max-width:100%}.ng-scroll-viewport[_nghost-%COMP%], .ng-scrollbar-external-viewport[_nghost-%COMP%]     .ng-scroll-viewport{position:relative;overflow:var(--_viewport-overflow);scroll-timeline:var(--_viewport_scroll-timeline);box-sizing:border-box!important;-webkit-overflow-scrolling:touch;will-change:scroll-position;-webkit-user-select:var(--_viewport-user-select);user-select:var(--_viewport-user-select);overscroll-behavior:var(--_viewport-overscroll-behavior);pointer-events:var(--_viewport-pointer-events)}.ng-scroll-viewport[_nghost-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], .ng-scrollbar-external-viewport[_nghost-%COMP%]     .ng-scroll-viewport>.ng-scroll-content{width:var(--_scrollbar-content-width);z-index:1;min-width:100%;min-height:100%;contain:content;padding:var(--_viewport-padding-top, 0) var(--_viewport-padding-right, 0) var(--_viewport-padding-bottom, 0) var(--_viewport-padding-left, 0)}[appearance=native][_nghost-%COMP%]{--_spacer-width: calc(var(--spacer-width) + var(--_scrollbar-thickness));--_spacer-height: calc(var(--spacer-height) + var(--_scrollbar-thickness))}.ng-scroll-viewport[_nghost-%COMP%] > .ng-scroll-spacer[_ngcontent-%COMP%], .ng-scrollbar-external-viewport[_nghost-%COMP%]     .ng-scroll-viewport>.ng-scroll-spacer{position:relative;width:calc(var(--_spacer-width) * 1px);height:calc(var(--_spacer-height) * 1px)}.ng-scroll-viewport[_nghost-%COMP%], .ng-scrollbar-external-viewport[_nghost-%COMP%]     .ng-scroll-viewport{scrollbar-width:none!important}.ng-scroll-viewport[_nghost-%COMP%]::-webkit-scrollbar, .ng-scrollbar-external-viewport[_nghost-%COMP%]     .ng-scroll-viewport::-webkit-scrollbar{display:none!important}[position=invertX][_nghost-%COMP%], [position=invertAll][_nghost-%COMP%]{--_horizontal-top: var(--_scrollbar-offset-px);--_horizontal-bottom: initial;--_scrollbar-wrapper-x-top: 0}[dir=ltr][_nghost-%COMP%]{--_scrollbar-wrapper-y-right: initial;--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-left: calc(var(--viewport-width) - var(--_scrollbar-thickness))}[dir=ltr][position=invertY][_nghost-%COMP%], [dir=ltr][position=invertAll][_nghost-%COMP%]{--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-left: 0}[dir=rtl][_nghost-%COMP%]{--_scrollbar-wrapper-y-left: initial;--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-right: calc(var(--viewport-width) - var(--_scrollbar-thickness))}[dir=rtl][position=invertY][_nghost-%COMP%], [dir=rtl][position=invertAll][_nghost-%COMP%]{--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-right: 0}[verticalUsed=true][horizontalUsed=true][_nghost-%COMP%]{--_scrollbar-thickness-margin: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 3);--_scrollbar-thickness-margin-px: calc(var(--_scrollbar-thickness-margin) * 1px)}[horizontalUsed=true][_nghost-%COMP%]{--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-thickness-margin-px)}[horizontalUsed=true][position=invertX][_nghost-%COMP%], [horizontalUsed=true][position=invertAll][_nghost-%COMP%]{--_vertical-top: var(--_scrollbar-thickness-margin-px);--_vertical-bottom: var(--_scrollbar-offset-px)}[verticalUsed=true][dir=ltr][_nghost-%COMP%]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}[verticalUsed=true][dir=rtl][_nghost-%COMP%]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}[verticalUsed=true][position=invertY][dir=ltr][_nghost-%COMP%], [verticalUsed=true][position=invertAll][dir=ltr][_nghost-%COMP%]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}[verticalUsed=true][position=invertY][dir=rtl][_nghost-%COMP%], [verticalUsed=true][position=invertAll][dir=rtl][_nghost-%COMP%]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}[appearance=native][verticalUsed=true][dir=ltr][_nghost-%COMP%]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}[appearance=native][verticalUsed=true][dir=rtl][_nghost-%COMP%]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}[appearance=native][verticalUsed=true][position=invertY][dir=ltr][_nghost-%COMP%], [appearance=native][verticalUsed=true][position=invertAll][dir=ltr][_nghost-%COMP%]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}[appearance=native][verticalUsed=true][position=invertY][dir=rtl][_nghost-%COMP%], [appearance=native][verticalUsed=true][position=invertAll][dir=rtl][_nghost-%COMP%]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}[appearance=native][horizontalUsed=true][_nghost-%COMP%]{--_viewport-padding-top: 0;--_viewport-padding-bottom: calc(var(--_scrollbar-thickness) * 1px)}[appearance=native][horizontalUsed=true][position=invertX][_nghost-%COMP%], [appearance=native][horizontalUsed=true][position=invertAll][_nghost-%COMP%]{--_viewport-padding-top: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-bottom: 0}[visibility=hover][_nghost-%COMP%]{--_scrollbar-hover-opacity: 0;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-leave-duration) var(--INTERNAL-scrollbar-hover-opacity-transition-leave-delay)}[visibility=hover][_nghost-%COMP%]:hover, [visibility=hover][_nghost-%COMP%]:active, [visibility=hover][_nghost-%COMP%]:focus{--_scrollbar-hover-opacity: 1;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-enter-duration)}[dir=ltr][_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=start], [dir=ltr][_nghost-%COMP%]     .scroll-dropped-trigger-element[trigger=start]{left:0;right:unset}[dir=ltr][_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=end], [dir=ltr][_nghost-%COMP%]     .scroll-dropped-trigger-element[trigger=end]{right:0;left:unset}[dir=rtl][_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=start], [dir=rtl][_nghost-%COMP%]     .scroll-dropped-trigger-element[trigger=start]{right:0;left:unset}[dir=rtl][_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=end], [dir=rtl][_nghost-%COMP%]     .scroll-dropped-trigger-element[trigger=end]{left:0;right:unset}[_nghost-%COMP%]     .ng-scroll-reached-wrapper, [_nghost-%COMP%]     .ng-scroll-dropped-wrapper, [_nghost-%COMP%]     .scroll-reached-trigger-element, [_nghost-%COMP%]     .scroll-dropped-trigger-element{position:absolute;-webkit-user-select:none;user-select:none;pointer-events:none;z-index:-9999}[_nghost-%COMP%]     .ng-scroll-reached-wrapper, [_nghost-%COMP%]     .ng-scroll-dropped-wrapper{visibility:hidden;inset:0;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}[_nghost-%COMP%]     [isHorizontallyScrollable=false] .scroll-reached-trigger-element[trigger=end], [_nghost-%COMP%]     [isHorizontallyScrollable=false] .scroll-dropped-trigger-element[trigger=end]{display:none}[_nghost-%COMP%]     [isVerticallyScrollable=false] .scroll-reached-trigger-element[trigger=bottom], [_nghost-%COMP%]     [isVerticallyScrollable=false] .scroll-dropped-trigger-element[trigger=bottom]{display:none}[_nghost-%COMP%]     .scroll-reached-trigger-element{background:red}[_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=top], [_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=bottom]{left:0;right:0}[_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=start], [_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=end]{top:0;bottom:0}[_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=top]{top:0;height:var(--reached-offset-top)}[_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=bottom]{bottom:0;height:var(--reached-offset-bottom)}[_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=start]{width:var(--reached-offset-start)}[_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=end]{width:var(--reached-offset-end)}[_nghost-%COMP%]   .scroll-dropped-trigger-element[_ngcontent-%COMP%]{background:#00f}[_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=top][_ngcontent-%COMP%], [_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=bottom][_ngcontent-%COMP%]{left:0;right:0}[_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=start][_ngcontent-%COMP%], [_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=end][_ngcontent-%COMP%]{top:0;bottom:0}[_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=top][_ngcontent-%COMP%]{top:0;height:var(--dropped-offset-top)}[_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=bottom][_ngcontent-%COMP%]{bottom:0;height:var(--dropped-offset-bottom)}[_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=start][_ngcontent-%COMP%]{width:var(--dropped-offset-start)}[_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=end][_ngcontent-%COMP%]{width:var(--dropped-offset-end)}[verticalUsed=true][_nghost-%COMP%]{--_timeline-scope: --scrollerY;--_animation-timeline-y: --scrollerY;--_viewport_scroll-timeline: --scrollerY y}[horizontalUsed=true][_nghost-%COMP%]{--_timeline-scope: --scrollerX;--_animation-timeline-x: --scrollerX;--_viewport_scroll-timeline: --scrollerX x}[verticalUsed=true][horizontalUsed=true][_nghost-%COMP%]{--_timeline-scope: --scrollerX, --scrollerY;--_viewport_scroll-timeline: --scrollerX x, --scrollerY y}[orientation=vertical][_nghost-%COMP%]{--_viewport-overflow: hidden auto;--_scrollbar-content-width: unset}[orientation=horizontal][_nghost-%COMP%]{--_viewport-overflow: auto hidden}[disableInteraction=true][_nghost-%COMP%]{--_viewport-pointer-events: none;--_scrollbar-pointer-events: none}[isVerticallyScrollable=false][_nghost-%COMP%]{--_vertical-thumb-display: none}[isHorizontallyScrollable=false][_nghost-%COMP%]{--_horizontal-thumb-display: none}[dragging=x][_nghost-%COMP%], [dragging=y][_nghost-%COMP%]{--_viewport-user-select: none}[dragging=x][_nghost-%COMP%]{--_track-x-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-x-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}[dragging=y][_nghost-%COMP%]{--_track-y-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-y-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}[mobile=true][_nghost-%COMP%]{--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-mobile-overscroll-behavior)}\";\nclass ScrollViewport {\n  constructor() {\n    this.nativeElement = inject(ElementRef).nativeElement;\n  }\n  static {\n    this.ɵfac = function ScrollViewport_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollViewport)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ScrollViewport,\n      selectors: [[\"\", \"scrollViewport\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollViewport, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[scrollViewport]'\n    }]\n  }], null, null);\n})();\nfunction preventSelection(doc) {\n  return tap(() => doc.onselectstart = () => false);\n}\nfunction enableSelection(doc) {\n  return tap(() => doc.onselectstart = null);\n}\nfunction stopPropagation() {\n  return tap(e => {\n    // Have to prevent default to avoid unexpected movement whe you grab object beneath scrollbar #476\n    // https://github.com/MurhafSousli/ngx-scrollbar/issues/476\n    e.preventDefault();\n    e.stopPropagation();\n  });\n}\nfunction getThrottledStream(stream, duration) {\n  return stream.pipe(throttleTime(duration || 0, null, {\n    leading: false,\n    trailing: true\n  }));\n}\nvar ViewportClasses;\n(function (ViewportClasses) {\n  ViewportClasses[\"Viewport\"] = \"ng-scroll-viewport\";\n  ViewportClasses[\"Content\"] = \"ng-scroll-content\";\n  ViewportClasses[\"Spacer\"] = \"ng-scroll-spacer\";\n})(ViewportClasses || (ViewportClasses = {}));\n\n/**\n * Class representing a viewport adapter.\n * Provides methods and properties to interact with a viewport and its content.\n */\nclass ViewportAdapter {\n  constructor() {\n    /*\n     * A signal that indicates when viewport adapter is initialized\n     */\n    this.initialized = signal(false);\n  }\n  /** Viewport clientHeight */\n  get offsetHeight() {\n    return this.nativeElement.offsetHeight;\n  }\n  /** Viewport clientWidth */\n  get offsetWidth() {\n    return this.nativeElement.offsetWidth;\n  }\n  /** Viewport scrollTop */\n  get scrollTop() {\n    return this.nativeElement.scrollTop;\n  }\n  /** Viewport scrollLeft */\n  get scrollLeft() {\n    return this.nativeElement.scrollLeft;\n  }\n  /** Content height */\n  get contentHeight() {\n    return this.contentWrapperElement.offsetHeight;\n  }\n  /** Content width */\n  get contentWidth() {\n    return this.contentWrapperElement.offsetWidth;\n  }\n  /** The remaining vertical scrollable distance. */\n  get scrollMaxX() {\n    return this.contentWidth - this.offsetWidth;\n  }\n  /** The vertical remaining scrollable distance */\n  get scrollMaxY() {\n    return this.contentHeight - this.offsetHeight;\n  }\n  /**\n   * Initialize viewport\n   */\n  init(viewportElement, contentElement, spacerElement) {\n    // Add viewport class\n    viewportElement.classList.add(ViewportClasses.Viewport);\n    this.nativeElement = viewportElement;\n    // Add content wrapper class\n    contentElement.classList.add(ViewportClasses.Content);\n    // When integrating the scrollbar with virtual scroll, the content wrapper will have fake size,\n    // and a spacer element will have the real size\n    // Therefore, if spaceElement is provided, it will be observed instead of the content wrapper\n    if (spacerElement) {\n      spacerElement.classList.add(ViewportClasses.Spacer);\n      this.contentWrapperElement = spacerElement;\n    } else {\n      // If spacer is not provided, set it as the content wrapper\n      this.contentWrapperElement = contentElement;\n    }\n    this.initialized.set(true);\n  }\n  reset() {\n    this.nativeElement = null;\n    this.contentWrapperElement = null;\n    this.initialized.set(false);\n  }\n  /**\n   * Scrolls the viewport vertically to the specified value.\n   */\n  scrollYTo(value) {\n    this.nativeElement.scrollTop = value;\n  }\n  /**\n   * Scrolls the viewport horizontally to the specified value.\n   */\n  scrollXTo(value) {\n    this.nativeElement.scrollLeft = value;\n  }\n  static {\n    this.ɵfac = function ViewportAdapter_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ViewportAdapter)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ViewportAdapter,\n      factory: ViewportAdapter.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ViewportAdapter, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Injection token that can be used to query for a `NgScrollbar`.\n * Used primarily to avoid circular imports.\n */\nconst NG_SCROLLBAR = new InjectionToken('NG_SCROLLBAR');\nconst defaultOptions = {\n  trackClass: '',\n  thumbClass: '',\n  buttonClass: '',\n  orientation: 'auto',\n  appearance: 'native',\n  visibility: 'native',\n  position: 'native',\n  trackScrollDuration: 50,\n  sensorThrottleTime: 0,\n  disableSensor: false,\n  disableInteraction: false,\n  buttons: false,\n  hoverOffset: false\n};\n// This CDN link for a modified version of the polyfill to fix firefox bug https://github.com/MurhafSousli/ngx-scrollbar/issues/615\nconst defaultScrollTimelinePolyfill = 'https://cdn.statically.io/gist/MurhafSousli/c852b6a672069396953f06ddd4b64620/raw/ef55db72e2abb7bc002ed79f4ad4cf408bfdb72f/scroll-timeline-lite.js';\nvar ScrollbarUpdateReason;\n(function (ScrollbarUpdateReason) {\n  ScrollbarUpdateReason[\"AfterInit\"] = \"AfterInit\";\n  ScrollbarUpdateReason[\"Resized\"] = \"ResizeObserver\";\n})(ScrollbarUpdateReason || (ScrollbarUpdateReason = {}));\nconst NG_SCROLLBAR_OPTIONS = new InjectionToken('NG_SCROLLBAR_OPTIONS', {\n  providedIn: 'root',\n  factory: () => defaultOptions\n});\nconst NG_SCROLLBAR_POLYFILL = new InjectionToken('NG_SCROLLBAR_POLYFILL', {\n  providedIn: 'root',\n  factory: () => defaultScrollTimelinePolyfill\n});\nfunction filterResizeEntries(entries, target) {\n  return entries.filter(entry => entry.target === target)[0]?.contentRect;\n}\nclass NgScrollbarCore {\n  constructor() {\n    /** Global options */\n    this.options = inject(NG_SCROLLBAR_OPTIONS);\n    this.sharedResizeObserver = inject(SharedResizeObserver);\n    this.zone = inject(NgZone);\n    this.platform = inject(Platform);\n    /** A flag that indicates if the platform is mobile */\n    this.isMobile = this.platform.IOS || this.platform.ANDROID;\n    this.dir = inject(Directionality);\n    this.smoothScroll = inject(SmoothScrollManager);\n    /** Viewport adapter instance */\n    this.viewport = inject(ViewportAdapter, {\n      self: true\n    });\n    this.nativeElement = inject(ElementRef).nativeElement;\n    /**\n     * Indicates if the direction is 'ltr' or 'rtl'\n     */\n    this.direction = toSignal(this.dir.change, {\n      initialValue: this.dir.value\n    });\n    /**\n     * Indicates when scrollbar thumb is being dragged\n     */\n    this.dragging = signal('none');\n    /**\n     * Sets the supported scroll track of the viewport, there are 3 options:\n     *\n     * - `vertical` Use both vertical and horizontal scrollbar\n     * - `horizontal` Use both vertical and horizontal scrollbar\n     * - `auto` Use both vertical and horizontal scrollbar\n     */\n    this.orientation = input(this.options.orientation);\n    /**\n     * When to show the scrollbar, and there are 3 options:\n     *\n     * - `native` (default) Scrollbar will be visible when viewport is scrollable like with native scrollbar\n     * - `hover` Scrollbars are hidden by default, only visible on scrolling or hovering\n     * - `always` Scrollbars are always shown even if the viewport is not scrollable\n     */\n    this.visibility = input(this.options.visibility);\n    /** Show scrollbar buttons */\n    this.buttons = input(this.options.buttons, {\n      transform: booleanAttribute\n    });\n    /** Disables scrollbar interaction like dragging thumb and jumping by track click */\n    this.disableInteraction = input(this.options.disableInteraction, {\n      transform: booleanAttribute\n    });\n    /** Whether ResizeObserver is disabled */\n    this.disableSensor = input(this.options.disableSensor, {\n      transform: booleanAttribute\n    });\n    /** Throttle interval for detecting changes via ResizeObserver */\n    this.sensorThrottleTime = input(this.options.sensorThrottleTime, {\n      transform: numberAttribute\n    });\n    /** A flag used to activate hover effect on the offset area around the scrollbar */\n    this.hoverOffset = input(this.options.hoverOffset, {\n      transform: booleanAttribute\n    });\n    /** Viewport dimension */\n    this.viewportDimension = signal({\n      width: 0,\n      height: 0\n    });\n    /** Content dimension */\n    this.contentDimension = signal({\n      width: 0,\n      height: 0\n    });\n    this.state = computed(() => {\n      let verticalUsed = false;\n      let horizontalUsed = false;\n      let isVerticallyScrollable = false;\n      let isHorizontallyScrollable = false;\n      const orientation = this.orientation();\n      const visibility = this.visibility();\n      const viewportDimensions = this.viewportDimension();\n      const contentDimensions = this.contentDimension();\n      // Check if vertical scrollbar should be displayed\n      if (orientation === 'auto' || orientation === 'vertical') {\n        isVerticallyScrollable = contentDimensions.height > viewportDimensions.height;\n        verticalUsed = visibility === 'visible' || isVerticallyScrollable;\n      }\n      // Check if horizontal scrollbar should be displayed\n      if (orientation === 'auto' || orientation === 'horizontal') {\n        isHorizontallyScrollable = contentDimensions.width > viewportDimensions.width;\n        horizontalUsed = visibility === 'visible' || isHorizontallyScrollable;\n      }\n      return {\n        verticalUsed,\n        horizontalUsed,\n        isVerticallyScrollable,\n        isHorizontallyScrollable\n      };\n    });\n    this.isVerticallyScrollable = computed(() => this.state().isVerticallyScrollable);\n    this.isHorizontallyScrollable = computed(() => this.state().isHorizontallyScrollable);\n    this.verticalUsed = computed(() => this.state().verticalUsed);\n    this.horizontalUsed = computed(() => this.state().horizontalUsed);\n    /** Scroll duration when the scroll track is clicked */\n    this.trackScrollDuration = input(this.options.trackScrollDuration, {\n      transform: numberAttribute\n    });\n    /**\n     *  Sets the appearance of the scrollbar, there are 2 options:\n     *\n     * - `native` (default) scrollbar space will be reserved just like with native scrollbar.\n     * - `compact` scrollbar doesn't reserve any space, they are placed over the viewport.\n     */\n    this.appearance = input(this.options.appearance);\n    /**\n     * Sets the position of each scrollbar, there are 4 options:\n     *\n     * - `native` (Default) Use the default position like in native scrollbar.\n     * - `invertY` Inverts vertical scrollbar position\n     * - `invertX` Inverts Horizontal scrollbar position\n     * - `invertAll` Inverts both scrollbar positions\n     */\n    this.position = input(this.options.position);\n    /** A class forwarded to the scrollbar track element */\n    this.trackClass = input(this.options.trackClass);\n    /** A class forwarded to the scrollbar thumb element */\n    this.thumbClass = input(this.options.thumbClass);\n    /** A class forwarded to the scrollbar button element */\n    this.buttonClass = input(this.options.thumbClass);\n    /** Steam that emits when scrollbar is initialized */\n    this.afterInit = output();\n    /** Steam that emits when scrollbar is updated */\n    this.afterUpdate = output();\n    let resizeSub$;\n    let hasInitialized;\n    afterRenderEffect({\n      earlyRead: onCleanup => {\n        const disableSensor = this.disableSensor();\n        const throttleDuration = this.sensorThrottleTime();\n        const viewportInit = this.viewport.initialized();\n        untracked(() => {\n          if (viewportInit) {\n            // If resize sensor is disabled, update manually the first time\n            if (disableSensor) {\n              requestAnimationFrame(() => this.update(ScrollbarUpdateReason.AfterInit));\n            } else {\n              // Observe size changes for viewport and content wrapper\n              this.zone.runOutsideAngular(() => {\n                resizeSub$ = getThrottledStream(combineLatest([this.sharedResizeObserver.observe(this.viewport.nativeElement), this.sharedResizeObserver.observe(this.viewport.contentWrapperElement)]), throttleDuration).subscribe(() => {\n                  // After deep investigation, it appears that setting the dimension directly from the element properties\n                  // is much faster than to set them from resize callback values\n                  this.zone.run(() => {\n                    this.updateDimensions();\n                    if (hasInitialized) {\n                      this.afterUpdate.emit();\n                    } else {\n                      this.afterInit.emit();\n                    }\n                    hasInitialized = true;\n                  });\n                });\n              });\n            }\n          }\n          onCleanup(() => resizeSub$?.unsubscribe());\n        });\n      }\n    });\n  }\n  /**\n   * Manual update\n   */\n  update(reason) {\n    this.updateDimensions();\n    if (reason === ScrollbarUpdateReason.AfterInit) {\n      this.afterInit.emit();\n    } else {\n      this.afterUpdate.emit();\n    }\n  }\n  /**\n   * Smooth scroll functions\n   */\n  scrollTo(options) {\n    return this.smoothScroll.scrollTo(this.viewport.nativeElement, options);\n  }\n  /**\n   * Scroll to element by reference or selector\n   */\n  scrollToElement(target, options) {\n    return this.smoothScroll.scrollToElement(this.viewport.nativeElement, target, options);\n  }\n  updateDimensions() {\n    this.viewportDimension.set({\n      width: this.viewport.offsetWidth,\n      height: this.viewport.offsetHeight\n    });\n    this.contentDimension.set({\n      width: this.viewport.contentWidth,\n      height: this.viewport.contentHeight\n    });\n  }\n  static {\n    this.ɵfac = function NgScrollbarCore_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgScrollbarCore)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgScrollbarCore,\n      hostVars: 22,\n      hostBindings: function NgScrollbarCore_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"verticalUsed\", ctx.verticalUsed())(\"horizontalUsed\", ctx.horizontalUsed())(\"isVerticallyScrollable\", ctx.isVerticallyScrollable())(\"isHorizontallyScrollable\", ctx.isHorizontallyScrollable())(\"mobile\", ctx.isMobile)(\"dir\", ctx.direction())(\"position\", ctx.position())(\"dragging\", ctx.dragging())(\"appearance\", ctx.appearance())(\"visibility\", ctx.visibility())(\"orientation\", ctx.orientation())(\"disableInteraction\", ctx.disableInteraction());\n          i0.ɵɵstyleProp(\"--content-height\", ctx.contentDimension().height)(\"--content-width\", ctx.contentDimension().width)(\"--viewport-height\", ctx.viewportDimension().height)(\"--viewport-width\", ctx.viewportDimension().width);\n          i0.ɵɵclassProp(\"ng-scrollbar\", true);\n        }\n      },\n      inputs: {\n        orientation: [1, \"orientation\"],\n        visibility: [1, \"visibility\"],\n        buttons: [1, \"buttons\"],\n        disableInteraction: [1, \"disableInteraction\"],\n        disableSensor: [1, \"disableSensor\"],\n        sensorThrottleTime: [1, \"sensorThrottleTime\"],\n        hoverOffset: [1, \"hoverOffset\"],\n        trackScrollDuration: [1, \"trackScrollDuration\"],\n        appearance: [1, \"appearance\"],\n        position: [1, \"position\"],\n        trackClass: [1, \"trackClass\"],\n        thumbClass: [1, \"thumbClass\"],\n        buttonClass: [1, \"buttonClass\"]\n      },\n      outputs: {\n        afterInit: \"afterInit\",\n        afterUpdate: \"afterUpdate\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_SCROLLBAR,\n        useExisting: NgScrollbarCore\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgScrollbarCore, [{\n    type: Directive,\n    args: [{\n      host: {\n        '[class.ng-scrollbar]': 'true',\n        '[attr.verticalUsed]': 'verticalUsed()',\n        '[attr.horizontalUsed]': 'horizontalUsed()',\n        '[attr.isVerticallyScrollable]': 'isVerticallyScrollable()',\n        '[attr.isHorizontallyScrollable]': 'isHorizontallyScrollable()',\n        '[attr.mobile]': 'isMobile',\n        '[attr.dir]': 'direction()',\n        '[attr.position]': 'position()',\n        '[attr.dragging]': 'dragging()',\n        '[attr.appearance]': 'appearance()',\n        '[attr.visibility]': 'visibility()',\n        '[attr.orientation]': 'orientation()',\n        '[attr.disableInteraction]': 'disableInteraction()',\n        '[style.--content-height]': 'contentDimension().height',\n        '[style.--content-width]': 'contentDimension().width',\n        '[style.--viewport-height]': 'viewportDimension().height',\n        '[style.--viewport-width]': 'viewportDimension().width'\n      },\n      providers: [{\n        provide: NG_SCROLLBAR,\n        useExisting: NgScrollbarCore\n      }]\n    }]\n  }], () => [], null);\n})();\nconst SCROLLBAR_CONTROL = new InjectionToken('SCROLLBAR_CONTROL');\nclass ScrollbarAdapter {\n  constructor() {\n    this.trackSize = signal(0);\n    // Host component reference\n    this.cmp = inject(NG_SCROLLBAR);\n  }\n  static {\n    this.ɵfac = function ScrollbarAdapter_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollbarAdapter)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ScrollbarAdapter\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollbarAdapter, [{\n    type: Directive\n  }], null, null);\n})();\nclass PointerEventsAdapter {\n  constructor() {\n    // Reference to the NgScrollbar component\n    this.cmp = inject(NG_SCROLLBAR);\n    // Reference to the Scrollbar control component\n    this.control = inject(SCROLLBAR_CONTROL);\n    // Reference to the Document element\n    this.document = inject(DOCUMENT);\n    // Reference to angular zone\n    this.zone = inject(NgZone);\n    // The native element of the directive\n    this.nativeElement = inject(ElementRef).nativeElement;\n    effect(onCleanup => {\n      const disableInteraction = this.cmp.disableInteraction();\n      untracked(() => {\n        if (!disableInteraction) {\n          this.zone.runOutsideAngular(() => {\n            this._pointerEventsSub = this.pointerEvents.subscribe();\n          });\n        }\n        onCleanup(() => this._pointerEventsSub?.unsubscribe());\n      });\n    });\n  }\n  static {\n    this.ɵfac = function PointerEventsAdapter_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PointerEventsAdapter)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: PointerEventsAdapter\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PointerEventsAdapter, [{\n    type: Directive\n  }], () => [], null);\n})();\nclass TrackAdapter extends PointerEventsAdapter {\n  // Returns viewport client size\n  get viewportSize() {\n    return this.cmp.viewport[this.control.sizeProperty];\n  }\n  // Get track client rect\n  get clientRect() {\n    return this.nativeElement.getBoundingClientRect();\n  }\n  // Scrollbar track offset\n  get offset() {\n    return this.clientRect[this.control.rectOffsetProperty];\n  }\n  // Scrollbar track length\n  get size() {\n    // Noticed that clientHeight is evaluated before getClientRect.height,\n    // causing a wrong track size when integrated in dropdown integration\n    return this.nativeElement[this.control.sizeProperty];\n  }\n  // Observable for track dragging events\n  get pointerEvents() {\n    // Observable streams for pointer events\n    const pointerDown$ = fromEvent(this.nativeElement, 'pointerdown').pipe(stopPropagation(), preventSelection(this.document));\n    const pointerUp$ = fromEvent(this.document, 'pointerup', {\n      passive: true\n    }).pipe(enableSelection(this.document));\n    const pointerEnter$ = fromEvent(this.nativeElement, 'pointerover', {\n      passive: true\n    }).pipe(\n    // When mouse is out and enters again, must set the current position first\n    tap(e => this.currMousePosition = e[this.control.offsetProperty]), map(() => true));\n    const pointerLeave$ = fromEvent(this.nativeElement, 'pointerout', {\n      passive: true\n    }).pipe(map(() => false));\n    const pointerOver$ = merge(pointerEnter$, pointerLeave$).pipe(startWith(true));\n    // Keep track of current mouse location while dragging\n    const pointerMove$ = fromEvent(this.nativeElement, 'pointermove', {\n      passive: true\n    }).pipe(tap(e => this.currMousePosition = e[this.control.offsetProperty]));\n    return pointerDown$.pipe(switchMap(startEvent => {\n      // Track pointer location while dragging\n      pointerMove$.pipe(takeUntil(pointerUp$)).subscribe();\n      return this.onTrackFirstClick(startEvent).pipe(delay(200), switchMap(() => {\n        // Otherwise, activate pointermove and pointerout events and switch to ongoing scroll calls\n        return pointerOver$.pipe(switchMap(over => {\n          const currDirection = this.getScrollDirection(this.currMousePosition);\n          const sameDirection = this.scrollDirection === currDirection;\n          // If mouse is out the track pause the scroll calls, otherwise keep going\n          return over && sameDirection ? this.onTrackOngoingMousedown() : EMPTY;\n        }));\n      }), takeUntil(pointerUp$));\n    }));\n  }\n  constructor() {\n    afterRenderEffect({\n      earlyRead: () => {\n        this.cmp.viewportDimension();\n        this.cmp.contentDimension();\n        untracked(() => {\n          this.control.trackSize.set(this.size);\n          if (!this.size) {\n            // In some rare cases size could be 0 due to first render, use animation frame to give the track element time to render\n            requestAnimationFrame(() => this.control.trackSize.set(this.size));\n          }\n        });\n      }\n    });\n    super();\n  }\n  /**\n   *  Callback when mouse is first clicked on the track\n   */\n  onTrackFirstClick(e) {\n    // Initialize variables and determine scroll direction\n    this.currMousePosition = e[this.control.offsetProperty];\n    this.scrollDirection = this.getScrollDirection(this.currMousePosition);\n    this.scrollMax = this.control.viewportScrollMax;\n    return this.scrollTo(this.nextStep());\n  }\n  nextStep() {\n    // Check which direction should the scroll go (forward or backward)\n    if (this.scrollDirection === 'forward') {\n      // Scroll forward\n      const scrollForwardIncrement = this.getScrollForwardStep();\n      // Check if the incremental position is bigger than the scroll max\n      if (scrollForwardIncrement >= this.scrollMax) {\n        return this.scrollMax;\n      }\n      return scrollForwardIncrement;\n    }\n    // Scroll backward\n    const scrollBackwardIncrement = this.getScrollBackwardStep();\n    if (scrollBackwardIncrement <= 0) {\n      return 0;\n    }\n    return scrollBackwardIncrement;\n  }\n  /**\n   * Callback when mouse is still down on the track\n   * Incrementally scrolls towards target position until reached\n   */\n  onTrackOngoingMousedown() {\n    const position = this.nextStep();\n    return this.scrollTo(position).pipe(takeWhile(() => !this.isReached(position)), switchMap(() => this.onTrackOngoingMousedown()));\n  }\n  /**\n   * Returns a flag that determines whether the scroll from the given position is the final step or not\n   */\n  isReached(position) {\n    if (this.scrollDirection === 'forward') {\n      return position >= this.scrollMax;\n    }\n    return position <= 0;\n  }\n  static {\n    this.ɵfac = function TrackAdapter_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TrackAdapter)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TrackAdapter,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TrackAdapter, [{\n    type: Directive\n  }], () => [], null);\n})();\nclass TrackXDirective extends TrackAdapter {\n  get contentSize() {\n    return this.cmp.viewport.contentWidth;\n  }\n  constructor() {\n    effect(() => {\n      if (this.cmp.direction() === 'rtl') {\n        this.getCurrPosition = () => {\n          const offset = this.contentSize - this.viewportSize - this.control.viewportScrollOffset;\n          return offset * this.size / this.contentSize;\n        };\n        this.getScrollDirection = position => {\n          return position < this.getCurrPosition() ? 'forward' : 'backward';\n        };\n      } else {\n        this.getCurrPosition = () => {\n          return this.control.viewportScrollOffset * this.size / this.contentSize;\n        };\n        this.getScrollDirection = position => {\n          return position > this.getCurrPosition() ? 'forward' : 'backward';\n        };\n      }\n    });\n    super();\n  }\n  scrollTo(start) {\n    return from(this.cmp.scrollTo({\n      start,\n      duration: this.cmp.trackScrollDuration()\n    }));\n  }\n  getScrollForwardStep() {\n    return this.control.viewportScrollOffset + this.viewportSize;\n  }\n  getScrollBackwardStep() {\n    return this.control.viewportScrollOffset - this.viewportSize;\n  }\n  static {\n    this.ɵfac = function TrackXDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TrackXDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TrackXDirective,\n      selectors: [[\"\", \"scrollbarTrackX\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: TrackAdapter,\n        useExisting: TrackXDirective\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TrackXDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[scrollbarTrackX]',\n      providers: [{\n        provide: TrackAdapter,\n        useExisting: TrackXDirective\n      }]\n    }]\n  }], () => [], null);\n})();\nclass TrackYDirective extends TrackAdapter {\n  get contentSize() {\n    return this.cmp.viewport.contentHeight;\n  }\n  getCurrPosition() {\n    return this.control.viewportScrollOffset * this.size / this.contentSize;\n  }\n  getScrollDirection(position) {\n    return position > this.getCurrPosition() ? 'forward' : 'backward';\n  }\n  scrollTo(top) {\n    return from(this.cmp.scrollTo({\n      top,\n      duration: this.cmp.trackScrollDuration()\n    }));\n  }\n  getScrollForwardStep() {\n    return this.control.viewportScrollOffset + this.viewportSize;\n  }\n  getScrollBackwardStep() {\n    return this.control.viewportScrollOffset - this.viewportSize;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTrackYDirective_BaseFactory;\n      return function TrackYDirective_Factory(__ngFactoryType__) {\n        return (ɵTrackYDirective_BaseFactory || (ɵTrackYDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TrackYDirective)))(__ngFactoryType__ || TrackYDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TrackYDirective,\n      selectors: [[\"\", \"scrollbarTrackY\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: TrackAdapter,\n        useExisting: TrackYDirective\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TrackYDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[scrollbarTrackY]',\n      providers: [{\n        provide: TrackAdapter,\n        useExisting: TrackYDirective\n      }]\n    }]\n  }], null, null);\n})();\nclass ScrollbarManager {\n  constructor() {\n    this.isBrowser = isPlatformBrowser(inject(PLATFORM_ID));\n    this._polyfillUrl = inject(NG_SCROLLBAR_POLYFILL);\n    this.document = inject(DOCUMENT);\n    this.window = this.document.defaultView;\n    this.scrollTimelinePolyfill = signal(null);\n    if (this.isBrowser && (!this.window['ScrollTimeline'] || !CSS.supports('animation-timeline', 'scroll()'))) {\n      this.initPolyfill();\n    }\n  }\n  initPolyfill() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Create a script element\n        const script = _this.document.createElement('script');\n        script.src = _this._polyfillUrl;\n        // Wait for the script to load\n        yield new Promise((resolve, reject) => {\n          script.onload = resolve;\n          script.onerror = reject;\n          _this.document.head.appendChild(script);\n        });\n        // Once loaded, access and execute the function attached to the window object\n        if (_this.window['ScrollTimeline']) {\n          _this.scrollTimelinePolyfill.set(_this.window['ScrollTimeline']);\n        } else {\n          console.error('[NgScrollbar]: ScrollTimeline is not attached to the window object.');\n        }\n      } catch (error) {\n        console.error('[NgScrollbar]: Error loading ScrollTimeline script:', error);\n      }\n    })();\n  }\n  static {\n    this.ɵfac = function ScrollbarManager_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollbarManager)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ScrollbarManager,\n      factory: ScrollbarManager.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollbarManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass ThumbAdapter extends PointerEventsAdapter {\n  // Returns thumb size\n  get size() {\n    return this.nativeElement.getBoundingClientRect()[this.control.rectSizeProperty];\n  }\n  // The maximum space available for scrolling.\n  get trackMax() {\n    return this.track.size - this.size;\n  }\n  /**\n   * Stream that emits the 'scrollTo' position when a scrollbar thumb element is dragged\n   * This function is called by thumb drag event using viewport or scrollbar pointer events\n   */\n  get pointerEvents() {\n    return fromEvent(this.nativeElement, 'pointerdown').pipe(stopPropagation(), preventSelection(this.document), switchMap(e => {\n      let startTrackMax;\n      let startScrollMax;\n      const dragStart = of(e).pipe(tap(() => {\n        // Capture scrollMax and trackMax once\n        startTrackMax = this.trackMax;\n        startScrollMax = this.control.viewportScrollMax;\n        this.setDragging(this.control.axis);\n      }));\n      const dragging = fromEvent(this.document, 'pointermove').pipe(stopPropagation());\n      const dragEnd = fromEvent(this.document, 'pointerup', {\n        capture: true\n      }).pipe(stopPropagation(), enableSelection(this.document), tap(() => this.setDragging('none')));\n      return dragStart.pipe(map(startEvent => startEvent[this.control.offsetProperty]), switchMap(startOffset => dragging.pipe(map(moveEvent => moveEvent[this.control.clientProperty]),\n      // Calculate how far the pointer is from the top/left of the scrollbar (minus the dragOffset).\n      map(moveClient => moveClient - this.track.offset), map(trackRelativeOffset => startScrollMax * (trackRelativeOffset - startOffset) / startTrackMax), tap(scrollPosition => this.control.instantScrollTo(scrollPosition, startScrollMax)), takeUntil(dragEnd))));\n    }));\n  }\n  constructor() {\n    afterRenderEffect({\n      earlyRead: () => {\n        const script = this.manager.scrollTimelinePolyfill();\n        untracked(() => {\n          if (script && !this._animation) {\n            this._animation = startPolyfill(script, this.nativeElement, this.cmp.viewport.nativeElement, this.control.axis);\n          }\n        });\n      }\n    });\n    super();\n    this.manager = inject(ScrollbarManager);\n    this.track = inject(TrackAdapter);\n  }\n  setDragging(value) {\n    this.zone.run(() => this.cmp.dragging.set(value));\n  }\n  static {\n    this.ɵfac = function ThumbAdapter_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ThumbAdapter)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ThumbAdapter,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ThumbAdapter, [{\n    type: Directive\n  }], () => [], null);\n})();\nfunction startPolyfill(ScrollTimeline, element, source, axis) {\n  return element.animate({\n    translate: ['var(--_scrollbar-thumb-transform-from)', 'var(--_scrollbar-thumb-transform-to)']\n  }, {\n    fill: 'both',\n    easing: 'linear',\n    timeline: new ScrollTimeline({\n      source,\n      axis\n    })\n  });\n}\nclass ThumbXDirective extends ThumbAdapter {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵThumbXDirective_BaseFactory;\n      return function ThumbXDirective_Factory(__ngFactoryType__) {\n        return (ɵThumbXDirective_BaseFactory || (ɵThumbXDirective_BaseFactory = i0.ɵɵgetInheritedFactory(ThumbXDirective)))(__ngFactoryType__ || ThumbXDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ThumbXDirective,\n      selectors: [[\"\", \"scrollbarThumbX\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: ThumbAdapter,\n        useExisting: ThumbXDirective\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ThumbXDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[scrollbarThumbX]',\n      providers: [{\n        provide: ThumbAdapter,\n        useExisting: ThumbXDirective\n      }]\n    }]\n  }], null, null);\n})();\nclass ThumbYDirective extends ThumbAdapter {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵThumbYDirective_BaseFactory;\n      return function ThumbYDirective_Factory(__ngFactoryType__) {\n        return (ɵThumbYDirective_BaseFactory || (ɵThumbYDirective_BaseFactory = i0.ɵɵgetInheritedFactory(ThumbYDirective)))(__ngFactoryType__ || ThumbYDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ThumbYDirective,\n      selectors: [[\"\", \"scrollbarThumbY\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: ThumbAdapter,\n        useExisting: ThumbYDirective\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ThumbYDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[scrollbarThumbY]',\n      providers: [{\n        provide: ThumbAdapter,\n        useExisting: ThumbYDirective\n      }]\n    }]\n  }], null, null);\n})();\n\n// canScroll function can work for y-axis and x-axis for both LTR and RTL directions\nconst canScrollFunc = {\n  forward: (scrollOffset, scrollMax) => scrollOffset < scrollMax,\n  backward: scrollOffset => scrollOffset > 0\n};\nconst scrollStepFunc = {\n  forward: (scrollBy, offset) => offset + scrollBy,\n  backward: (scrollBy, offset) => offset - scrollBy\n};\nconst horizontalScrollStepFunc = {\n  rtl: {\n    forward: (scrollBy, offset, scrollMax) => scrollMax - offset - scrollBy,\n    backward: (scrollBy, offset, scrollMax) => scrollMax - offset + scrollBy\n  },\n  ltr: scrollStepFunc\n};\nclass ScrollbarButton extends PointerEventsAdapter {\n  get pointerEvents() {\n    const pointerDown$ = fromEvent(this.nativeElement, 'pointerdown').pipe(stopPropagation(), preventSelection(this.document));\n    const pointerUp$ = fromEvent(this.document, 'pointerup', {\n      passive: true\n    }).pipe(enableSelection(this.document));\n    const pointerLeave$ = fromEvent(this.nativeElement, 'pointerleave', {\n      passive: true\n    });\n    // Combine pointerup and pointerleave events into one stream\n    const pointerUpOrLeave$ = merge(pointerUp$, pointerLeave$);\n    return pointerDown$.pipe(switchMap(() => this.firstScrollStep().pipe(delay(this.afterFirstClickDelay), switchMap(() => this.onOngoingPointerdown()), takeUntil(pointerUpOrLeave$))));\n  }\n  constructor() {\n    effect(() => {\n      const scrollDirection = this.scrollDirection();\n      const dir = this.cmp.direction();\n      untracked(() => {\n        // Get the canScroll function according to scroll direction (forward/backward)\n        this.canScroll = canScrollFunc[scrollDirection];\n        if (this.control.axis === 'x') {\n          // Get the nextStep function according to scroll direction (forward/backward) and layout direction (LTR/RTL)\n          this.nextStep = horizontalScrollStepFunc[dir][scrollDirection];\n        } else {\n          // Get the nextStep function according to scroll direction (forward/backward)\n          this.nextStep = scrollStepFunc[scrollDirection];\n        }\n      });\n    });\n    super();\n    this.scrollbarButton = input.required();\n    this.scrollDirection = input.required();\n    this.afterFirstClickDelay = 120;\n    this.firstClickDuration = 100;\n    this.scrollBy = 50;\n    this.onGoingScrollBy = 12;\n  }\n  firstScrollStep() {\n    const value = this.nextStep(this.scrollBy, this.control.viewportScrollOffset, this.control.viewportScrollMax);\n    return this.control.scrollTo(value, this.firstClickDuration);\n  }\n  onGoingScrollStep() {\n    const scrollMax = this.control.viewportScrollMax;\n    const value = this.nextStep(this.onGoingScrollBy, this.control.viewportScrollOffset, scrollMax);\n    this.control.instantScrollTo(value, scrollMax);\n  }\n  onOngoingPointerdown() {\n    return interval(0, animationFrameScheduler).pipe(takeWhile(() => this.canScroll(this.control.viewportScrollOffset, this.control.viewportScrollMax)), tap(() => this.onGoingScrollStep()));\n  }\n  static {\n    this.ɵfac = function ScrollbarButton_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollbarButton)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ScrollbarButton,\n      selectors: [[\"button\", \"scrollbarButton\", \"\"]],\n      inputs: {\n        scrollbarButton: [1, \"scrollbarButton\"],\n        scrollDirection: [1, \"scrollDirection\"]\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      attrs: _c0,\n      decls: 3,\n      vars: 0,\n      consts: [[1, \"ng-scrollbar-button-icon\"], [\"viewBox\", \"0 0 512 512\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M413.1,327.3l-1.8-2.1l-136-156.5c-4.6-5.3-11.5-8.6-19.2-8.6c-7.7,0-14.6,3.4-19.2,8.6L101,324.9l-2.3,2.6  C97,330,96,333,96,336.2c0,8.7,7.4,15.8,16.6,15.8v0h286.8v0c9.2,0,16.6-7.1,16.6-15.8C416,332.9,414.9,329.8,413.1,327.3z\"]],\n      template: function ScrollbarButton_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 1);\n          i0.ɵɵelement(2, \"path\", 2);\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\"[_nghost-%COMP%]{position:relative;border:none;margin:0;padding:0;border-radius:0;appearance:none;background-color:var(--INTERNAL-scrollbar-button-color)}[_nghost-%COMP%]   svg[_ngcontent-%COMP%]{width:100%;height:100%;fill:var(--INTERNAL-scrollbar-button-fill)}[_nghost-%COMP%]:hover{background:var(--INTERNAL-scrollbar-button-hover-color)}[_nghost-%COMP%]:hover   svg[_ngcontent-%COMP%]{fill:var(--INTERNAL-scrollbar-button-hover-fill)}[_nghost-%COMP%]:active{background:var(--INTERNAL-scrollbar-button-active-color)}[_nghost-%COMP%]:active   svg[_ngcontent-%COMP%]{fill:var(--INTERNAL-scrollbar-button-active-fill)}[scrollbarButton=top][_nghost-%COMP%], [scrollbarButton=start][_nghost-%COMP%]{order:1}[scrollbarButton=bottom][_nghost-%COMP%], [scrollbarButton=end][_nghost-%COMP%]{order:3}[scrollbarButton=top][_nghost-%COMP%], [scrollbarButton=bottom][_nghost-%COMP%]{width:100%;height:var(--INTERNAL-scrollbar-button-size)}[scrollbarButton=start][_nghost-%COMP%], [scrollbarButton=end][_nghost-%COMP%]{width:var(--INTERNAL-scrollbar-button-size);height:100%}[scrollbarButton=bottom][_nghost-%COMP%]{--_button-rotate: 180deg}[scrollbarButton=start][_nghost-%COMP%]{--_button-rotate: -90deg}[scrollbarButton=start][_nghost-%COMP%]   .ng-scrollbar-button-icon[_ngcontent-%COMP%]{writing-mode:vertical-lr}[scrollbarButton=end][_nghost-%COMP%]{--_button-rotate: 90deg}[scrollbarButton=end][_nghost-%COMP%]   .ng-scrollbar-button-icon[_ngcontent-%COMP%]{writing-mode:vertical-rl}.ng-scrollbar-button-icon[_ngcontent-%COMP%]{rotate:var(--_button-rotate);display:flex;place-content:center;place-items:center;width:100%;height:100%}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollbarButton, [{\n    type: Component,\n    args: [{\n      selector: 'button[scrollbarButton]',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"ng-scrollbar-button-icon\\\">\\r\\n  <svg viewBox=\\\"0 0 512 512\\\"\\r\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\">\\r\\n    <path\\r\\n      d=\\\"M413.1,327.3l-1.8-2.1l-136-156.5c-4.6-5.3-11.5-8.6-19.2-8.6c-7.7,0-14.6,3.4-19.2,8.6L101,324.9l-2.3,2.6  C97,330,96,333,96,336.2c0,8.7,7.4,15.8,16.6,15.8v0h286.8v0c9.2,0,16.6-7.1,16.6-15.8C416,332.9,414.9,329.8,413.1,327.3z\\\"/>\\r\\n  </svg>\\r\\n</div>\\r\\n\",\n      styles: [\":host{position:relative;border:none;margin:0;padding:0;border-radius:0;appearance:none;background-color:var(--INTERNAL-scrollbar-button-color)}:host svg{width:100%;height:100%;fill:var(--INTERNAL-scrollbar-button-fill)}:host:hover{background:var(--INTERNAL-scrollbar-button-hover-color)}:host:hover svg{fill:var(--INTERNAL-scrollbar-button-hover-fill)}:host:active{background:var(--INTERNAL-scrollbar-button-active-color)}:host:active svg{fill:var(--INTERNAL-scrollbar-button-active-fill)}:host[scrollbarButton=top],:host[scrollbarButton=start]{order:1}:host[scrollbarButton=bottom],:host[scrollbarButton=end]{order:3}:host[scrollbarButton=top],:host[scrollbarButton=bottom]{width:100%;height:var(--INTERNAL-scrollbar-button-size)}:host[scrollbarButton=start],:host[scrollbarButton=end]{width:var(--INTERNAL-scrollbar-button-size);height:100%}:host[scrollbarButton=bottom]{--_button-rotate: 180deg}:host[scrollbarButton=start]{--_button-rotate: -90deg}:host[scrollbarButton=start] .ng-scrollbar-button-icon{writing-mode:vertical-lr}:host[scrollbarButton=end]{--_button-rotate: 90deg}:host[scrollbarButton=end] .ng-scrollbar-button-icon{writing-mode:vertical-rl}.ng-scrollbar-button-icon{rotate:var(--_button-rotate);display:flex;place-content:center;place-items:center;width:100%;height:100%}\\n\"]\n    }]\n  }], () => [], null);\n})();\nclass ScrollbarY extends ScrollbarAdapter {\n  constructor() {\n    super(...arguments);\n    this.rectOffsetProperty = 'top';\n    this.rectSizeProperty = 'height';\n    this.sizeProperty = 'offsetHeight';\n    this.clientProperty = 'clientY';\n    this.offsetProperty = 'offsetY';\n    this.axis = 'y';\n  }\n  get viewportScrollMax() {\n    return this.cmp.viewport.scrollMaxY;\n  }\n  get viewportScrollOffset() {\n    return this.cmp.viewport.scrollTop;\n  }\n  scrollTo(top, duration) {\n    return from(this.cmp.scrollTo({\n      top,\n      duration\n    }));\n  }\n  instantScrollTo(value) {\n    this.cmp.viewport.scrollYTo(value);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵScrollbarY_BaseFactory;\n      return function ScrollbarY_Factory(__ngFactoryType__) {\n        return (ɵScrollbarY_BaseFactory || (ɵScrollbarY_BaseFactory = i0.ɵɵgetInheritedFactory(ScrollbarY)))(__ngFactoryType__ || ScrollbarY);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ScrollbarY,\n      selectors: [[\"scrollbar-y\"]],\n      hostVars: 2,\n      hostBindings: function ScrollbarY_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--track-size\", ctx.trackSize());\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: SCROLLBAR_CONTROL,\n        useExisting: ScrollbarY\n      }]), i0.ɵɵInheritDefinitionFeature],\n      decls: 5,\n      vars: 11,\n      consts: [[1, \"ng-scrollbar-sticky\"], [1, \"ng-scrollbar-track-wrapper\"], [\"scrollbarTrackY\", \"\"], [\"scrollbarThumbY\", \"\"], [\"scrollbarButton\", \"top\", \"scrollDirection\", \"backward\"], [\"scrollbarButton\", \"bottom\", \"scrollDirection\", \"forward\"]],\n      template: function ScrollbarY_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵconditionalCreate(4, ScrollbarY_Conditional_4_Template, 2, 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ng-scrollbar-hover\", ctx.cmp.hoverOffset());\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"ng-scrollbar-hover\", !ctx.cmp.hoverOffset());\n          i0.ɵɵadvance();\n          i0.ɵɵclassMapInterpolate1(\"ng-scrollbar-track \", ctx.cmp.trackClass());\n          i0.ɵɵadvance();\n          i0.ɵɵclassMapInterpolate1(\"ng-scrollbar-thumb \", ctx.cmp.thumbClass());\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.cmp.buttons() ? 4 : -1);\n        }\n      },\n      dependencies: [TrackYDirective, ThumbYDirective, ScrollbarButton],\n      styles: [\"[_nghost-%COMP%]{position:absolute;inset:0;pointer-events:none;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}.ng-scrollbar-sticky[_ngcontent-%COMP%]{top:calc(var(--_scrollbar-wrapper-top) * 1px);left:calc(var(--_scrollbar-wrapper-left) * 1px);right:calc(var(--_scrollbar-wrapper-right) * 1px);height:calc(var(--_scrollbar-wrapper-height) * 1px);width:calc(var(--_scrollbar-wrapper-width) * 1px);position:sticky;z-index:100;opacity:var(--_scrollbar-hover-opacity);transition:var(--_scrollbar-opacity-transition);pointer-events:var(--_scrollbar-pointer-events)}.ng-scrollbar-track-wrapper[_ngcontent-%COMP%]{touch-action:none;-webkit-user-select:none;user-select:none;top:var(--_scrollbar-track-top);bottom:var(--_scrollbar-track-bottom);right:var(--_scrollbar-track-right);left:var(--_scrollbar-track-left);transition:var(--INTERNAL-scrollbar-track-wrapper-transition);position:absolute;overflow:hidden;display:flex;place-items:center}.ng-scrollbar-track[_ngcontent-%COMP%]{position:relative;width:100%;height:100%;background-color:var(--INTERNAL-scrollbar-track-color);border-radius:var(--INTERNAL-scrollbar-border-radius);cursor:default;z-index:1;order:2}.ng-scrollbar-thumb[_ngcontent-%COMP%]{box-sizing:border-box;position:absolute;transition:var(--INTERNAL-scrollbar-thumb-transition);border-radius:var(--INTERNAL-scrollbar-border-radius);height:var(--_thumb-height);width:var(--_thumb-width);animation-name:_ngcontent-%COMP%_scrollbarThumbAnimation;animation-duration:1ms;animation-timing-function:linear}@keyframes _ngcontent-%COMP%_scrollbarThumbAnimation{0%{translate:var(--_scrollbar-thumb-transform-from)}to{translate:var(--_scrollbar-thumb-transform-to)}}\", \"[_nghost-%COMP%]{--_scrollbar-wrapper-top: 0;--_scrollbar-wrapper-left: var(--_scrollbar-wrapper-y-left);--_scrollbar-wrapper-right: var(--_scrollbar-wrapper-y-right);--_scrollbar-wrapper-height: var(--viewport-height);--_scrollbar-wrapper-width: var(--_scrollbar-thickness);--_scrollbar-track-top: var(--_vertical-top);--_scrollbar-track-bottom: var(--_vertical-bottom);--_scrollbar-track-right: var(--_vertical-right);--_scrollbar-track-left: var(--_vertical-left);--thumb-size: max(calc(var(--viewport-height) * var(--track-size) / var(--content-height)), var(--INTERNAL-scrollbar-thumb-min-size));--_thumb-height: calc(var(--thumb-size) * 1px);--_thumb-width: 100%;--_scrollbar-y-thumb-transform-to-value: calc(var(--track-size) - var(--thumb-size));--_scrollbar-thumb-transform-from: 0 0;--_scrollbar-thumb-transform-to: 0 calc(var(--_scrollbar-y-thumb-transform-to-value) * 1px)}.ng-scrollbar-track-wrapper[_ngcontent-%COMP%]{width:var(--_track-y-thickness);flex-direction:column}.ng-scrollbar-hover[_ngcontent-%COMP%]:hover, .ng-scrollbar-hover[_ngcontent-%COMP%]:active{--_track-y-thickness: var(--_scrollbar-hover-thickness-px);--_thumb-y-color: var(--INTERNAL-scrollbar-thumb-hover-color)}.ng-scrollbar-thumb[_ngcontent-%COMP%]{animation-timeline:var(--_animation-timeline-y);min-height:calc(var(--INTERNAL-scrollbar-thumb-min-size) * 1px);display:var(--_vertical-thumb-display);background-color:var(--_thumb-y-color)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollbarY, [{\n    type: Component,\n    args: [{\n      selector: 'scrollbar-y',\n      template: `\n    <div class=\"ng-scrollbar-sticky\"\n         [class.ng-scrollbar-hover]=\"cmp.hoverOffset()\">\n      <div class=\"ng-scrollbar-track-wrapper\"\n           [class.ng-scrollbar-hover]=\"!cmp.hoverOffset()\">\n        <div scrollbarTrackY class=\"ng-scrollbar-track {{ cmp.trackClass() }}\">\n          <div scrollbarThumbY class=\"ng-scrollbar-thumb {{ cmp.thumbClass() }}\"></div>\n        </div>\n        @if (cmp.buttons()) {\n          <button class=\"ng-scrollbar-button {{ cmp.buttonClass() }}\"\n                  scrollbarButton=\"top\"\n                  scrollDirection=\"backward\"></button>\n          <button class=\"ng-scrollbar-button {{ cmp.buttonClass() }}\"\n                  scrollbarButton=\"bottom\"\n                  scrollDirection=\"forward\"></button>\n        }\n      </div>\n    </div>\n  `,\n      imports: [TrackYDirective, ThumbYDirective, ScrollbarButton],\n      providers: [{\n        provide: SCROLLBAR_CONTROL,\n        useExisting: ScrollbarY\n      }],\n      host: {\n        '[style.--track-size]': 'trackSize()'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\":host{position:absolute;inset:0;pointer-events:none;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}.ng-scrollbar-sticky{top:calc(var(--_scrollbar-wrapper-top) * 1px);left:calc(var(--_scrollbar-wrapper-left) * 1px);right:calc(var(--_scrollbar-wrapper-right) * 1px);height:calc(var(--_scrollbar-wrapper-height) * 1px);width:calc(var(--_scrollbar-wrapper-width) * 1px);position:sticky;z-index:100;opacity:var(--_scrollbar-hover-opacity);transition:var(--_scrollbar-opacity-transition);pointer-events:var(--_scrollbar-pointer-events)}.ng-scrollbar-track-wrapper{touch-action:none;-webkit-user-select:none;user-select:none;top:var(--_scrollbar-track-top);bottom:var(--_scrollbar-track-bottom);right:var(--_scrollbar-track-right);left:var(--_scrollbar-track-left);transition:var(--INTERNAL-scrollbar-track-wrapper-transition);position:absolute;overflow:hidden;display:flex;place-items:center}.ng-scrollbar-track{position:relative;width:100%;height:100%;background-color:var(--INTERNAL-scrollbar-track-color);border-radius:var(--INTERNAL-scrollbar-border-radius);cursor:default;z-index:1;order:2}.ng-scrollbar-thumb{box-sizing:border-box;position:absolute;transition:var(--INTERNAL-scrollbar-thumb-transition);border-radius:var(--INTERNAL-scrollbar-border-radius);height:var(--_thumb-height);width:var(--_thumb-width);animation-name:scrollbarThumbAnimation;animation-duration:1ms;animation-timing-function:linear}@keyframes scrollbarThumbAnimation{0%{translate:var(--_scrollbar-thumb-transform-from)}to{translate:var(--_scrollbar-thumb-transform-to)}}\\n\", \":host{--_scrollbar-wrapper-top: 0;--_scrollbar-wrapper-left: var(--_scrollbar-wrapper-y-left);--_scrollbar-wrapper-right: var(--_scrollbar-wrapper-y-right);--_scrollbar-wrapper-height: var(--viewport-height);--_scrollbar-wrapper-width: var(--_scrollbar-thickness);--_scrollbar-track-top: var(--_vertical-top);--_scrollbar-track-bottom: var(--_vertical-bottom);--_scrollbar-track-right: var(--_vertical-right);--_scrollbar-track-left: var(--_vertical-left);--thumb-size: max(calc(var(--viewport-height) * var(--track-size) / var(--content-height)), var(--INTERNAL-scrollbar-thumb-min-size));--_thumb-height: calc(var(--thumb-size) * 1px);--_thumb-width: 100%;--_scrollbar-y-thumb-transform-to-value: calc(var(--track-size) - var(--thumb-size));--_scrollbar-thumb-transform-from: 0 0;--_scrollbar-thumb-transform-to: 0 calc(var(--_scrollbar-y-thumb-transform-to-value) * 1px)}.ng-scrollbar-track-wrapper{width:var(--_track-y-thickness);flex-direction:column}.ng-scrollbar-hover:hover,.ng-scrollbar-hover:active{--_track-y-thickness: var(--_scrollbar-hover-thickness-px);--_thumb-y-color: var(--INTERNAL-scrollbar-thumb-hover-color)}.ng-scrollbar-thumb{animation-timeline:var(--_animation-timeline-y);min-height:calc(var(--INTERNAL-scrollbar-thumb-min-size) * 1px);display:var(--_vertical-thumb-display);background-color:var(--_thumb-y-color)}\\n\"]\n    }]\n  }], null, null);\n})();\nclass ScrollbarX extends ScrollbarAdapter {\n  get viewportScrollMax() {\n    return this.cmp.viewport.scrollMaxX;\n  }\n  get viewportScrollOffset() {\n    // Keep scrollLeft value positive for horizontal scrollbar\n    return Math.abs(this.cmp.viewport.scrollLeft);\n  }\n  constructor() {\n    effect(() => {\n      if (this.cmp.direction() === 'rtl') {\n        this.handlePosition = (position, scrollMax) => -(scrollMax - position);\n      } else {\n        this.handlePosition = position => position;\n      }\n    });\n    super();\n    this.manager = inject(ScrollbarManager);\n    this.rectOffsetProperty = 'left';\n    this.rectSizeProperty = 'width';\n    this.sizeProperty = 'offsetWidth';\n    this.clientProperty = 'clientX';\n    this.offsetProperty = 'offsetX';\n    this.axis = 'x';\n  }\n  scrollTo(left, duration) {\n    return from(this.cmp.scrollTo({\n      left,\n      duration\n    }));\n  }\n  instantScrollTo(value, scrollMax) {\n    this.cmp.viewport.scrollXTo(this.handlePosition(value, scrollMax));\n  }\n  static {\n    this.ɵfac = function ScrollbarX_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollbarX)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ScrollbarX,\n      selectors: [[\"scrollbar-x\"]],\n      hostVars: 3,\n      hostBindings: function ScrollbarX_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"dir\", ctx.cmp.direction());\n          i0.ɵɵstyleProp(\"--track-size\", ctx.trackSize());\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: SCROLLBAR_CONTROL,\n        useExisting: ScrollbarX\n      }]), i0.ɵɵInheritDefinitionFeature],\n      decls: 5,\n      vars: 11,\n      consts: [[1, \"ng-scrollbar-sticky\"], [1, \"ng-scrollbar-track-wrapper\"], [\"scrollbarTrackX\", \"\"], [\"scrollbarThumbX\", \"\"], [\"scrollbarButton\", \"start\", \"scrollDirection\", \"backward\"], [\"scrollbarButton\", \"end\", \"scrollDirection\", \"forward\"]],\n      template: function ScrollbarX_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵconditionalCreate(4, ScrollbarX_Conditional_4_Template, 2, 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ng-scrollbar-hover\", ctx.cmp.hoverOffset());\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"ng-scrollbar-hover\", !ctx.cmp.hoverOffset());\n          i0.ɵɵadvance();\n          i0.ɵɵclassMapInterpolate1(\"ng-scrollbar-track \", ctx.cmp.trackClass());\n          i0.ɵɵadvance();\n          i0.ɵɵclassMapInterpolate1(\"ng-scrollbar-thumb \", ctx.cmp.thumbClass());\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.cmp.buttons() ? 4 : -1);\n        }\n      },\n      dependencies: [TrackXDirective, ThumbXDirective, ScrollbarButton],\n      styles: [_c1, \"[_nghost-%COMP%]{--_scrollbar-wrapper-top: var(--_scrollbar-wrapper-x-top);--_scrollbar-wrapper-left: 0;--_scrollbar-wrapper-right: 0;--_scrollbar-wrapper-height: var(--_scrollbar-thickness);--_scrollbar-wrapper-width: var(--viewport-width);--_scrollbar-track-top: var(--_horizontal-top);--_scrollbar-track-bottom: var(--_horizontal-bottom);--_scrollbar-track-right: var(--_horizontal-right);--_scrollbar-track-left: var(--_horizontal-left);--thumb-size: max(calc(var(--viewport-width) * var(--track-size) / var(--content-width)), var(--INTERNAL-scrollbar-thumb-min-size));--_thumb-height: 100%;--_thumb-width: calc(var(--thumb-size) * 1px);--_scrollbar-x-thumb-transform-to-value: calc(var(--track-size) - var(--thumb-size));--_scrollbar-thumb-transform-from: 0;--_scrollbar-thumb-transform-to: calc(var(--_scrollbar-x-thumb-transform-to-value) * 1px)}[_nghost-%COMP%]   .ng-scrollbar-button[scrollbarButton=start][_ngcontent-%COMP%]{_--button-rotate:90}[_nghost-%COMP%]   .ng-scrollbar-button[scrollbarButton=end][_ngcontent-%COMP%]{_--button-rotate:-90}[dir=rtl][_nghost-%COMP%]   .ng-scrollbar-thumb[_ngcontent-%COMP%]{animation-name:_ngcontent-%COMP%_scrollbarThumbRTLAnimation;will-change:right;--_scrollbar-thumb-transform-to: calc(var(--_scrollbar-x-thumb-transform-to-value) * -1px)}[dir=rtl][_nghost-%COMP%]   .ng-scrollbar-button[scrollbarButton=start][_ngcontent-%COMP%]{--_button-rotate: 90deg}[dir=rtl][_nghost-%COMP%]   .ng-scrollbar-button[scrollbarButton=end][_ngcontent-%COMP%]{--_button-rotate: -90deg}.ng-scrollbar-track-wrapper[_ngcontent-%COMP%]{height:var(--_track-x-thickness);flex-direction:row}.ng-scrollbar-hover[_ngcontent-%COMP%]:hover, .ng-scrollbar-hover[_ngcontent-%COMP%]:active{--_track-x-thickness: var(--_scrollbar-hover-thickness-px);--_thumb-x-color: var(--INTERNAL-scrollbar-thumb-hover-color)}.ng-scrollbar-thumb[_ngcontent-%COMP%]{animation-timeline:var(--_animation-timeline-x);min-width:calc(var(--INTERNAL-scrollbar-thumb-min-size) * 1px);display:var(--_horizontal-thumb-display);background-color:var(--_thumb-x-color)}@keyframes _ngcontent-%COMP%_scrollbarThumbRTLAnimation{0%{right:var(--_scrollbar-thumb-transform-from)}to{right:calc(var(--_scrollbar-thumb-transform-to) * -1)}}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollbarX, [{\n    type: Component,\n    args: [{\n      selector: 'scrollbar-x',\n      template: `\n    <div class=\"ng-scrollbar-sticky\"\n         [class.ng-scrollbar-hover]=\"cmp.hoverOffset()\">\n      <div class=\"ng-scrollbar-track-wrapper\"\n           [class.ng-scrollbar-hover]=\"!cmp.hoverOffset()\">\n        <div scrollbarTrackX class=\"ng-scrollbar-track {{ cmp.trackClass() }}\">\n          <div scrollbarThumbX class=\"ng-scrollbar-thumb {{ cmp.thumbClass() }}\"></div>\n        </div>\n        @if (cmp.buttons()) {\n          <button class=\"ng-scrollbar-button {{ cmp.buttonClass() }}\"\n                  scrollbarButton=\"start\"\n                  scrollDirection=\"backward\"></button>\n          <button class=\"ng-scrollbar-button {{ cmp.buttonClass() }}\"\n                  scrollbarButton=\"end\"\n                  scrollDirection=\"forward\"></button>\n        }\n      </div>\n    </div>\n  `,\n      imports: [TrackXDirective, ThumbXDirective, ScrollbarButton],\n      providers: [{\n        provide: SCROLLBAR_CONTROL,\n        useExisting: ScrollbarX\n      }],\n      host: {\n        '[attr.dir]': 'cmp.direction()',\n        '[style.--track-size]': 'trackSize()'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\":host{position:absolute;inset:0;pointer-events:none;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}.ng-scrollbar-sticky{top:calc(var(--_scrollbar-wrapper-top) * 1px);left:calc(var(--_scrollbar-wrapper-left) * 1px);right:calc(var(--_scrollbar-wrapper-right) * 1px);height:calc(var(--_scrollbar-wrapper-height) * 1px);width:calc(var(--_scrollbar-wrapper-width) * 1px);position:sticky;z-index:100;opacity:var(--_scrollbar-hover-opacity);transition:var(--_scrollbar-opacity-transition);pointer-events:var(--_scrollbar-pointer-events)}.ng-scrollbar-track-wrapper{touch-action:none;-webkit-user-select:none;user-select:none;top:var(--_scrollbar-track-top);bottom:var(--_scrollbar-track-bottom);right:var(--_scrollbar-track-right);left:var(--_scrollbar-track-left);transition:var(--INTERNAL-scrollbar-track-wrapper-transition);position:absolute;overflow:hidden;display:flex;place-items:center}.ng-scrollbar-track{position:relative;width:100%;height:100%;background-color:var(--INTERNAL-scrollbar-track-color);border-radius:var(--INTERNAL-scrollbar-border-radius);cursor:default;z-index:1;order:2}.ng-scrollbar-thumb{box-sizing:border-box;position:absolute;transition:var(--INTERNAL-scrollbar-thumb-transition);border-radius:var(--INTERNAL-scrollbar-border-radius);height:var(--_thumb-height);width:var(--_thumb-width);animation-name:scrollbarThumbAnimation;animation-duration:1ms;animation-timing-function:linear}@keyframes scrollbarThumbAnimation{0%{translate:var(--_scrollbar-thumb-transform-from)}to{translate:var(--_scrollbar-thumb-transform-to)}}\\n\", \":host{--_scrollbar-wrapper-top: var(--_scrollbar-wrapper-x-top);--_scrollbar-wrapper-left: 0;--_scrollbar-wrapper-right: 0;--_scrollbar-wrapper-height: var(--_scrollbar-thickness);--_scrollbar-wrapper-width: var(--viewport-width);--_scrollbar-track-top: var(--_horizontal-top);--_scrollbar-track-bottom: var(--_horizontal-bottom);--_scrollbar-track-right: var(--_horizontal-right);--_scrollbar-track-left: var(--_horizontal-left);--thumb-size: max(calc(var(--viewport-width) * var(--track-size) / var(--content-width)), var(--INTERNAL-scrollbar-thumb-min-size));--_thumb-height: 100%;--_thumb-width: calc(var(--thumb-size) * 1px);--_scrollbar-x-thumb-transform-to-value: calc(var(--track-size) - var(--thumb-size));--_scrollbar-thumb-transform-from: 0;--_scrollbar-thumb-transform-to: calc(var(--_scrollbar-x-thumb-transform-to-value) * 1px)}:host .ng-scrollbar-button[scrollbarButton=start]{_--button-rotate:90}:host .ng-scrollbar-button[scrollbarButton=end]{_--button-rotate:-90}:host[dir=rtl] .ng-scrollbar-thumb{animation-name:scrollbarThumbRTLAnimation;will-change:right;--_scrollbar-thumb-transform-to: calc(var(--_scrollbar-x-thumb-transform-to-value) * -1px)}:host[dir=rtl] .ng-scrollbar-button[scrollbarButton=start]{--_button-rotate: 90deg}:host[dir=rtl] .ng-scrollbar-button[scrollbarButton=end]{--_button-rotate: -90deg}.ng-scrollbar-track-wrapper{height:var(--_track-x-thickness);flex-direction:row}.ng-scrollbar-hover:hover,.ng-scrollbar-hover:active{--_track-x-thickness: var(--_scrollbar-hover-thickness-px);--_thumb-x-color: var(--INTERNAL-scrollbar-thumb-hover-color)}.ng-scrollbar-thumb{animation-timeline:var(--_animation-timeline-x);min-width:calc(var(--INTERNAL-scrollbar-thumb-min-size) * 1px);display:var(--_horizontal-thumb-display);background-color:var(--_thumb-x-color)}@keyframes scrollbarThumbRTLAnimation{0%{right:var(--_scrollbar-thumb-transform-from)}to{right:calc(var(--_scrollbar-thumb-transform-to) * -1)}}\\n\"]\n    }]\n  }], () => [], null);\n})();\nclass Scrollbars {\n  constructor() {\n    this.cmp = inject(NG_SCROLLBAR);\n  }\n  static {\n    this.ɵfac = function Scrollbars_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Scrollbars)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Scrollbars,\n      selectors: [[\"scrollbars\"]],\n      decls: 2,\n      vars: 2,\n      template: function Scrollbars_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵconditionalCreate(0, Scrollbars_Conditional_0_Template, 1, 0, \"scrollbar-y\");\n          i0.ɵɵconditionalCreate(1, Scrollbars_Conditional_1_Template, 1, 0, \"scrollbar-x\");\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.cmp.verticalUsed() ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.cmp.horizontalUsed() ? 1 : -1);\n        }\n      },\n      dependencies: [ScrollbarX, ScrollbarY],\n      styles: [\"[_nghost-%COMP%]{display:contents}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Scrollbars, [{\n    type: Component,\n    args: [{\n      selector: 'scrollbars',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [ScrollbarX, ScrollbarY],\n      template: `\n    @if (cmp.verticalUsed()) {\n      <scrollbar-y/>\n    }\n    @if (cmp.horizontalUsed()) {\n      <scrollbar-x/>\n    }\n  `,\n      styles: [\":host{display:contents}\\n\"]\n    }]\n  }], null, null);\n})();\nclass NgScrollbar extends NgScrollbarCore {\n  constructor() {\n    effect(() => {\n      const contentWrapper = this.contentWrapper().nativeElement;\n      untracked(() => {\n        this.viewport.init(this.nativeElement, contentWrapper);\n      });\n    });\n    super();\n    this.contentWrapper = viewChild.required('contentWrapper');\n    this._scrollbars = viewChild.required(Scrollbars);\n  }\n  static {\n    this.ɵfac = function NgScrollbar_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgScrollbar)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NgScrollbar,\n      selectors: [[\"ng-scrollbar\", 3, \"externalViewport\", \"\"]],\n      viewQuery: function NgScrollbar_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuerySignal(ctx.contentWrapper, _c2, 5);\n          i0.ɵɵviewQuerySignal(ctx._scrollbars, Scrollbars, 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵqueryAdvance(2);\n        }\n      },\n      exportAs: [\"ngScrollbar\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_SCROLLBAR,\n        useExisting: NgScrollbar\n      }, ViewportAdapter]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c3,\n      decls: 4,\n      vars: 0,\n      consts: [[\"contentWrapper\", \"\"]],\n      template: function NgScrollbar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", null, 0);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelement(3, \"scrollbars\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [Scrollbars],\n      styles: [\"[_nghost-%COMP%]{display:block;position:relative;max-height:100%;max-width:100%;--INTERNAL-scrollbar-border-radius: var(--scrollbar-border-radius, 0px);--INTERNAL-scrollbar-thickness: var(--scrollbar-thickness, 5);--INTERNAL-scrollbar-offset: var(--scrollbar-offset, 0);--INTERNAL-scrollbar-track-wrapper-transition: var(--scrollbar-track-wrapper-transition, width 60ms linear, height 60ms linear);--INTERNAL-scrollbar-track-color: var(--scrollbar-track-color, transparent);--INTERNAL-scrollbar-thumb-color: var(--scrollbar-thumb-color, rgb(0 0 0 / 20%));--INTERNAL-scrollbar-thumb-hover-color: var(--scrollbar-thumb-hover-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-hover-thickness: var(--scrollbar-hover-thickness, var(--INTERNAL-scrollbar-thickness));--INTERNAL-scrollbar-thumb-transition: var(--scrollbar-thumb-transition, none);--INTERNAL-scrollbar-thumb-min-size: var(--scrollbar-thumb-min-size, 20);--INTERNAL-scrollbar-button-color: var(--scrollbar-button-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-button-hover-color: var(--scrollbar-button-hover-color, var(--INTERNAL-scrollbar-button-color));--INTERNAL-scrollbar-button-active-color: var(--scrollbar-button-active-color, var(--INTERNAL-scrollbar-button-hover-color));--INTERNAL-scrollbar-button-fill: var(--scrollbar-button-fill, white);--INTERNAL-scrollbar-button-hover-fill: var(--scrollbar-button-hover-fill, var(--INTERNAL-scrollbar-button-fill));--INTERNAL-scrollbar-button-active-fill: var(--scrollbar-button-active-fill, var(--INTERNAL-scrollbar-button-hover-fill));--INTERNAL-scrollbar-button-size: var(--scrollbar-button-size, 20px);--INTERNAL-scrollbar-hover-opacity-transition-enter-duration: var(--scrollbar-hover-opacity-transition-enter-duration, 0);--INTERNAL-scrollbar-hover-opacity-transition-leave-duration: var(--scrollbar-hover-opacity-transition-leave-duration, .4s);--INTERNAL-scrollbar-hover-opacity-transition-leave-delay: var(--scrollbar-hover-opacity-transition-leave-delay, 1s);--INTERNAL-scrollbar-overscroll-behavior: var(--scrollbar-overscroll-behavior, initial);--INTERNAL-scrollbar-mobile-overscroll-behavior: var(--scrollbar-mobile-overscroll-behavior, none);--_scrollbar-thickness: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 2);--_scrollbar-pointer-events: auto;--_scrollbar-offset-px: calc(var(--INTERNAL-scrollbar-offset) * 1px);--_scrollbar-thickness-px: calc(var(--INTERNAL-scrollbar-thickness) * 1px);--_scrollbar-hover-thickness-px: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_viewport-padding-top: 0;--_viewport-padding-bottom: 0;--_viewport-padding-left: 0;--_viewport-padding-right: 0;--_horizontal-thumb-display: block;--_vertical-thumb-display: block;--_viewport-overflow: auto;--_viewport-pointer-events: auto;--_thumb-x-color: var(--INTERNAL-scrollbar-thumb-color);--_thumb-y-color: var(--INTERNAL-scrollbar-thumb-color);--_track-y-thickness: var(--_scrollbar-thickness-px);--_track-x-thickness: var(--_scrollbar-thickness-px);--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-overscroll-behavior);--_scrollbar-content-width: fit-content}[_nghost-%COMP%]{--_spacer-width: var(--spacer-width);--_spacer-height: var(--spacer-height);--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-offset-px);--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-offset-px);--_horizontal-top: initial;--_horizontal-bottom: var(--_scrollbar-offset-px);--_scrollbar-wrapper-x-top: calc(var(--viewport-height) - var(--_scrollbar-thickness));--reached-offset: 1px;--reached-offset-top: var(--reached-offset);--reached-offset-bottom: var(--reached-offset);--reached-offset-start: var(--reached-offset);--reached-offset-end: var(--reached-offset);--dropped-offset: 1px;--dropped-offset-top: var(--dropped-offset);--dropped-offset-bottom: var(--dropped-offset);--dropped-offset-start: var(--dropped-offset);--dropped-offset-end: var(--dropped-offset);--_viewport_scroll-timeline: unset;--_animation-timeline-y: unset;--_scrollbar-y-thumb-transform-to-value: unset;--_scrollbar-x-thumb-transform-to-value: unset;--_scrollbar-thumb-transform-from: unset;--_scrollbar-thumb-transform-to: unset}.ng-scrollbar-external-viewport[_nghost-%COMP%]     .ng-scroll-viewport{min-height:100%;min-width:100%;height:100%;max-height:100%;max-width:100%}.ng-scroll-viewport[_nghost-%COMP%], .ng-scrollbar-external-viewport[_nghost-%COMP%]     .ng-scroll-viewport{position:relative;overflow:var(--_viewport-overflow);scroll-timeline:var(--_viewport_scroll-timeline);box-sizing:border-box!important;-webkit-overflow-scrolling:touch;will-change:scroll-position;-webkit-user-select:var(--_viewport-user-select);user-select:var(--_viewport-user-select);overscroll-behavior:var(--_viewport-overscroll-behavior);pointer-events:var(--_viewport-pointer-events)}.ng-scroll-viewport[_nghost-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], .ng-scrollbar-external-viewport[_nghost-%COMP%]     .ng-scroll-viewport>.ng-scroll-content{width:var(--_scrollbar-content-width);z-index:1;min-width:100%;min-height:100%;contain:content;padding:var(--_viewport-padding-top, 0) var(--_viewport-padding-right, 0) var(--_viewport-padding-bottom, 0) var(--_viewport-padding-left, 0)}[appearance=native][_nghost-%COMP%]{--_spacer-width: calc(var(--spacer-width) + var(--_scrollbar-thickness));--_spacer-height: calc(var(--spacer-height) + var(--_scrollbar-thickness))}.ng-scroll-viewport[_nghost-%COMP%] > .ng-scroll-spacer[_ngcontent-%COMP%], .ng-scrollbar-external-viewport[_nghost-%COMP%]     .ng-scroll-viewport>.ng-scroll-spacer{position:relative;width:calc(var(--_spacer-width) * 1px);height:calc(var(--_spacer-height) * 1px)}.ng-scroll-viewport[_nghost-%COMP%], .ng-scrollbar-external-viewport[_nghost-%COMP%]     .ng-scroll-viewport{scrollbar-width:none!important}.ng-scroll-viewport[_nghost-%COMP%]::-webkit-scrollbar, .ng-scrollbar-external-viewport[_nghost-%COMP%]     .ng-scroll-viewport::-webkit-scrollbar{display:none!important}[position=invertX][_nghost-%COMP%], [position=invertAll][_nghost-%COMP%]{--_horizontal-top: var(--_scrollbar-offset-px);--_horizontal-bottom: initial;--_scrollbar-wrapper-x-top: 0}[dir=ltr][_nghost-%COMP%]{--_scrollbar-wrapper-y-right: initial;--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-left: calc(var(--viewport-width) - var(--_scrollbar-thickness))}[dir=ltr][position=invertY][_nghost-%COMP%], [dir=ltr][position=invertAll][_nghost-%COMP%]{--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-left: 0}[dir=rtl][_nghost-%COMP%]{--_scrollbar-wrapper-y-left: initial;--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-right: calc(var(--viewport-width) - var(--_scrollbar-thickness))}[dir=rtl][position=invertY][_nghost-%COMP%], [dir=rtl][position=invertAll][_nghost-%COMP%]{--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-right: 0}[verticalUsed=true][horizontalUsed=true][_nghost-%COMP%]{--_scrollbar-thickness-margin: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 3);--_scrollbar-thickness-margin-px: calc(var(--_scrollbar-thickness-margin) * 1px)}[horizontalUsed=true][_nghost-%COMP%]{--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-thickness-margin-px)}[horizontalUsed=true][position=invertX][_nghost-%COMP%], [horizontalUsed=true][position=invertAll][_nghost-%COMP%]{--_vertical-top: var(--_scrollbar-thickness-margin-px);--_vertical-bottom: var(--_scrollbar-offset-px)}[verticalUsed=true][dir=ltr][_nghost-%COMP%]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}[verticalUsed=true][dir=rtl][_nghost-%COMP%]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}[verticalUsed=true][position=invertY][dir=ltr][_nghost-%COMP%], [verticalUsed=true][position=invertAll][dir=ltr][_nghost-%COMP%]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}[verticalUsed=true][position=invertY][dir=rtl][_nghost-%COMP%], [verticalUsed=true][position=invertAll][dir=rtl][_nghost-%COMP%]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}[appearance=native][verticalUsed=true][dir=ltr][_nghost-%COMP%]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}[appearance=native][verticalUsed=true][dir=rtl][_nghost-%COMP%]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}[appearance=native][verticalUsed=true][position=invertY][dir=ltr][_nghost-%COMP%], [appearance=native][verticalUsed=true][position=invertAll][dir=ltr][_nghost-%COMP%]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}[appearance=native][verticalUsed=true][position=invertY][dir=rtl][_nghost-%COMP%], [appearance=native][verticalUsed=true][position=invertAll][dir=rtl][_nghost-%COMP%]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}[appearance=native][horizontalUsed=true][_nghost-%COMP%]{--_viewport-padding-top: 0;--_viewport-padding-bottom: calc(var(--_scrollbar-thickness) * 1px)}[appearance=native][horizontalUsed=true][position=invertX][_nghost-%COMP%], [appearance=native][horizontalUsed=true][position=invertAll][_nghost-%COMP%]{--_viewport-padding-top: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-bottom: 0}[visibility=hover][_nghost-%COMP%]{--_scrollbar-hover-opacity: 0;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-leave-duration) var(--INTERNAL-scrollbar-hover-opacity-transition-leave-delay)}[visibility=hover][_nghost-%COMP%]:hover, [visibility=hover][_nghost-%COMP%]:active, [visibility=hover][_nghost-%COMP%]:focus{--_scrollbar-hover-opacity: 1;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-enter-duration)}[dir=ltr][_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=start], [dir=ltr][_nghost-%COMP%]     .scroll-dropped-trigger-element[trigger=start]{left:0;right:unset}[dir=ltr][_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=end], [dir=ltr][_nghost-%COMP%]     .scroll-dropped-trigger-element[trigger=end]{right:0;left:unset}[dir=rtl][_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=start], [dir=rtl][_nghost-%COMP%]     .scroll-dropped-trigger-element[trigger=start]{right:0;left:unset}[dir=rtl][_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=end], [dir=rtl][_nghost-%COMP%]     .scroll-dropped-trigger-element[trigger=end]{left:0;right:unset}[_nghost-%COMP%]     .ng-scroll-reached-wrapper, [_nghost-%COMP%]     .ng-scroll-dropped-wrapper, [_nghost-%COMP%]     .scroll-reached-trigger-element, [_nghost-%COMP%]     .scroll-dropped-trigger-element{position:absolute;-webkit-user-select:none;user-select:none;pointer-events:none;z-index:-9999}[_nghost-%COMP%]     .ng-scroll-reached-wrapper, [_nghost-%COMP%]     .ng-scroll-dropped-wrapper{visibility:hidden;inset:0;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}[_nghost-%COMP%]     [isHorizontallyScrollable=false] .scroll-reached-trigger-element[trigger=end], [_nghost-%COMP%]     [isHorizontallyScrollable=false] .scroll-dropped-trigger-element[trigger=end]{display:none}[_nghost-%COMP%]     [isVerticallyScrollable=false] .scroll-reached-trigger-element[trigger=bottom], [_nghost-%COMP%]     [isVerticallyScrollable=false] .scroll-dropped-trigger-element[trigger=bottom]{display:none}[_nghost-%COMP%]     .scroll-reached-trigger-element{background:red}[_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=top], [_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=bottom]{left:0;right:0}[_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=start], [_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=end]{top:0;bottom:0}[_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=top]{top:0;height:var(--reached-offset-top)}[_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=bottom]{bottom:0;height:var(--reached-offset-bottom)}[_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=start]{width:var(--reached-offset-start)}[_nghost-%COMP%]     .scroll-reached-trigger-element[trigger=end]{width:var(--reached-offset-end)}[_nghost-%COMP%]   .scroll-dropped-trigger-element[_ngcontent-%COMP%]{background:#00f}[_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=top][_ngcontent-%COMP%], [_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=bottom][_ngcontent-%COMP%]{left:0;right:0}[_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=start][_ngcontent-%COMP%], [_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=end][_ngcontent-%COMP%]{top:0;bottom:0}[_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=top][_ngcontent-%COMP%]{top:0;height:var(--dropped-offset-top)}[_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=bottom][_ngcontent-%COMP%]{bottom:0;height:var(--dropped-offset-bottom)}[_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=start][_ngcontent-%COMP%]{width:var(--dropped-offset-start)}[_nghost-%COMP%]   .scroll-dropped-trigger-element[trigger=end][_ngcontent-%COMP%]{width:var(--dropped-offset-end)}[verticalUsed=true][_nghost-%COMP%]{--_timeline-scope: --scrollerY;--_animation-timeline-y: --scrollerY;--_viewport_scroll-timeline: --scrollerY y}[horizontalUsed=true][_nghost-%COMP%]{--_timeline-scope: --scrollerX;--_animation-timeline-x: --scrollerX;--_viewport_scroll-timeline: --scrollerX x}[verticalUsed=true][horizontalUsed=true][_nghost-%COMP%]{--_timeline-scope: --scrollerX, --scrollerY;--_viewport_scroll-timeline: --scrollerX x, --scrollerY y}[orientation=vertical][_nghost-%COMP%]{--_viewport-overflow: hidden auto;--_scrollbar-content-width: unset}[orientation=horizontal][_nghost-%COMP%]{--_viewport-overflow: auto hidden}[disableInteraction=true][_nghost-%COMP%]{--_viewport-pointer-events: none;--_scrollbar-pointer-events: none}[isVerticallyScrollable=false][_nghost-%COMP%]{--_vertical-thumb-display: none}[isHorizontallyScrollable=false][_nghost-%COMP%]{--_horizontal-thumb-display: none}[dragging=x][_nghost-%COMP%], [dragging=y][_nghost-%COMP%]{--_viewport-user-select: none}[dragging=x][_nghost-%COMP%]{--_track-x-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-x-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}[dragging=y][_nghost-%COMP%]{--_track-y-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-y-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}[mobile=true][_nghost-%COMP%]{--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-mobile-overscroll-behavior)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgScrollbar, [{\n    type: Component,\n    args: [{\n      selector: 'ng-scrollbar:not([externalViewport])',\n      exportAs: 'ngScrollbar',\n      imports: [Scrollbars],\n      template: `\n    <div #contentWrapper>\n      <ng-content/>\n      <scrollbars/>\n    </div>\n  `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: NG_SCROLLBAR,\n        useExisting: NgScrollbar\n      }, ViewportAdapter],\n      styles: [\":host{display:block;position:relative;max-height:100%;max-width:100%;--INTERNAL-scrollbar-border-radius: var(--scrollbar-border-radius, 0px);--INTERNAL-scrollbar-thickness: var(--scrollbar-thickness, 5);--INTERNAL-scrollbar-offset: var(--scrollbar-offset, 0);--INTERNAL-scrollbar-track-wrapper-transition: var(--scrollbar-track-wrapper-transition, width 60ms linear, height 60ms linear);--INTERNAL-scrollbar-track-color: var(--scrollbar-track-color, transparent);--INTERNAL-scrollbar-thumb-color: var(--scrollbar-thumb-color, rgb(0 0 0 / 20%));--INTERNAL-scrollbar-thumb-hover-color: var(--scrollbar-thumb-hover-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-hover-thickness: var(--scrollbar-hover-thickness, var(--INTERNAL-scrollbar-thickness));--INTERNAL-scrollbar-thumb-transition: var(--scrollbar-thumb-transition, none);--INTERNAL-scrollbar-thumb-min-size: var(--scrollbar-thumb-min-size, 20);--INTERNAL-scrollbar-button-color: var(--scrollbar-button-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-button-hover-color: var(--scrollbar-button-hover-color, var(--INTERNAL-scrollbar-button-color));--INTERNAL-scrollbar-button-active-color: var(--scrollbar-button-active-color, var(--INTERNAL-scrollbar-button-hover-color));--INTERNAL-scrollbar-button-fill: var(--scrollbar-button-fill, white);--INTERNAL-scrollbar-button-hover-fill: var(--scrollbar-button-hover-fill, var(--INTERNAL-scrollbar-button-fill));--INTERNAL-scrollbar-button-active-fill: var(--scrollbar-button-active-fill, var(--INTERNAL-scrollbar-button-hover-fill));--INTERNAL-scrollbar-button-size: var(--scrollbar-button-size, 20px);--INTERNAL-scrollbar-hover-opacity-transition-enter-duration: var(--scrollbar-hover-opacity-transition-enter-duration, 0);--INTERNAL-scrollbar-hover-opacity-transition-leave-duration: var(--scrollbar-hover-opacity-transition-leave-duration, .4s);--INTERNAL-scrollbar-hover-opacity-transition-leave-delay: var(--scrollbar-hover-opacity-transition-leave-delay, 1s);--INTERNAL-scrollbar-overscroll-behavior: var(--scrollbar-overscroll-behavior, initial);--INTERNAL-scrollbar-mobile-overscroll-behavior: var(--scrollbar-mobile-overscroll-behavior, none);--_scrollbar-thickness: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 2);--_scrollbar-pointer-events: auto;--_scrollbar-offset-px: calc(var(--INTERNAL-scrollbar-offset) * 1px);--_scrollbar-thickness-px: calc(var(--INTERNAL-scrollbar-thickness) * 1px);--_scrollbar-hover-thickness-px: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_viewport-padding-top: 0;--_viewport-padding-bottom: 0;--_viewport-padding-left: 0;--_viewport-padding-right: 0;--_horizontal-thumb-display: block;--_vertical-thumb-display: block;--_viewport-overflow: auto;--_viewport-pointer-events: auto;--_thumb-x-color: var(--INTERNAL-scrollbar-thumb-color);--_thumb-y-color: var(--INTERNAL-scrollbar-thumb-color);--_track-y-thickness: var(--_scrollbar-thickness-px);--_track-x-thickness: var(--_scrollbar-thickness-px);--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-overscroll-behavior);--_scrollbar-content-width: fit-content}:host{--_spacer-width: var(--spacer-width);--_spacer-height: var(--spacer-height);--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-offset-px);--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-offset-px);--_horizontal-top: initial;--_horizontal-bottom: var(--_scrollbar-offset-px);--_scrollbar-wrapper-x-top: calc(var(--viewport-height) - var(--_scrollbar-thickness));--reached-offset: 1px;--reached-offset-top: var(--reached-offset);--reached-offset-bottom: var(--reached-offset);--reached-offset-start: var(--reached-offset);--reached-offset-end: var(--reached-offset);--dropped-offset: 1px;--dropped-offset-top: var(--dropped-offset);--dropped-offset-bottom: var(--dropped-offset);--dropped-offset-start: var(--dropped-offset);--dropped-offset-end: var(--dropped-offset);--_viewport_scroll-timeline: unset;--_animation-timeline-y: unset;--_scrollbar-y-thumb-transform-to-value: unset;--_scrollbar-x-thumb-transform-to-value: unset;--_scrollbar-thumb-transform-from: unset;--_scrollbar-thumb-transform-to: unset}:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{min-height:100%;min-width:100%;height:100%;max-height:100%;max-width:100%}:host.ng-scroll-viewport,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{position:relative;overflow:var(--_viewport-overflow);scroll-timeline:var(--_viewport_scroll-timeline);box-sizing:border-box!important;-webkit-overflow-scrolling:touch;will-change:scroll-position;-webkit-user-select:var(--_viewport-user-select);user-select:var(--_viewport-user-select);overscroll-behavior:var(--_viewport-overscroll-behavior);pointer-events:var(--_viewport-pointer-events)}:host.ng-scroll-viewport>.ng-scroll-content,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport>.ng-scroll-content{width:var(--_scrollbar-content-width);z-index:1;min-width:100%;min-height:100%;contain:content;padding:var(--_viewport-padding-top, 0) var(--_viewport-padding-right, 0) var(--_viewport-padding-bottom, 0) var(--_viewport-padding-left, 0)}:host[appearance=native]{--_spacer-width: calc(var(--spacer-width) + var(--_scrollbar-thickness));--_spacer-height: calc(var(--spacer-height) + var(--_scrollbar-thickness))}:host.ng-scroll-viewport>.ng-scroll-spacer,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport>.ng-scroll-spacer{position:relative;width:calc(var(--_spacer-width) * 1px);height:calc(var(--_spacer-height) * 1px)}:host.ng-scroll-viewport,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{scrollbar-width:none!important}:host.ng-scroll-viewport::-webkit-scrollbar,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport::-webkit-scrollbar{display:none!important}:host[position=invertX],:host[position=invertAll]{--_horizontal-top: var(--_scrollbar-offset-px);--_horizontal-bottom: initial;--_scrollbar-wrapper-x-top: 0}:host[dir=ltr]{--_scrollbar-wrapper-y-right: initial;--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-left: calc(var(--viewport-width) - var(--_scrollbar-thickness))}:host[dir=ltr][position=invertY],:host[dir=ltr][position=invertAll]{--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-left: 0}:host[dir=rtl]{--_scrollbar-wrapper-y-left: initial;--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-right: calc(var(--viewport-width) - var(--_scrollbar-thickness))}:host[dir=rtl][position=invertY],:host[dir=rtl][position=invertAll]{--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-right: 0}:host[verticalUsed=true][horizontalUsed=true]{--_scrollbar-thickness-margin: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 3);--_scrollbar-thickness-margin-px: calc(var(--_scrollbar-thickness-margin) * 1px)}:host[horizontalUsed=true]{--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-thickness-margin-px)}:host[horizontalUsed=true][position=invertX],:host[horizontalUsed=true][position=invertAll]{--_vertical-top: var(--_scrollbar-thickness-margin-px);--_vertical-bottom: var(--_scrollbar-offset-px)}:host[verticalUsed=true][dir=ltr]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}:host[verticalUsed=true][dir=rtl]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}:host[verticalUsed=true][position=invertY][dir=ltr],:host[verticalUsed=true][position=invertAll][dir=ltr]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}:host[verticalUsed=true][position=invertY][dir=rtl],:host[verticalUsed=true][position=invertAll][dir=rtl]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}:host[appearance=native][verticalUsed=true][dir=ltr]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][verticalUsed=true][dir=rtl]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}:host[appearance=native][verticalUsed=true][position=invertY][dir=ltr],:host[appearance=native][verticalUsed=true][position=invertAll][dir=ltr]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}:host[appearance=native][verticalUsed=true][position=invertY][dir=rtl],:host[appearance=native][verticalUsed=true][position=invertAll][dir=rtl]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][horizontalUsed=true]{--_viewport-padding-top: 0;--_viewport-padding-bottom: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][horizontalUsed=true][position=invertX],:host[appearance=native][horizontalUsed=true][position=invertAll]{--_viewport-padding-top: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-bottom: 0}:host[visibility=hover]{--_scrollbar-hover-opacity: 0;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-leave-duration) var(--INTERNAL-scrollbar-hover-opacity-transition-leave-delay)}:host[visibility=hover]:hover,:host[visibility=hover]:active,:host[visibility=hover]:focus{--_scrollbar-hover-opacity: 1;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-enter-duration)}:host[dir=ltr] ::ng-deep .scroll-reached-trigger-element[trigger=start],:host[dir=ltr] ::ng-deep .scroll-dropped-trigger-element[trigger=start]{left:0;right:unset}:host[dir=ltr] ::ng-deep .scroll-reached-trigger-element[trigger=end],:host[dir=ltr] ::ng-deep .scroll-dropped-trigger-element[trigger=end]{right:0;left:unset}:host[dir=rtl] ::ng-deep .scroll-reached-trigger-element[trigger=start],:host[dir=rtl] ::ng-deep .scroll-dropped-trigger-element[trigger=start]{right:0;left:unset}:host[dir=rtl] ::ng-deep .scroll-reached-trigger-element[trigger=end],:host[dir=rtl] ::ng-deep .scroll-dropped-trigger-element[trigger=end]{left:0;right:unset}:host ::ng-deep .ng-scroll-reached-wrapper,:host ::ng-deep .ng-scroll-dropped-wrapper,:host ::ng-deep .scroll-reached-trigger-element,:host ::ng-deep .scroll-dropped-trigger-element{position:absolute;-webkit-user-select:none;user-select:none;pointer-events:none;z-index:-9999}:host ::ng-deep .ng-scroll-reached-wrapper,:host ::ng-deep .ng-scroll-dropped-wrapper{visibility:hidden;inset:0;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}:host ::ng-deep [isHorizontallyScrollable=false] .scroll-reached-trigger-element[trigger=end],:host ::ng-deep [isHorizontallyScrollable=false] .scroll-dropped-trigger-element[trigger=end]{display:none}:host ::ng-deep [isVerticallyScrollable=false] .scroll-reached-trigger-element[trigger=bottom],:host ::ng-deep [isVerticallyScrollable=false] .scroll-dropped-trigger-element[trigger=bottom]{display:none}:host ::ng-deep .scroll-reached-trigger-element{background:red}:host ::ng-deep .scroll-reached-trigger-element[trigger=top],:host ::ng-deep .scroll-reached-trigger-element[trigger=bottom]{left:0;right:0}:host ::ng-deep .scroll-reached-trigger-element[trigger=start],:host ::ng-deep .scroll-reached-trigger-element[trigger=end]{top:0;bottom:0}:host ::ng-deep .scroll-reached-trigger-element[trigger=top]{top:0;height:var(--reached-offset-top)}:host ::ng-deep .scroll-reached-trigger-element[trigger=bottom]{bottom:0;height:var(--reached-offset-bottom)}:host ::ng-deep .scroll-reached-trigger-element[trigger=start]{width:var(--reached-offset-start)}:host ::ng-deep .scroll-reached-trigger-element[trigger=end]{width:var(--reached-offset-end)}:host .scroll-dropped-trigger-element{background:#00f}:host .scroll-dropped-trigger-element[trigger=top],:host .scroll-dropped-trigger-element[trigger=bottom]{left:0;right:0}:host .scroll-dropped-trigger-element[trigger=start],:host .scroll-dropped-trigger-element[trigger=end]{top:0;bottom:0}:host .scroll-dropped-trigger-element[trigger=top]{top:0;height:var(--dropped-offset-top)}:host .scroll-dropped-trigger-element[trigger=bottom]{bottom:0;height:var(--dropped-offset-bottom)}:host .scroll-dropped-trigger-element[trigger=start]{width:var(--dropped-offset-start)}:host .scroll-dropped-trigger-element[trigger=end]{width:var(--dropped-offset-end)}:host[verticalUsed=true]{--_timeline-scope: --scrollerY;--_animation-timeline-y: --scrollerY;--_viewport_scroll-timeline: --scrollerY y}:host[horizontalUsed=true]{--_timeline-scope: --scrollerX;--_animation-timeline-x: --scrollerX;--_viewport_scroll-timeline: --scrollerX x}:host[verticalUsed=true][horizontalUsed=true]{--_timeline-scope: --scrollerX, --scrollerY;--_viewport_scroll-timeline: --scrollerX x, --scrollerY y}:host[orientation=vertical]{--_viewport-overflow: hidden auto;--_scrollbar-content-width: unset}:host[orientation=horizontal]{--_viewport-overflow: auto hidden}:host[disableInteraction=true]{--_viewport-pointer-events: none;--_scrollbar-pointer-events: none}:host[isVerticallyScrollable=false]{--_vertical-thumb-display: none}:host[isHorizontallyScrollable=false]{--_horizontal-thumb-display: none}:host[dragging=x],:host[dragging=y]{--_viewport-user-select: none}:host[dragging=x]{--_track-x-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-x-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}:host[dragging=y]{--_track-y-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-y-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}:host[mobile=true]{--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-mobile-overscroll-behavior)}\\n\"]\n    }]\n  }], () => [], null);\n})();\nclass NgScrollbarExt extends NgScrollbarCore {\n  constructor() {\n    // Using `afterRenderEffect` would run twice, one when viewport directive is detected\n    // and one when content wrapper is detected, therefore `effect` is better because it runs only once.\n    effect(() => {\n      const viewportElement = this.viewportElement();\n      const contentWrapperElement = this.contentWrapperElement();\n      const spacerElement = this.spacerElement();\n      const viewportError = this.viewportError();\n      const contentWrapperError = this.contentWrapperError();\n      const spacerError = this.spacerError();\n      untracked(() => {\n        if (!this.skipInit) {\n          const error = viewportError || contentWrapperError || spacerError;\n          if (error) {\n            console.error(error);\n          } else {\n            this.initialize(viewportElement, contentWrapperElement, spacerElement);\n          }\n        }\n      });\n    });\n    super();\n    this.renderer = inject(Renderer2);\n    this.appRef = inject(ApplicationRef);\n    this._scrollbars = signal(null);\n    /**\n     * Selector used to query the viewport element.\n     */\n    this.externalViewport = input();\n    /**\n     * Selector used to query the content wrapper element.\n     */\n    this.externalContentWrapper = input();\n    /**\n     * Selector used to query the spacer element (virtual scroll integration).\n     * In the case of integrating the scrollbar with a virtual scroll component,\n     * a spacer element is typically created to match the real size of the content.\n     * The scrollbar will use the size of this spacer element for calculations instead of the content wrapper size.\n     */\n    this.externalSpacer = input();\n    this.viewportElement = linkedSignal({\n      source: this.externalViewport,\n      // If viewport selector was defined, query the element\n      computation: selector => this.getElement(selector) || this.customViewport()?.nativeElement\n    });\n    this.viewportError = computed(() => {\n      return !this.viewportElement() ? `[NgScrollbar]: Could not find the viewport element for the provided selector \"${this.externalViewport()}\"` : null;\n    });\n    this.contentWrapperElement = linkedSignal({\n      source: this.externalContentWrapper,\n      computation: selector => this.getElement(selector)\n    });\n    this.contentWrapperError = computed(() => {\n      return !this.contentWrapperElement() && this.externalContentWrapper() ? `[NgScrollbar]: Content wrapper element not found for the provided selector \"${this.externalContentWrapper()}\"` : null;\n    });\n    this.spacerElement = linkedSignal({\n      source: this.externalSpacer,\n      computation: selector => this.getElement(selector)\n    });\n    this.spacerError = computed(() => {\n      return !this.spacerElement() && this.externalSpacer() ? `[NgScrollbar]: Spacer element not found for the provided selector \"${this.externalSpacer()}\"` : null;\n    });\n    /**\n     * Reference to the external viewport directive if used\n     */\n    this.customViewport = contentChild(ScrollViewport, {\n      descendants: true\n    });\n  }\n  ngOnDestroy() {\n    if (this._scrollbarsRef) {\n      this.appRef.detachView(this._scrollbarsRef.hostView);\n      this._scrollbarsRef.destroy();\n    }\n  }\n  initialize(viewportElement, contentWrapperElement, spacerElement) {\n    if (this.skipInit) {\n      // If initialized via async detection, then we should set the signals\n      this.viewportElement.set(viewportElement);\n      this.contentWrapperElement.set(contentWrapperElement);\n      this.spacerElement.set(spacerElement);\n    }\n    // If no external spacer and no content wrapper are provided, create a content wrapper element\n    if (!spacerElement && !contentWrapperElement) {\n      contentWrapperElement = this.renderer.createElement('div');\n      // Move all content of the viewport into the content wrapper\n      const childNodes = Array.from(viewportElement.childNodes);\n      childNodes.forEach(node => this.renderer.appendChild(contentWrapperElement, node));\n      // Append the content wrapper to the viewport\n      this.renderer.appendChild(viewportElement, contentWrapperElement);\n    }\n    // Make sure content wrapper element is defined to proceed\n    if (contentWrapperElement) {\n      // Initialize viewport\n      this.viewport.init(viewportElement, contentWrapperElement, spacerElement);\n      // Attach scrollbars\n      this._attachScrollbars();\n    }\n  }\n  _attachScrollbars() {\n    // Create the scrollbars component\n    this._scrollbarsRef = createComponent(Scrollbars, {\n      environmentInjector: this.appRef.injector,\n      elementInjector: Injector.create({\n        providers: [{\n          provide: NG_SCROLLBAR,\n          useValue: this\n        }]\n      })\n    });\n    // Attach scrollbar to the content wrapper\n    this.renderer.appendChild(this.viewport.contentWrapperElement, this._scrollbarsRef.location.nativeElement);\n    // Attach the host view of the component to the main change detection tree, so that its lifecycle hooks run.\n    this.appRef.attachView(this._scrollbarsRef.hostView);\n    // Set the scrollbars instance\n    this._scrollbars.set(this._scrollbarsRef.instance);\n  }\n  getElement(selector) {\n    return selector ? this.nativeElement.querySelector(selector) : null;\n  }\n  static {\n    this.ɵfac = function NgScrollbarExt_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgScrollbarExt)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NgScrollbarExt,\n      selectors: [[\"ng-scrollbar\", \"externalViewport\", \"\"]],\n      contentQueries: function NgScrollbarExt_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuerySignal(dirIndex, ctx.customViewport, ScrollViewport, 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵqueryAdvance();\n        }\n      },\n      hostAttrs: [\"ngSkipHydration\", \"true\"],\n      hostVars: 2,\n      hostBindings: function NgScrollbarExt_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ng-scrollbar-external-viewport\", true);\n        }\n      },\n      inputs: {\n        externalViewport: [1, \"externalViewport\"],\n        externalContentWrapper: [1, \"externalContentWrapper\"],\n        externalSpacer: [1, \"externalSpacer\"]\n      },\n      exportAs: [\"ngScrollbar\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_SCROLLBAR,\n        useExisting: NgScrollbarExt\n      }, {\n        provide: NgScrollbarCore,\n        useExisting: NgScrollbar\n      }, ViewportAdapter]), i0.ɵɵInheritDefinitionFeature],\n      attrs: _c4,\n      ngContentSelectors: _c3,\n      decls: 1,\n      vars: 0,\n      template: function NgScrollbarExt_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [_c5],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgScrollbarExt, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'ng-scrollbar[externalViewport]',\n      exportAs: 'ngScrollbar',\n      template: '<ng-content/>',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        // This component appends a content wrapper element to the viewport\n        // A hydration mismatch error will be thrown (NG0500) during DOM manipulation.\n        // To avoid this error, the 'ngSkipHydration' attribute is added to skip hydration.\n        ngSkipHydration: 'true',\n        '[class.ng-scrollbar-external-viewport]': 'true'\n      },\n      providers: [{\n        provide: NG_SCROLLBAR,\n        useExisting: NgScrollbarExt\n      }, {\n        provide: NgScrollbarCore,\n        useExisting: NgScrollbar\n      }, ViewportAdapter],\n      styles: [\":host{display:block;position:relative;max-height:100%;max-width:100%;--INTERNAL-scrollbar-border-radius: var(--scrollbar-border-radius, 0px);--INTERNAL-scrollbar-thickness: var(--scrollbar-thickness, 5);--INTERNAL-scrollbar-offset: var(--scrollbar-offset, 0);--INTERNAL-scrollbar-track-wrapper-transition: var(--scrollbar-track-wrapper-transition, width 60ms linear, height 60ms linear);--INTERNAL-scrollbar-track-color: var(--scrollbar-track-color, transparent);--INTERNAL-scrollbar-thumb-color: var(--scrollbar-thumb-color, rgb(0 0 0 / 20%));--INTERNAL-scrollbar-thumb-hover-color: var(--scrollbar-thumb-hover-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-hover-thickness: var(--scrollbar-hover-thickness, var(--INTERNAL-scrollbar-thickness));--INTERNAL-scrollbar-thumb-transition: var(--scrollbar-thumb-transition, none);--INTERNAL-scrollbar-thumb-min-size: var(--scrollbar-thumb-min-size, 20);--INTERNAL-scrollbar-button-color: var(--scrollbar-button-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-button-hover-color: var(--scrollbar-button-hover-color, var(--INTERNAL-scrollbar-button-color));--INTERNAL-scrollbar-button-active-color: var(--scrollbar-button-active-color, var(--INTERNAL-scrollbar-button-hover-color));--INTERNAL-scrollbar-button-fill: var(--scrollbar-button-fill, white);--INTERNAL-scrollbar-button-hover-fill: var(--scrollbar-button-hover-fill, var(--INTERNAL-scrollbar-button-fill));--INTERNAL-scrollbar-button-active-fill: var(--scrollbar-button-active-fill, var(--INTERNAL-scrollbar-button-hover-fill));--INTERNAL-scrollbar-button-size: var(--scrollbar-button-size, 20px);--INTERNAL-scrollbar-hover-opacity-transition-enter-duration: var(--scrollbar-hover-opacity-transition-enter-duration, 0);--INTERNAL-scrollbar-hover-opacity-transition-leave-duration: var(--scrollbar-hover-opacity-transition-leave-duration, .4s);--INTERNAL-scrollbar-hover-opacity-transition-leave-delay: var(--scrollbar-hover-opacity-transition-leave-delay, 1s);--INTERNAL-scrollbar-overscroll-behavior: var(--scrollbar-overscroll-behavior, initial);--INTERNAL-scrollbar-mobile-overscroll-behavior: var(--scrollbar-mobile-overscroll-behavior, none);--_scrollbar-thickness: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 2);--_scrollbar-pointer-events: auto;--_scrollbar-offset-px: calc(var(--INTERNAL-scrollbar-offset) * 1px);--_scrollbar-thickness-px: calc(var(--INTERNAL-scrollbar-thickness) * 1px);--_scrollbar-hover-thickness-px: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_viewport-padding-top: 0;--_viewport-padding-bottom: 0;--_viewport-padding-left: 0;--_viewport-padding-right: 0;--_horizontal-thumb-display: block;--_vertical-thumb-display: block;--_viewport-overflow: auto;--_viewport-pointer-events: auto;--_thumb-x-color: var(--INTERNAL-scrollbar-thumb-color);--_thumb-y-color: var(--INTERNAL-scrollbar-thumb-color);--_track-y-thickness: var(--_scrollbar-thickness-px);--_track-x-thickness: var(--_scrollbar-thickness-px);--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-overscroll-behavior);--_scrollbar-content-width: fit-content}:host{--_spacer-width: var(--spacer-width);--_spacer-height: var(--spacer-height);--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-offset-px);--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-offset-px);--_horizontal-top: initial;--_horizontal-bottom: var(--_scrollbar-offset-px);--_scrollbar-wrapper-x-top: calc(var(--viewport-height) - var(--_scrollbar-thickness));--reached-offset: 1px;--reached-offset-top: var(--reached-offset);--reached-offset-bottom: var(--reached-offset);--reached-offset-start: var(--reached-offset);--reached-offset-end: var(--reached-offset);--dropped-offset: 1px;--dropped-offset-top: var(--dropped-offset);--dropped-offset-bottom: var(--dropped-offset);--dropped-offset-start: var(--dropped-offset);--dropped-offset-end: var(--dropped-offset);--_viewport_scroll-timeline: unset;--_animation-timeline-y: unset;--_scrollbar-y-thumb-transform-to-value: unset;--_scrollbar-x-thumb-transform-to-value: unset;--_scrollbar-thumb-transform-from: unset;--_scrollbar-thumb-transform-to: unset}:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{min-height:100%;min-width:100%;height:100%;max-height:100%;max-width:100%}:host.ng-scroll-viewport,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{position:relative;overflow:var(--_viewport-overflow);scroll-timeline:var(--_viewport_scroll-timeline);box-sizing:border-box!important;-webkit-overflow-scrolling:touch;will-change:scroll-position;-webkit-user-select:var(--_viewport-user-select);user-select:var(--_viewport-user-select);overscroll-behavior:var(--_viewport-overscroll-behavior);pointer-events:var(--_viewport-pointer-events)}:host.ng-scroll-viewport>.ng-scroll-content,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport>.ng-scroll-content{width:var(--_scrollbar-content-width);z-index:1;min-width:100%;min-height:100%;contain:content;padding:var(--_viewport-padding-top, 0) var(--_viewport-padding-right, 0) var(--_viewport-padding-bottom, 0) var(--_viewport-padding-left, 0)}:host[appearance=native]{--_spacer-width: calc(var(--spacer-width) + var(--_scrollbar-thickness));--_spacer-height: calc(var(--spacer-height) + var(--_scrollbar-thickness))}:host.ng-scroll-viewport>.ng-scroll-spacer,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport>.ng-scroll-spacer{position:relative;width:calc(var(--_spacer-width) * 1px);height:calc(var(--_spacer-height) * 1px)}:host.ng-scroll-viewport,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{scrollbar-width:none!important}:host.ng-scroll-viewport::-webkit-scrollbar,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport::-webkit-scrollbar{display:none!important}:host[position=invertX],:host[position=invertAll]{--_horizontal-top: var(--_scrollbar-offset-px);--_horizontal-bottom: initial;--_scrollbar-wrapper-x-top: 0}:host[dir=ltr]{--_scrollbar-wrapper-y-right: initial;--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-left: calc(var(--viewport-width) - var(--_scrollbar-thickness))}:host[dir=ltr][position=invertY],:host[dir=ltr][position=invertAll]{--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-left: 0}:host[dir=rtl]{--_scrollbar-wrapper-y-left: initial;--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-right: calc(var(--viewport-width) - var(--_scrollbar-thickness))}:host[dir=rtl][position=invertY],:host[dir=rtl][position=invertAll]{--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-right: 0}:host[verticalUsed=true][horizontalUsed=true]{--_scrollbar-thickness-margin: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 3);--_scrollbar-thickness-margin-px: calc(var(--_scrollbar-thickness-margin) * 1px)}:host[horizontalUsed=true]{--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-thickness-margin-px)}:host[horizontalUsed=true][position=invertX],:host[horizontalUsed=true][position=invertAll]{--_vertical-top: var(--_scrollbar-thickness-margin-px);--_vertical-bottom: var(--_scrollbar-offset-px)}:host[verticalUsed=true][dir=ltr]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}:host[verticalUsed=true][dir=rtl]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}:host[verticalUsed=true][position=invertY][dir=ltr],:host[verticalUsed=true][position=invertAll][dir=ltr]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}:host[verticalUsed=true][position=invertY][dir=rtl],:host[verticalUsed=true][position=invertAll][dir=rtl]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}:host[appearance=native][verticalUsed=true][dir=ltr]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][verticalUsed=true][dir=rtl]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}:host[appearance=native][verticalUsed=true][position=invertY][dir=ltr],:host[appearance=native][verticalUsed=true][position=invertAll][dir=ltr]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}:host[appearance=native][verticalUsed=true][position=invertY][dir=rtl],:host[appearance=native][verticalUsed=true][position=invertAll][dir=rtl]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][horizontalUsed=true]{--_viewport-padding-top: 0;--_viewport-padding-bottom: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][horizontalUsed=true][position=invertX],:host[appearance=native][horizontalUsed=true][position=invertAll]{--_viewport-padding-top: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-bottom: 0}:host[visibility=hover]{--_scrollbar-hover-opacity: 0;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-leave-duration) var(--INTERNAL-scrollbar-hover-opacity-transition-leave-delay)}:host[visibility=hover]:hover,:host[visibility=hover]:active,:host[visibility=hover]:focus{--_scrollbar-hover-opacity: 1;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-enter-duration)}:host[dir=ltr] ::ng-deep .scroll-reached-trigger-element[trigger=start],:host[dir=ltr] ::ng-deep .scroll-dropped-trigger-element[trigger=start]{left:0;right:unset}:host[dir=ltr] ::ng-deep .scroll-reached-trigger-element[trigger=end],:host[dir=ltr] ::ng-deep .scroll-dropped-trigger-element[trigger=end]{right:0;left:unset}:host[dir=rtl] ::ng-deep .scroll-reached-trigger-element[trigger=start],:host[dir=rtl] ::ng-deep .scroll-dropped-trigger-element[trigger=start]{right:0;left:unset}:host[dir=rtl] ::ng-deep .scroll-reached-trigger-element[trigger=end],:host[dir=rtl] ::ng-deep .scroll-dropped-trigger-element[trigger=end]{left:0;right:unset}:host ::ng-deep .ng-scroll-reached-wrapper,:host ::ng-deep .ng-scroll-dropped-wrapper,:host ::ng-deep .scroll-reached-trigger-element,:host ::ng-deep .scroll-dropped-trigger-element{position:absolute;-webkit-user-select:none;user-select:none;pointer-events:none;z-index:-9999}:host ::ng-deep .ng-scroll-reached-wrapper,:host ::ng-deep .ng-scroll-dropped-wrapper{visibility:hidden;inset:0;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}:host ::ng-deep [isHorizontallyScrollable=false] .scroll-reached-trigger-element[trigger=end],:host ::ng-deep [isHorizontallyScrollable=false] .scroll-dropped-trigger-element[trigger=end]{display:none}:host ::ng-deep [isVerticallyScrollable=false] .scroll-reached-trigger-element[trigger=bottom],:host ::ng-deep [isVerticallyScrollable=false] .scroll-dropped-trigger-element[trigger=bottom]{display:none}:host ::ng-deep .scroll-reached-trigger-element{background:red}:host ::ng-deep .scroll-reached-trigger-element[trigger=top],:host ::ng-deep .scroll-reached-trigger-element[trigger=bottom]{left:0;right:0}:host ::ng-deep .scroll-reached-trigger-element[trigger=start],:host ::ng-deep .scroll-reached-trigger-element[trigger=end]{top:0;bottom:0}:host ::ng-deep .scroll-reached-trigger-element[trigger=top]{top:0;height:var(--reached-offset-top)}:host ::ng-deep .scroll-reached-trigger-element[trigger=bottom]{bottom:0;height:var(--reached-offset-bottom)}:host ::ng-deep .scroll-reached-trigger-element[trigger=start]{width:var(--reached-offset-start)}:host ::ng-deep .scroll-reached-trigger-element[trigger=end]{width:var(--reached-offset-end)}:host .scroll-dropped-trigger-element{background:#00f}:host .scroll-dropped-trigger-element[trigger=top],:host .scroll-dropped-trigger-element[trigger=bottom]{left:0;right:0}:host .scroll-dropped-trigger-element[trigger=start],:host .scroll-dropped-trigger-element[trigger=end]{top:0;bottom:0}:host .scroll-dropped-trigger-element[trigger=top]{top:0;height:var(--dropped-offset-top)}:host .scroll-dropped-trigger-element[trigger=bottom]{bottom:0;height:var(--dropped-offset-bottom)}:host .scroll-dropped-trigger-element[trigger=start]{width:var(--dropped-offset-start)}:host .scroll-dropped-trigger-element[trigger=end]{width:var(--dropped-offset-end)}:host[verticalUsed=true]{--_timeline-scope: --scrollerY;--_animation-timeline-y: --scrollerY;--_viewport_scroll-timeline: --scrollerY y}:host[horizontalUsed=true]{--_timeline-scope: --scrollerX;--_animation-timeline-x: --scrollerX;--_viewport_scroll-timeline: --scrollerX x}:host[verticalUsed=true][horizontalUsed=true]{--_timeline-scope: --scrollerX, --scrollerY;--_viewport_scroll-timeline: --scrollerX x, --scrollerY y}:host[orientation=vertical]{--_viewport-overflow: hidden auto;--_scrollbar-content-width: unset}:host[orientation=horizontal]{--_viewport-overflow: auto hidden}:host[disableInteraction=true]{--_viewport-pointer-events: none;--_scrollbar-pointer-events: none}:host[isVerticallyScrollable=false]{--_vertical-thumb-display: none}:host[isHorizontallyScrollable=false]{--_horizontal-thumb-display: none}:host[dragging=x],:host[dragging=y]{--_viewport-user-select: none}:host[dragging=x]{--_track-x-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-x-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}:host[dragging=y]{--_track-y-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-y-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}:host[mobile=true]{--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-mobile-overscroll-behavior)}\\n\"]\n    }]\n  }], () => [], null);\n})();\nclass AsyncDetection {\n  constructor() {\n    this.scrollbar = inject(NgScrollbarExt, {\n      self: true\n    });\n    this.zone = inject(NgZone);\n    this.contentObserver = inject(ContentObserver);\n    this.asyncDetection = input();\n    this.scrollbar.skipInit = true;\n    let sub$;\n    effect(onCleanup => {\n      const init = this.scrollbar.viewport.initialized();\n      const externalViewport = this.scrollbar.externalViewport();\n      const externalContentWrapper = this.scrollbar.externalContentWrapper();\n      const externalSpacer = this.scrollbar.externalSpacer();\n      const asyncDetection = this.asyncDetection();\n      untracked(() => {\n        let viewportElement;\n        let contentWrapperElement;\n        this.zone.runOutsideAngular(() => {\n          // The content observer should not be throttled using the same function we use for ResizeObserver,\n          // It should detect the content change asap to attach the scrollbar\n          sub$ = this.contentObserver.observe(this.scrollbar.nativeElement).pipe(throttleTime(100, null, {\n            leading: true,\n            trailing: true\n          })).subscribe(() => {\n            // Search for external viewport\n            viewportElement = this.scrollbar.nativeElement.querySelector(externalViewport);\n            // Search for external content wrapper\n            contentWrapperElement = this.scrollbar.nativeElement.querySelector(externalContentWrapper);\n            this.zone.run(() => {\n              if (!init && viewportElement && contentWrapperElement) {\n                // If an external spacer selector is provided, search for it\n                let spacerElement;\n                if (externalSpacer) {\n                  spacerElement = this.scrollbar.nativeElement.querySelector(externalSpacer);\n                }\n                this.scrollbar.initialize(viewportElement, contentWrapperElement, spacerElement);\n              } else if (!viewportElement || !contentWrapperElement) {\n                this.scrollbar.viewport.reset();\n              }\n            });\n            if (asyncDetection !== 'auto') {\n              sub$.unsubscribe();\n            }\n          });\n        });\n        onCleanup(() => sub$?.unsubscribe());\n      });\n    });\n  }\n  static {\n    this.ɵfac = function AsyncDetection_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AsyncDetection)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: AsyncDetection,\n      selectors: [[\"ng-scrollbar\", \"externalViewport\", \"\", \"asyncDetection\", \"\"]],\n      inputs: {\n        asyncDetection: [1, \"asyncDetection\"]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AsyncDetection, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-scrollbar[externalViewport][asyncDetection]'\n    }]\n  }], () => [], null);\n})();\nclass SyncSpacer {\n  constructor() {\n    this.sharedResizeObserver = inject(SharedResizeObserver);\n    this.scrollbar = inject(NgScrollbarExt, {\n      self: true\n    });\n    this.zone = inject(NgZone);\n    /**\n     * A signal used to sync spacer dimension when content dimension changes\n     */\n    this.spacerDimension = signal({});\n    let sub$;\n    effect(onCleanup => {\n      const spacerElement = this.scrollbar.spacerElement();\n      const contentWrapperElement = this.scrollbar.contentWrapperElement();\n      const throttleDuration = this.scrollbar.sensorThrottleTime();\n      const disableSensor = this.scrollbar.disableSensor();\n      untracked(() => {\n        if (!disableSensor && contentWrapperElement && spacerElement) {\n          // Sync spacer dimension with content wrapper dimensions to allow both scrollbars to be displayed\n          this.zone.runOutsideAngular(() => {\n            sub$ = getThrottledStream(this.sharedResizeObserver.observe(contentWrapperElement), throttleDuration).pipe(map(entries => filterResizeEntries(entries, contentWrapperElement))).subscribe(() => {\n              this.zone.run(() => {\n                // Use animation frame to avoid \"ResizeObserver loop completed with undelivered notifications.\" error\n                requestAnimationFrame(() => {\n                  this.spacerDimension.set({\n                    width: contentWrapperElement.offsetWidth,\n                    height: contentWrapperElement.offsetHeight\n                  });\n                });\n              });\n            });\n          });\n        }\n        onCleanup(() => sub$?.unsubscribe());\n      });\n    });\n  }\n  static {\n    this.ɵfac = function SyncSpacer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SyncSpacer)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SyncSpacer,\n      selectors: [[\"ng-scrollbar\", \"externalViewport\", \"\", \"syncSpacer\", \"\"]],\n      hostVars: 4,\n      hostBindings: function SyncSpacer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--spacer-width\", ctx.spacerDimension().width)(\"--spacer-height\", ctx.spacerDimension().height);\n        }\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SyncSpacer, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-scrollbar[externalViewport][syncSpacer]',\n      host: {\n        '[style.--spacer-width]': 'spacerDimension().width',\n        '[style.--spacer-height]': 'spacerDimension().height'\n      }\n    }]\n  }], () => [], null);\n})();\nclass NgScrollbarModule {\n  static {\n    this.ɵfac = function NgScrollbarModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgScrollbarModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NgScrollbarModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgScrollbarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NgScrollbar, ScrollViewport, NgScrollbarExt, AsyncDetection, SyncSpacer],\n      exports: [NgScrollbar, ScrollViewport, NgScrollbarExt, AsyncDetection, SyncSpacer]\n    }]\n  }], null, null);\n})();\nfunction provideScrollbarOptions(options) {\n  return [{\n    provide: NG_SCROLLBAR_OPTIONS,\n    useValue: {\n      ...defaultOptions,\n      ...options\n    }\n  }];\n}\nfunction provideScrollbarPolyfill(url) {\n  return makeEnvironmentProviders([{\n    provide: NG_SCROLLBAR_POLYFILL,\n    useValue: url\n  }]);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AsyncDetection, NG_SCROLLBAR, NG_SCROLLBAR_OPTIONS, NG_SCROLLBAR_POLYFILL, NgScrollbar, NgScrollbarExt, NgScrollbarModule, ScrollViewport, ScrollbarUpdateReason, SyncSpacer, filterResizeEntries, provideScrollbarOptions, provideScrollbarPolyfill };", "map": {"version": 3, "names": ["i0", "inject", "ElementRef", "Directive", "signal", "Injectable", "InjectionToken", "NgZone", "input", "booleanAttribute", "numberAttribute", "computed", "output", "afterRenderEffect", "untracked", "effect", "PLATFORM_ID", "Component", "ChangeDetectionStrategy", "viewChild", "Renderer2", "ApplicationRef", "linkedSignal", "contentChild", "createComponent", "Injector", "NgModule", "makeEnvironmentProviders", "tap", "throttleTime", "combineLatest", "fromEvent", "map", "merge", "startWith", "switchMap", "takeUntil", "delay", "EMPTY", "<PERSON><PERSON><PERSON><PERSON>", "from", "of", "interval", "animationFrameScheduler", "Platform", "Directionality", "SharedResizeObserver", "toSignal", "SmoothScrollManager", "DOCUMENT", "isPlatformBrowser", "ContentObserver", "_c0", "ScrollbarY_Conditional_4_Template", "rf", "ctx", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵclassMapInterpolate1", "cmp", "buttonClass", "ɵɵadvance", "ScrollbarX_Conditional_4_Template", "_c1", "Scrollbars_Conditional_0_Template", "Scrollbars_Conditional_1_Template", "_c2", "_c3", "_c4", "_c5", "ScrollViewport", "constructor", "nativeElement", "ɵfac", "ScrollViewport_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "ngDevMode", "ɵsetClassMetadata", "args", "standalone", "selector", "preventSelection", "doc", "onselectstart", "enableSelection", "stopPropagation", "e", "preventDefault", "getThrottledStream", "stream", "duration", "pipe", "leading", "trailing", "ViewportClasses", "ViewportAdapter", "initialized", "offsetHeight", "offsetWidth", "scrollTop", "scrollLeft", "contentHeight", "contentWrapperElement", "contentWidth", "scrollMaxX", "scrollMaxY", "init", "viewportElement", "contentElement", "spacerElement", "classList", "add", "Viewport", "Content", "Spacer", "set", "reset", "scrollYTo", "value", "scrollXTo", "ViewportAdapter_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "NG_SCROLLBAR", "defaultOptions", "trackClass", "thumbClass", "orientation", "appearance", "visibility", "position", "trackScrollDuration", "sensorThrottleTime", "disableSensor", "disableInteraction", "buttons", "hoverOffset", "defaultScrollTimelinePolyfill", "ScrollbarUpdateReason", "NG_SCROLLBAR_OPTIONS", "providedIn", "NG_SCROLLBAR_POLYFILL", "filterResizeEntries", "entries", "target", "filter", "entry", "contentRect", "NgScrollbarCore", "options", "sharedResizeObserver", "zone", "platform", "isMobile", "IOS", "ANDROID", "dir", "smoothScroll", "viewport", "self", "direction", "change", "initialValue", "dragging", "transform", "viewportDimension", "width", "height", "contentDimension", "state", "verticalUsed", "horizontalUsed", "isVerticallyScrollable", "isHorizontallyScrollable", "viewportDimensions", "contentDimensions", "afterInit", "afterUpdate", "resizeSub$", "hasInitialized", "earlyRead", "onCleanup", "throttleDuration", "viewportInit", "requestAnimationFrame", "update", "AfterInit", "runOutsideAngular", "observe", "subscribe", "run", "updateDimensions", "emit", "unsubscribe", "reason", "scrollTo", "scrollToElement", "NgScrollbarCore_Factory", "hostVars", "hostBindings", "NgScrollbarCore_HostBindings", "ɵɵattribute", "ɵɵstyleProp", "ɵɵclassProp", "inputs", "outputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "host", "providers", "SCROLLBAR_CONTROL", "ScrollbarAdapter", "trackSize", "ScrollbarAdapter_Factory", "PointerEventsAdapter", "control", "document", "_pointerEventsSub", "pointerEvents", "PointerEventsAdapter_Factory", "TrackAdapter", "viewportSize", "sizeProperty", "clientRect", "getBoundingClientRect", "offset", "rectOffsetProperty", "size", "pointerDown$", "pointerUp$", "passive", "pointerEnter$", "currMousePosition", "offsetProperty", "pointerLeave$", "pointerOver$", "pointerMove$", "startEvent", "onTrackFirstClick", "over", "currDirection", "getScrollDirection", "sameDirection", "scrollDirection", "onTrackOngoingMousedown", "scrollMax", "viewportScrollMax", "nextStep", "scrollForwardIncrement", "getScrollForwardStep", "scrollBackwardIncrement", "getScrollBackwardStep", "isReached", "TrackAdapter_Factory", "ɵɵInheritDefinitionFeature", "TrackXDirective", "contentSize", "getCurrPosition", "viewportScrollOffset", "start", "TrackXDirective_Factory", "TrackYDirective", "top", "ɵTrackYDirective_BaseFactory", "TrackYDirective_Factory", "ɵɵgetInheritedFactory", "ScrollbarManager", "<PERSON><PERSON><PERSON><PERSON>", "_polyfillUrl", "window", "defaultView", "scrollTimelinePolyfill", "CSS", "supports", "initPolyfill", "_this", "_asyncToGenerator", "script", "createElement", "src", "Promise", "resolve", "reject", "onload", "onerror", "head", "append<PERSON><PERSON><PERSON>", "console", "error", "ScrollbarManager_Factory", "ThumbAdapter", "rectSizeProperty", "trackMax", "track", "startTrackMax", "startScrollMax", "dragStart", "setDragging", "axis", "dragEnd", "capture", "startOffset", "moveEvent", "clientProperty", "moveClient", "trackRelativeOffset", "scrollPosition", "instantScrollTo", "manager", "_animation", "startPolyfill", "ThumbAdapter_Factory", "ScrollTimeline", "element", "source", "animate", "translate", "fill", "easing", "timeline", "ThumbXDirective", "ɵThumbXDirective_BaseFactory", "ThumbXDirective_Factory", "ThumbYDirective", "ɵThumbYDirective_BaseFactory", "ThumbYDirective_Factory", "canScrollFunc", "forward", "scrollOffset", "backward", "scrollStepFunc", "scrollBy", "horizontalScrollStepFunc", "rtl", "ltr", "ScrollbarButton", "pointerUpOrLeave$", "firstScrollStep", "afterFirstClickDelay", "onOngoingPointerdown", "canScroll", "scrollbarButton", "required", "firstClickDuration", "onGoingScrollBy", "onGoingScrollStep", "ScrollbarButton_Factory", "ɵcmp", "ɵɵdefineComponent", "attrs", "decls", "vars", "consts", "template", "ScrollbarButton_Template", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelementEnd", "styles", "changeDetection", "OnPush", "ScrollbarY", "arguments", "ɵScrollbarY_BaseFactory", "ScrollbarY_Factory", "ScrollbarY_HostBindings", "ScrollbarY_Template", "ɵɵconditionalCreate", "ɵɵconditional", "dependencies", "imports", "ScrollbarX", "Math", "abs", "handlePosition", "left", "ScrollbarX_Factory", "ScrollbarX_HostBindings", "ScrollbarX_Template", "Scrollbars", "Scrollbars_Factory", "Scrollbars_Template", "NgScrollbar", "contentWrapper", "_scrollbars", "NgScrollbar_Factory", "viewQuery", "NgScrollbar_Query", "ɵɵviewQuerySignal", "ɵɵqueryAdvance", "exportAs", "ngContentSelectors", "NgScrollbar_Template", "ɵɵprojectionDef", "ɵɵprojection", "NgScrollbarExt", "viewportError", "contentWrapperError", "spacerError", "skipInit", "initialize", "renderer", "appRef", "externalViewport", "externalContentWrapper", "externalSpacer", "computation", "getElement", "customViewport", "descendants", "ngOnDestroy", "_scrollbarsRef", "detach<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "childNodes", "Array", "for<PERSON>ach", "node", "_attachScrollbars", "environmentInjector", "injector", "elementInjector", "create", "useValue", "location", "attachView", "instance", "querySelector", "NgScrollbarExt_Factory", "contentQueries", "NgScrollbarExt_ContentQueries", "dirIndex", "ɵɵcontentQuerySignal", "hostAttrs", "NgScrollbarExt_HostBindings", "NgScrollbarExt_Template", "ngSkipHydration", "AsyncDetection", "scrollbar", "contentObserver", "asyncDetection", "sub$", "AsyncDetection_Factory", "SyncSpacer", "spacerDimension", "SyncSpacer_Factory", "SyncSpacer_HostBindings", "NgScrollbarModule", "NgScrollbarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "provideScrollbarOptions", "provideScrollbarPolyfill", "url"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/ngx-scrollbar/fesm2022/ngx-scrollbar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, ElementRef, Directive, signal, Injectable, InjectionToken, NgZone, input, booleanAttribute, numberAttribute, computed, output, afterRenderEffect, untracked, effect, PLATFORM_ID, Component, ChangeDetectionStrategy, viewChild, Renderer2, ApplicationRef, linkedSignal, contentChild, createComponent, Injector, NgModule, makeEnvironmentProviders } from '@angular/core';\nimport { tap, throttleTime, combineLatest, fromEvent, map, merge, startWith, switchMap, takeUntil, delay, EMPTY, takeWhile, from, of, interval, animationFrameScheduler } from 'rxjs';\nimport { Platform } from '@angular/cdk/platform';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { SmoothScrollManager } from 'ngx-scrollbar/smooth-scroll';\nimport { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { ContentObserver } from '@angular/cdk/observers';\n\nclass ScrollViewport {\n    constructor() {\n        this.nativeElement = inject((ElementRef)).nativeElement;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ScrollViewport, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.1.1\", type: ScrollViewport, isStandalone: true, selector: \"[scrollViewport]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ScrollViewport, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[scrollViewport]'\n                }]\n        }] });\n\nfunction preventSelection(doc) {\n    return tap(() => doc.onselectstart = () => false);\n}\nfunction enableSelection(doc) {\n    return tap(() => doc.onselectstart = null);\n}\nfunction stopPropagation() {\n    return tap((e) => {\n        // Have to prevent default to avoid unexpected movement whe you grab object beneath scrollbar #476\n        // https://github.com/MurhafSousli/ngx-scrollbar/issues/476\n        e.preventDefault();\n        e.stopPropagation();\n    });\n}\nfunction getThrottledStream(stream, duration) {\n    return stream.pipe(throttleTime(duration || 0, null, {\n        leading: false,\n        trailing: true\n    }));\n}\nvar ViewportClasses;\n(function (ViewportClasses) {\n    ViewportClasses[\"Viewport\"] = \"ng-scroll-viewport\";\n    ViewportClasses[\"Content\"] = \"ng-scroll-content\";\n    ViewportClasses[\"Spacer\"] = \"ng-scroll-spacer\";\n})(ViewportClasses || (ViewportClasses = {}));\n\n/**\n * Class representing a viewport adapter.\n * Provides methods and properties to interact with a viewport and its content.\n */\nclass ViewportAdapter {\n    constructor() {\n        /*\n         * A signal that indicates when viewport adapter is initialized\n         */\n        this.initialized = signal(false);\n    }\n    /** Viewport clientHeight */\n    get offsetHeight() {\n        return this.nativeElement.offsetHeight;\n    }\n    /** Viewport clientWidth */\n    get offsetWidth() {\n        return this.nativeElement.offsetWidth;\n    }\n    /** Viewport scrollTop */\n    get scrollTop() {\n        return this.nativeElement.scrollTop;\n    }\n    /** Viewport scrollLeft */\n    get scrollLeft() {\n        return this.nativeElement.scrollLeft;\n    }\n    /** Content height */\n    get contentHeight() {\n        return this.contentWrapperElement.offsetHeight;\n    }\n    /** Content width */\n    get contentWidth() {\n        return this.contentWrapperElement.offsetWidth;\n    }\n    /** The remaining vertical scrollable distance. */\n    get scrollMaxX() {\n        return this.contentWidth - this.offsetWidth;\n    }\n    /** The vertical remaining scrollable distance */\n    get scrollMaxY() {\n        return this.contentHeight - this.offsetHeight;\n    }\n    /**\n     * Initialize viewport\n     */\n    init(viewportElement, contentElement, spacerElement) {\n        // Add viewport class\n        viewportElement.classList.add(ViewportClasses.Viewport);\n        this.nativeElement = viewportElement;\n        // Add content wrapper class\n        contentElement.classList.add(ViewportClasses.Content);\n        // When integrating the scrollbar with virtual scroll, the content wrapper will have fake size,\n        // and a spacer element will have the real size\n        // Therefore, if spaceElement is provided, it will be observed instead of the content wrapper\n        if (spacerElement) {\n            spacerElement.classList.add(ViewportClasses.Spacer);\n            this.contentWrapperElement = spacerElement;\n        }\n        else {\n            // If spacer is not provided, set it as the content wrapper\n            this.contentWrapperElement = contentElement;\n        }\n        this.initialized.set(true);\n    }\n    reset() {\n        this.nativeElement = null;\n        this.contentWrapperElement = null;\n        this.initialized.set(false);\n    }\n    /**\n     * Scrolls the viewport vertically to the specified value.\n     */\n    scrollYTo(value) {\n        this.nativeElement.scrollTop = value;\n    }\n    /**\n     * Scrolls the viewport horizontally to the specified value.\n     */\n    scrollXTo(value) {\n        this.nativeElement.scrollLeft = value;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ViewportAdapter, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ViewportAdapter }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ViewportAdapter, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * Injection token that can be used to query for a `NgScrollbar`.\n * Used primarily to avoid circular imports.\n */\nconst NG_SCROLLBAR = new InjectionToken('NG_SCROLLBAR');\n\nconst defaultOptions = {\n    trackClass: '',\n    thumbClass: '',\n    buttonClass: '',\n    orientation: 'auto',\n    appearance: 'native',\n    visibility: 'native',\n    position: 'native',\n    trackScrollDuration: 50,\n    sensorThrottleTime: 0,\n    disableSensor: false,\n    disableInteraction: false,\n    buttons: false,\n    hoverOffset: false\n};\n// This CDN link for a modified version of the polyfill to fix firefox bug https://github.com/MurhafSousli/ngx-scrollbar/issues/615\nconst defaultScrollTimelinePolyfill = 'https://cdn.statically.io/gist/MurhafSousli/c852b6a672069396953f06ddd4b64620/raw/ef55db72e2abb7bc002ed79f4ad4cf408bfdb72f/scroll-timeline-lite.js';\n\nvar ScrollbarUpdateReason;\n(function (ScrollbarUpdateReason) {\n    ScrollbarUpdateReason[\"AfterInit\"] = \"AfterInit\";\n    ScrollbarUpdateReason[\"Resized\"] = \"ResizeObserver\";\n})(ScrollbarUpdateReason || (ScrollbarUpdateReason = {}));\nconst NG_SCROLLBAR_OPTIONS = new InjectionToken('NG_SCROLLBAR_OPTIONS', {\n    providedIn: 'root',\n    factory: () => defaultOptions\n});\nconst NG_SCROLLBAR_POLYFILL = new InjectionToken('NG_SCROLLBAR_POLYFILL', {\n    providedIn: 'root',\n    factory: () => defaultScrollTimelinePolyfill\n});\nfunction filterResizeEntries(entries, target) {\n    return entries.filter((entry) => entry.target === target)[0]?.contentRect;\n}\n\nclass NgScrollbarCore {\n    constructor() {\n        /** Global options */\n        this.options = inject(NG_SCROLLBAR_OPTIONS);\n        this.sharedResizeObserver = inject(SharedResizeObserver);\n        this.zone = inject(NgZone);\n        this.platform = inject(Platform);\n        /** A flag that indicates if the platform is mobile */\n        this.isMobile = this.platform.IOS || this.platform.ANDROID;\n        this.dir = inject(Directionality);\n        this.smoothScroll = inject(SmoothScrollManager);\n        /** Viewport adapter instance */\n        this.viewport = inject(ViewportAdapter, { self: true });\n        this.nativeElement = inject((ElementRef)).nativeElement;\n        /**\n         * Indicates if the direction is 'ltr' or 'rtl'\n         */\n        this.direction = toSignal(this.dir.change, { initialValue: this.dir.value });\n        /**\n         * Indicates when scrollbar thumb is being dragged\n         */\n        this.dragging = signal('none');\n        /**\n         * Sets the supported scroll track of the viewport, there are 3 options:\n         *\n         * - `vertical` Use both vertical and horizontal scrollbar\n         * - `horizontal` Use both vertical and horizontal scrollbar\n         * - `auto` Use both vertical and horizontal scrollbar\n         */\n        this.orientation = input(this.options.orientation);\n        /**\n         * When to show the scrollbar, and there are 3 options:\n         *\n         * - `native` (default) Scrollbar will be visible when viewport is scrollable like with native scrollbar\n         * - `hover` Scrollbars are hidden by default, only visible on scrolling or hovering\n         * - `always` Scrollbars are always shown even if the viewport is not scrollable\n         */\n        this.visibility = input(this.options.visibility);\n        /** Show scrollbar buttons */\n        this.buttons = input(this.options.buttons, {\n            transform: booleanAttribute\n        });\n        /** Disables scrollbar interaction like dragging thumb and jumping by track click */\n        this.disableInteraction = input(this.options.disableInteraction, {\n            transform: booleanAttribute\n        });\n        /** Whether ResizeObserver is disabled */\n        this.disableSensor = input(this.options.disableSensor, {\n            transform: booleanAttribute\n        });\n        /** Throttle interval for detecting changes via ResizeObserver */\n        this.sensorThrottleTime = input(this.options.sensorThrottleTime, {\n            transform: numberAttribute\n        });\n        /** A flag used to activate hover effect on the offset area around the scrollbar */\n        this.hoverOffset = input(this.options.hoverOffset, {\n            transform: booleanAttribute\n        });\n        /** Viewport dimension */\n        this.viewportDimension = signal({ width: 0, height: 0 });\n        /** Content dimension */\n        this.contentDimension = signal({ width: 0, height: 0 });\n        this.state = computed(() => {\n            let verticalUsed = false;\n            let horizontalUsed = false;\n            let isVerticallyScrollable = false;\n            let isHorizontallyScrollable = false;\n            const orientation = this.orientation();\n            const visibility = this.visibility();\n            const viewportDimensions = this.viewportDimension();\n            const contentDimensions = this.contentDimension();\n            // Check if vertical scrollbar should be displayed\n            if (orientation === 'auto' || orientation === 'vertical') {\n                isVerticallyScrollable = contentDimensions.height > viewportDimensions.height;\n                verticalUsed = visibility === 'visible' || isVerticallyScrollable;\n            }\n            // Check if horizontal scrollbar should be displayed\n            if (orientation === 'auto' || orientation === 'horizontal') {\n                isHorizontallyScrollable = contentDimensions.width > viewportDimensions.width;\n                horizontalUsed = visibility === 'visible' || isHorizontallyScrollable;\n            }\n            return {\n                verticalUsed,\n                horizontalUsed,\n                isVerticallyScrollable,\n                isHorizontallyScrollable,\n            };\n        });\n        this.isVerticallyScrollable = computed(() => this.state().isVerticallyScrollable);\n        this.isHorizontallyScrollable = computed(() => this.state().isHorizontallyScrollable);\n        this.verticalUsed = computed(() => this.state().verticalUsed);\n        this.horizontalUsed = computed(() => this.state().horizontalUsed);\n        /** Scroll duration when the scroll track is clicked */\n        this.trackScrollDuration = input(this.options.trackScrollDuration, {\n            transform: numberAttribute\n        });\n        /**\n         *  Sets the appearance of the scrollbar, there are 2 options:\n         *\n         * - `native` (default) scrollbar space will be reserved just like with native scrollbar.\n         * - `compact` scrollbar doesn't reserve any space, they are placed over the viewport.\n         */\n        this.appearance = input(this.options.appearance);\n        /**\n         * Sets the position of each scrollbar, there are 4 options:\n         *\n         * - `native` (Default) Use the default position like in native scrollbar.\n         * - `invertY` Inverts vertical scrollbar position\n         * - `invertX` Inverts Horizontal scrollbar position\n         * - `invertAll` Inverts both scrollbar positions\n         */\n        this.position = input(this.options.position);\n        /** A class forwarded to the scrollbar track element */\n        this.trackClass = input(this.options.trackClass);\n        /** A class forwarded to the scrollbar thumb element */\n        this.thumbClass = input(this.options.thumbClass);\n        /** A class forwarded to the scrollbar button element */\n        this.buttonClass = input(this.options.thumbClass);\n        /** Steam that emits when scrollbar is initialized */\n        this.afterInit = output();\n        /** Steam that emits when scrollbar is updated */\n        this.afterUpdate = output();\n        let resizeSub$;\n        let hasInitialized;\n        afterRenderEffect({\n            earlyRead: (onCleanup) => {\n                const disableSensor = this.disableSensor();\n                const throttleDuration = this.sensorThrottleTime();\n                const viewportInit = this.viewport.initialized();\n                untracked(() => {\n                    if (viewportInit) {\n                        // If resize sensor is disabled, update manually the first time\n                        if (disableSensor) {\n                            requestAnimationFrame(() => this.update(ScrollbarUpdateReason.AfterInit));\n                        }\n                        else {\n                            // Observe size changes for viewport and content wrapper\n                            this.zone.runOutsideAngular(() => {\n                                resizeSub$ = getThrottledStream(combineLatest([\n                                    this.sharedResizeObserver.observe(this.viewport.nativeElement),\n                                    this.sharedResizeObserver.observe(this.viewport.contentWrapperElement)\n                                ]), throttleDuration).subscribe(() => {\n                                    // After deep investigation, it appears that setting the dimension directly from the element properties\n                                    // is much faster than to set them from resize callback values\n                                    this.zone.run(() => {\n                                        this.updateDimensions();\n                                        if (hasInitialized) {\n                                            this.afterUpdate.emit();\n                                        }\n                                        else {\n                                            this.afterInit.emit();\n                                        }\n                                        hasInitialized = true;\n                                    });\n                                });\n                            });\n                        }\n                    }\n                    onCleanup(() => resizeSub$?.unsubscribe());\n                });\n            }\n        });\n    }\n    /**\n     * Manual update\n     */\n    update(reason) {\n        this.updateDimensions();\n        if (reason === ScrollbarUpdateReason.AfterInit) {\n            this.afterInit.emit();\n        }\n        else {\n            this.afterUpdate.emit();\n        }\n    }\n    /**\n     * Smooth scroll functions\n     */\n    scrollTo(options) {\n        return this.smoothScroll.scrollTo(this.viewport.nativeElement, options);\n    }\n    /**\n     * Scroll to element by reference or selector\n     */\n    scrollToElement(target, options) {\n        return this.smoothScroll.scrollToElement(this.viewport.nativeElement, target, options);\n    }\n    updateDimensions() {\n        this.viewportDimension.set({ width: this.viewport.offsetWidth, height: this.viewport.offsetHeight });\n        this.contentDimension.set({ width: this.viewport.contentWidth, height: this.viewport.contentHeight });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: NgScrollbarCore, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"17.1.0\", version: \"19.1.1\", type: NgScrollbarCore, isStandalone: true, inputs: { orientation: { classPropertyName: \"orientation\", publicName: \"orientation\", isSignal: true, isRequired: false, transformFunction: null }, visibility: { classPropertyName: \"visibility\", publicName: \"visibility\", isSignal: true, isRequired: false, transformFunction: null }, buttons: { classPropertyName: \"buttons\", publicName: \"buttons\", isSignal: true, isRequired: false, transformFunction: null }, disableInteraction: { classPropertyName: \"disableInteraction\", publicName: \"disableInteraction\", isSignal: true, isRequired: false, transformFunction: null }, disableSensor: { classPropertyName: \"disableSensor\", publicName: \"disableSensor\", isSignal: true, isRequired: false, transformFunction: null }, sensorThrottleTime: { classPropertyName: \"sensorThrottleTime\", publicName: \"sensorThrottleTime\", isSignal: true, isRequired: false, transformFunction: null }, hoverOffset: { classPropertyName: \"hoverOffset\", publicName: \"hoverOffset\", isSignal: true, isRequired: false, transformFunction: null }, trackScrollDuration: { classPropertyName: \"trackScrollDuration\", publicName: \"trackScrollDuration\", isSignal: true, isRequired: false, transformFunction: null }, appearance: { classPropertyName: \"appearance\", publicName: \"appearance\", isSignal: true, isRequired: false, transformFunction: null }, position: { classPropertyName: \"position\", publicName: \"position\", isSignal: true, isRequired: false, transformFunction: null }, trackClass: { classPropertyName: \"trackClass\", publicName: \"trackClass\", isSignal: true, isRequired: false, transformFunction: null }, thumbClass: { classPropertyName: \"thumbClass\", publicName: \"thumbClass\", isSignal: true, isRequired: false, transformFunction: null }, buttonClass: { classPropertyName: \"buttonClass\", publicName: \"buttonClass\", isSignal: true, isRequired: false, transformFunction: null } }, outputs: { afterInit: \"afterInit\", afterUpdate: \"afterUpdate\" }, host: { properties: { \"class.ng-scrollbar\": \"true\", \"attr.verticalUsed\": \"verticalUsed()\", \"attr.horizontalUsed\": \"horizontalUsed()\", \"attr.isVerticallyScrollable\": \"isVerticallyScrollable()\", \"attr.isHorizontallyScrollable\": \"isHorizontallyScrollable()\", \"attr.mobile\": \"isMobile\", \"attr.dir\": \"direction()\", \"attr.position\": \"position()\", \"attr.dragging\": \"dragging()\", \"attr.appearance\": \"appearance()\", \"attr.visibility\": \"visibility()\", \"attr.orientation\": \"orientation()\", \"attr.disableInteraction\": \"disableInteraction()\", \"style.--content-height\": \"contentDimension().height\", \"style.--content-width\": \"contentDimension().width\", \"style.--viewport-height\": \"viewportDimension().height\", \"style.--viewport-width\": \"viewportDimension().width\" } }, providers: [\n            { provide: NG_SCROLLBAR, useExisting: NgScrollbarCore }\n        ], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: NgScrollbarCore, decorators: [{\n            type: Directive,\n            args: [{\n                    host: {\n                        '[class.ng-scrollbar]': 'true',\n                        '[attr.verticalUsed]': 'verticalUsed()',\n                        '[attr.horizontalUsed]': 'horizontalUsed()',\n                        '[attr.isVerticallyScrollable]': 'isVerticallyScrollable()',\n                        '[attr.isHorizontallyScrollable]': 'isHorizontallyScrollable()',\n                        '[attr.mobile]': 'isMobile',\n                        '[attr.dir]': 'direction()',\n                        '[attr.position]': 'position()',\n                        '[attr.dragging]': 'dragging()',\n                        '[attr.appearance]': 'appearance()',\n                        '[attr.visibility]': 'visibility()',\n                        '[attr.orientation]': 'orientation()',\n                        '[attr.disableInteraction]': 'disableInteraction()',\n                        '[style.--content-height]': 'contentDimension().height',\n                        '[style.--content-width]': 'contentDimension().width',\n                        '[style.--viewport-height]': 'viewportDimension().height',\n                        '[style.--viewport-width]': 'viewportDimension().width'\n                    },\n                    providers: [\n                        { provide: NG_SCROLLBAR, useExisting: NgScrollbarCore }\n                    ]\n                }]\n        }], ctorParameters: () => [] });\n\nconst SCROLLBAR_CONTROL = new InjectionToken('SCROLLBAR_CONTROL');\nclass ScrollbarAdapter {\n    constructor() {\n        this.trackSize = signal(0);\n        // Host component reference\n        this.cmp = inject(NG_SCROLLBAR);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ScrollbarAdapter, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.1.1\", type: ScrollbarAdapter, isStandalone: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ScrollbarAdapter, decorators: [{\n            type: Directive\n        }] });\n\nclass PointerEventsAdapter {\n    constructor() {\n        // Reference to the NgScrollbar component\n        this.cmp = inject(NG_SCROLLBAR);\n        // Reference to the Scrollbar control component\n        this.control = inject(SCROLLBAR_CONTROL);\n        // Reference to the Document element\n        this.document = inject(DOCUMENT);\n        // Reference to angular zone\n        this.zone = inject(NgZone);\n        // The native element of the directive\n        this.nativeElement = inject((ElementRef)).nativeElement;\n        effect((onCleanup) => {\n            const disableInteraction = this.cmp.disableInteraction();\n            untracked(() => {\n                if (!disableInteraction) {\n                    this.zone.runOutsideAngular(() => {\n                        this._pointerEventsSub = this.pointerEvents.subscribe();\n                    });\n                }\n                onCleanup(() => this._pointerEventsSub?.unsubscribe());\n            });\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: PointerEventsAdapter, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.1.1\", type: PointerEventsAdapter, isStandalone: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: PointerEventsAdapter, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [] });\n\nclass TrackAdapter extends PointerEventsAdapter {\n    // Returns viewport client size\n    get viewportSize() {\n        return this.cmp.viewport[this.control.sizeProperty];\n    }\n    // Get track client rect\n    get clientRect() {\n        return this.nativeElement.getBoundingClientRect();\n    }\n    // Scrollbar track offset\n    get offset() {\n        return this.clientRect[this.control.rectOffsetProperty];\n    }\n    // Scrollbar track length\n    get size() {\n        // Noticed that clientHeight is evaluated before getClientRect.height,\n        // causing a wrong track size when integrated in dropdown integration\n        return this.nativeElement[this.control.sizeProperty];\n    }\n    // Observable for track dragging events\n    get pointerEvents() {\n        // Observable streams for pointer events\n        const pointerDown$ = fromEvent(this.nativeElement, 'pointerdown').pipe(stopPropagation(), preventSelection(this.document));\n        const pointerUp$ = fromEvent(this.document, 'pointerup', { passive: true }).pipe(enableSelection(this.document));\n        const pointerEnter$ = fromEvent(this.nativeElement, 'pointerover', { passive: true }).pipe(\n        // When mouse is out and enters again, must set the current position first\n        tap((e) => this.currMousePosition = e[this.control.offsetProperty]), map(() => true));\n        const pointerLeave$ = fromEvent(this.nativeElement, 'pointerout', { passive: true }).pipe(map(() => false));\n        const pointerOver$ = merge(pointerEnter$, pointerLeave$).pipe(startWith(true));\n        // Keep track of current mouse location while dragging\n        const pointerMove$ = fromEvent(this.nativeElement, 'pointermove', { passive: true }).pipe(tap((e) => this.currMousePosition = e[this.control.offsetProperty]));\n        return pointerDown$.pipe(switchMap((startEvent) => {\n            // Track pointer location while dragging\n            pointerMove$.pipe(takeUntil(pointerUp$)).subscribe();\n            return this.onTrackFirstClick(startEvent).pipe(delay(200), switchMap(() => {\n                // Otherwise, activate pointermove and pointerout events and switch to ongoing scroll calls\n                return pointerOver$.pipe(switchMap((over) => {\n                    const currDirection = this.getScrollDirection(this.currMousePosition);\n                    const sameDirection = this.scrollDirection === currDirection;\n                    // If mouse is out the track pause the scroll calls, otherwise keep going\n                    return (over && sameDirection) ? this.onTrackOngoingMousedown() : EMPTY;\n                }));\n            }), takeUntil(pointerUp$));\n        }));\n    }\n    constructor() {\n        afterRenderEffect({\n            earlyRead: () => {\n                this.cmp.viewportDimension();\n                this.cmp.contentDimension();\n                untracked(() => {\n                    this.control.trackSize.set(this.size);\n                    if (!this.size) {\n                        // In some rare cases size could be 0 due to first render, use animation frame to give the track element time to render\n                        requestAnimationFrame(() => this.control.trackSize.set(this.size));\n                    }\n                });\n            }\n        });\n        super();\n    }\n    /**\n     *  Callback when mouse is first clicked on the track\n     */\n    onTrackFirstClick(e) {\n        // Initialize variables and determine scroll direction\n        this.currMousePosition = e[this.control.offsetProperty];\n        this.scrollDirection = this.getScrollDirection(this.currMousePosition);\n        this.scrollMax = this.control.viewportScrollMax;\n        return this.scrollTo(this.nextStep());\n    }\n    nextStep() {\n        // Check which direction should the scroll go (forward or backward)\n        if (this.scrollDirection === 'forward') {\n            // Scroll forward\n            const scrollForwardIncrement = this.getScrollForwardStep();\n            // Check if the incremental position is bigger than the scroll max\n            if (scrollForwardIncrement >= this.scrollMax) {\n                return this.scrollMax;\n            }\n            return scrollForwardIncrement;\n        }\n        // Scroll backward\n        const scrollBackwardIncrement = this.getScrollBackwardStep();\n        if (scrollBackwardIncrement <= 0) {\n            return 0;\n        }\n        return scrollBackwardIncrement;\n    }\n    /**\n     * Callback when mouse is still down on the track\n     * Incrementally scrolls towards target position until reached\n     */\n    onTrackOngoingMousedown() {\n        const position = this.nextStep();\n        return this.scrollTo(position).pipe(takeWhile(() => !this.isReached(position)), switchMap(() => this.onTrackOngoingMousedown()));\n    }\n    /**\n     * Returns a flag that determines whether the scroll from the given position is the final step or not\n     */\n    isReached(position) {\n        if (this.scrollDirection === 'forward') {\n            return position >= this.scrollMax;\n        }\n        return position <= 0;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: TrackAdapter, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.1.1\", type: TrackAdapter, isStandalone: true, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: TrackAdapter, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [] });\n\nclass TrackXDirective extends TrackAdapter {\n    get contentSize() {\n        return this.cmp.viewport.contentWidth;\n    }\n    constructor() {\n        effect(() => {\n            if (this.cmp.direction() === 'rtl') {\n                this.getCurrPosition = () => {\n                    const offset = this.contentSize - this.viewportSize - this.control.viewportScrollOffset;\n                    return offset * this.size / this.contentSize;\n                };\n                this.getScrollDirection = (position) => {\n                    return position < this.getCurrPosition() ? 'forward' : 'backward';\n                };\n            }\n            else {\n                this.getCurrPosition = () => {\n                    return this.control.viewportScrollOffset * this.size / this.contentSize;\n                };\n                this.getScrollDirection = (position) => {\n                    return position > this.getCurrPosition() ? 'forward' : 'backward';\n                };\n            }\n        });\n        super();\n    }\n    scrollTo(start) {\n        return from(this.cmp.scrollTo({ start, duration: this.cmp.trackScrollDuration() }));\n    }\n    getScrollForwardStep() {\n        return this.control.viewportScrollOffset + this.viewportSize;\n    }\n    getScrollBackwardStep() {\n        return this.control.viewportScrollOffset - this.viewportSize;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: TrackXDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.1.1\", type: TrackXDirective, isStandalone: true, selector: \"[scrollbarTrackX]\", providers: [{ provide: TrackAdapter, useExisting: TrackXDirective }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: TrackXDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[scrollbarTrackX]',\n                    providers: [{ provide: TrackAdapter, useExisting: TrackXDirective }]\n                }]\n        }], ctorParameters: () => [] });\nclass TrackYDirective extends TrackAdapter {\n    get contentSize() {\n        return this.cmp.viewport.contentHeight;\n    }\n    getCurrPosition() {\n        return this.control.viewportScrollOffset * this.size / this.contentSize;\n    }\n    getScrollDirection(position) {\n        return position > this.getCurrPosition() ? 'forward' : 'backward';\n    }\n    scrollTo(top) {\n        return from(this.cmp.scrollTo({ top, duration: this.cmp.trackScrollDuration() }));\n    }\n    getScrollForwardStep() {\n        return this.control.viewportScrollOffset + this.viewportSize;\n    }\n    getScrollBackwardStep() {\n        return this.control.viewportScrollOffset - this.viewportSize;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: TrackYDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.1.1\", type: TrackYDirective, isStandalone: true, selector: \"[scrollbarTrackY]\", providers: [{ provide: TrackAdapter, useExisting: TrackYDirective }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: TrackYDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[scrollbarTrackY]',\n                    providers: [{ provide: TrackAdapter, useExisting: TrackYDirective }]\n                }]\n        }] });\n\nclass ScrollbarManager {\n    constructor() {\n        this.isBrowser = isPlatformBrowser(inject(PLATFORM_ID));\n        this._polyfillUrl = inject(NG_SCROLLBAR_POLYFILL);\n        this.document = inject(DOCUMENT);\n        this.window = this.document.defaultView;\n        this.scrollTimelinePolyfill = signal(null);\n        if (this.isBrowser && (!this.window['ScrollTimeline'] || !CSS.supports('animation-timeline', 'scroll()'))) {\n            this.initPolyfill();\n        }\n    }\n    async initPolyfill() {\n        try {\n            // Create a script element\n            const script = this.document.createElement('script');\n            script.src = this._polyfillUrl;\n            // Wait for the script to load\n            await new Promise((resolve, reject) => {\n                script.onload = resolve;\n                script.onerror = reject;\n                this.document.head.appendChild(script);\n            });\n            // Once loaded, access and execute the function attached to the window object\n            if (this.window['ScrollTimeline']) {\n                this.scrollTimelinePolyfill.set(this.window['ScrollTimeline']);\n            }\n            else {\n                console.error('[NgScrollbar]: ScrollTimeline is not attached to the window object.');\n            }\n        }\n        catch (error) {\n            console.error('[NgScrollbar]: Error loading ScrollTimeline script:', error);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ScrollbarManager, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ScrollbarManager, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ScrollbarManager, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nclass ThumbAdapter extends PointerEventsAdapter {\n    // Returns thumb size\n    get size() {\n        return this.nativeElement.getBoundingClientRect()[this.control.rectSizeProperty];\n    }\n    // The maximum space available for scrolling.\n    get trackMax() {\n        return this.track.size - this.size;\n    }\n    /**\n     * Stream that emits the 'scrollTo' position when a scrollbar thumb element is dragged\n     * This function is called by thumb drag event using viewport or scrollbar pointer events\n     */\n    get pointerEvents() {\n        return fromEvent(this.nativeElement, 'pointerdown').pipe(stopPropagation(), preventSelection(this.document), switchMap((e) => {\n            let startTrackMax;\n            let startScrollMax;\n            const dragStart = of(e).pipe(tap(() => {\n                // Capture scrollMax and trackMax once\n                startTrackMax = this.trackMax;\n                startScrollMax = this.control.viewportScrollMax;\n                this.setDragging(this.control.axis);\n            }));\n            const dragging = fromEvent(this.document, 'pointermove').pipe(stopPropagation());\n            const dragEnd = fromEvent(this.document, 'pointerup', { capture: true }).pipe(stopPropagation(), enableSelection(this.document), tap(() => this.setDragging('none')));\n            return dragStart.pipe(map((startEvent) => startEvent[this.control.offsetProperty]), switchMap((startOffset) => dragging.pipe(map((moveEvent) => moveEvent[this.control.clientProperty]), \n            // Calculate how far the pointer is from the top/left of the scrollbar (minus the dragOffset).\n            map((moveClient) => moveClient - this.track.offset), map((trackRelativeOffset) => startScrollMax * (trackRelativeOffset - startOffset) / startTrackMax), tap((scrollPosition) => this.control.instantScrollTo(scrollPosition, startScrollMax)), takeUntil(dragEnd))));\n        }));\n    }\n    constructor() {\n        afterRenderEffect({\n            earlyRead: () => {\n                const script = this.manager.scrollTimelinePolyfill();\n                untracked(() => {\n                    if (script && !this._animation) {\n                        this._animation = startPolyfill(script, this.nativeElement, this.cmp.viewport.nativeElement, this.control.axis);\n                    }\n                });\n            }\n        });\n        super();\n        this.manager = inject(ScrollbarManager);\n        this.track = inject(TrackAdapter);\n    }\n    setDragging(value) {\n        this.zone.run(() => this.cmp.dragging.set(value));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ThumbAdapter, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.1.1\", type: ThumbAdapter, isStandalone: true, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ThumbAdapter, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [] });\nfunction startPolyfill(ScrollTimeline, element, source, axis) {\n    return element.animate({\n        translate: [\n            'var(--_scrollbar-thumb-transform-from)',\n            'var(--_scrollbar-thumb-transform-to)'\n        ]\n    }, {\n        fill: 'both',\n        easing: 'linear',\n        timeline: new ScrollTimeline({ source, axis })\n    });\n}\n\nclass ThumbXDirective extends ThumbAdapter {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ThumbXDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.1.1\", type: ThumbXDirective, isStandalone: true, selector: \"[scrollbarThumbX]\", providers: [{ provide: ThumbAdapter, useExisting: ThumbXDirective }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ThumbXDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[scrollbarThumbX]',\n                    providers: [{ provide: ThumbAdapter, useExisting: ThumbXDirective }]\n                }]\n        }] });\nclass ThumbYDirective extends ThumbAdapter {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ThumbYDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.1.1\", type: ThumbYDirective, isStandalone: true, selector: \"[scrollbarThumbY]\", providers: [{ provide: ThumbAdapter, useExisting: ThumbYDirective }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ThumbYDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[scrollbarThumbY]',\n                    providers: [{ provide: ThumbAdapter, useExisting: ThumbYDirective }]\n                }]\n        }] });\n\n// canScroll function can work for y-axis and x-axis for both LTR and RTL directions\nconst canScrollFunc = {\n    forward: (scrollOffset, scrollMax) => scrollOffset < scrollMax,\n    backward: (scrollOffset) => scrollOffset > 0\n};\nconst scrollStepFunc = {\n    forward: (scrollBy, offset) => offset + scrollBy,\n    backward: (scrollBy, offset) => offset - scrollBy\n};\nconst horizontalScrollStepFunc = {\n    rtl: {\n        forward: (scrollBy, offset, scrollMax) => scrollMax - offset - scrollBy,\n        backward: (scrollBy, offset, scrollMax) => scrollMax - offset + scrollBy\n    },\n    ltr: scrollStepFunc\n};\nclass ScrollbarButton extends PointerEventsAdapter {\n    get pointerEvents() {\n        const pointerDown$ = fromEvent(this.nativeElement, 'pointerdown').pipe(stopPropagation(), preventSelection(this.document));\n        const pointerUp$ = fromEvent(this.document, 'pointerup', { passive: true }).pipe(enableSelection(this.document));\n        const pointerLeave$ = fromEvent(this.nativeElement, 'pointerleave', { passive: true });\n        // Combine pointerup and pointerleave events into one stream\n        const pointerUpOrLeave$ = merge(pointerUp$, pointerLeave$);\n        return pointerDown$.pipe(switchMap(() => this.firstScrollStep().pipe(delay(this.afterFirstClickDelay), switchMap(() => this.onOngoingPointerdown()), takeUntil(pointerUpOrLeave$))));\n    }\n    constructor() {\n        effect(() => {\n            const scrollDirection = this.scrollDirection();\n            const dir = this.cmp.direction();\n            untracked(() => {\n                // Get the canScroll function according to scroll direction (forward/backward)\n                this.canScroll = canScrollFunc[scrollDirection];\n                if (this.control.axis === 'x') {\n                    // Get the nextStep function according to scroll direction (forward/backward) and layout direction (LTR/RTL)\n                    this.nextStep = horizontalScrollStepFunc[dir][scrollDirection];\n                }\n                else {\n                    // Get the nextStep function according to scroll direction (forward/backward)\n                    this.nextStep = scrollStepFunc[scrollDirection];\n                }\n            });\n        });\n        super();\n        this.scrollbarButton = input.required();\n        this.scrollDirection = input.required();\n        this.afterFirstClickDelay = 120;\n        this.firstClickDuration = 100;\n        this.scrollBy = 50;\n        this.onGoingScrollBy = 12;\n    }\n    firstScrollStep() {\n        const value = this.nextStep(this.scrollBy, this.control.viewportScrollOffset, this.control.viewportScrollMax);\n        return this.control.scrollTo(value, this.firstClickDuration);\n    }\n    onGoingScrollStep() {\n        const scrollMax = this.control.viewportScrollMax;\n        const value = this.nextStep(this.onGoingScrollBy, this.control.viewportScrollOffset, scrollMax);\n        this.control.instantScrollTo(value, scrollMax);\n    }\n    onOngoingPointerdown() {\n        return interval(0, animationFrameScheduler).pipe(takeWhile(() => this.canScroll(this.control.viewportScrollOffset, this.control.viewportScrollMax)), tap(() => this.onGoingScrollStep()));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ScrollbarButton, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.1.0\", version: \"19.1.1\", type: ScrollbarButton, isStandalone: true, selector: \"button[scrollbarButton]\", inputs: { scrollbarButton: { classPropertyName: \"scrollbarButton\", publicName: \"scrollbarButton\", isSignal: true, isRequired: true, transformFunction: null }, scrollDirection: { classPropertyName: \"scrollDirection\", publicName: \"scrollDirection\", isSignal: true, isRequired: true, transformFunction: null } }, usesInheritance: true, ngImport: i0, template: \"<div class=\\\"ng-scrollbar-button-icon\\\">\\r\\n  <svg viewBox=\\\"0 0 512 512\\\"\\r\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\">\\r\\n    <path\\r\\n      d=\\\"M413.1,327.3l-1.8-2.1l-136-156.5c-4.6-5.3-11.5-8.6-19.2-8.6c-7.7,0-14.6,3.4-19.2,8.6L101,324.9l-2.3,2.6  C97,330,96,333,96,336.2c0,8.7,7.4,15.8,16.6,15.8v0h286.8v0c9.2,0,16.6-7.1,16.6-15.8C416,332.9,414.9,329.8,413.1,327.3z\\\"/>\\r\\n  </svg>\\r\\n</div>\\r\\n\", styles: [\":host{position:relative;border:none;margin:0;padding:0;border-radius:0;appearance:none;background-color:var(--INTERNAL-scrollbar-button-color)}:host svg{width:100%;height:100%;fill:var(--INTERNAL-scrollbar-button-fill)}:host:hover{background:var(--INTERNAL-scrollbar-button-hover-color)}:host:hover svg{fill:var(--INTERNAL-scrollbar-button-hover-fill)}:host:active{background:var(--INTERNAL-scrollbar-button-active-color)}:host:active svg{fill:var(--INTERNAL-scrollbar-button-active-fill)}:host[scrollbarButton=top],:host[scrollbarButton=start]{order:1}:host[scrollbarButton=bottom],:host[scrollbarButton=end]{order:3}:host[scrollbarButton=top],:host[scrollbarButton=bottom]{width:100%;height:var(--INTERNAL-scrollbar-button-size)}:host[scrollbarButton=start],:host[scrollbarButton=end]{width:var(--INTERNAL-scrollbar-button-size);height:100%}:host[scrollbarButton=bottom]{--_button-rotate: 180deg}:host[scrollbarButton=start]{--_button-rotate: -90deg}:host[scrollbarButton=start] .ng-scrollbar-button-icon{writing-mode:vertical-lr}:host[scrollbarButton=end]{--_button-rotate: 90deg}:host[scrollbarButton=end] .ng-scrollbar-button-icon{writing-mode:vertical-rl}.ng-scrollbar-button-icon{rotate:var(--_button-rotate);display:flex;place-content:center;place-items:center;width:100%;height:100%}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ScrollbarButton, decorators: [{\n            type: Component,\n            args: [{ selector: 'button[scrollbarButton]', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div class=\\\"ng-scrollbar-button-icon\\\">\\r\\n  <svg viewBox=\\\"0 0 512 512\\\"\\r\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\">\\r\\n    <path\\r\\n      d=\\\"M413.1,327.3l-1.8-2.1l-136-156.5c-4.6-5.3-11.5-8.6-19.2-8.6c-7.7,0-14.6,3.4-19.2,8.6L101,324.9l-2.3,2.6  C97,330,96,333,96,336.2c0,8.7,7.4,15.8,16.6,15.8v0h286.8v0c9.2,0,16.6-7.1,16.6-15.8C416,332.9,414.9,329.8,413.1,327.3z\\\"/>\\r\\n  </svg>\\r\\n</div>\\r\\n\", styles: [\":host{position:relative;border:none;margin:0;padding:0;border-radius:0;appearance:none;background-color:var(--INTERNAL-scrollbar-button-color)}:host svg{width:100%;height:100%;fill:var(--INTERNAL-scrollbar-button-fill)}:host:hover{background:var(--INTERNAL-scrollbar-button-hover-color)}:host:hover svg{fill:var(--INTERNAL-scrollbar-button-hover-fill)}:host:active{background:var(--INTERNAL-scrollbar-button-active-color)}:host:active svg{fill:var(--INTERNAL-scrollbar-button-active-fill)}:host[scrollbarButton=top],:host[scrollbarButton=start]{order:1}:host[scrollbarButton=bottom],:host[scrollbarButton=end]{order:3}:host[scrollbarButton=top],:host[scrollbarButton=bottom]{width:100%;height:var(--INTERNAL-scrollbar-button-size)}:host[scrollbarButton=start],:host[scrollbarButton=end]{width:var(--INTERNAL-scrollbar-button-size);height:100%}:host[scrollbarButton=bottom]{--_button-rotate: 180deg}:host[scrollbarButton=start]{--_button-rotate: -90deg}:host[scrollbarButton=start] .ng-scrollbar-button-icon{writing-mode:vertical-lr}:host[scrollbarButton=end]{--_button-rotate: 90deg}:host[scrollbarButton=end] .ng-scrollbar-button-icon{writing-mode:vertical-rl}.ng-scrollbar-button-icon{rotate:var(--_button-rotate);display:flex;place-content:center;place-items:center;width:100%;height:100%}\\n\"] }]\n        }], ctorParameters: () => [] });\n\nclass ScrollbarY extends ScrollbarAdapter {\n    constructor() {\n        super(...arguments);\n        this.rectOffsetProperty = 'top';\n        this.rectSizeProperty = 'height';\n        this.sizeProperty = 'offsetHeight';\n        this.clientProperty = 'clientY';\n        this.offsetProperty = 'offsetY';\n        this.axis = 'y';\n    }\n    get viewportScrollMax() {\n        return this.cmp.viewport.scrollMaxY;\n    }\n    get viewportScrollOffset() {\n        return this.cmp.viewport.scrollTop;\n    }\n    scrollTo(top, duration) {\n        return from(this.cmp.scrollTo({ top, duration }));\n    }\n    instantScrollTo(value) {\n        this.cmp.viewport.scrollYTo(value);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ScrollbarY, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.1.1\", type: ScrollbarY, isStandalone: true, selector: \"scrollbar-y\", host: { properties: { \"style.--track-size\": \"trackSize()\" } }, providers: [\n            { provide: SCROLLBAR_CONTROL, useExisting: ScrollbarY }\n        ], usesInheritance: true, ngImport: i0, template: `\r\n    <div class=\"ng-scrollbar-sticky\"\r\n         [class.ng-scrollbar-hover]=\"cmp.hoverOffset()\">\r\n      <div class=\"ng-scrollbar-track-wrapper\"\r\n           [class.ng-scrollbar-hover]=\"!cmp.hoverOffset()\">\r\n        <div scrollbarTrackY class=\"ng-scrollbar-track {{ cmp.trackClass() }}\">\r\n          <div scrollbarThumbY class=\"ng-scrollbar-thumb {{ cmp.thumbClass() }}\"></div>\r\n        </div>\r\n        @if (cmp.buttons()) {\r\n          <button class=\"ng-scrollbar-button {{ cmp.buttonClass() }}\"\r\n                  scrollbarButton=\"top\"\r\n                  scrollDirection=\"backward\"></button>\r\n          <button class=\"ng-scrollbar-button {{ cmp.buttonClass() }}\"\r\n                  scrollbarButton=\"bottom\"\r\n                  scrollDirection=\"forward\"></button>\r\n        }\r\n      </div>\r\n    </div>\r\n  `, isInline: true, styles: [\":host{position:absolute;inset:0;pointer-events:none;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}.ng-scrollbar-sticky{top:calc(var(--_scrollbar-wrapper-top) * 1px);left:calc(var(--_scrollbar-wrapper-left) * 1px);right:calc(var(--_scrollbar-wrapper-right) * 1px);height:calc(var(--_scrollbar-wrapper-height) * 1px);width:calc(var(--_scrollbar-wrapper-width) * 1px);position:sticky;z-index:100;opacity:var(--_scrollbar-hover-opacity);transition:var(--_scrollbar-opacity-transition);pointer-events:var(--_scrollbar-pointer-events)}.ng-scrollbar-track-wrapper{touch-action:none;-webkit-user-select:none;user-select:none;top:var(--_scrollbar-track-top);bottom:var(--_scrollbar-track-bottom);right:var(--_scrollbar-track-right);left:var(--_scrollbar-track-left);transition:var(--INTERNAL-scrollbar-track-wrapper-transition);position:absolute;overflow:hidden;display:flex;place-items:center}.ng-scrollbar-track{position:relative;width:100%;height:100%;background-color:var(--INTERNAL-scrollbar-track-color);border-radius:var(--INTERNAL-scrollbar-border-radius);cursor:default;z-index:1;order:2}.ng-scrollbar-thumb{box-sizing:border-box;position:absolute;transition:var(--INTERNAL-scrollbar-thumb-transition);border-radius:var(--INTERNAL-scrollbar-border-radius);height:var(--_thumb-height);width:var(--_thumb-width);animation-name:scrollbarThumbAnimation;animation-duration:1ms;animation-timing-function:linear}@keyframes scrollbarThumbAnimation{0%{translate:var(--_scrollbar-thumb-transform-from)}to{translate:var(--_scrollbar-thumb-transform-to)}}\\n\", \":host{--_scrollbar-wrapper-top: 0;--_scrollbar-wrapper-left: var(--_scrollbar-wrapper-y-left);--_scrollbar-wrapper-right: var(--_scrollbar-wrapper-y-right);--_scrollbar-wrapper-height: var(--viewport-height);--_scrollbar-wrapper-width: var(--_scrollbar-thickness);--_scrollbar-track-top: var(--_vertical-top);--_scrollbar-track-bottom: var(--_vertical-bottom);--_scrollbar-track-right: var(--_vertical-right);--_scrollbar-track-left: var(--_vertical-left);--thumb-size: max(calc(var(--viewport-height) * var(--track-size) / var(--content-height)), var(--INTERNAL-scrollbar-thumb-min-size));--_thumb-height: calc(var(--thumb-size) * 1px);--_thumb-width: 100%;--_scrollbar-y-thumb-transform-to-value: calc(var(--track-size) - var(--thumb-size));--_scrollbar-thumb-transform-from: 0 0;--_scrollbar-thumb-transform-to: 0 calc(var(--_scrollbar-y-thumb-transform-to-value) * 1px)}.ng-scrollbar-track-wrapper{width:var(--_track-y-thickness);flex-direction:column}.ng-scrollbar-hover:hover,.ng-scrollbar-hover:active{--_track-y-thickness: var(--_scrollbar-hover-thickness-px);--_thumb-y-color: var(--INTERNAL-scrollbar-thumb-hover-color)}.ng-scrollbar-thumb{animation-timeline:var(--_animation-timeline-y);min-height:calc(var(--INTERNAL-scrollbar-thumb-min-size) * 1px);display:var(--_vertical-thumb-display);background-color:var(--_thumb-y-color)}\\n\"], dependencies: [{ kind: \"directive\", type: TrackYDirective, selector: \"[scrollbarTrackY]\" }, { kind: \"directive\", type: ThumbYDirective, selector: \"[scrollbarThumbY]\" }, { kind: \"component\", type: ScrollbarButton, selector: \"button[scrollbarButton]\", inputs: [\"scrollbarButton\", \"scrollDirection\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ScrollbarY, decorators: [{\n            type: Component,\n            args: [{ selector: 'scrollbar-y', template: `\r\n    <div class=\"ng-scrollbar-sticky\"\r\n         [class.ng-scrollbar-hover]=\"cmp.hoverOffset()\">\r\n      <div class=\"ng-scrollbar-track-wrapper\"\r\n           [class.ng-scrollbar-hover]=\"!cmp.hoverOffset()\">\r\n        <div scrollbarTrackY class=\"ng-scrollbar-track {{ cmp.trackClass() }}\">\r\n          <div scrollbarThumbY class=\"ng-scrollbar-thumb {{ cmp.thumbClass() }}\"></div>\r\n        </div>\r\n        @if (cmp.buttons()) {\r\n          <button class=\"ng-scrollbar-button {{ cmp.buttonClass() }}\"\r\n                  scrollbarButton=\"top\"\r\n                  scrollDirection=\"backward\"></button>\r\n          <button class=\"ng-scrollbar-button {{ cmp.buttonClass() }}\"\r\n                  scrollbarButton=\"bottom\"\r\n                  scrollDirection=\"forward\"></button>\r\n        }\r\n      </div>\r\n    </div>\r\n  `, imports: [TrackYDirective, ThumbYDirective, ScrollbarButton], providers: [\n                        { provide: SCROLLBAR_CONTROL, useExisting: ScrollbarY }\n                    ], host: {\n                        '[style.--track-size]': 'trackSize()'\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\":host{position:absolute;inset:0;pointer-events:none;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}.ng-scrollbar-sticky{top:calc(var(--_scrollbar-wrapper-top) * 1px);left:calc(var(--_scrollbar-wrapper-left) * 1px);right:calc(var(--_scrollbar-wrapper-right) * 1px);height:calc(var(--_scrollbar-wrapper-height) * 1px);width:calc(var(--_scrollbar-wrapper-width) * 1px);position:sticky;z-index:100;opacity:var(--_scrollbar-hover-opacity);transition:var(--_scrollbar-opacity-transition);pointer-events:var(--_scrollbar-pointer-events)}.ng-scrollbar-track-wrapper{touch-action:none;-webkit-user-select:none;user-select:none;top:var(--_scrollbar-track-top);bottom:var(--_scrollbar-track-bottom);right:var(--_scrollbar-track-right);left:var(--_scrollbar-track-left);transition:var(--INTERNAL-scrollbar-track-wrapper-transition);position:absolute;overflow:hidden;display:flex;place-items:center}.ng-scrollbar-track{position:relative;width:100%;height:100%;background-color:var(--INTERNAL-scrollbar-track-color);border-radius:var(--INTERNAL-scrollbar-border-radius);cursor:default;z-index:1;order:2}.ng-scrollbar-thumb{box-sizing:border-box;position:absolute;transition:var(--INTERNAL-scrollbar-thumb-transition);border-radius:var(--INTERNAL-scrollbar-border-radius);height:var(--_thumb-height);width:var(--_thumb-width);animation-name:scrollbarThumbAnimation;animation-duration:1ms;animation-timing-function:linear}@keyframes scrollbarThumbAnimation{0%{translate:var(--_scrollbar-thumb-transform-from)}to{translate:var(--_scrollbar-thumb-transform-to)}}\\n\", \":host{--_scrollbar-wrapper-top: 0;--_scrollbar-wrapper-left: var(--_scrollbar-wrapper-y-left);--_scrollbar-wrapper-right: var(--_scrollbar-wrapper-y-right);--_scrollbar-wrapper-height: var(--viewport-height);--_scrollbar-wrapper-width: var(--_scrollbar-thickness);--_scrollbar-track-top: var(--_vertical-top);--_scrollbar-track-bottom: var(--_vertical-bottom);--_scrollbar-track-right: var(--_vertical-right);--_scrollbar-track-left: var(--_vertical-left);--thumb-size: max(calc(var(--viewport-height) * var(--track-size) / var(--content-height)), var(--INTERNAL-scrollbar-thumb-min-size));--_thumb-height: calc(var(--thumb-size) * 1px);--_thumb-width: 100%;--_scrollbar-y-thumb-transform-to-value: calc(var(--track-size) - var(--thumb-size));--_scrollbar-thumb-transform-from: 0 0;--_scrollbar-thumb-transform-to: 0 calc(var(--_scrollbar-y-thumb-transform-to-value) * 1px)}.ng-scrollbar-track-wrapper{width:var(--_track-y-thickness);flex-direction:column}.ng-scrollbar-hover:hover,.ng-scrollbar-hover:active{--_track-y-thickness: var(--_scrollbar-hover-thickness-px);--_thumb-y-color: var(--INTERNAL-scrollbar-thumb-hover-color)}.ng-scrollbar-thumb{animation-timeline:var(--_animation-timeline-y);min-height:calc(var(--INTERNAL-scrollbar-thumb-min-size) * 1px);display:var(--_vertical-thumb-display);background-color:var(--_thumb-y-color)}\\n\"] }]\n        }] });\nclass ScrollbarX extends ScrollbarAdapter {\n    get viewportScrollMax() {\n        return this.cmp.viewport.scrollMaxX;\n    }\n    get viewportScrollOffset() {\n        // Keep scrollLeft value positive for horizontal scrollbar\n        return Math.abs(this.cmp.viewport.scrollLeft);\n    }\n    constructor() {\n        effect(() => {\n            if (this.cmp.direction() === 'rtl') {\n                this.handlePosition = (position, scrollMax) => -(scrollMax - position);\n            }\n            else {\n                this.handlePosition = (position) => position;\n            }\n        });\n        super();\n        this.manager = inject(ScrollbarManager);\n        this.rectOffsetProperty = 'left';\n        this.rectSizeProperty = 'width';\n        this.sizeProperty = 'offsetWidth';\n        this.clientProperty = 'clientX';\n        this.offsetProperty = 'offsetX';\n        this.axis = 'x';\n    }\n    scrollTo(left, duration) {\n        return from(this.cmp.scrollTo({ left, duration }));\n    }\n    instantScrollTo(value, scrollMax) {\n        this.cmp.viewport.scrollXTo(this.handlePosition(value, scrollMax));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ScrollbarX, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.1.1\", type: ScrollbarX, isStandalone: true, selector: \"scrollbar-x\", host: { properties: { \"attr.dir\": \"cmp.direction()\", \"style.--track-size\": \"trackSize()\" } }, providers: [\n            { provide: SCROLLBAR_CONTROL, useExisting: ScrollbarX }\n        ], usesInheritance: true, ngImport: i0, template: `\r\n    <div class=\"ng-scrollbar-sticky\"\r\n         [class.ng-scrollbar-hover]=\"cmp.hoverOffset()\">\r\n      <div class=\"ng-scrollbar-track-wrapper\"\r\n           [class.ng-scrollbar-hover]=\"!cmp.hoverOffset()\">\r\n        <div scrollbarTrackX class=\"ng-scrollbar-track {{ cmp.trackClass() }}\">\r\n          <div scrollbarThumbX class=\"ng-scrollbar-thumb {{ cmp.thumbClass() }}\"></div>\r\n        </div>\r\n        @if (cmp.buttons()) {\r\n          <button class=\"ng-scrollbar-button {{ cmp.buttonClass() }}\"\r\n                  scrollbarButton=\"start\"\r\n                  scrollDirection=\"backward\"></button>\r\n          <button class=\"ng-scrollbar-button {{ cmp.buttonClass() }}\"\r\n                  scrollbarButton=\"end\"\r\n                  scrollDirection=\"forward\"></button>\r\n        }\r\n      </div>\r\n    </div>\r\n  `, isInline: true, styles: [\":host{position:absolute;inset:0;pointer-events:none;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}.ng-scrollbar-sticky{top:calc(var(--_scrollbar-wrapper-top) * 1px);left:calc(var(--_scrollbar-wrapper-left) * 1px);right:calc(var(--_scrollbar-wrapper-right) * 1px);height:calc(var(--_scrollbar-wrapper-height) * 1px);width:calc(var(--_scrollbar-wrapper-width) * 1px);position:sticky;z-index:100;opacity:var(--_scrollbar-hover-opacity);transition:var(--_scrollbar-opacity-transition);pointer-events:var(--_scrollbar-pointer-events)}.ng-scrollbar-track-wrapper{touch-action:none;-webkit-user-select:none;user-select:none;top:var(--_scrollbar-track-top);bottom:var(--_scrollbar-track-bottom);right:var(--_scrollbar-track-right);left:var(--_scrollbar-track-left);transition:var(--INTERNAL-scrollbar-track-wrapper-transition);position:absolute;overflow:hidden;display:flex;place-items:center}.ng-scrollbar-track{position:relative;width:100%;height:100%;background-color:var(--INTERNAL-scrollbar-track-color);border-radius:var(--INTERNAL-scrollbar-border-radius);cursor:default;z-index:1;order:2}.ng-scrollbar-thumb{box-sizing:border-box;position:absolute;transition:var(--INTERNAL-scrollbar-thumb-transition);border-radius:var(--INTERNAL-scrollbar-border-radius);height:var(--_thumb-height);width:var(--_thumb-width);animation-name:scrollbarThumbAnimation;animation-duration:1ms;animation-timing-function:linear}@keyframes scrollbarThumbAnimation{0%{translate:var(--_scrollbar-thumb-transform-from)}to{translate:var(--_scrollbar-thumb-transform-to)}}\\n\", \":host{--_scrollbar-wrapper-top: var(--_scrollbar-wrapper-x-top);--_scrollbar-wrapper-left: 0;--_scrollbar-wrapper-right: 0;--_scrollbar-wrapper-height: var(--_scrollbar-thickness);--_scrollbar-wrapper-width: var(--viewport-width);--_scrollbar-track-top: var(--_horizontal-top);--_scrollbar-track-bottom: var(--_horizontal-bottom);--_scrollbar-track-right: var(--_horizontal-right);--_scrollbar-track-left: var(--_horizontal-left);--thumb-size: max(calc(var(--viewport-width) * var(--track-size) / var(--content-width)), var(--INTERNAL-scrollbar-thumb-min-size));--_thumb-height: 100%;--_thumb-width: calc(var(--thumb-size) * 1px);--_scrollbar-x-thumb-transform-to-value: calc(var(--track-size) - var(--thumb-size));--_scrollbar-thumb-transform-from: 0;--_scrollbar-thumb-transform-to: calc(var(--_scrollbar-x-thumb-transform-to-value) * 1px)}:host .ng-scrollbar-button[scrollbarButton=start]{_--button-rotate:90}:host .ng-scrollbar-button[scrollbarButton=end]{_--button-rotate:-90}:host[dir=rtl] .ng-scrollbar-thumb{animation-name:scrollbarThumbRTLAnimation;will-change:right;--_scrollbar-thumb-transform-to: calc(var(--_scrollbar-x-thumb-transform-to-value) * -1px)}:host[dir=rtl] .ng-scrollbar-button[scrollbarButton=start]{--_button-rotate: 90deg}:host[dir=rtl] .ng-scrollbar-button[scrollbarButton=end]{--_button-rotate: -90deg}.ng-scrollbar-track-wrapper{height:var(--_track-x-thickness);flex-direction:row}.ng-scrollbar-hover:hover,.ng-scrollbar-hover:active{--_track-x-thickness: var(--_scrollbar-hover-thickness-px);--_thumb-x-color: var(--INTERNAL-scrollbar-thumb-hover-color)}.ng-scrollbar-thumb{animation-timeline:var(--_animation-timeline-x);min-width:calc(var(--INTERNAL-scrollbar-thumb-min-size) * 1px);display:var(--_horizontal-thumb-display);background-color:var(--_thumb-x-color)}@keyframes scrollbarThumbRTLAnimation{0%{right:var(--_scrollbar-thumb-transform-from)}to{right:calc(var(--_scrollbar-thumb-transform-to) * -1)}}\\n\"], dependencies: [{ kind: \"directive\", type: TrackXDirective, selector: \"[scrollbarTrackX]\" }, { kind: \"directive\", type: ThumbXDirective, selector: \"[scrollbarThumbX]\" }, { kind: \"component\", type: ScrollbarButton, selector: \"button[scrollbarButton]\", inputs: [\"scrollbarButton\", \"scrollDirection\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: ScrollbarX, decorators: [{\n            type: Component,\n            args: [{ selector: 'scrollbar-x', template: `\r\n    <div class=\"ng-scrollbar-sticky\"\r\n         [class.ng-scrollbar-hover]=\"cmp.hoverOffset()\">\r\n      <div class=\"ng-scrollbar-track-wrapper\"\r\n           [class.ng-scrollbar-hover]=\"!cmp.hoverOffset()\">\r\n        <div scrollbarTrackX class=\"ng-scrollbar-track {{ cmp.trackClass() }}\">\r\n          <div scrollbarThumbX class=\"ng-scrollbar-thumb {{ cmp.thumbClass() }}\"></div>\r\n        </div>\r\n        @if (cmp.buttons()) {\r\n          <button class=\"ng-scrollbar-button {{ cmp.buttonClass() }}\"\r\n                  scrollbarButton=\"start\"\r\n                  scrollDirection=\"backward\"></button>\r\n          <button class=\"ng-scrollbar-button {{ cmp.buttonClass() }}\"\r\n                  scrollbarButton=\"end\"\r\n                  scrollDirection=\"forward\"></button>\r\n        }\r\n      </div>\r\n    </div>\r\n  `, imports: [TrackXDirective, ThumbXDirective, ScrollbarButton], providers: [\n                        { provide: SCROLLBAR_CONTROL, useExisting: ScrollbarX }\n                    ], host: {\n                        '[attr.dir]': 'cmp.direction()',\n                        '[style.--track-size]': 'trackSize()'\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\":host{position:absolute;inset:0;pointer-events:none;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}.ng-scrollbar-sticky{top:calc(var(--_scrollbar-wrapper-top) * 1px);left:calc(var(--_scrollbar-wrapper-left) * 1px);right:calc(var(--_scrollbar-wrapper-right) * 1px);height:calc(var(--_scrollbar-wrapper-height) * 1px);width:calc(var(--_scrollbar-wrapper-width) * 1px);position:sticky;z-index:100;opacity:var(--_scrollbar-hover-opacity);transition:var(--_scrollbar-opacity-transition);pointer-events:var(--_scrollbar-pointer-events)}.ng-scrollbar-track-wrapper{touch-action:none;-webkit-user-select:none;user-select:none;top:var(--_scrollbar-track-top);bottom:var(--_scrollbar-track-bottom);right:var(--_scrollbar-track-right);left:var(--_scrollbar-track-left);transition:var(--INTERNAL-scrollbar-track-wrapper-transition);position:absolute;overflow:hidden;display:flex;place-items:center}.ng-scrollbar-track{position:relative;width:100%;height:100%;background-color:var(--INTERNAL-scrollbar-track-color);border-radius:var(--INTERNAL-scrollbar-border-radius);cursor:default;z-index:1;order:2}.ng-scrollbar-thumb{box-sizing:border-box;position:absolute;transition:var(--INTERNAL-scrollbar-thumb-transition);border-radius:var(--INTERNAL-scrollbar-border-radius);height:var(--_thumb-height);width:var(--_thumb-width);animation-name:scrollbarThumbAnimation;animation-duration:1ms;animation-timing-function:linear}@keyframes scrollbarThumbAnimation{0%{translate:var(--_scrollbar-thumb-transform-from)}to{translate:var(--_scrollbar-thumb-transform-to)}}\\n\", \":host{--_scrollbar-wrapper-top: var(--_scrollbar-wrapper-x-top);--_scrollbar-wrapper-left: 0;--_scrollbar-wrapper-right: 0;--_scrollbar-wrapper-height: var(--_scrollbar-thickness);--_scrollbar-wrapper-width: var(--viewport-width);--_scrollbar-track-top: var(--_horizontal-top);--_scrollbar-track-bottom: var(--_horizontal-bottom);--_scrollbar-track-right: var(--_horizontal-right);--_scrollbar-track-left: var(--_horizontal-left);--thumb-size: max(calc(var(--viewport-width) * var(--track-size) / var(--content-width)), var(--INTERNAL-scrollbar-thumb-min-size));--_thumb-height: 100%;--_thumb-width: calc(var(--thumb-size) * 1px);--_scrollbar-x-thumb-transform-to-value: calc(var(--track-size) - var(--thumb-size));--_scrollbar-thumb-transform-from: 0;--_scrollbar-thumb-transform-to: calc(var(--_scrollbar-x-thumb-transform-to-value) * 1px)}:host .ng-scrollbar-button[scrollbarButton=start]{_--button-rotate:90}:host .ng-scrollbar-button[scrollbarButton=end]{_--button-rotate:-90}:host[dir=rtl] .ng-scrollbar-thumb{animation-name:scrollbarThumbRTLAnimation;will-change:right;--_scrollbar-thumb-transform-to: calc(var(--_scrollbar-x-thumb-transform-to-value) * -1px)}:host[dir=rtl] .ng-scrollbar-button[scrollbarButton=start]{--_button-rotate: 90deg}:host[dir=rtl] .ng-scrollbar-button[scrollbarButton=end]{--_button-rotate: -90deg}.ng-scrollbar-track-wrapper{height:var(--_track-x-thickness);flex-direction:row}.ng-scrollbar-hover:hover,.ng-scrollbar-hover:active{--_track-x-thickness: var(--_scrollbar-hover-thickness-px);--_thumb-x-color: var(--INTERNAL-scrollbar-thumb-hover-color)}.ng-scrollbar-thumb{animation-timeline:var(--_animation-timeline-x);min-width:calc(var(--INTERNAL-scrollbar-thumb-min-size) * 1px);display:var(--_horizontal-thumb-display);background-color:var(--_thumb-x-color)}@keyframes scrollbarThumbRTLAnimation{0%{right:var(--_scrollbar-thumb-transform-from)}to{right:calc(var(--_scrollbar-thumb-transform-to) * -1)}}\\n\"] }]\n        }], ctorParameters: () => [] });\n\nclass Scrollbars {\n    constructor() {\n        this.cmp = inject(NG_SCROLLBAR);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: Scrollbars, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.1.1\", type: Scrollbars, isStandalone: true, selector: \"scrollbars\", ngImport: i0, template: `\r\n    @if (cmp.verticalUsed()) {\r\n      <scrollbar-y/>\r\n    }\r\n    @if (cmp.horizontalUsed()) {\r\n      <scrollbar-x/>\r\n    }\r\n  `, isInline: true, styles: [\":host{display:contents}\\n\"], dependencies: [{ kind: \"component\", type: ScrollbarX, selector: \"scrollbar-x\" }, { kind: \"component\", type: ScrollbarY, selector: \"scrollbar-y\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: Scrollbars, decorators: [{\n            type: Component,\n            args: [{ selector: 'scrollbars', changeDetection: ChangeDetectionStrategy.OnPush, imports: [ScrollbarX, ScrollbarY], template: `\r\n    @if (cmp.verticalUsed()) {\r\n      <scrollbar-y/>\r\n    }\r\n    @if (cmp.horizontalUsed()) {\r\n      <scrollbar-x/>\r\n    }\r\n  `, styles: [\":host{display:contents}\\n\"] }]\n        }] });\n\nclass NgScrollbar extends NgScrollbarCore {\n    constructor() {\n        effect(() => {\n            const contentWrapper = this.contentWrapper().nativeElement;\n            untracked(() => {\n                this.viewport.init(this.nativeElement, contentWrapper);\n            });\n        });\n        super();\n        this.contentWrapper = viewChild.required('contentWrapper');\n        this._scrollbars = viewChild.required(Scrollbars);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: NgScrollbar, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.2.0\", version: \"19.1.1\", type: NgScrollbar, isStandalone: true, selector: \"ng-scrollbar:not([externalViewport])\", providers: [\n            { provide: NG_SCROLLBAR, useExisting: NgScrollbar },\n            ViewportAdapter\n        ], viewQueries: [{ propertyName: \"contentWrapper\", first: true, predicate: [\"contentWrapper\"], descendants: true, isSignal: true }, { propertyName: \"_scrollbars\", first: true, predicate: Scrollbars, descendants: true, isSignal: true }], exportAs: [\"ngScrollbar\"], usesInheritance: true, ngImport: i0, template: `\r\n    <div #contentWrapper>\r\n      <ng-content/>\r\n      <scrollbars/>\r\n    </div>\r\n  `, isInline: true, styles: [\":host{display:block;position:relative;max-height:100%;max-width:100%;--INTERNAL-scrollbar-border-radius: var(--scrollbar-border-radius, 0px);--INTERNAL-scrollbar-thickness: var(--scrollbar-thickness, 5);--INTERNAL-scrollbar-offset: var(--scrollbar-offset, 0);--INTERNAL-scrollbar-track-wrapper-transition: var(--scrollbar-track-wrapper-transition, width 60ms linear, height 60ms linear);--INTERNAL-scrollbar-track-color: var(--scrollbar-track-color, transparent);--INTERNAL-scrollbar-thumb-color: var(--scrollbar-thumb-color, rgb(0 0 0 / 20%));--INTERNAL-scrollbar-thumb-hover-color: var(--scrollbar-thumb-hover-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-hover-thickness: var(--scrollbar-hover-thickness, var(--INTERNAL-scrollbar-thickness));--INTERNAL-scrollbar-thumb-transition: var(--scrollbar-thumb-transition, none);--INTERNAL-scrollbar-thumb-min-size: var(--scrollbar-thumb-min-size, 20);--INTERNAL-scrollbar-button-color: var(--scrollbar-button-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-button-hover-color: var(--scrollbar-button-hover-color, var(--INTERNAL-scrollbar-button-color));--INTERNAL-scrollbar-button-active-color: var(--scrollbar-button-active-color, var(--INTERNAL-scrollbar-button-hover-color));--INTERNAL-scrollbar-button-fill: var(--scrollbar-button-fill, white);--INTERNAL-scrollbar-button-hover-fill: var(--scrollbar-button-hover-fill, var(--INTERNAL-scrollbar-button-fill));--INTERNAL-scrollbar-button-active-fill: var(--scrollbar-button-active-fill, var(--INTERNAL-scrollbar-button-hover-fill));--INTERNAL-scrollbar-button-size: var(--scrollbar-button-size, 20px);--INTERNAL-scrollbar-hover-opacity-transition-enter-duration: var(--scrollbar-hover-opacity-transition-enter-duration, 0);--INTERNAL-scrollbar-hover-opacity-transition-leave-duration: var(--scrollbar-hover-opacity-transition-leave-duration, .4s);--INTERNAL-scrollbar-hover-opacity-transition-leave-delay: var(--scrollbar-hover-opacity-transition-leave-delay, 1s);--INTERNAL-scrollbar-overscroll-behavior: var(--scrollbar-overscroll-behavior, initial);--INTERNAL-scrollbar-mobile-overscroll-behavior: var(--scrollbar-mobile-overscroll-behavior, none);--_scrollbar-thickness: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 2);--_scrollbar-pointer-events: auto;--_scrollbar-offset-px: calc(var(--INTERNAL-scrollbar-offset) * 1px);--_scrollbar-thickness-px: calc(var(--INTERNAL-scrollbar-thickness) * 1px);--_scrollbar-hover-thickness-px: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_viewport-padding-top: 0;--_viewport-padding-bottom: 0;--_viewport-padding-left: 0;--_viewport-padding-right: 0;--_horizontal-thumb-display: block;--_vertical-thumb-display: block;--_viewport-overflow: auto;--_viewport-pointer-events: auto;--_thumb-x-color: var(--INTERNAL-scrollbar-thumb-color);--_thumb-y-color: var(--INTERNAL-scrollbar-thumb-color);--_track-y-thickness: var(--_scrollbar-thickness-px);--_track-x-thickness: var(--_scrollbar-thickness-px);--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-overscroll-behavior);--_scrollbar-content-width: fit-content}:host{--_spacer-width: var(--spacer-width);--_spacer-height: var(--spacer-height);--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-offset-px);--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-offset-px);--_horizontal-top: initial;--_horizontal-bottom: var(--_scrollbar-offset-px);--_scrollbar-wrapper-x-top: calc(var(--viewport-height) - var(--_scrollbar-thickness));--reached-offset: 1px;--reached-offset-top: var(--reached-offset);--reached-offset-bottom: var(--reached-offset);--reached-offset-start: var(--reached-offset);--reached-offset-end: var(--reached-offset);--dropped-offset: 1px;--dropped-offset-top: var(--dropped-offset);--dropped-offset-bottom: var(--dropped-offset);--dropped-offset-start: var(--dropped-offset);--dropped-offset-end: var(--dropped-offset);--_viewport_scroll-timeline: unset;--_animation-timeline-y: unset;--_scrollbar-y-thumb-transform-to-value: unset;--_scrollbar-x-thumb-transform-to-value: unset;--_scrollbar-thumb-transform-from: unset;--_scrollbar-thumb-transform-to: unset}:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{min-height:100%;min-width:100%;height:100%;max-height:100%;max-width:100%}:host.ng-scroll-viewport,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{position:relative;overflow:var(--_viewport-overflow);scroll-timeline:var(--_viewport_scroll-timeline);box-sizing:border-box!important;-webkit-overflow-scrolling:touch;will-change:scroll-position;-webkit-user-select:var(--_viewport-user-select);user-select:var(--_viewport-user-select);overscroll-behavior:var(--_viewport-overscroll-behavior);pointer-events:var(--_viewport-pointer-events)}:host.ng-scroll-viewport>.ng-scroll-content,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport>.ng-scroll-content{width:var(--_scrollbar-content-width);z-index:1;min-width:100%;min-height:100%;contain:content;padding:var(--_viewport-padding-top, 0) var(--_viewport-padding-right, 0) var(--_viewport-padding-bottom, 0) var(--_viewport-padding-left, 0)}:host[appearance=native]{--_spacer-width: calc(var(--spacer-width) + var(--_scrollbar-thickness));--_spacer-height: calc(var(--spacer-height) + var(--_scrollbar-thickness))}:host.ng-scroll-viewport>.ng-scroll-spacer,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport>.ng-scroll-spacer{position:relative;width:calc(var(--_spacer-width) * 1px);height:calc(var(--_spacer-height) * 1px)}:host.ng-scroll-viewport,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{scrollbar-width:none!important}:host.ng-scroll-viewport::-webkit-scrollbar,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport::-webkit-scrollbar{display:none!important}:host[position=invertX],:host[position=invertAll]{--_horizontal-top: var(--_scrollbar-offset-px);--_horizontal-bottom: initial;--_scrollbar-wrapper-x-top: 0}:host[dir=ltr]{--_scrollbar-wrapper-y-right: initial;--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-left: calc(var(--viewport-width) - var(--_scrollbar-thickness))}:host[dir=ltr][position=invertY],:host[dir=ltr][position=invertAll]{--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-left: 0}:host[dir=rtl]{--_scrollbar-wrapper-y-left: initial;--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-right: calc(var(--viewport-width) - var(--_scrollbar-thickness))}:host[dir=rtl][position=invertY],:host[dir=rtl][position=invertAll]{--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-right: 0}:host[verticalUsed=true][horizontalUsed=true]{--_scrollbar-thickness-margin: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 3);--_scrollbar-thickness-margin-px: calc(var(--_scrollbar-thickness-margin) * 1px)}:host[horizontalUsed=true]{--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-thickness-margin-px)}:host[horizontalUsed=true][position=invertX],:host[horizontalUsed=true][position=invertAll]{--_vertical-top: var(--_scrollbar-thickness-margin-px);--_vertical-bottom: var(--_scrollbar-offset-px)}:host[verticalUsed=true][dir=ltr]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}:host[verticalUsed=true][dir=rtl]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}:host[verticalUsed=true][position=invertY][dir=ltr],:host[verticalUsed=true][position=invertAll][dir=ltr]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}:host[verticalUsed=true][position=invertY][dir=rtl],:host[verticalUsed=true][position=invertAll][dir=rtl]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}:host[appearance=native][verticalUsed=true][dir=ltr]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][verticalUsed=true][dir=rtl]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}:host[appearance=native][verticalUsed=true][position=invertY][dir=ltr],:host[appearance=native][verticalUsed=true][position=invertAll][dir=ltr]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}:host[appearance=native][verticalUsed=true][position=invertY][dir=rtl],:host[appearance=native][verticalUsed=true][position=invertAll][dir=rtl]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][horizontalUsed=true]{--_viewport-padding-top: 0;--_viewport-padding-bottom: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][horizontalUsed=true][position=invertX],:host[appearance=native][horizontalUsed=true][position=invertAll]{--_viewport-padding-top: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-bottom: 0}:host[visibility=hover]{--_scrollbar-hover-opacity: 0;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-leave-duration) var(--INTERNAL-scrollbar-hover-opacity-transition-leave-delay)}:host[visibility=hover]:hover,:host[visibility=hover]:active,:host[visibility=hover]:focus{--_scrollbar-hover-opacity: 1;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-enter-duration)}:host[dir=ltr] ::ng-deep .scroll-reached-trigger-element[trigger=start],:host[dir=ltr] ::ng-deep .scroll-dropped-trigger-element[trigger=start]{left:0;right:unset}:host[dir=ltr] ::ng-deep .scroll-reached-trigger-element[trigger=end],:host[dir=ltr] ::ng-deep .scroll-dropped-trigger-element[trigger=end]{right:0;left:unset}:host[dir=rtl] ::ng-deep .scroll-reached-trigger-element[trigger=start],:host[dir=rtl] ::ng-deep .scroll-dropped-trigger-element[trigger=start]{right:0;left:unset}:host[dir=rtl] ::ng-deep .scroll-reached-trigger-element[trigger=end],:host[dir=rtl] ::ng-deep .scroll-dropped-trigger-element[trigger=end]{left:0;right:unset}:host ::ng-deep .ng-scroll-reached-wrapper,:host ::ng-deep .ng-scroll-dropped-wrapper,:host ::ng-deep .scroll-reached-trigger-element,:host ::ng-deep .scroll-dropped-trigger-element{position:absolute;-webkit-user-select:none;user-select:none;pointer-events:none;z-index:-9999}:host ::ng-deep .ng-scroll-reached-wrapper,:host ::ng-deep .ng-scroll-dropped-wrapper{visibility:hidden;inset:0;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}:host ::ng-deep [isHorizontallyScrollable=false] .scroll-reached-trigger-element[trigger=end],:host ::ng-deep [isHorizontallyScrollable=false] .scroll-dropped-trigger-element[trigger=end]{display:none}:host ::ng-deep [isVerticallyScrollable=false] .scroll-reached-trigger-element[trigger=bottom],:host ::ng-deep [isVerticallyScrollable=false] .scroll-dropped-trigger-element[trigger=bottom]{display:none}:host ::ng-deep .scroll-reached-trigger-element{background:red}:host ::ng-deep .scroll-reached-trigger-element[trigger=top],:host ::ng-deep .scroll-reached-trigger-element[trigger=bottom]{left:0;right:0}:host ::ng-deep .scroll-reached-trigger-element[trigger=start],:host ::ng-deep .scroll-reached-trigger-element[trigger=end]{top:0;bottom:0}:host ::ng-deep .scroll-reached-trigger-element[trigger=top]{top:0;height:var(--reached-offset-top)}:host ::ng-deep .scroll-reached-trigger-element[trigger=bottom]{bottom:0;height:var(--reached-offset-bottom)}:host ::ng-deep .scroll-reached-trigger-element[trigger=start]{width:var(--reached-offset-start)}:host ::ng-deep .scroll-reached-trigger-element[trigger=end]{width:var(--reached-offset-end)}:host .scroll-dropped-trigger-element{background:#00f}:host .scroll-dropped-trigger-element[trigger=top],:host .scroll-dropped-trigger-element[trigger=bottom]{left:0;right:0}:host .scroll-dropped-trigger-element[trigger=start],:host .scroll-dropped-trigger-element[trigger=end]{top:0;bottom:0}:host .scroll-dropped-trigger-element[trigger=top]{top:0;height:var(--dropped-offset-top)}:host .scroll-dropped-trigger-element[trigger=bottom]{bottom:0;height:var(--dropped-offset-bottom)}:host .scroll-dropped-trigger-element[trigger=start]{width:var(--dropped-offset-start)}:host .scroll-dropped-trigger-element[trigger=end]{width:var(--dropped-offset-end)}:host[verticalUsed=true]{--_timeline-scope: --scrollerY;--_animation-timeline-y: --scrollerY;--_viewport_scroll-timeline: --scrollerY y}:host[horizontalUsed=true]{--_timeline-scope: --scrollerX;--_animation-timeline-x: --scrollerX;--_viewport_scroll-timeline: --scrollerX x}:host[verticalUsed=true][horizontalUsed=true]{--_timeline-scope: --scrollerX, --scrollerY;--_viewport_scroll-timeline: --scrollerX x, --scrollerY y}:host[orientation=vertical]{--_viewport-overflow: hidden auto;--_scrollbar-content-width: unset}:host[orientation=horizontal]{--_viewport-overflow: auto hidden}:host[disableInteraction=true]{--_viewport-pointer-events: none;--_scrollbar-pointer-events: none}:host[isVerticallyScrollable=false]{--_vertical-thumb-display: none}:host[isHorizontallyScrollable=false]{--_horizontal-thumb-display: none}:host[dragging=x],:host[dragging=y]{--_viewport-user-select: none}:host[dragging=x]{--_track-x-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-x-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}:host[dragging=y]{--_track-y-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-y-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}:host[mobile=true]{--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-mobile-overscroll-behavior)}\\n\"], dependencies: [{ kind: \"component\", type: Scrollbars, selector: \"scrollbars\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: NgScrollbar, decorators: [{\n            type: Component,\n            args: [{ selector: 'ng-scrollbar:not([externalViewport])', exportAs: 'ngScrollbar', imports: [Scrollbars], template: `\r\n    <div #contentWrapper>\r\n      <ng-content/>\r\n      <scrollbars/>\r\n    </div>\r\n  `, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        { provide: NG_SCROLLBAR, useExisting: NgScrollbar },\n                        ViewportAdapter\n                    ], styles: [\":host{display:block;position:relative;max-height:100%;max-width:100%;--INTERNAL-scrollbar-border-radius: var(--scrollbar-border-radius, 0px);--INTERNAL-scrollbar-thickness: var(--scrollbar-thickness, 5);--INTERNAL-scrollbar-offset: var(--scrollbar-offset, 0);--INTERNAL-scrollbar-track-wrapper-transition: var(--scrollbar-track-wrapper-transition, width 60ms linear, height 60ms linear);--INTERNAL-scrollbar-track-color: var(--scrollbar-track-color, transparent);--INTERNAL-scrollbar-thumb-color: var(--scrollbar-thumb-color, rgb(0 0 0 / 20%));--INTERNAL-scrollbar-thumb-hover-color: var(--scrollbar-thumb-hover-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-hover-thickness: var(--scrollbar-hover-thickness, var(--INTERNAL-scrollbar-thickness));--INTERNAL-scrollbar-thumb-transition: var(--scrollbar-thumb-transition, none);--INTERNAL-scrollbar-thumb-min-size: var(--scrollbar-thumb-min-size, 20);--INTERNAL-scrollbar-button-color: var(--scrollbar-button-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-button-hover-color: var(--scrollbar-button-hover-color, var(--INTERNAL-scrollbar-button-color));--INTERNAL-scrollbar-button-active-color: var(--scrollbar-button-active-color, var(--INTERNAL-scrollbar-button-hover-color));--INTERNAL-scrollbar-button-fill: var(--scrollbar-button-fill, white);--INTERNAL-scrollbar-button-hover-fill: var(--scrollbar-button-hover-fill, var(--INTERNAL-scrollbar-button-fill));--INTERNAL-scrollbar-button-active-fill: var(--scrollbar-button-active-fill, var(--INTERNAL-scrollbar-button-hover-fill));--INTERNAL-scrollbar-button-size: var(--scrollbar-button-size, 20px);--INTERNAL-scrollbar-hover-opacity-transition-enter-duration: var(--scrollbar-hover-opacity-transition-enter-duration, 0);--INTERNAL-scrollbar-hover-opacity-transition-leave-duration: var(--scrollbar-hover-opacity-transition-leave-duration, .4s);--INTERNAL-scrollbar-hover-opacity-transition-leave-delay: var(--scrollbar-hover-opacity-transition-leave-delay, 1s);--INTERNAL-scrollbar-overscroll-behavior: var(--scrollbar-overscroll-behavior, initial);--INTERNAL-scrollbar-mobile-overscroll-behavior: var(--scrollbar-mobile-overscroll-behavior, none);--_scrollbar-thickness: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 2);--_scrollbar-pointer-events: auto;--_scrollbar-offset-px: calc(var(--INTERNAL-scrollbar-offset) * 1px);--_scrollbar-thickness-px: calc(var(--INTERNAL-scrollbar-thickness) * 1px);--_scrollbar-hover-thickness-px: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_viewport-padding-top: 0;--_viewport-padding-bottom: 0;--_viewport-padding-left: 0;--_viewport-padding-right: 0;--_horizontal-thumb-display: block;--_vertical-thumb-display: block;--_viewport-overflow: auto;--_viewport-pointer-events: auto;--_thumb-x-color: var(--INTERNAL-scrollbar-thumb-color);--_thumb-y-color: var(--INTERNAL-scrollbar-thumb-color);--_track-y-thickness: var(--_scrollbar-thickness-px);--_track-x-thickness: var(--_scrollbar-thickness-px);--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-overscroll-behavior);--_scrollbar-content-width: fit-content}:host{--_spacer-width: var(--spacer-width);--_spacer-height: var(--spacer-height);--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-offset-px);--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-offset-px);--_horizontal-top: initial;--_horizontal-bottom: var(--_scrollbar-offset-px);--_scrollbar-wrapper-x-top: calc(var(--viewport-height) - var(--_scrollbar-thickness));--reached-offset: 1px;--reached-offset-top: var(--reached-offset);--reached-offset-bottom: var(--reached-offset);--reached-offset-start: var(--reached-offset);--reached-offset-end: var(--reached-offset);--dropped-offset: 1px;--dropped-offset-top: var(--dropped-offset);--dropped-offset-bottom: var(--dropped-offset);--dropped-offset-start: var(--dropped-offset);--dropped-offset-end: var(--dropped-offset);--_viewport_scroll-timeline: unset;--_animation-timeline-y: unset;--_scrollbar-y-thumb-transform-to-value: unset;--_scrollbar-x-thumb-transform-to-value: unset;--_scrollbar-thumb-transform-from: unset;--_scrollbar-thumb-transform-to: unset}:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{min-height:100%;min-width:100%;height:100%;max-height:100%;max-width:100%}:host.ng-scroll-viewport,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{position:relative;overflow:var(--_viewport-overflow);scroll-timeline:var(--_viewport_scroll-timeline);box-sizing:border-box!important;-webkit-overflow-scrolling:touch;will-change:scroll-position;-webkit-user-select:var(--_viewport-user-select);user-select:var(--_viewport-user-select);overscroll-behavior:var(--_viewport-overscroll-behavior);pointer-events:var(--_viewport-pointer-events)}:host.ng-scroll-viewport>.ng-scroll-content,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport>.ng-scroll-content{width:var(--_scrollbar-content-width);z-index:1;min-width:100%;min-height:100%;contain:content;padding:var(--_viewport-padding-top, 0) var(--_viewport-padding-right, 0) var(--_viewport-padding-bottom, 0) var(--_viewport-padding-left, 0)}:host[appearance=native]{--_spacer-width: calc(var(--spacer-width) + var(--_scrollbar-thickness));--_spacer-height: calc(var(--spacer-height) + var(--_scrollbar-thickness))}:host.ng-scroll-viewport>.ng-scroll-spacer,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport>.ng-scroll-spacer{position:relative;width:calc(var(--_spacer-width) * 1px);height:calc(var(--_spacer-height) * 1px)}:host.ng-scroll-viewport,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{scrollbar-width:none!important}:host.ng-scroll-viewport::-webkit-scrollbar,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport::-webkit-scrollbar{display:none!important}:host[position=invertX],:host[position=invertAll]{--_horizontal-top: var(--_scrollbar-offset-px);--_horizontal-bottom: initial;--_scrollbar-wrapper-x-top: 0}:host[dir=ltr]{--_scrollbar-wrapper-y-right: initial;--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-left: calc(var(--viewport-width) - var(--_scrollbar-thickness))}:host[dir=ltr][position=invertY],:host[dir=ltr][position=invertAll]{--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-left: 0}:host[dir=rtl]{--_scrollbar-wrapper-y-left: initial;--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-right: calc(var(--viewport-width) - var(--_scrollbar-thickness))}:host[dir=rtl][position=invertY],:host[dir=rtl][position=invertAll]{--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-right: 0}:host[verticalUsed=true][horizontalUsed=true]{--_scrollbar-thickness-margin: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 3);--_scrollbar-thickness-margin-px: calc(var(--_scrollbar-thickness-margin) * 1px)}:host[horizontalUsed=true]{--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-thickness-margin-px)}:host[horizontalUsed=true][position=invertX],:host[horizontalUsed=true][position=invertAll]{--_vertical-top: var(--_scrollbar-thickness-margin-px);--_vertical-bottom: var(--_scrollbar-offset-px)}:host[verticalUsed=true][dir=ltr]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}:host[verticalUsed=true][dir=rtl]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}:host[verticalUsed=true][position=invertY][dir=ltr],:host[verticalUsed=true][position=invertAll][dir=ltr]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}:host[verticalUsed=true][position=invertY][dir=rtl],:host[verticalUsed=true][position=invertAll][dir=rtl]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}:host[appearance=native][verticalUsed=true][dir=ltr]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][verticalUsed=true][dir=rtl]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}:host[appearance=native][verticalUsed=true][position=invertY][dir=ltr],:host[appearance=native][verticalUsed=true][position=invertAll][dir=ltr]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}:host[appearance=native][verticalUsed=true][position=invertY][dir=rtl],:host[appearance=native][verticalUsed=true][position=invertAll][dir=rtl]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][horizontalUsed=true]{--_viewport-padding-top: 0;--_viewport-padding-bottom: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][horizontalUsed=true][position=invertX],:host[appearance=native][horizontalUsed=true][position=invertAll]{--_viewport-padding-top: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-bottom: 0}:host[visibility=hover]{--_scrollbar-hover-opacity: 0;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-leave-duration) var(--INTERNAL-scrollbar-hover-opacity-transition-leave-delay)}:host[visibility=hover]:hover,:host[visibility=hover]:active,:host[visibility=hover]:focus{--_scrollbar-hover-opacity: 1;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-enter-duration)}:host[dir=ltr] ::ng-deep .scroll-reached-trigger-element[trigger=start],:host[dir=ltr] ::ng-deep .scroll-dropped-trigger-element[trigger=start]{left:0;right:unset}:host[dir=ltr] ::ng-deep .scroll-reached-trigger-element[trigger=end],:host[dir=ltr] ::ng-deep .scroll-dropped-trigger-element[trigger=end]{right:0;left:unset}:host[dir=rtl] ::ng-deep .scroll-reached-trigger-element[trigger=start],:host[dir=rtl] ::ng-deep .scroll-dropped-trigger-element[trigger=start]{right:0;left:unset}:host[dir=rtl] ::ng-deep .scroll-reached-trigger-element[trigger=end],:host[dir=rtl] ::ng-deep .scroll-dropped-trigger-element[trigger=end]{left:0;right:unset}:host ::ng-deep .ng-scroll-reached-wrapper,:host ::ng-deep .ng-scroll-dropped-wrapper,:host ::ng-deep .scroll-reached-trigger-element,:host ::ng-deep .scroll-dropped-trigger-element{position:absolute;-webkit-user-select:none;user-select:none;pointer-events:none;z-index:-9999}:host ::ng-deep .ng-scroll-reached-wrapper,:host ::ng-deep .ng-scroll-dropped-wrapper{visibility:hidden;inset:0;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}:host ::ng-deep [isHorizontallyScrollable=false] .scroll-reached-trigger-element[trigger=end],:host ::ng-deep [isHorizontallyScrollable=false] .scroll-dropped-trigger-element[trigger=end]{display:none}:host ::ng-deep [isVerticallyScrollable=false] .scroll-reached-trigger-element[trigger=bottom],:host ::ng-deep [isVerticallyScrollable=false] .scroll-dropped-trigger-element[trigger=bottom]{display:none}:host ::ng-deep .scroll-reached-trigger-element{background:red}:host ::ng-deep .scroll-reached-trigger-element[trigger=top],:host ::ng-deep .scroll-reached-trigger-element[trigger=bottom]{left:0;right:0}:host ::ng-deep .scroll-reached-trigger-element[trigger=start],:host ::ng-deep .scroll-reached-trigger-element[trigger=end]{top:0;bottom:0}:host ::ng-deep .scroll-reached-trigger-element[trigger=top]{top:0;height:var(--reached-offset-top)}:host ::ng-deep .scroll-reached-trigger-element[trigger=bottom]{bottom:0;height:var(--reached-offset-bottom)}:host ::ng-deep .scroll-reached-trigger-element[trigger=start]{width:var(--reached-offset-start)}:host ::ng-deep .scroll-reached-trigger-element[trigger=end]{width:var(--reached-offset-end)}:host .scroll-dropped-trigger-element{background:#00f}:host .scroll-dropped-trigger-element[trigger=top],:host .scroll-dropped-trigger-element[trigger=bottom]{left:0;right:0}:host .scroll-dropped-trigger-element[trigger=start],:host .scroll-dropped-trigger-element[trigger=end]{top:0;bottom:0}:host .scroll-dropped-trigger-element[trigger=top]{top:0;height:var(--dropped-offset-top)}:host .scroll-dropped-trigger-element[trigger=bottom]{bottom:0;height:var(--dropped-offset-bottom)}:host .scroll-dropped-trigger-element[trigger=start]{width:var(--dropped-offset-start)}:host .scroll-dropped-trigger-element[trigger=end]{width:var(--dropped-offset-end)}:host[verticalUsed=true]{--_timeline-scope: --scrollerY;--_animation-timeline-y: --scrollerY;--_viewport_scroll-timeline: --scrollerY y}:host[horizontalUsed=true]{--_timeline-scope: --scrollerX;--_animation-timeline-x: --scrollerX;--_viewport_scroll-timeline: --scrollerX x}:host[verticalUsed=true][horizontalUsed=true]{--_timeline-scope: --scrollerX, --scrollerY;--_viewport_scroll-timeline: --scrollerX x, --scrollerY y}:host[orientation=vertical]{--_viewport-overflow: hidden auto;--_scrollbar-content-width: unset}:host[orientation=horizontal]{--_viewport-overflow: auto hidden}:host[disableInteraction=true]{--_viewport-pointer-events: none;--_scrollbar-pointer-events: none}:host[isVerticallyScrollable=false]{--_vertical-thumb-display: none}:host[isHorizontallyScrollable=false]{--_horizontal-thumb-display: none}:host[dragging=x],:host[dragging=y]{--_viewport-user-select: none}:host[dragging=x]{--_track-x-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-x-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}:host[dragging=y]{--_track-y-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-y-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}:host[mobile=true]{--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-mobile-overscroll-behavior)}\\n\"] }]\n        }], ctorParameters: () => [] });\n\nclass NgScrollbarExt extends NgScrollbarCore {\n    constructor() {\n        // Using `afterRenderEffect` would run twice, one when viewport directive is detected\n        // and one when content wrapper is detected, therefore `effect` is better because it runs only once.\n        effect(() => {\n            const viewportElement = this.viewportElement();\n            const contentWrapperElement = this.contentWrapperElement();\n            const spacerElement = this.spacerElement();\n            const viewportError = this.viewportError();\n            const contentWrapperError = this.contentWrapperError();\n            const spacerError = this.spacerError();\n            untracked(() => {\n                if (!this.skipInit) {\n                    const error = viewportError || contentWrapperError || spacerError;\n                    if (error) {\n                        console.error(error);\n                    }\n                    else {\n                        this.initialize(viewportElement, contentWrapperElement, spacerElement);\n                    }\n                }\n            });\n        });\n        super();\n        this.renderer = inject(Renderer2);\n        this.appRef = inject(ApplicationRef);\n        this._scrollbars = signal(null);\n        /**\n         * Selector used to query the viewport element.\n         */\n        this.externalViewport = input();\n        /**\n         * Selector used to query the content wrapper element.\n         */\n        this.externalContentWrapper = input();\n        /**\n         * Selector used to query the spacer element (virtual scroll integration).\n         * In the case of integrating the scrollbar with a virtual scroll component,\n         * a spacer element is typically created to match the real size of the content.\n         * The scrollbar will use the size of this spacer element for calculations instead of the content wrapper size.\n         */\n        this.externalSpacer = input();\n        this.viewportElement = linkedSignal({\n            source: this.externalViewport,\n            // If viewport selector was defined, query the element\n            computation: (selector) => this.getElement(selector) || this.customViewport()?.nativeElement\n        });\n        this.viewportError = computed(() => {\n            return !this.viewportElement()\n                ? `[NgScrollbar]: Could not find the viewport element for the provided selector \"${this.externalViewport()}\"`\n                : null;\n        });\n        this.contentWrapperElement = linkedSignal({\n            source: this.externalContentWrapper,\n            computation: (selector) => this.getElement(selector)\n        });\n        this.contentWrapperError = computed(() => {\n            return !this.contentWrapperElement() && this.externalContentWrapper()\n                ? `[NgScrollbar]: Content wrapper element not found for the provided selector \"${this.externalContentWrapper()}\"`\n                : null;\n        });\n        this.spacerElement = linkedSignal({\n            source: this.externalSpacer,\n            computation: (selector) => this.getElement(selector)\n        });\n        this.spacerError = computed(() => {\n            return !this.spacerElement() && this.externalSpacer()\n                ? `[NgScrollbar]: Spacer element not found for the provided selector \"${this.externalSpacer()}\"`\n                : null;\n        });\n        /**\n         * Reference to the external viewport directive if used\n         */\n        this.customViewport = contentChild(ScrollViewport, { descendants: true });\n    }\n    ngOnDestroy() {\n        if (this._scrollbarsRef) {\n            this.appRef.detachView(this._scrollbarsRef.hostView);\n            this._scrollbarsRef.destroy();\n        }\n    }\n    initialize(viewportElement, contentWrapperElement, spacerElement) {\n        if (this.skipInit) {\n            // If initialized via async detection, then we should set the signals\n            this.viewportElement.set(viewportElement);\n            this.contentWrapperElement.set(contentWrapperElement);\n            this.spacerElement.set(spacerElement);\n        }\n        // If no external spacer and no content wrapper are provided, create a content wrapper element\n        if (!spacerElement && !contentWrapperElement) {\n            contentWrapperElement = this.renderer.createElement('div');\n            // Move all content of the viewport into the content wrapper\n            const childNodes = Array.from(viewportElement.childNodes);\n            childNodes.forEach((node) => this.renderer.appendChild(contentWrapperElement, node));\n            // Append the content wrapper to the viewport\n            this.renderer.appendChild(viewportElement, contentWrapperElement);\n        }\n        // Make sure content wrapper element is defined to proceed\n        if (contentWrapperElement) {\n            // Initialize viewport\n            this.viewport.init(viewportElement, contentWrapperElement, spacerElement);\n            // Attach scrollbars\n            this._attachScrollbars();\n        }\n    }\n    _attachScrollbars() {\n        // Create the scrollbars component\n        this._scrollbarsRef = createComponent(Scrollbars, {\n            environmentInjector: this.appRef.injector,\n            elementInjector: Injector.create({ providers: [{ provide: NG_SCROLLBAR, useValue: this }] })\n        });\n        // Attach scrollbar to the content wrapper\n        this.renderer.appendChild(this.viewport.contentWrapperElement, this._scrollbarsRef.location.nativeElement);\n        // Attach the host view of the component to the main change detection tree, so that its lifecycle hooks run.\n        this.appRef.attachView(this._scrollbarsRef.hostView);\n        // Set the scrollbars instance\n        this._scrollbars.set(this._scrollbarsRef.instance);\n    }\n    getElement(selector) {\n        return selector ? this.nativeElement.querySelector(selector) : null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: NgScrollbarExt, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.2.0\", version: \"19.1.1\", type: NgScrollbarExt, isStandalone: true, selector: \"ng-scrollbar[externalViewport]\", inputs: { externalViewport: { classPropertyName: \"externalViewport\", publicName: \"externalViewport\", isSignal: true, isRequired: false, transformFunction: null }, externalContentWrapper: { classPropertyName: \"externalContentWrapper\", publicName: \"externalContentWrapper\", isSignal: true, isRequired: false, transformFunction: null }, externalSpacer: { classPropertyName: \"externalSpacer\", publicName: \"externalSpacer\", isSignal: true, isRequired: false, transformFunction: null } }, host: { attributes: { \"ngSkipHydration\": \"true\" }, properties: { \"class.ng-scrollbar-external-viewport\": \"true\" } }, providers: [\n            { provide: NG_SCROLLBAR, useExisting: NgScrollbarExt },\n            { provide: NgScrollbarCore, useExisting: NgScrollbar },\n            ViewportAdapter\n        ], queries: [{ propertyName: \"customViewport\", first: true, predicate: ScrollViewport, descendants: true, isSignal: true }], exportAs: [\"ngScrollbar\"], usesInheritance: true, ngImport: i0, template: '<ng-content/>', isInline: true, styles: [\":host{display:block;position:relative;max-height:100%;max-width:100%;--INTERNAL-scrollbar-border-radius: var(--scrollbar-border-radius, 0px);--INTERNAL-scrollbar-thickness: var(--scrollbar-thickness, 5);--INTERNAL-scrollbar-offset: var(--scrollbar-offset, 0);--INTERNAL-scrollbar-track-wrapper-transition: var(--scrollbar-track-wrapper-transition, width 60ms linear, height 60ms linear);--INTERNAL-scrollbar-track-color: var(--scrollbar-track-color, transparent);--INTERNAL-scrollbar-thumb-color: var(--scrollbar-thumb-color, rgb(0 0 0 / 20%));--INTERNAL-scrollbar-thumb-hover-color: var(--scrollbar-thumb-hover-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-hover-thickness: var(--scrollbar-hover-thickness, var(--INTERNAL-scrollbar-thickness));--INTERNAL-scrollbar-thumb-transition: var(--scrollbar-thumb-transition, none);--INTERNAL-scrollbar-thumb-min-size: var(--scrollbar-thumb-min-size, 20);--INTERNAL-scrollbar-button-color: var(--scrollbar-button-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-button-hover-color: var(--scrollbar-button-hover-color, var(--INTERNAL-scrollbar-button-color));--INTERNAL-scrollbar-button-active-color: var(--scrollbar-button-active-color, var(--INTERNAL-scrollbar-button-hover-color));--INTERNAL-scrollbar-button-fill: var(--scrollbar-button-fill, white);--INTERNAL-scrollbar-button-hover-fill: var(--scrollbar-button-hover-fill, var(--INTERNAL-scrollbar-button-fill));--INTERNAL-scrollbar-button-active-fill: var(--scrollbar-button-active-fill, var(--INTERNAL-scrollbar-button-hover-fill));--INTERNAL-scrollbar-button-size: var(--scrollbar-button-size, 20px);--INTERNAL-scrollbar-hover-opacity-transition-enter-duration: var(--scrollbar-hover-opacity-transition-enter-duration, 0);--INTERNAL-scrollbar-hover-opacity-transition-leave-duration: var(--scrollbar-hover-opacity-transition-leave-duration, .4s);--INTERNAL-scrollbar-hover-opacity-transition-leave-delay: var(--scrollbar-hover-opacity-transition-leave-delay, 1s);--INTERNAL-scrollbar-overscroll-behavior: var(--scrollbar-overscroll-behavior, initial);--INTERNAL-scrollbar-mobile-overscroll-behavior: var(--scrollbar-mobile-overscroll-behavior, none);--_scrollbar-thickness: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 2);--_scrollbar-pointer-events: auto;--_scrollbar-offset-px: calc(var(--INTERNAL-scrollbar-offset) * 1px);--_scrollbar-thickness-px: calc(var(--INTERNAL-scrollbar-thickness) * 1px);--_scrollbar-hover-thickness-px: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_viewport-padding-top: 0;--_viewport-padding-bottom: 0;--_viewport-padding-left: 0;--_viewport-padding-right: 0;--_horizontal-thumb-display: block;--_vertical-thumb-display: block;--_viewport-overflow: auto;--_viewport-pointer-events: auto;--_thumb-x-color: var(--INTERNAL-scrollbar-thumb-color);--_thumb-y-color: var(--INTERNAL-scrollbar-thumb-color);--_track-y-thickness: var(--_scrollbar-thickness-px);--_track-x-thickness: var(--_scrollbar-thickness-px);--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-overscroll-behavior);--_scrollbar-content-width: fit-content}:host{--_spacer-width: var(--spacer-width);--_spacer-height: var(--spacer-height);--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-offset-px);--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-offset-px);--_horizontal-top: initial;--_horizontal-bottom: var(--_scrollbar-offset-px);--_scrollbar-wrapper-x-top: calc(var(--viewport-height) - var(--_scrollbar-thickness));--reached-offset: 1px;--reached-offset-top: var(--reached-offset);--reached-offset-bottom: var(--reached-offset);--reached-offset-start: var(--reached-offset);--reached-offset-end: var(--reached-offset);--dropped-offset: 1px;--dropped-offset-top: var(--dropped-offset);--dropped-offset-bottom: var(--dropped-offset);--dropped-offset-start: var(--dropped-offset);--dropped-offset-end: var(--dropped-offset);--_viewport_scroll-timeline: unset;--_animation-timeline-y: unset;--_scrollbar-y-thumb-transform-to-value: unset;--_scrollbar-x-thumb-transform-to-value: unset;--_scrollbar-thumb-transform-from: unset;--_scrollbar-thumb-transform-to: unset}:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{min-height:100%;min-width:100%;height:100%;max-height:100%;max-width:100%}:host.ng-scroll-viewport,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{position:relative;overflow:var(--_viewport-overflow);scroll-timeline:var(--_viewport_scroll-timeline);box-sizing:border-box!important;-webkit-overflow-scrolling:touch;will-change:scroll-position;-webkit-user-select:var(--_viewport-user-select);user-select:var(--_viewport-user-select);overscroll-behavior:var(--_viewport-overscroll-behavior);pointer-events:var(--_viewport-pointer-events)}:host.ng-scroll-viewport>.ng-scroll-content,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport>.ng-scroll-content{width:var(--_scrollbar-content-width);z-index:1;min-width:100%;min-height:100%;contain:content;padding:var(--_viewport-padding-top, 0) var(--_viewport-padding-right, 0) var(--_viewport-padding-bottom, 0) var(--_viewport-padding-left, 0)}:host[appearance=native]{--_spacer-width: calc(var(--spacer-width) + var(--_scrollbar-thickness));--_spacer-height: calc(var(--spacer-height) + var(--_scrollbar-thickness))}:host.ng-scroll-viewport>.ng-scroll-spacer,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport>.ng-scroll-spacer{position:relative;width:calc(var(--_spacer-width) * 1px);height:calc(var(--_spacer-height) * 1px)}:host.ng-scroll-viewport,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{scrollbar-width:none!important}:host.ng-scroll-viewport::-webkit-scrollbar,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport::-webkit-scrollbar{display:none!important}:host[position=invertX],:host[position=invertAll]{--_horizontal-top: var(--_scrollbar-offset-px);--_horizontal-bottom: initial;--_scrollbar-wrapper-x-top: 0}:host[dir=ltr]{--_scrollbar-wrapper-y-right: initial;--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-left: calc(var(--viewport-width) - var(--_scrollbar-thickness))}:host[dir=ltr][position=invertY],:host[dir=ltr][position=invertAll]{--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-left: 0}:host[dir=rtl]{--_scrollbar-wrapper-y-left: initial;--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-right: calc(var(--viewport-width) - var(--_scrollbar-thickness))}:host[dir=rtl][position=invertY],:host[dir=rtl][position=invertAll]{--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-right: 0}:host[verticalUsed=true][horizontalUsed=true]{--_scrollbar-thickness-margin: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 3);--_scrollbar-thickness-margin-px: calc(var(--_scrollbar-thickness-margin) * 1px)}:host[horizontalUsed=true]{--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-thickness-margin-px)}:host[horizontalUsed=true][position=invertX],:host[horizontalUsed=true][position=invertAll]{--_vertical-top: var(--_scrollbar-thickness-margin-px);--_vertical-bottom: var(--_scrollbar-offset-px)}:host[verticalUsed=true][dir=ltr]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}:host[verticalUsed=true][dir=rtl]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}:host[verticalUsed=true][position=invertY][dir=ltr],:host[verticalUsed=true][position=invertAll][dir=ltr]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}:host[verticalUsed=true][position=invertY][dir=rtl],:host[verticalUsed=true][position=invertAll][dir=rtl]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}:host[appearance=native][verticalUsed=true][dir=ltr]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][verticalUsed=true][dir=rtl]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}:host[appearance=native][verticalUsed=true][position=invertY][dir=ltr],:host[appearance=native][verticalUsed=true][position=invertAll][dir=ltr]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}:host[appearance=native][verticalUsed=true][position=invertY][dir=rtl],:host[appearance=native][verticalUsed=true][position=invertAll][dir=rtl]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][horizontalUsed=true]{--_viewport-padding-top: 0;--_viewport-padding-bottom: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][horizontalUsed=true][position=invertX],:host[appearance=native][horizontalUsed=true][position=invertAll]{--_viewport-padding-top: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-bottom: 0}:host[visibility=hover]{--_scrollbar-hover-opacity: 0;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-leave-duration) var(--INTERNAL-scrollbar-hover-opacity-transition-leave-delay)}:host[visibility=hover]:hover,:host[visibility=hover]:active,:host[visibility=hover]:focus{--_scrollbar-hover-opacity: 1;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-enter-duration)}:host[dir=ltr] ::ng-deep .scroll-reached-trigger-element[trigger=start],:host[dir=ltr] ::ng-deep .scroll-dropped-trigger-element[trigger=start]{left:0;right:unset}:host[dir=ltr] ::ng-deep .scroll-reached-trigger-element[trigger=end],:host[dir=ltr] ::ng-deep .scroll-dropped-trigger-element[trigger=end]{right:0;left:unset}:host[dir=rtl] ::ng-deep .scroll-reached-trigger-element[trigger=start],:host[dir=rtl] ::ng-deep .scroll-dropped-trigger-element[trigger=start]{right:0;left:unset}:host[dir=rtl] ::ng-deep .scroll-reached-trigger-element[trigger=end],:host[dir=rtl] ::ng-deep .scroll-dropped-trigger-element[trigger=end]{left:0;right:unset}:host ::ng-deep .ng-scroll-reached-wrapper,:host ::ng-deep .ng-scroll-dropped-wrapper,:host ::ng-deep .scroll-reached-trigger-element,:host ::ng-deep .scroll-dropped-trigger-element{position:absolute;-webkit-user-select:none;user-select:none;pointer-events:none;z-index:-9999}:host ::ng-deep .ng-scroll-reached-wrapper,:host ::ng-deep .ng-scroll-dropped-wrapper{visibility:hidden;inset:0;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}:host ::ng-deep [isHorizontallyScrollable=false] .scroll-reached-trigger-element[trigger=end],:host ::ng-deep [isHorizontallyScrollable=false] .scroll-dropped-trigger-element[trigger=end]{display:none}:host ::ng-deep [isVerticallyScrollable=false] .scroll-reached-trigger-element[trigger=bottom],:host ::ng-deep [isVerticallyScrollable=false] .scroll-dropped-trigger-element[trigger=bottom]{display:none}:host ::ng-deep .scroll-reached-trigger-element{background:red}:host ::ng-deep .scroll-reached-trigger-element[trigger=top],:host ::ng-deep .scroll-reached-trigger-element[trigger=bottom]{left:0;right:0}:host ::ng-deep .scroll-reached-trigger-element[trigger=start],:host ::ng-deep .scroll-reached-trigger-element[trigger=end]{top:0;bottom:0}:host ::ng-deep .scroll-reached-trigger-element[trigger=top]{top:0;height:var(--reached-offset-top)}:host ::ng-deep .scroll-reached-trigger-element[trigger=bottom]{bottom:0;height:var(--reached-offset-bottom)}:host ::ng-deep .scroll-reached-trigger-element[trigger=start]{width:var(--reached-offset-start)}:host ::ng-deep .scroll-reached-trigger-element[trigger=end]{width:var(--reached-offset-end)}:host .scroll-dropped-trigger-element{background:#00f}:host .scroll-dropped-trigger-element[trigger=top],:host .scroll-dropped-trigger-element[trigger=bottom]{left:0;right:0}:host .scroll-dropped-trigger-element[trigger=start],:host .scroll-dropped-trigger-element[trigger=end]{top:0;bottom:0}:host .scroll-dropped-trigger-element[trigger=top]{top:0;height:var(--dropped-offset-top)}:host .scroll-dropped-trigger-element[trigger=bottom]{bottom:0;height:var(--dropped-offset-bottom)}:host .scroll-dropped-trigger-element[trigger=start]{width:var(--dropped-offset-start)}:host .scroll-dropped-trigger-element[trigger=end]{width:var(--dropped-offset-end)}:host[verticalUsed=true]{--_timeline-scope: --scrollerY;--_animation-timeline-y: --scrollerY;--_viewport_scroll-timeline: --scrollerY y}:host[horizontalUsed=true]{--_timeline-scope: --scrollerX;--_animation-timeline-x: --scrollerX;--_viewport_scroll-timeline: --scrollerX x}:host[verticalUsed=true][horizontalUsed=true]{--_timeline-scope: --scrollerX, --scrollerY;--_viewport_scroll-timeline: --scrollerX x, --scrollerY y}:host[orientation=vertical]{--_viewport-overflow: hidden auto;--_scrollbar-content-width: unset}:host[orientation=horizontal]{--_viewport-overflow: auto hidden}:host[disableInteraction=true]{--_viewport-pointer-events: none;--_scrollbar-pointer-events: none}:host[isVerticallyScrollable=false]{--_vertical-thumb-display: none}:host[isHorizontallyScrollable=false]{--_horizontal-thumb-display: none}:host[dragging=x],:host[dragging=y]{--_viewport-user-select: none}:host[dragging=x]{--_track-x-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-x-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}:host[dragging=y]{--_track-y-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-y-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}:host[mobile=true]{--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-mobile-overscroll-behavior)}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: NgScrollbarExt, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'ng-scrollbar[externalViewport]', exportAs: 'ngScrollbar', template: '<ng-content/>', changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        // This component appends a content wrapper element to the viewport\n                        // A hydration mismatch error will be thrown (NG0500) during DOM manipulation.\n                        // To avoid this error, the 'ngSkipHydration' attribute is added to skip hydration.\n                        ngSkipHydration: 'true',\n                        '[class.ng-scrollbar-external-viewport]': 'true'\n                    }, providers: [\n                        { provide: NG_SCROLLBAR, useExisting: NgScrollbarExt },\n                        { provide: NgScrollbarCore, useExisting: NgScrollbar },\n                        ViewportAdapter\n                    ], styles: [\":host{display:block;position:relative;max-height:100%;max-width:100%;--INTERNAL-scrollbar-border-radius: var(--scrollbar-border-radius, 0px);--INTERNAL-scrollbar-thickness: var(--scrollbar-thickness, 5);--INTERNAL-scrollbar-offset: var(--scrollbar-offset, 0);--INTERNAL-scrollbar-track-wrapper-transition: var(--scrollbar-track-wrapper-transition, width 60ms linear, height 60ms linear);--INTERNAL-scrollbar-track-color: var(--scrollbar-track-color, transparent);--INTERNAL-scrollbar-thumb-color: var(--scrollbar-thumb-color, rgb(0 0 0 / 20%));--INTERNAL-scrollbar-thumb-hover-color: var(--scrollbar-thumb-hover-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-hover-thickness: var(--scrollbar-hover-thickness, var(--INTERNAL-scrollbar-thickness));--INTERNAL-scrollbar-thumb-transition: var(--scrollbar-thumb-transition, none);--INTERNAL-scrollbar-thumb-min-size: var(--scrollbar-thumb-min-size, 20);--INTERNAL-scrollbar-button-color: var(--scrollbar-button-color, var(--INTERNAL-scrollbar-thumb-color));--INTERNAL-scrollbar-button-hover-color: var(--scrollbar-button-hover-color, var(--INTERNAL-scrollbar-button-color));--INTERNAL-scrollbar-button-active-color: var(--scrollbar-button-active-color, var(--INTERNAL-scrollbar-button-hover-color));--INTERNAL-scrollbar-button-fill: var(--scrollbar-button-fill, white);--INTERNAL-scrollbar-button-hover-fill: var(--scrollbar-button-hover-fill, var(--INTERNAL-scrollbar-button-fill));--INTERNAL-scrollbar-button-active-fill: var(--scrollbar-button-active-fill, var(--INTERNAL-scrollbar-button-hover-fill));--INTERNAL-scrollbar-button-size: var(--scrollbar-button-size, 20px);--INTERNAL-scrollbar-hover-opacity-transition-enter-duration: var(--scrollbar-hover-opacity-transition-enter-duration, 0);--INTERNAL-scrollbar-hover-opacity-transition-leave-duration: var(--scrollbar-hover-opacity-transition-leave-duration, .4s);--INTERNAL-scrollbar-hover-opacity-transition-leave-delay: var(--scrollbar-hover-opacity-transition-leave-delay, 1s);--INTERNAL-scrollbar-overscroll-behavior: var(--scrollbar-overscroll-behavior, initial);--INTERNAL-scrollbar-mobile-overscroll-behavior: var(--scrollbar-mobile-overscroll-behavior, none);--_scrollbar-thickness: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 2);--_scrollbar-pointer-events: auto;--_scrollbar-offset-px: calc(var(--INTERNAL-scrollbar-offset) * 1px);--_scrollbar-thickness-px: calc(var(--INTERNAL-scrollbar-thickness) * 1px);--_scrollbar-hover-thickness-px: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_viewport-padding-top: 0;--_viewport-padding-bottom: 0;--_viewport-padding-left: 0;--_viewport-padding-right: 0;--_horizontal-thumb-display: block;--_vertical-thumb-display: block;--_viewport-overflow: auto;--_viewport-pointer-events: auto;--_thumb-x-color: var(--INTERNAL-scrollbar-thumb-color);--_thumb-y-color: var(--INTERNAL-scrollbar-thumb-color);--_track-y-thickness: var(--_scrollbar-thickness-px);--_track-x-thickness: var(--_scrollbar-thickness-px);--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-overscroll-behavior);--_scrollbar-content-width: fit-content}:host{--_spacer-width: var(--spacer-width);--_spacer-height: var(--spacer-height);--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-offset-px);--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-offset-px);--_horizontal-top: initial;--_horizontal-bottom: var(--_scrollbar-offset-px);--_scrollbar-wrapper-x-top: calc(var(--viewport-height) - var(--_scrollbar-thickness));--reached-offset: 1px;--reached-offset-top: var(--reached-offset);--reached-offset-bottom: var(--reached-offset);--reached-offset-start: var(--reached-offset);--reached-offset-end: var(--reached-offset);--dropped-offset: 1px;--dropped-offset-top: var(--dropped-offset);--dropped-offset-bottom: var(--dropped-offset);--dropped-offset-start: var(--dropped-offset);--dropped-offset-end: var(--dropped-offset);--_viewport_scroll-timeline: unset;--_animation-timeline-y: unset;--_scrollbar-y-thumb-transform-to-value: unset;--_scrollbar-x-thumb-transform-to-value: unset;--_scrollbar-thumb-transform-from: unset;--_scrollbar-thumb-transform-to: unset}:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{min-height:100%;min-width:100%;height:100%;max-height:100%;max-width:100%}:host.ng-scroll-viewport,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{position:relative;overflow:var(--_viewport-overflow);scroll-timeline:var(--_viewport_scroll-timeline);box-sizing:border-box!important;-webkit-overflow-scrolling:touch;will-change:scroll-position;-webkit-user-select:var(--_viewport-user-select);user-select:var(--_viewport-user-select);overscroll-behavior:var(--_viewport-overscroll-behavior);pointer-events:var(--_viewport-pointer-events)}:host.ng-scroll-viewport>.ng-scroll-content,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport>.ng-scroll-content{width:var(--_scrollbar-content-width);z-index:1;min-width:100%;min-height:100%;contain:content;padding:var(--_viewport-padding-top, 0) var(--_viewport-padding-right, 0) var(--_viewport-padding-bottom, 0) var(--_viewport-padding-left, 0)}:host[appearance=native]{--_spacer-width: calc(var(--spacer-width) + var(--_scrollbar-thickness));--_spacer-height: calc(var(--spacer-height) + var(--_scrollbar-thickness))}:host.ng-scroll-viewport>.ng-scroll-spacer,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport>.ng-scroll-spacer{position:relative;width:calc(var(--_spacer-width) * 1px);height:calc(var(--_spacer-height) * 1px)}:host.ng-scroll-viewport,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport{scrollbar-width:none!important}:host.ng-scroll-viewport::-webkit-scrollbar,:host.ng-scrollbar-external-viewport ::ng-deep .ng-scroll-viewport::-webkit-scrollbar{display:none!important}:host[position=invertX],:host[position=invertAll]{--_horizontal-top: var(--_scrollbar-offset-px);--_horizontal-bottom: initial;--_scrollbar-wrapper-x-top: 0}:host[dir=ltr]{--_scrollbar-wrapper-y-right: initial;--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-left: calc(var(--viewport-width) - var(--_scrollbar-thickness))}:host[dir=ltr][position=invertY],:host[dir=ltr][position=invertAll]{--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-left: 0}:host[dir=rtl]{--_scrollbar-wrapper-y-left: initial;--_vertical-left: var(--_scrollbar-offset-px);--_vertical-right: initial;--_scrollbar-wrapper-y-right: calc(var(--viewport-width) - var(--_scrollbar-thickness))}:host[dir=rtl][position=invertY],:host[dir=rtl][position=invertAll]{--_vertical-right: var(--_scrollbar-offset-px);--_vertical-left: initial;--_scrollbar-wrapper-y-right: 0}:host[verticalUsed=true][horizontalUsed=true]{--_scrollbar-thickness-margin: calc(var(--INTERNAL-scrollbar-thickness) + var(--INTERNAL-scrollbar-offset) * 3);--_scrollbar-thickness-margin-px: calc(var(--_scrollbar-thickness-margin) * 1px)}:host[horizontalUsed=true]{--_vertical-top: var(--_scrollbar-offset-px);--_vertical-bottom: var(--_scrollbar-thickness-margin-px)}:host[horizontalUsed=true][position=invertX],:host[horizontalUsed=true][position=invertAll]{--_vertical-top: var(--_scrollbar-thickness-margin-px);--_vertical-bottom: var(--_scrollbar-offset-px)}:host[verticalUsed=true][dir=ltr]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}:host[verticalUsed=true][dir=rtl]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}:host[verticalUsed=true][position=invertY][dir=ltr],:host[verticalUsed=true][position=invertAll][dir=ltr]{--_horizontal-left: var(--_scrollbar-thickness-margin-px);--_horizontal-right: var(--_scrollbar-offset-px)}:host[verticalUsed=true][position=invertY][dir=rtl],:host[verticalUsed=true][position=invertAll][dir=rtl]{--_horizontal-left: var(--_scrollbar-offset-px);--_horizontal-right: var(--_scrollbar-thickness-margin-px)}:host[appearance=native][verticalUsed=true][dir=ltr]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][verticalUsed=true][dir=rtl]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}:host[appearance=native][verticalUsed=true][position=invertY][dir=ltr],:host[appearance=native][verticalUsed=true][position=invertAll][dir=ltr]{--_viewport-padding-left: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-right: 0}:host[appearance=native][verticalUsed=true][position=invertY][dir=rtl],:host[appearance=native][verticalUsed=true][position=invertAll][dir=rtl]{--_viewport-padding-left: 0;--_viewport-padding-right: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][horizontalUsed=true]{--_viewport-padding-top: 0;--_viewport-padding-bottom: calc(var(--_scrollbar-thickness) * 1px)}:host[appearance=native][horizontalUsed=true][position=invertX],:host[appearance=native][horizontalUsed=true][position=invertAll]{--_viewport-padding-top: calc(var(--_scrollbar-thickness) * 1px);--_viewport-padding-bottom: 0}:host[visibility=hover]{--_scrollbar-hover-opacity: 0;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-leave-duration) var(--INTERNAL-scrollbar-hover-opacity-transition-leave-delay)}:host[visibility=hover]:hover,:host[visibility=hover]:active,:host[visibility=hover]:focus{--_scrollbar-hover-opacity: 1;--_scrollbar-opacity-transition: opacity var(--INTERNAL-scrollbar-hover-opacity-transition-enter-duration)}:host[dir=ltr] ::ng-deep .scroll-reached-trigger-element[trigger=start],:host[dir=ltr] ::ng-deep .scroll-dropped-trigger-element[trigger=start]{left:0;right:unset}:host[dir=ltr] ::ng-deep .scroll-reached-trigger-element[trigger=end],:host[dir=ltr] ::ng-deep .scroll-dropped-trigger-element[trigger=end]{right:0;left:unset}:host[dir=rtl] ::ng-deep .scroll-reached-trigger-element[trigger=start],:host[dir=rtl] ::ng-deep .scroll-dropped-trigger-element[trigger=start]{right:0;left:unset}:host[dir=rtl] ::ng-deep .scroll-reached-trigger-element[trigger=end],:host[dir=rtl] ::ng-deep .scroll-dropped-trigger-element[trigger=end]{left:0;right:unset}:host ::ng-deep .ng-scroll-reached-wrapper,:host ::ng-deep .ng-scroll-dropped-wrapper,:host ::ng-deep .scroll-reached-trigger-element,:host ::ng-deep .scroll-dropped-trigger-element{position:absolute;-webkit-user-select:none;user-select:none;pointer-events:none;z-index:-9999}:host ::ng-deep .ng-scroll-reached-wrapper,:host ::ng-deep .ng-scroll-dropped-wrapper{visibility:hidden;inset:0;min-width:calc(var(--viewport-width) * 1px);min-height:calc(var(--viewport-height) * 1px)}:host ::ng-deep [isHorizontallyScrollable=false] .scroll-reached-trigger-element[trigger=end],:host ::ng-deep [isHorizontallyScrollable=false] .scroll-dropped-trigger-element[trigger=end]{display:none}:host ::ng-deep [isVerticallyScrollable=false] .scroll-reached-trigger-element[trigger=bottom],:host ::ng-deep [isVerticallyScrollable=false] .scroll-dropped-trigger-element[trigger=bottom]{display:none}:host ::ng-deep .scroll-reached-trigger-element{background:red}:host ::ng-deep .scroll-reached-trigger-element[trigger=top],:host ::ng-deep .scroll-reached-trigger-element[trigger=bottom]{left:0;right:0}:host ::ng-deep .scroll-reached-trigger-element[trigger=start],:host ::ng-deep .scroll-reached-trigger-element[trigger=end]{top:0;bottom:0}:host ::ng-deep .scroll-reached-trigger-element[trigger=top]{top:0;height:var(--reached-offset-top)}:host ::ng-deep .scroll-reached-trigger-element[trigger=bottom]{bottom:0;height:var(--reached-offset-bottom)}:host ::ng-deep .scroll-reached-trigger-element[trigger=start]{width:var(--reached-offset-start)}:host ::ng-deep .scroll-reached-trigger-element[trigger=end]{width:var(--reached-offset-end)}:host .scroll-dropped-trigger-element{background:#00f}:host .scroll-dropped-trigger-element[trigger=top],:host .scroll-dropped-trigger-element[trigger=bottom]{left:0;right:0}:host .scroll-dropped-trigger-element[trigger=start],:host .scroll-dropped-trigger-element[trigger=end]{top:0;bottom:0}:host .scroll-dropped-trigger-element[trigger=top]{top:0;height:var(--dropped-offset-top)}:host .scroll-dropped-trigger-element[trigger=bottom]{bottom:0;height:var(--dropped-offset-bottom)}:host .scroll-dropped-trigger-element[trigger=start]{width:var(--dropped-offset-start)}:host .scroll-dropped-trigger-element[trigger=end]{width:var(--dropped-offset-end)}:host[verticalUsed=true]{--_timeline-scope: --scrollerY;--_animation-timeline-y: --scrollerY;--_viewport_scroll-timeline: --scrollerY y}:host[horizontalUsed=true]{--_timeline-scope: --scrollerX;--_animation-timeline-x: --scrollerX;--_viewport_scroll-timeline: --scrollerX x}:host[verticalUsed=true][horizontalUsed=true]{--_timeline-scope: --scrollerX, --scrollerY;--_viewport_scroll-timeline: --scrollerX x, --scrollerY y}:host[orientation=vertical]{--_viewport-overflow: hidden auto;--_scrollbar-content-width: unset}:host[orientation=horizontal]{--_viewport-overflow: auto hidden}:host[disableInteraction=true]{--_viewport-pointer-events: none;--_scrollbar-pointer-events: none}:host[isVerticallyScrollable=false]{--_vertical-thumb-display: none}:host[isHorizontallyScrollable=false]{--_horizontal-thumb-display: none}:host[dragging=x],:host[dragging=y]{--_viewport-user-select: none}:host[dragging=x]{--_track-x-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-x-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}:host[dragging=y]{--_track-y-thickness: calc(var(--INTERNAL-scrollbar-hover-thickness) * 1px);--_thumb-y-color: var(var(--INTERNAL-scrollbar-thumb-min-size))}:host[mobile=true]{--_viewport-overscroll-behavior: var(--INTERNAL-scrollbar-mobile-overscroll-behavior)}\\n\"] }]\n        }], ctorParameters: () => [] });\n\nclass AsyncDetection {\n    constructor() {\n        this.scrollbar = inject(NgScrollbarExt, { self: true });\n        this.zone = inject(NgZone);\n        this.contentObserver = inject(ContentObserver);\n        this.asyncDetection = input();\n        this.scrollbar.skipInit = true;\n        let sub$;\n        effect((onCleanup) => {\n            const init = this.scrollbar.viewport.initialized();\n            const externalViewport = this.scrollbar.externalViewport();\n            const externalContentWrapper = this.scrollbar.externalContentWrapper();\n            const externalSpacer = this.scrollbar.externalSpacer();\n            const asyncDetection = this.asyncDetection();\n            untracked(() => {\n                let viewportElement;\n                let contentWrapperElement;\n                this.zone.runOutsideAngular(() => {\n                    // The content observer should not be throttled using the same function we use for ResizeObserver,\n                    // It should detect the content change asap to attach the scrollbar\n                    sub$ = this.contentObserver.observe(this.scrollbar.nativeElement).pipe(throttleTime(100, null, {\n                        leading: true,\n                        trailing: true\n                    })).subscribe(() => {\n                        // Search for external viewport\n                        viewportElement = this.scrollbar.nativeElement.querySelector(externalViewport);\n                        // Search for external content wrapper\n                        contentWrapperElement = this.scrollbar.nativeElement.querySelector(externalContentWrapper);\n                        this.zone.run(() => {\n                            if (!init && viewportElement && contentWrapperElement) {\n                                // If an external spacer selector is provided, search for it\n                                let spacerElement;\n                                if (externalSpacer) {\n                                    spacerElement = this.scrollbar.nativeElement.querySelector(externalSpacer);\n                                }\n                                this.scrollbar.initialize(viewportElement, contentWrapperElement, spacerElement);\n                            }\n                            else if (!viewportElement || !contentWrapperElement) {\n                                this.scrollbar.viewport.reset();\n                            }\n                        });\n                        if (asyncDetection !== 'auto') {\n                            sub$.unsubscribe();\n                        }\n                    });\n                });\n                onCleanup(() => sub$?.unsubscribe());\n            });\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: AsyncDetection, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"17.1.0\", version: \"19.1.1\", type: AsyncDetection, isStandalone: true, selector: \"ng-scrollbar[externalViewport][asyncDetection]\", inputs: { asyncDetection: { classPropertyName: \"asyncDetection\", publicName: \"asyncDetection\", isSignal: true, isRequired: false, transformFunction: null } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: AsyncDetection, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-scrollbar[externalViewport][asyncDetection]'\n                }]\n        }], ctorParameters: () => [] });\n\nclass SyncSpacer {\n    constructor() {\n        this.sharedResizeObserver = inject(SharedResizeObserver);\n        this.scrollbar = inject(NgScrollbarExt, { self: true });\n        this.zone = inject(NgZone);\n        /**\n         * A signal used to sync spacer dimension when content dimension changes\n         */\n        this.spacerDimension = signal({});\n        let sub$;\n        effect((onCleanup) => {\n            const spacerElement = this.scrollbar.spacerElement();\n            const contentWrapperElement = this.scrollbar.contentWrapperElement();\n            const throttleDuration = this.scrollbar.sensorThrottleTime();\n            const disableSensor = this.scrollbar.disableSensor();\n            untracked(() => {\n                if (!disableSensor && contentWrapperElement && spacerElement) {\n                    // Sync spacer dimension with content wrapper dimensions to allow both scrollbars to be displayed\n                    this.zone.runOutsideAngular(() => {\n                        sub$ = getThrottledStream(this.sharedResizeObserver.observe(contentWrapperElement), throttleDuration).pipe(map((entries) => filterResizeEntries(entries, contentWrapperElement))).subscribe(() => {\n                            this.zone.run(() => {\n                                // Use animation frame to avoid \"ResizeObserver loop completed with undelivered notifications.\" error\n                                requestAnimationFrame(() => {\n                                    this.spacerDimension.set({\n                                        width: contentWrapperElement.offsetWidth,\n                                        height: contentWrapperElement.offsetHeight\n                                    });\n                                });\n                            });\n                        });\n                    });\n                }\n                onCleanup(() => sub$?.unsubscribe());\n            });\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: SyncSpacer, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.1.1\", type: SyncSpacer, isStandalone: true, selector: \"ng-scrollbar[externalViewport][syncSpacer]\", host: { properties: { \"style.--spacer-width\": \"spacerDimension().width\", \"style.--spacer-height\": \"spacerDimension().height\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: SyncSpacer, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-scrollbar[externalViewport][syncSpacer]',\n                    host: {\n                        '[style.--spacer-width]': 'spacerDimension().width',\n                        '[style.--spacer-height]': 'spacerDimension().height'\n                    }\n                }]\n        }], ctorParameters: () => [] });\n\nclass NgScrollbarModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: NgScrollbarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.1.1\", ngImport: i0, type: NgScrollbarModule, imports: [NgScrollbar,\n            ScrollViewport,\n            NgScrollbarExt,\n            AsyncDetection,\n            SyncSpacer], exports: [NgScrollbar,\n            ScrollViewport,\n            NgScrollbarExt,\n            AsyncDetection,\n            SyncSpacer] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: NgScrollbarModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: NgScrollbarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        NgScrollbar,\n                        ScrollViewport,\n                        NgScrollbarExt,\n                        AsyncDetection,\n                        SyncSpacer\n                    ],\n                    exports: [\n                        NgScrollbar,\n                        ScrollViewport,\n                        NgScrollbarExt,\n                        AsyncDetection,\n                        SyncSpacer\n                    ]\n                }]\n        }] });\nfunction provideScrollbarOptions(options) {\n    return [\n        {\n            provide: NG_SCROLLBAR_OPTIONS,\n            useValue: { ...defaultOptions, ...options }\n        }\n    ];\n}\nfunction provideScrollbarPolyfill(url) {\n    return makeEnvironmentProviders([\n        {\n            provide: NG_SCROLLBAR_POLYFILL,\n            useValue: url\n        }\n    ]);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AsyncDetection, NG_SCROLLBAR, NG_SCROLLBAR_OPTIONS, NG_SCROLLBAR_POLYFILL, NgScrollbar, NgScrollbarExt, NgScrollbarModule, ScrollViewport, ScrollbarUpdateReason, SyncSpacer, filterResizeEntries, provideScrollbarOptions, provideScrollbarPolyfill };\n"], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,cAAc,EAAEC,YAAY,EAAEC,YAAY,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,wBAAwB,QAAQ,eAAe;AAC7X,SAASC,GAAG,EAAEC,YAAY,EAAEC,aAAa,EAAEC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,IAAI,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,uBAAuB,QAAQ,MAAM;AACrL,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,QAAQ,EAAEC,iBAAiB,QAAQ,iBAAiB;AAC7D,SAASC,eAAe,QAAQ,wBAAwB;AAAC,MAAAC,GAAA;AAAA,SAAAC,kCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAM2CtD,EAAE,CAAAwD,SAAA,eAm2BjD,CAAC,eAGF,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAt2B+CzD,EAAE,CAAA0D,aAAA;IAAF1D,EAAE,CAAA2D,sBAAA,yBAAAF,MAAA,CAAAG,GAAA,CAAAC,WAAA,EAi2BlC,CAAC;IAj2B+B7D,EAAE,CAAA8D,SAAA,CAo2BlC,CAAC;IAp2B+B9D,EAAE,CAAA2D,sBAAA,yBAAAF,MAAA,CAAAG,GAAA,CAAAC,WAAA,EAo2BlC,CAAC;EAAA;AAAA;AAAA,SAAAE,kCAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAp2B+BtD,EAAE,CAAAwD,SAAA,eAo7BjD,CAAC,eAGF,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAv7B+CzD,EAAE,CAAA0D,aAAA;IAAF1D,EAAE,CAAA2D,sBAAA,yBAAAF,MAAA,CAAAG,GAAA,CAAAC,WAAA,EAk7BlC,CAAC;IAl7B+B7D,EAAE,CAAA8D,SAAA,CAq7BlC,CAAC;IAr7B+B9D,EAAE,CAAA2D,sBAAA,yBAAAF,MAAA,CAAAG,GAAA,CAAAC,WAAA,EAq7BlC,CAAC;EAAA;AAAA;AAAA,MAAAG,GAAA;AAAA,SAAAC,kCAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAr7B+BtD,EAAE,CAAAwD,SAAA,iBAg+BnF,CAAC;EAAA;AAAA;AAAA,SAAAU,kCAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAh+BgFtD,EAAE,CAAAwD,SAAA,iBAm+BnF,CAAC;EAAA;AAAA;AAAA,MAAAW,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAv+BpB,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,aAAa,GAAGxE,MAAM,CAAEC,UAAW,CAAC,CAACuE,aAAa;EAC3D;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,uBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFL,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACM,IAAI,kBAD8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EACJR,cAAc;MAAAS,SAAA;IAAA,EAAmE;EAAE;AACrL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGjF,EAAE,CAAAkF,iBAAA,CAGXX,cAAc,EAAc,CAAC;IAC5GQ,IAAI,EAAE5E,SAAS;IACfgF,IAAI,EAAE,CAAC;MACCC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,OAAO3D,GAAG,CAAC,MAAM2D,GAAG,CAACC,aAAa,GAAG,MAAM,KAAK,CAAC;AACrD;AACA,SAASC,eAAeA,CAACF,GAAG,EAAE;EAC1B,OAAO3D,GAAG,CAAC,MAAM2D,GAAG,CAACC,aAAa,GAAG,IAAI,CAAC;AAC9C;AACA,SAASE,eAAeA,CAAA,EAAG;EACvB,OAAO9D,GAAG,CAAE+D,CAAC,IAAK;IACd;IACA;IACAA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACD,eAAe,CAAC,CAAC;EACvB,CAAC,CAAC;AACN;AACA,SAASG,kBAAkBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC1C,OAAOD,MAAM,CAACE,IAAI,CAACnE,YAAY,CAACkE,QAAQ,IAAI,CAAC,EAAE,IAAI,EAAE;IACjDE,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE;EACd,CAAC,CAAC,CAAC;AACP;AACA,IAAIC,eAAe;AACnB,CAAC,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAAC,UAAU,CAAC,GAAG,oBAAoB;EAClDA,eAAe,CAAC,SAAS,CAAC,GAAG,mBAAmB;EAChDA,eAAe,CAAC,QAAQ,CAAC,GAAG,kBAAkB;AAClD,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;;AAE7C;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClB5B,WAAWA,CAAA,EAAG;IACV;AACR;AACA;IACQ,IAAI,CAAC6B,WAAW,GAAGjG,MAAM,CAAC,KAAK,CAAC;EACpC;EACA;EACA,IAAIkG,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC7B,aAAa,CAAC6B,YAAY;EAC1C;EACA;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC9B,aAAa,CAAC8B,WAAW;EACzC;EACA;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC/B,aAAa,CAAC+B,SAAS;EACvC;EACA;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAChC,aAAa,CAACgC,UAAU;EACxC;EACA;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,qBAAqB,CAACL,YAAY;EAClD;EACA;EACA,IAAIM,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACD,qBAAqB,CAACJ,WAAW;EACjD;EACA;EACA,IAAIM,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACD,YAAY,GAAG,IAAI,CAACL,WAAW;EAC/C;EACA;EACA,IAAIO,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,aAAa,GAAG,IAAI,CAACJ,YAAY;EACjD;EACA;AACJ;AACA;EACIS,IAAIA,CAACC,eAAe,EAAEC,cAAc,EAAEC,aAAa,EAAE;IACjD;IACAF,eAAe,CAACG,SAAS,CAACC,GAAG,CAACjB,eAAe,CAACkB,QAAQ,CAAC;IACvD,IAAI,CAAC5C,aAAa,GAAGuC,eAAe;IACpC;IACAC,cAAc,CAACE,SAAS,CAACC,GAAG,CAACjB,eAAe,CAACmB,OAAO,CAAC;IACrD;IACA;IACA;IACA,IAAIJ,aAAa,EAAE;MACfA,aAAa,CAACC,SAAS,CAACC,GAAG,CAACjB,eAAe,CAACoB,MAAM,CAAC;MACnD,IAAI,CAACZ,qBAAqB,GAAGO,aAAa;IAC9C,CAAC,MACI;MACD;MACA,IAAI,CAACP,qBAAqB,GAAGM,cAAc;IAC/C;IACA,IAAI,CAACZ,WAAW,CAACmB,GAAG,CAAC,IAAI,CAAC;EAC9B;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAChD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACkC,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACN,WAAW,CAACmB,GAAG,CAAC,KAAK,CAAC;EAC/B;EACA;AACJ;AACA;EACIE,SAASA,CAACC,KAAK,EAAE;IACb,IAAI,CAAClD,aAAa,CAAC+B,SAAS,GAAGmB,KAAK;EACxC;EACA;AACJ;AACA;EACIC,SAASA,CAACD,KAAK,EAAE;IACb,IAAI,CAAClD,aAAa,CAACgC,UAAU,GAAGkB,KAAK;EACzC;EACA;IAAS,IAAI,CAACjD,IAAI,YAAAmD,wBAAAjD,iBAAA;MAAA,YAAAA,iBAAA,IAAwFwB,eAAe;IAAA,CAAoD;EAAE;EAC/K;IAAS,IAAI,CAAC0B,KAAK,kBAzH6E9H,EAAE,CAAA+H,kBAAA;MAAAC,KAAA,EAyHY5B,eAAe;MAAA6B,OAAA,EAAf7B,eAAe,CAAA1B;IAAA,EAAG;EAAE;AACtI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KA3HoGjF,EAAE,CAAAkF,iBAAA,CA2HXkB,eAAe,EAAc,CAAC;IAC7GrB,IAAI,EAAE1E;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAM6H,YAAY,GAAG,IAAI5H,cAAc,CAAC,cAAc,CAAC;AAEvD,MAAM6H,cAAc,GAAG;EACnBC,UAAU,EAAE,EAAE;EACdC,UAAU,EAAE,EAAE;EACdxE,WAAW,EAAE,EAAE;EACfyE,WAAW,EAAE,MAAM;EACnBC,UAAU,EAAE,QAAQ;EACpBC,UAAU,EAAE,QAAQ;EACpBC,QAAQ,EAAE,QAAQ;EAClBC,mBAAmB,EAAE,EAAE;EACvBC,kBAAkB,EAAE,CAAC;EACrBC,aAAa,EAAE,KAAK;EACpBC,kBAAkB,EAAE,KAAK;EACzBC,OAAO,EAAE,KAAK;EACdC,WAAW,EAAE;AACjB,CAAC;AACD;AACA,MAAMC,6BAA6B,GAAG,mJAAmJ;AAEzL,IAAIC,qBAAqB;AACzB,CAAC,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAAC,WAAW,CAAC,GAAG,WAAW;EAChDA,qBAAqB,CAAC,SAAS,CAAC,GAAG,gBAAgB;AACvD,CAAC,EAAEA,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD,MAAMC,oBAAoB,GAAG,IAAI5I,cAAc,CAAC,sBAAsB,EAAE;EACpE6I,UAAU,EAAE,MAAM;EAClBlB,OAAO,EAAEA,CAAA,KAAME;AACnB,CAAC,CAAC;AACF,MAAMiB,qBAAqB,GAAG,IAAI9I,cAAc,CAAC,uBAAuB,EAAE;EACtE6I,UAAU,EAAE,MAAM;EAClBlB,OAAO,EAAEA,CAAA,KAAMe;AACnB,CAAC,CAAC;AACF,SAASK,mBAAmBA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAC1C,OAAOD,OAAO,CAACE,MAAM,CAAEC,KAAK,IAAKA,KAAK,CAACF,MAAM,KAAKA,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEG,WAAW;AAC7E;AAEA,MAAMC,eAAe,CAAC;EAClBnF,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACoF,OAAO,GAAG3J,MAAM,CAACiJ,oBAAoB,CAAC;IAC3C,IAAI,CAACW,oBAAoB,GAAG5J,MAAM,CAAC6C,oBAAoB,CAAC;IACxD,IAAI,CAACgH,IAAI,GAAG7J,MAAM,CAACM,MAAM,CAAC;IAC1B,IAAI,CAACwJ,QAAQ,GAAG9J,MAAM,CAAC2C,QAAQ,CAAC;IAChC;IACA,IAAI,CAACoH,QAAQ,GAAG,IAAI,CAACD,QAAQ,CAACE,GAAG,IAAI,IAAI,CAACF,QAAQ,CAACG,OAAO;IAC1D,IAAI,CAACC,GAAG,GAAGlK,MAAM,CAAC4C,cAAc,CAAC;IACjC,IAAI,CAACuH,YAAY,GAAGnK,MAAM,CAAC+C,mBAAmB,CAAC;IAC/C;IACA,IAAI,CAACqH,QAAQ,GAAGpK,MAAM,CAACmG,eAAe,EAAE;MAAEkE,IAAI,EAAE;IAAK,CAAC,CAAC;IACvD,IAAI,CAAC7F,aAAa,GAAGxE,MAAM,CAAEC,UAAW,CAAC,CAACuE,aAAa;IACvD;AACR;AACA;IACQ,IAAI,CAAC8F,SAAS,GAAGxH,QAAQ,CAAC,IAAI,CAACoH,GAAG,CAACK,MAAM,EAAE;MAAEC,YAAY,EAAE,IAAI,CAACN,GAAG,CAACxC;IAAM,CAAC,CAAC;IAC5E;AACR;AACA;IACQ,IAAI,CAAC+C,QAAQ,GAAGtK,MAAM,CAAC,MAAM,CAAC;IAC9B;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkI,WAAW,GAAG9H,KAAK,CAAC,IAAI,CAACoJ,OAAO,CAACtB,WAAW,CAAC;IAClD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACE,UAAU,GAAGhI,KAAK,CAAC,IAAI,CAACoJ,OAAO,CAACpB,UAAU,CAAC;IAChD;IACA,IAAI,CAACM,OAAO,GAAGtI,KAAK,CAAC,IAAI,CAACoJ,OAAO,CAACd,OAAO,EAAE;MACvC6B,SAAS,EAAElK;IACf,CAAC,CAAC;IACF;IACA,IAAI,CAACoI,kBAAkB,GAAGrI,KAAK,CAAC,IAAI,CAACoJ,OAAO,CAACf,kBAAkB,EAAE;MAC7D8B,SAAS,EAAElK;IACf,CAAC,CAAC;IACF;IACA,IAAI,CAACmI,aAAa,GAAGpI,KAAK,CAAC,IAAI,CAACoJ,OAAO,CAAChB,aAAa,EAAE;MACnD+B,SAAS,EAAElK;IACf,CAAC,CAAC;IACF;IACA,IAAI,CAACkI,kBAAkB,GAAGnI,KAAK,CAAC,IAAI,CAACoJ,OAAO,CAACjB,kBAAkB,EAAE;MAC7DgC,SAAS,EAAEjK;IACf,CAAC,CAAC;IACF;IACA,IAAI,CAACqI,WAAW,GAAGvI,KAAK,CAAC,IAAI,CAACoJ,OAAO,CAACb,WAAW,EAAE;MAC/C4B,SAAS,EAAElK;IACf,CAAC,CAAC;IACF;IACA,IAAI,CAACmK,iBAAiB,GAAGxK,MAAM,CAAC;MAAEyK,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC,CAAC;IACxD;IACA,IAAI,CAACC,gBAAgB,GAAG3K,MAAM,CAAC;MAAEyK,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC,CAAC;IACvD,IAAI,CAACE,KAAK,GAAGrK,QAAQ,CAAC,MAAM;MACxB,IAAIsK,YAAY,GAAG,KAAK;MACxB,IAAIC,cAAc,GAAG,KAAK;MAC1B,IAAIC,sBAAsB,GAAG,KAAK;MAClC,IAAIC,wBAAwB,GAAG,KAAK;MACpC,MAAM9C,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC,CAAC;MACtC,MAAME,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC;MACpC,MAAM6C,kBAAkB,GAAG,IAAI,CAACT,iBAAiB,CAAC,CAAC;MACnD,MAAMU,iBAAiB,GAAG,IAAI,CAACP,gBAAgB,CAAC,CAAC;MACjD;MACA,IAAIzC,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,UAAU,EAAE;QACtD6C,sBAAsB,GAAGG,iBAAiB,CAACR,MAAM,GAAGO,kBAAkB,CAACP,MAAM;QAC7EG,YAAY,GAAGzC,UAAU,KAAK,SAAS,IAAI2C,sBAAsB;MACrE;MACA;MACA,IAAI7C,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,YAAY,EAAE;QACxD8C,wBAAwB,GAAGE,iBAAiB,CAACT,KAAK,GAAGQ,kBAAkB,CAACR,KAAK;QAC7EK,cAAc,GAAG1C,UAAU,KAAK,SAAS,IAAI4C,wBAAwB;MACzE;MACA,OAAO;QACHH,YAAY;QACZC,cAAc;QACdC,sBAAsB;QACtBC;MACJ,CAAC;IACL,CAAC,CAAC;IACF,IAAI,CAACD,sBAAsB,GAAGxK,QAAQ,CAAC,MAAM,IAAI,CAACqK,KAAK,CAAC,CAAC,CAACG,sBAAsB,CAAC;IACjF,IAAI,CAACC,wBAAwB,GAAGzK,QAAQ,CAAC,MAAM,IAAI,CAACqK,KAAK,CAAC,CAAC,CAACI,wBAAwB,CAAC;IACrF,IAAI,CAACH,YAAY,GAAGtK,QAAQ,CAAC,MAAM,IAAI,CAACqK,KAAK,CAAC,CAAC,CAACC,YAAY,CAAC;IAC7D,IAAI,CAACC,cAAc,GAAGvK,QAAQ,CAAC,MAAM,IAAI,CAACqK,KAAK,CAAC,CAAC,CAACE,cAAc,CAAC;IACjE;IACA,IAAI,CAACxC,mBAAmB,GAAGlI,KAAK,CAAC,IAAI,CAACoJ,OAAO,CAAClB,mBAAmB,EAAE;MAC/DiC,SAAS,EAAEjK;IACf,CAAC,CAAC;IACF;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC6H,UAAU,GAAG/H,KAAK,CAAC,IAAI,CAACoJ,OAAO,CAACrB,UAAU,CAAC;IAChD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACE,QAAQ,GAAGjI,KAAK,CAAC,IAAI,CAACoJ,OAAO,CAACnB,QAAQ,CAAC;IAC5C;IACA,IAAI,CAACL,UAAU,GAAG5H,KAAK,CAAC,IAAI,CAACoJ,OAAO,CAACxB,UAAU,CAAC;IAChD;IACA,IAAI,CAACC,UAAU,GAAG7H,KAAK,CAAC,IAAI,CAACoJ,OAAO,CAACvB,UAAU,CAAC;IAChD;IACA,IAAI,CAACxE,WAAW,GAAGrD,KAAK,CAAC,IAAI,CAACoJ,OAAO,CAACvB,UAAU,CAAC;IACjD;IACA,IAAI,CAACkD,SAAS,GAAG3K,MAAM,CAAC,CAAC;IACzB;IACA,IAAI,CAAC4K,WAAW,GAAG5K,MAAM,CAAC,CAAC;IAC3B,IAAI6K,UAAU;IACd,IAAIC,cAAc;IAClB7K,iBAAiB,CAAC;MACd8K,SAAS,EAAGC,SAAS,IAAK;QACtB,MAAMhD,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC,CAAC;QAC1C,MAAMiD,gBAAgB,GAAG,IAAI,CAAClD,kBAAkB,CAAC,CAAC;QAClD,MAAMmD,YAAY,GAAG,IAAI,CAACzB,QAAQ,CAAChE,WAAW,CAAC,CAAC;QAChDvF,SAAS,CAAC,MAAM;UACZ,IAAIgL,YAAY,EAAE;YACd;YACA,IAAIlD,aAAa,EAAE;cACfmD,qBAAqB,CAAC,MAAM,IAAI,CAACC,MAAM,CAAC/C,qBAAqB,CAACgD,SAAS,CAAC,CAAC;YAC7E,CAAC,MACI;cACD;cACA,IAAI,CAACnC,IAAI,CAACoC,iBAAiB,CAAC,MAAM;gBAC9BT,UAAU,GAAG5F,kBAAkB,CAAC/D,aAAa,CAAC,CAC1C,IAAI,CAAC+H,oBAAoB,CAACsC,OAAO,CAAC,IAAI,CAAC9B,QAAQ,CAAC5F,aAAa,CAAC,EAC9D,IAAI,CAACoF,oBAAoB,CAACsC,OAAO,CAAC,IAAI,CAAC9B,QAAQ,CAAC1D,qBAAqB,CAAC,CACzE,CAAC,EAAEkF,gBAAgB,CAAC,CAACO,SAAS,CAAC,MAAM;kBAClC;kBACA;kBACA,IAAI,CAACtC,IAAI,CAACuC,GAAG,CAAC,MAAM;oBAChB,IAAI,CAACC,gBAAgB,CAAC,CAAC;oBACvB,IAAIZ,cAAc,EAAE;sBAChB,IAAI,CAACF,WAAW,CAACe,IAAI,CAAC,CAAC;oBAC3B,CAAC,MACI;sBACD,IAAI,CAAChB,SAAS,CAACgB,IAAI,CAAC,CAAC;oBACzB;oBACAb,cAAc,GAAG,IAAI;kBACzB,CAAC,CAAC;gBACN,CAAC,CAAC;cACN,CAAC,CAAC;YACN;UACJ;UACAE,SAAS,CAAC,MAAMH,UAAU,EAAEe,WAAW,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIR,MAAMA,CAACS,MAAM,EAAE;IACX,IAAI,CAACH,gBAAgB,CAAC,CAAC;IACvB,IAAIG,MAAM,KAAKxD,qBAAqB,CAACgD,SAAS,EAAE;MAC5C,IAAI,CAACV,SAAS,CAACgB,IAAI,CAAC,CAAC;IACzB,CAAC,MACI;MACD,IAAI,CAACf,WAAW,CAACe,IAAI,CAAC,CAAC;IAC3B;EACJ;EACA;AACJ;AACA;EACIG,QAAQA,CAAC9C,OAAO,EAAE;IACd,OAAO,IAAI,CAACQ,YAAY,CAACsC,QAAQ,CAAC,IAAI,CAACrC,QAAQ,CAAC5F,aAAa,EAAEmF,OAAO,CAAC;EAC3E;EACA;AACJ;AACA;EACI+C,eAAeA,CAACpD,MAAM,EAAEK,OAAO,EAAE;IAC7B,OAAO,IAAI,CAACQ,YAAY,CAACuC,eAAe,CAAC,IAAI,CAACtC,QAAQ,CAAC5F,aAAa,EAAE8E,MAAM,EAAEK,OAAO,CAAC;EAC1F;EACA0C,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC1B,iBAAiB,CAACpD,GAAG,CAAC;MAAEqD,KAAK,EAAE,IAAI,CAACR,QAAQ,CAAC9D,WAAW;MAAEuE,MAAM,EAAE,IAAI,CAACT,QAAQ,CAAC/D;IAAa,CAAC,CAAC;IACpG,IAAI,CAACyE,gBAAgB,CAACvD,GAAG,CAAC;MAAEqD,KAAK,EAAE,IAAI,CAACR,QAAQ,CAACzD,YAAY;MAAEkE,MAAM,EAAE,IAAI,CAACT,QAAQ,CAAC3D;IAAc,CAAC,CAAC;EACzG;EACA;IAAS,IAAI,CAAChC,IAAI,YAAAkI,wBAAAhI,iBAAA;MAAA,YAAAA,iBAAA,IAAwF+E,eAAe;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAAC9E,IAAI,kBAxW8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EAwWJ4E,eAAe;MAAAkD,QAAA;MAAAC,YAAA,WAAAC,6BAAAzJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxWbtD,EAAE,CAAAgN,WAAA,iBAwWJzJ,GAAA,CAAA0H,YAAA,CAAa,CAAC,oBAAd1H,GAAA,CAAA2H,cAAA,CAAe,CAAC,4BAAhB3H,GAAA,CAAA4H,sBAAA,CAAuB,CAAC,8BAAxB5H,GAAA,CAAA6H,wBAAA,CAAyB,CAAC,YAAA7H,GAAA,CAAAyG,QAAA,SAA1BzG,GAAA,CAAAgH,SAAA,CAAU,CAAC,cAAXhH,GAAA,CAAAkF,QAAA,CAAS,CAAC,cAAVlF,GAAA,CAAAmH,QAAA,CAAS,CAAC,gBAAVnH,GAAA,CAAAgF,UAAA,CAAW,CAAC,gBAAZhF,GAAA,CAAAiF,UAAA,CAAW,CAAC,iBAAZjF,GAAA,CAAA+E,WAAA,CAAY,CAAC,wBAAb/E,GAAA,CAAAsF,kBAAA,CAAmB,CAAC;UAxWlB7I,EAAE,CAAAiN,WAAA,qBAwWJ1J,GAAA,CAAAwH,gBAAA,CAAiB,CAAC,CAAAD,MAAJ,CAAC,oBAAfvH,GAAA,CAAAwH,gBAAA,CAAiB,CAAC,CAAAF,KAAJ,CAAC,sBAAftH,GAAA,CAAAqH,iBAAA,CAAkB,CAAC,CAAAE,MAAL,CAAC,qBAAfvH,GAAA,CAAAqH,iBAAA,CAAkB,CAAC,CAAAC,KAAL,CAAC;UAxWb7K,EAAE,CAAAkN,WAAA,iBAwWJ,IAAc,CAAC;QAAA;MAAA;MAAAC,MAAA;QAAA7E,WAAA;QAAAE,UAAA;QAAAM,OAAA;QAAAD,kBAAA;QAAAD,aAAA;QAAAD,kBAAA;QAAAI,WAAA;QAAAL,mBAAA;QAAAH,UAAA;QAAAE,QAAA;QAAAL,UAAA;QAAAC,UAAA;QAAAxE,WAAA;MAAA;MAAAuJ,OAAA;QAAA7B,SAAA;QAAAC,WAAA;MAAA;MAAA6B,QAAA,GAxWbrN,EAAE,CAAAsN,kBAAA,CAwWopF,CAC9uF;QAAEC,OAAO,EAAErF,YAAY;QAAEsF,WAAW,EAAE7D;MAAgB,CAAC,CAC1D;IAAA,EAAiB;EAAE;AAC5B;AACA;EAAA,QAAA1E,SAAA,oBAAAA,SAAA,KA5WoGjF,EAAE,CAAAkF,iBAAA,CA4WXyE,eAAe,EAAc,CAAC;IAC7G5E,IAAI,EAAE5E,SAAS;IACfgF,IAAI,EAAE,CAAC;MACCsI,IAAI,EAAE;QACF,sBAAsB,EAAE,MAAM;QAC9B,qBAAqB,EAAE,gBAAgB;QACvC,uBAAuB,EAAE,kBAAkB;QAC3C,+BAA+B,EAAE,0BAA0B;QAC3D,iCAAiC,EAAE,4BAA4B;QAC/D,eAAe,EAAE,UAAU;QAC3B,YAAY,EAAE,aAAa;QAC3B,iBAAiB,EAAE,YAAY;QAC/B,iBAAiB,EAAE,YAAY;QAC/B,mBAAmB,EAAE,cAAc;QACnC,mBAAmB,EAAE,cAAc;QACnC,oBAAoB,EAAE,eAAe;QACrC,2BAA2B,EAAE,sBAAsB;QACnD,0BAA0B,EAAE,2BAA2B;QACvD,yBAAyB,EAAE,0BAA0B;QACrD,2BAA2B,EAAE,4BAA4B;QACzD,0BAA0B,EAAE;MAChC,CAAC;MACDC,SAAS,EAAE,CACP;QAAEH,OAAO,EAAErF,YAAY;QAAEsF,WAAW,EAAE7D;MAAgB,CAAC;IAE/D,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMgE,iBAAiB,GAAG,IAAIrN,cAAc,CAAC,mBAAmB,CAAC;AACjE,MAAMsN,gBAAgB,CAAC;EACnBpJ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqJ,SAAS,GAAGzN,MAAM,CAAC,CAAC,CAAC;IAC1B;IACA,IAAI,CAACwD,GAAG,GAAG3D,MAAM,CAACiI,YAAY,CAAC;EACnC;EACA;IAAS,IAAI,CAACxD,IAAI,YAAAoJ,yBAAAlJ,iBAAA;MAAA,YAAAA,iBAAA,IAAwFgJ,gBAAgB;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAAC/I,IAAI,kBAhZ8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EAgZJ6I;IAAgB,EAAqC;EAAE;AACzJ;AACA;EAAA,QAAA3I,SAAA,oBAAAA,SAAA,KAlZoGjF,EAAE,CAAAkF,iBAAA,CAkZX0I,gBAAgB,EAAc,CAAC;IAC9G7I,IAAI,EAAE5E;EACV,CAAC,CAAC;AAAA;AAEV,MAAM4N,oBAAoB,CAAC;EACvBvJ,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACZ,GAAG,GAAG3D,MAAM,CAACiI,YAAY,CAAC;IAC/B;IACA,IAAI,CAAC8F,OAAO,GAAG/N,MAAM,CAAC0N,iBAAiB,CAAC;IACxC;IACA,IAAI,CAACM,QAAQ,GAAGhO,MAAM,CAACgD,QAAQ,CAAC;IAChC;IACA,IAAI,CAAC6G,IAAI,GAAG7J,MAAM,CAACM,MAAM,CAAC;IAC1B;IACA,IAAI,CAACkE,aAAa,GAAGxE,MAAM,CAAEC,UAAW,CAAC,CAACuE,aAAa;IACvD1D,MAAM,CAAE6K,SAAS,IAAK;MAClB,MAAM/C,kBAAkB,GAAG,IAAI,CAACjF,GAAG,CAACiF,kBAAkB,CAAC,CAAC;MACxD/H,SAAS,CAAC,MAAM;QACZ,IAAI,CAAC+H,kBAAkB,EAAE;UACrB,IAAI,CAACiB,IAAI,CAACoC,iBAAiB,CAAC,MAAM;YAC9B,IAAI,CAACgC,iBAAiB,GAAG,IAAI,CAACC,aAAa,CAAC/B,SAAS,CAAC,CAAC;UAC3D,CAAC,CAAC;QACN;QACAR,SAAS,CAAC,MAAM,IAAI,CAACsC,iBAAiB,EAAE1B,WAAW,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC9H,IAAI,YAAA0J,6BAAAxJ,iBAAA;MAAA,YAAAA,iBAAA,IAAwFmJ,oBAAoB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAAClJ,IAAI,kBA/a8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EA+aJgJ;IAAoB,EAAqC;EAAE;AAC7J;AACA;EAAA,QAAA9I,SAAA,oBAAAA,SAAA,KAjboGjF,EAAE,CAAAkF,iBAAA,CAibX6I,oBAAoB,EAAc,CAAC;IAClHhJ,IAAI,EAAE5E;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMkO,YAAY,SAASN,oBAAoB,CAAC;EAC5C;EACA,IAAIO,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC1K,GAAG,CAACyG,QAAQ,CAAC,IAAI,CAAC2D,OAAO,CAACO,YAAY,CAAC;EACvD;EACA;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC/J,aAAa,CAACgK,qBAAqB,CAAC,CAAC;EACrD;EACA;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,UAAU,CAAC,IAAI,CAACR,OAAO,CAACW,kBAAkB,CAAC;EAC3D;EACA;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP;IACA;IACA,OAAO,IAAI,CAACnK,aAAa,CAAC,IAAI,CAACuJ,OAAO,CAACO,YAAY,CAAC;EACxD;EACA;EACA,IAAIJ,aAAaA,CAAA,EAAG;IAChB;IACA,MAAMU,YAAY,GAAG9M,SAAS,CAAC,IAAI,CAAC0C,aAAa,EAAE,aAAa,CAAC,CAACuB,IAAI,CAACN,eAAe,CAAC,CAAC,EAAEJ,gBAAgB,CAAC,IAAI,CAAC2I,QAAQ,CAAC,CAAC;IAC1H,MAAMa,UAAU,GAAG/M,SAAS,CAAC,IAAI,CAACkM,QAAQ,EAAE,WAAW,EAAE;MAAEc,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC/I,IAAI,CAACP,eAAe,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC;IAChH,MAAMe,aAAa,GAAGjN,SAAS,CAAC,IAAI,CAAC0C,aAAa,EAAE,aAAa,EAAE;MAAEsK,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC/I,IAAI;IAC1F;IACApE,GAAG,CAAE+D,CAAC,IAAK,IAAI,CAACsJ,iBAAiB,GAAGtJ,CAAC,CAAC,IAAI,CAACqI,OAAO,CAACkB,cAAc,CAAC,CAAC,EAAElN,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;IACrF,MAAMmN,aAAa,GAAGpN,SAAS,CAAC,IAAI,CAAC0C,aAAa,EAAE,YAAY,EAAE;MAAEsK,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC/I,IAAI,CAAChE,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;IAC3G,MAAMoN,YAAY,GAAGnN,KAAK,CAAC+M,aAAa,EAAEG,aAAa,CAAC,CAACnJ,IAAI,CAAC9D,SAAS,CAAC,IAAI,CAAC,CAAC;IAC9E;IACA,MAAMmN,YAAY,GAAGtN,SAAS,CAAC,IAAI,CAAC0C,aAAa,EAAE,aAAa,EAAE;MAAEsK,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC/I,IAAI,CAACpE,GAAG,CAAE+D,CAAC,IAAK,IAAI,CAACsJ,iBAAiB,GAAGtJ,CAAC,CAAC,IAAI,CAACqI,OAAO,CAACkB,cAAc,CAAC,CAAC,CAAC;IAC9J,OAAOL,YAAY,CAAC7I,IAAI,CAAC7D,SAAS,CAAEmN,UAAU,IAAK;MAC/C;MACAD,YAAY,CAACrJ,IAAI,CAAC5D,SAAS,CAAC0M,UAAU,CAAC,CAAC,CAAC1C,SAAS,CAAC,CAAC;MACpD,OAAO,IAAI,CAACmD,iBAAiB,CAACD,UAAU,CAAC,CAACtJ,IAAI,CAAC3D,KAAK,CAAC,GAAG,CAAC,EAAEF,SAAS,CAAC,MAAM;QACvE;QACA,OAAOiN,YAAY,CAACpJ,IAAI,CAAC7D,SAAS,CAAEqN,IAAI,IAAK;UACzC,MAAMC,aAAa,GAAG,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACT,iBAAiB,CAAC;UACrE,MAAMU,aAAa,GAAG,IAAI,CAACC,eAAe,KAAKH,aAAa;UAC5D;UACA,OAAQD,IAAI,IAAIG,aAAa,GAAI,IAAI,CAACE,uBAAuB,CAAC,CAAC,GAAGvN,KAAK;QAC3E,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,EAAEF,SAAS,CAAC0M,UAAU,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;EACP;EACAtK,WAAWA,CAAA,EAAG;IACV3D,iBAAiB,CAAC;MACd8K,SAAS,EAAEA,CAAA,KAAM;QACb,IAAI,CAAC/H,GAAG,CAACgH,iBAAiB,CAAC,CAAC;QAC5B,IAAI,CAAChH,GAAG,CAACmH,gBAAgB,CAAC,CAAC;QAC3BjK,SAAS,CAAC,MAAM;UACZ,IAAI,CAACkN,OAAO,CAACH,SAAS,CAACrG,GAAG,CAAC,IAAI,CAACoH,IAAI,CAAC;UACrC,IAAI,CAAC,IAAI,CAACA,IAAI,EAAE;YACZ;YACA7C,qBAAqB,CAAC,MAAM,IAAI,CAACiC,OAAO,CAACH,SAAS,CAACrG,GAAG,CAAC,IAAI,CAACoH,IAAI,CAAC,CAAC;UACtE;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF,KAAK,CAAC,CAAC;EACX;EACA;AACJ;AACA;EACIW,iBAAiBA,CAAC5J,CAAC,EAAE;IACjB;IACA,IAAI,CAACsJ,iBAAiB,GAAGtJ,CAAC,CAAC,IAAI,CAACqI,OAAO,CAACkB,cAAc,CAAC;IACvD,IAAI,CAACU,eAAe,GAAG,IAAI,CAACF,kBAAkB,CAAC,IAAI,CAACT,iBAAiB,CAAC;IACtE,IAAI,CAACa,SAAS,GAAG,IAAI,CAAC9B,OAAO,CAAC+B,iBAAiB;IAC/C,OAAO,IAAI,CAACrD,QAAQ,CAAC,IAAI,CAACsD,QAAQ,CAAC,CAAC,CAAC;EACzC;EACAA,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,IAAI,CAACJ,eAAe,KAAK,SAAS,EAAE;MACpC;MACA,MAAMK,sBAAsB,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC1D;MACA,IAAID,sBAAsB,IAAI,IAAI,CAACH,SAAS,EAAE;QAC1C,OAAO,IAAI,CAACA,SAAS;MACzB;MACA,OAAOG,sBAAsB;IACjC;IACA;IACA,MAAME,uBAAuB,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5D,IAAID,uBAAuB,IAAI,CAAC,EAAE;MAC9B,OAAO,CAAC;IACZ;IACA,OAAOA,uBAAuB;EAClC;EACA;AACJ;AACA;AACA;EACIN,uBAAuBA,CAAA,EAAG;IACtB,MAAMpH,QAAQ,GAAG,IAAI,CAACuH,QAAQ,CAAC,CAAC;IAChC,OAAO,IAAI,CAACtD,QAAQ,CAACjE,QAAQ,CAAC,CAACzC,IAAI,CAACzD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC8N,SAAS,CAAC5H,QAAQ,CAAC,CAAC,EAAEtG,SAAS,CAAC,MAAM,IAAI,CAAC0N,uBAAuB,CAAC,CAAC,CAAC,CAAC;EACpI;EACA;AACJ;AACA;EACIQ,SAASA,CAAC5H,QAAQ,EAAE;IAChB,IAAI,IAAI,CAACmH,eAAe,KAAK,SAAS,EAAE;MACpC,OAAOnH,QAAQ,IAAI,IAAI,CAACqH,SAAS;IACrC;IACA,OAAOrH,QAAQ,IAAI,CAAC;EACxB;EACA;IAAS,IAAI,CAAC/D,IAAI,YAAA4L,qBAAA1L,iBAAA;MAAA,YAAAA,iBAAA,IAAwFyJ,YAAY;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACxJ,IAAI,kBAhiB8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EAgiBJsJ,YAAY;MAAAhB,QAAA,GAhiBVrN,EAAE,CAAAuQ,0BAAA;IAAA,EAgiBoE;EAAE;AAC5K;AACA;EAAA,QAAAtL,SAAA,oBAAAA,SAAA,KAliBoGjF,EAAE,CAAAkF,iBAAA,CAkiBXmJ,YAAY,EAAc,CAAC;IAC1GtJ,IAAI,EAAE5E;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMqQ,eAAe,SAASnC,YAAY,CAAC;EACvC,IAAIoC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC7M,GAAG,CAACyG,QAAQ,CAACzD,YAAY;EACzC;EACApC,WAAWA,CAAA,EAAG;IACVzD,MAAM,CAAC,MAAM;MACT,IAAI,IAAI,CAAC6C,GAAG,CAAC2G,SAAS,CAAC,CAAC,KAAK,KAAK,EAAE;QAChC,IAAI,CAACmG,eAAe,GAAG,MAAM;UACzB,MAAMhC,MAAM,GAAG,IAAI,CAAC+B,WAAW,GAAG,IAAI,CAACnC,YAAY,GAAG,IAAI,CAACN,OAAO,CAAC2C,oBAAoB;UACvF,OAAOjC,MAAM,GAAG,IAAI,CAACE,IAAI,GAAG,IAAI,CAAC6B,WAAW;QAChD,CAAC;QACD,IAAI,CAACf,kBAAkB,GAAIjH,QAAQ,IAAK;UACpC,OAAOA,QAAQ,GAAG,IAAI,CAACiI,eAAe,CAAC,CAAC,GAAG,SAAS,GAAG,UAAU;QACrE,CAAC;MACL,CAAC,MACI;QACD,IAAI,CAACA,eAAe,GAAG,MAAM;UACzB,OAAO,IAAI,CAAC1C,OAAO,CAAC2C,oBAAoB,GAAG,IAAI,CAAC/B,IAAI,GAAG,IAAI,CAAC6B,WAAW;QAC3E,CAAC;QACD,IAAI,CAACf,kBAAkB,GAAIjH,QAAQ,IAAK;UACpC,OAAOA,QAAQ,GAAG,IAAI,CAACiI,eAAe,CAAC,CAAC,GAAG,SAAS,GAAG,UAAU;QACrE,CAAC;MACL;IACJ,CAAC,CAAC;IACF,KAAK,CAAC,CAAC;EACX;EACAhE,QAAQA,CAACkE,KAAK,EAAE;IACZ,OAAOpO,IAAI,CAAC,IAAI,CAACoB,GAAG,CAAC8I,QAAQ,CAAC;MAAEkE,KAAK;MAAE7K,QAAQ,EAAE,IAAI,CAACnC,GAAG,CAAC8E,mBAAmB,CAAC;IAAE,CAAC,CAAC,CAAC;EACvF;EACAwH,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAClC,OAAO,CAAC2C,oBAAoB,GAAG,IAAI,CAACrC,YAAY;EAChE;EACA8B,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACpC,OAAO,CAAC2C,oBAAoB,GAAG,IAAI,CAACrC,YAAY;EAChE;EACA;IAAS,IAAI,CAAC5J,IAAI,YAAAmM,wBAAAjM,iBAAA;MAAA,YAAAA,iBAAA,IAAwF4L,eAAe;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAAC3L,IAAI,kBA1kB8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EA0kBJyL,eAAe;MAAAxL,SAAA;MAAAqI,QAAA,GA1kBbrN,EAAE,CAAAsN,kBAAA,CA0kB2E,CAAC;QAAEC,OAAO,EAAEc,YAAY;QAAEb,WAAW,EAAEgD;MAAgB,CAAC,CAAC,GA1kBtIxQ,EAAE,CAAAuQ,0BAAA;IAAA,EA0kB4K;EAAE;AACpR;AACA;EAAA,QAAAtL,SAAA,oBAAAA,SAAA,KA5kBoGjF,EAAE,CAAAkF,iBAAA,CA4kBXsL,eAAe,EAAc,CAAC;IAC7GzL,IAAI,EAAE5E,SAAS;IACfgF,IAAI,EAAE,CAAC;MACCC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,mBAAmB;MAC7BqI,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEc,YAAY;QAAEb,WAAW,EAAEgD;MAAgB,CAAC;IACvE,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC,MAAMM,eAAe,SAASzC,YAAY,CAAC;EACvC,IAAIoC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC7M,GAAG,CAACyG,QAAQ,CAAC3D,aAAa;EAC1C;EACAgK,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC1C,OAAO,CAAC2C,oBAAoB,GAAG,IAAI,CAAC/B,IAAI,GAAG,IAAI,CAAC6B,WAAW;EAC3E;EACAf,kBAAkBA,CAACjH,QAAQ,EAAE;IACzB,OAAOA,QAAQ,GAAG,IAAI,CAACiI,eAAe,CAAC,CAAC,GAAG,SAAS,GAAG,UAAU;EACrE;EACAhE,QAAQA,CAACqE,GAAG,EAAE;IACV,OAAOvO,IAAI,CAAC,IAAI,CAACoB,GAAG,CAAC8I,QAAQ,CAAC;MAAEqE,GAAG;MAAEhL,QAAQ,EAAE,IAAI,CAACnC,GAAG,CAAC8E,mBAAmB,CAAC;IAAE,CAAC,CAAC,CAAC;EACrF;EACAwH,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAClC,OAAO,CAAC2C,oBAAoB,GAAG,IAAI,CAACrC,YAAY;EAChE;EACA8B,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACpC,OAAO,CAAC2C,oBAAoB,GAAG,IAAI,CAACrC,YAAY;EAChE;EACA;IAAS,IAAI,CAAC5J,IAAI;MAAA,IAAAsM,4BAAA;MAAA,gBAAAC,wBAAArM,iBAAA;QAAA,QAAAoM,4BAAA,KAAAA,4BAAA,GAvmB8EhR,EAAE,CAAAkR,qBAAA,CAumBQJ,eAAe,IAAAlM,iBAAA,IAAfkM,eAAe;MAAA;IAAA,IAAqD;EAAE;EAChL;IAAS,IAAI,CAACjM,IAAI,kBAxmB8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EAwmBJ+L,eAAe;MAAA9L,SAAA;MAAAqI,QAAA,GAxmBbrN,EAAE,CAAAsN,kBAAA,CAwmB2E,CAAC;QAAEC,OAAO,EAAEc,YAAY;QAAEb,WAAW,EAAEsD;MAAgB,CAAC,CAAC,GAxmBtI9Q,EAAE,CAAAuQ,0BAAA;IAAA,EAwmB4K;EAAE;AACpR;AACA;EAAA,QAAAtL,SAAA,oBAAAA,SAAA,KA1mBoGjF,EAAE,CAAAkF,iBAAA,CA0mBX4L,eAAe,EAAc,CAAC;IAC7G/L,IAAI,EAAE5E,SAAS;IACfgF,IAAI,EAAE,CAAC;MACCC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,mBAAmB;MAC7BqI,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEc,YAAY;QAAEb,WAAW,EAAEsD;MAAgB,CAAC;IACvE,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMK,gBAAgB,CAAC;EACnB3M,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4M,SAAS,GAAGlO,iBAAiB,CAACjD,MAAM,CAACe,WAAW,CAAC,CAAC;IACvD,IAAI,CAACqQ,YAAY,GAAGpR,MAAM,CAACmJ,qBAAqB,CAAC;IACjD,IAAI,CAAC6E,QAAQ,GAAGhO,MAAM,CAACgD,QAAQ,CAAC;IAChC,IAAI,CAACqO,MAAM,GAAG,IAAI,CAACrD,QAAQ,CAACsD,WAAW;IACvC,IAAI,CAACC,sBAAsB,GAAGpR,MAAM,CAAC,IAAI,CAAC;IAC1C,IAAI,IAAI,CAACgR,SAAS,KAAK,CAAC,IAAI,CAACE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAACG,GAAG,CAACC,QAAQ,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC,EAAE;MACvG,IAAI,CAACC,YAAY,CAAC,CAAC;IACvB;EACJ;EACMA,YAAYA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACjB,IAAI;QACA;QACA,MAAMC,MAAM,GAAGF,KAAI,CAAC3D,QAAQ,CAAC8D,aAAa,CAAC,QAAQ,CAAC;QACpDD,MAAM,CAACE,GAAG,GAAGJ,KAAI,CAACP,YAAY;QAC9B;QACA,MAAM,IAAIY,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACnCL,MAAM,CAACM,MAAM,GAAGF,OAAO;UACvBJ,MAAM,CAACO,OAAO,GAAGF,MAAM;UACvBP,KAAI,CAAC3D,QAAQ,CAACqE,IAAI,CAACC,WAAW,CAACT,MAAM,CAAC;QAC1C,CAAC,CAAC;QACF;QACA,IAAIF,KAAI,CAACN,MAAM,CAAC,gBAAgB,CAAC,EAAE;UAC/BM,KAAI,CAACJ,sBAAsB,CAAChK,GAAG,CAACoK,KAAI,CAACN,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAClE,CAAC,MACI;UACDkB,OAAO,CAACC,KAAK,CAAC,qEAAqE,CAAC;QACxF;MACJ,CAAC,CACD,OAAOA,KAAK,EAAE;QACVD,OAAO,CAACC,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC/E;IAAC;EACL;EACA;IAAS,IAAI,CAAC/N,IAAI,YAAAgO,yBAAA9N,iBAAA;MAAA,YAAAA,iBAAA,IAAwFuM,gBAAgB;IAAA,CAAoD;EAAE;EAChL;IAAS,IAAI,CAACrJ,KAAK,kBAtpB6E9H,EAAE,CAAA+H,kBAAA;MAAAC,KAAA,EAspBYmJ,gBAAgB;MAAAlJ,OAAA,EAAhBkJ,gBAAgB,CAAAzM,IAAA;MAAAyE,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAlE,SAAA,oBAAAA,SAAA,KAxpBoGjF,EAAE,CAAAkF,iBAAA,CAwpBXiM,gBAAgB,EAAc,CAAC;IAC9GpM,IAAI,EAAE1E,UAAU;IAChB8E,IAAI,EAAE,CAAC;MAAEgE,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMwJ,YAAY,SAAS5E,oBAAoB,CAAC;EAC5C;EACA,IAAIa,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACnK,aAAa,CAACgK,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAACT,OAAO,CAAC4E,gBAAgB,CAAC;EACpF;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,KAAK,CAAClE,IAAI,GAAG,IAAI,CAACA,IAAI;EACtC;EACA;AACJ;AACA;AACA;EACI,IAAIT,aAAaA,CAAA,EAAG;IAChB,OAAOpM,SAAS,CAAC,IAAI,CAAC0C,aAAa,EAAE,aAAa,CAAC,CAACuB,IAAI,CAACN,eAAe,CAAC,CAAC,EAAEJ,gBAAgB,CAAC,IAAI,CAAC2I,QAAQ,CAAC,EAAE9L,SAAS,CAAEwD,CAAC,IAAK;MAC1H,IAAIoN,aAAa;MACjB,IAAIC,cAAc;MAClB,MAAMC,SAAS,GAAGxQ,EAAE,CAACkD,CAAC,CAAC,CAACK,IAAI,CAACpE,GAAG,CAAC,MAAM;QACnC;QACAmR,aAAa,GAAG,IAAI,CAACF,QAAQ;QAC7BG,cAAc,GAAG,IAAI,CAAChF,OAAO,CAAC+B,iBAAiB;QAC/C,IAAI,CAACmD,WAAW,CAAC,IAAI,CAAClF,OAAO,CAACmF,IAAI,CAAC;MACvC,CAAC,CAAC,CAAC;MACH,MAAMzI,QAAQ,GAAG3I,SAAS,CAAC,IAAI,CAACkM,QAAQ,EAAE,aAAa,CAAC,CAACjI,IAAI,CAACN,eAAe,CAAC,CAAC,CAAC;MAChF,MAAM0N,OAAO,GAAGrR,SAAS,CAAC,IAAI,CAACkM,QAAQ,EAAE,WAAW,EAAE;QAAEoF,OAAO,EAAE;MAAK,CAAC,CAAC,CAACrN,IAAI,CAACN,eAAe,CAAC,CAAC,EAAED,eAAe,CAAC,IAAI,CAACwI,QAAQ,CAAC,EAAErM,GAAG,CAAC,MAAM,IAAI,CAACsR,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;MACrK,OAAOD,SAAS,CAACjN,IAAI,CAAChE,GAAG,CAAEsN,UAAU,IAAKA,UAAU,CAAC,IAAI,CAACtB,OAAO,CAACkB,cAAc,CAAC,CAAC,EAAE/M,SAAS,CAAEmR,WAAW,IAAK5I,QAAQ,CAAC1E,IAAI,CAAChE,GAAG,CAAEuR,SAAS,IAAKA,SAAS,CAAC,IAAI,CAACvF,OAAO,CAACwF,cAAc,CAAC,CAAC;MACvL;MACAxR,GAAG,CAAEyR,UAAU,IAAKA,UAAU,GAAG,IAAI,CAACX,KAAK,CAACpE,MAAM,CAAC,EAAE1M,GAAG,CAAE0R,mBAAmB,IAAKV,cAAc,IAAIU,mBAAmB,GAAGJ,WAAW,CAAC,GAAGP,aAAa,CAAC,EAAEnR,GAAG,CAAE+R,cAAc,IAAK,IAAI,CAAC3F,OAAO,CAAC4F,eAAe,CAACD,cAAc,EAAEX,cAAc,CAAC,CAAC,EAAE5Q,SAAS,CAACgR,OAAO,CAAC,CAAC,CAAC,CAAC;IACzQ,CAAC,CAAC,CAAC;EACP;EACA5O,WAAWA,CAAA,EAAG;IACV3D,iBAAiB,CAAC;MACd8K,SAAS,EAAEA,CAAA,KAAM;QACb,MAAMmG,MAAM,GAAG,IAAI,CAAC+B,OAAO,CAACrC,sBAAsB,CAAC,CAAC;QACpD1Q,SAAS,CAAC,MAAM;UACZ,IAAIgR,MAAM,IAAI,CAAC,IAAI,CAACgC,UAAU,EAAE;YAC5B,IAAI,CAACA,UAAU,GAAGC,aAAa,CAACjC,MAAM,EAAE,IAAI,CAACrN,aAAa,EAAE,IAAI,CAACb,GAAG,CAACyG,QAAQ,CAAC5F,aAAa,EAAE,IAAI,CAACuJ,OAAO,CAACmF,IAAI,CAAC;UACnH;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF,KAAK,CAAC,CAAC;IACP,IAAI,CAACU,OAAO,GAAG5T,MAAM,CAACkR,gBAAgB,CAAC;IACvC,IAAI,CAAC2B,KAAK,GAAG7S,MAAM,CAACoO,YAAY,CAAC;EACrC;EACA6E,WAAWA,CAACvL,KAAK,EAAE;IACf,IAAI,CAACmC,IAAI,CAACuC,GAAG,CAAC,MAAM,IAAI,CAACzI,GAAG,CAAC8G,QAAQ,CAAClD,GAAG,CAACG,KAAK,CAAC,CAAC;EACrD;EACA;IAAS,IAAI,CAACjD,IAAI,YAAAsP,qBAAApP,iBAAA;MAAA,YAAAA,iBAAA,IAAwF+N,YAAY;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAAC9N,IAAI,kBA9sB8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EA8sBJ4N,YAAY;MAAAtF,QAAA,GA9sBVrN,EAAE,CAAAuQ,0BAAA;IAAA,EA8sBoE;EAAE;AAC5K;AACA;EAAA,QAAAtL,SAAA,oBAAAA,SAAA,KAhtBoGjF,EAAE,CAAAkF,iBAAA,CAgtBXyN,YAAY,EAAc,CAAC;IAC1G5N,IAAI,EAAE5E;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC,SAAS4T,aAAaA,CAACE,cAAc,EAAEC,OAAO,EAAEC,MAAM,EAAEhB,IAAI,EAAE;EAC1D,OAAOe,OAAO,CAACE,OAAO,CAAC;IACnBC,SAAS,EAAE,CACP,wCAAwC,EACxC,sCAAsC;EAE9C,CAAC,EAAE;IACCC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,IAAIP,cAAc,CAAC;MAAEE,MAAM;MAAEhB;IAAK,CAAC;EACjD,CAAC,CAAC;AACN;AAEA,MAAMsB,eAAe,SAAS9B,YAAY,CAAC;EACvC;IAAS,IAAI,CAACjO,IAAI;MAAA,IAAAgQ,4BAAA;MAAA,gBAAAC,wBAAA/P,iBAAA;QAAA,QAAA8P,4BAAA,KAAAA,4BAAA,GAjuB8E1U,EAAE,CAAAkR,qBAAA,CAiuBQuD,eAAe,IAAA7P,iBAAA,IAAf6P,eAAe;MAAA;IAAA,IAAqD;EAAE;EAChL;IAAS,IAAI,CAAC5P,IAAI,kBAluB8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EAkuBJ0P,eAAe;MAAAzP,SAAA;MAAAqI,QAAA,GAluBbrN,EAAE,CAAAsN,kBAAA,CAkuB2E,CAAC;QAAEC,OAAO,EAAEoF,YAAY;QAAEnF,WAAW,EAAEiH;MAAgB,CAAC,CAAC,GAluBtIzU,EAAE,CAAAuQ,0BAAA;IAAA,EAkuB4K;EAAE;AACpR;AACA;EAAA,QAAAtL,SAAA,oBAAAA,SAAA,KApuBoGjF,EAAE,CAAAkF,iBAAA,CAouBXuP,eAAe,EAAc,CAAC;IAC7G1P,IAAI,EAAE5E,SAAS;IACfgF,IAAI,EAAE,CAAC;MACCC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,mBAAmB;MAC7BqI,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEoF,YAAY;QAAEnF,WAAW,EAAEiH;MAAgB,CAAC;IACvE,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMG,eAAe,SAASjC,YAAY,CAAC;EACvC;IAAS,IAAI,CAACjO,IAAI;MAAA,IAAAmQ,4BAAA;MAAA,gBAAAC,wBAAAlQ,iBAAA;QAAA,QAAAiQ,4BAAA,KAAAA,4BAAA,GA7uB8E7U,EAAE,CAAAkR,qBAAA,CA6uBQ0D,eAAe,IAAAhQ,iBAAA,IAAfgQ,eAAe;MAAA;IAAA,IAAqD;EAAE;EAChL;IAAS,IAAI,CAAC/P,IAAI,kBA9uB8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EA8uBJ6P,eAAe;MAAA5P,SAAA;MAAAqI,QAAA,GA9uBbrN,EAAE,CAAAsN,kBAAA,CA8uB2E,CAAC;QAAEC,OAAO,EAAEoF,YAAY;QAAEnF,WAAW,EAAEoH;MAAgB,CAAC,CAAC,GA9uBtI5U,EAAE,CAAAuQ,0BAAA;IAAA,EA8uB4K;EAAE;AACpR;AACA;EAAA,QAAAtL,SAAA,oBAAAA,SAAA,KAhvBoGjF,EAAE,CAAAkF,iBAAA,CAgvBX0P,eAAe,EAAc,CAAC;IAC7G7P,IAAI,EAAE5E,SAAS;IACfgF,IAAI,EAAE,CAAC;MACCC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,mBAAmB;MAC7BqI,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEoF,YAAY;QAAEnF,WAAW,EAAEoH;MAAgB,CAAC;IACvE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMG,aAAa,GAAG;EAClBC,OAAO,EAAEA,CAACC,YAAY,EAAEnF,SAAS,KAAKmF,YAAY,GAAGnF,SAAS;EAC9DoF,QAAQ,EAAGD,YAAY,IAAKA,YAAY,GAAG;AAC/C,CAAC;AACD,MAAME,cAAc,GAAG;EACnBH,OAAO,EAAEA,CAACI,QAAQ,EAAE1G,MAAM,KAAKA,MAAM,GAAG0G,QAAQ;EAChDF,QAAQ,EAAEA,CAACE,QAAQ,EAAE1G,MAAM,KAAKA,MAAM,GAAG0G;AAC7C,CAAC;AACD,MAAMC,wBAAwB,GAAG;EAC7BC,GAAG,EAAE;IACDN,OAAO,EAAEA,CAACI,QAAQ,EAAE1G,MAAM,EAAEoB,SAAS,KAAKA,SAAS,GAAGpB,MAAM,GAAG0G,QAAQ;IACvEF,QAAQ,EAAEA,CAACE,QAAQ,EAAE1G,MAAM,EAAEoB,SAAS,KAAKA,SAAS,GAAGpB,MAAM,GAAG0G;EACpE,CAAC;EACDG,GAAG,EAAEJ;AACT,CAAC;AACD,MAAMK,eAAe,SAASzH,oBAAoB,CAAC;EAC/C,IAAII,aAAaA,CAAA,EAAG;IAChB,MAAMU,YAAY,GAAG9M,SAAS,CAAC,IAAI,CAAC0C,aAAa,EAAE,aAAa,CAAC,CAACuB,IAAI,CAACN,eAAe,CAAC,CAAC,EAAEJ,gBAAgB,CAAC,IAAI,CAAC2I,QAAQ,CAAC,CAAC;IAC1H,MAAMa,UAAU,GAAG/M,SAAS,CAAC,IAAI,CAACkM,QAAQ,EAAE,WAAW,EAAE;MAAEc,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC/I,IAAI,CAACP,eAAe,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC;IAChH,MAAMkB,aAAa,GAAGpN,SAAS,CAAC,IAAI,CAAC0C,aAAa,EAAE,cAAc,EAAE;MAAEsK,OAAO,EAAE;IAAK,CAAC,CAAC;IACtF;IACA,MAAM0G,iBAAiB,GAAGxT,KAAK,CAAC6M,UAAU,EAAEK,aAAa,CAAC;IAC1D,OAAON,YAAY,CAAC7I,IAAI,CAAC7D,SAAS,CAAC,MAAM,IAAI,CAACuT,eAAe,CAAC,CAAC,CAAC1P,IAAI,CAAC3D,KAAK,CAAC,IAAI,CAACsT,oBAAoB,CAAC,EAAExT,SAAS,CAAC,MAAM,IAAI,CAACyT,oBAAoB,CAAC,CAAC,CAAC,EAAExT,SAAS,CAACqT,iBAAiB,CAAC,CAAC,CAAC,CAAC;EACxL;EACAjR,WAAWA,CAAA,EAAG;IACVzD,MAAM,CAAC,MAAM;MACT,MAAM6O,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC,CAAC;MAC9C,MAAMzF,GAAG,GAAG,IAAI,CAACvG,GAAG,CAAC2G,SAAS,CAAC,CAAC;MAChCzJ,SAAS,CAAC,MAAM;QACZ;QACA,IAAI,CAAC+U,SAAS,GAAGd,aAAa,CAACnF,eAAe,CAAC;QAC/C,IAAI,IAAI,CAAC5B,OAAO,CAACmF,IAAI,KAAK,GAAG,EAAE;UAC3B;UACA,IAAI,CAACnD,QAAQ,GAAGqF,wBAAwB,CAAClL,GAAG,CAAC,CAACyF,eAAe,CAAC;QAClE,CAAC,MACI;UACD;UACA,IAAI,CAACI,QAAQ,GAAGmF,cAAc,CAACvF,eAAe,CAAC;QACnD;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,KAAK,CAAC,CAAC;IACP,IAAI,CAACkG,eAAe,GAAGtV,KAAK,CAACuV,QAAQ,CAAC,CAAC;IACvC,IAAI,CAACnG,eAAe,GAAGpP,KAAK,CAACuV,QAAQ,CAAC,CAAC;IACvC,IAAI,CAACJ,oBAAoB,GAAG,GAAG;IAC/B,IAAI,CAACK,kBAAkB,GAAG,GAAG;IAC7B,IAAI,CAACZ,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACa,eAAe,GAAG,EAAE;EAC7B;EACAP,eAAeA,CAAA,EAAG;IACd,MAAM/N,KAAK,GAAG,IAAI,CAACqI,QAAQ,CAAC,IAAI,CAACoF,QAAQ,EAAE,IAAI,CAACpH,OAAO,CAAC2C,oBAAoB,EAAE,IAAI,CAAC3C,OAAO,CAAC+B,iBAAiB,CAAC;IAC7G,OAAO,IAAI,CAAC/B,OAAO,CAACtB,QAAQ,CAAC/E,KAAK,EAAE,IAAI,CAACqO,kBAAkB,CAAC;EAChE;EACAE,iBAAiBA,CAAA,EAAG;IAChB,MAAMpG,SAAS,GAAG,IAAI,CAAC9B,OAAO,CAAC+B,iBAAiB;IAChD,MAAMpI,KAAK,GAAG,IAAI,CAACqI,QAAQ,CAAC,IAAI,CAACiG,eAAe,EAAE,IAAI,CAACjI,OAAO,CAAC2C,oBAAoB,EAAEb,SAAS,CAAC;IAC/F,IAAI,CAAC9B,OAAO,CAAC4F,eAAe,CAACjM,KAAK,EAAEmI,SAAS,CAAC;EAClD;EACA8F,oBAAoBA,CAAA,EAAG;IACnB,OAAOlT,QAAQ,CAAC,CAAC,EAAEC,uBAAuB,CAAC,CAACqD,IAAI,CAACzD,SAAS,CAAC,MAAM,IAAI,CAACsT,SAAS,CAAC,IAAI,CAAC7H,OAAO,CAAC2C,oBAAoB,EAAE,IAAI,CAAC3C,OAAO,CAAC+B,iBAAiB,CAAC,CAAC,EAAEnO,GAAG,CAAC,MAAM,IAAI,CAACsU,iBAAiB,CAAC,CAAC,CAAC,CAAC;EAC7L;EACA;IAAS,IAAI,CAACxR,IAAI,YAAAyR,wBAAAvR,iBAAA;MAAA,YAAAA,iBAAA,IAAwF4Q,eAAe;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACY,IAAI,kBAxzB8EpW,EAAE,CAAAqW,iBAAA;MAAAtR,IAAA,EAwzBJyQ,eAAe;MAAAxQ,SAAA;MAAAmI,MAAA;QAAA2I,eAAA;QAAAlG,eAAA;MAAA;MAAAvC,QAAA,GAxzBbrN,EAAE,CAAAuQ,0BAAA;MAAA+F,KAAA,EAAAlT,GAAA;MAAAmT,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAArT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFtD,EAAE,CAAA4W,cAAA,YAwzBmd,CAAC;UAxzBtd5W,EAAE,CAAA6W,cAAA;UAAF7W,EAAE,CAAA4W,cAAA,YAwzBqiB,CAAC;UAxzBxiB5W,EAAE,CAAAwD,SAAA,aAwzBmyB,CAAC;UAxzBtyBxD,EAAE,CAAA8W,YAAA,CAwzB+yB,CAAC,CAAS,CAAC;QAAA;MAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA,EAAw1C;EAAE;AAC1vE;AACA;EAAA,QAAA/R,SAAA,oBAAAA,SAAA,KA1zBoGjF,EAAE,CAAAkF,iBAAA,CA0zBXsQ,eAAe,EAAc,CAAC;IAC7GzQ,IAAI,EAAE9D,SAAS;IACfkE,IAAI,EAAE,CAAC;MAAEE,QAAQ,EAAE,yBAAyB;MAAE2R,eAAe,EAAE9V,uBAAuB,CAAC+V,MAAM;MAAEP,QAAQ,EAAE,oZAAoZ;MAAEK,MAAM,EAAE,CAAC,gxCAAgxC;IAAE,CAAC;EAC/xD,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMG,UAAU,SAAStJ,gBAAgB,CAAC;EACtCpJ,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAG2S,SAAS,CAAC;IACnB,IAAI,CAACxI,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACiE,gBAAgB,GAAG,QAAQ;IAChC,IAAI,CAACrE,YAAY,GAAG,cAAc;IAClC,IAAI,CAACiF,cAAc,GAAG,SAAS;IAC/B,IAAI,CAACtE,cAAc,GAAG,SAAS;IAC/B,IAAI,CAACiE,IAAI,GAAG,GAAG;EACnB;EACA,IAAIpD,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACnM,GAAG,CAACyG,QAAQ,CAACvD,UAAU;EACvC;EACA,IAAI6J,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAAC/M,GAAG,CAACyG,QAAQ,CAAC7D,SAAS;EACtC;EACAkG,QAAQA,CAACqE,GAAG,EAAEhL,QAAQ,EAAE;IACpB,OAAOvD,IAAI,CAAC,IAAI,CAACoB,GAAG,CAAC8I,QAAQ,CAAC;MAAEqE,GAAG;MAAEhL;IAAS,CAAC,CAAC,CAAC;EACrD;EACA6N,eAAeA,CAACjM,KAAK,EAAE;IACnB,IAAI,CAAC/D,GAAG,CAACyG,QAAQ,CAAC3C,SAAS,CAACC,KAAK,CAAC;EACtC;EACA;IAAS,IAAI,CAACjD,IAAI;MAAA,IAAA0S,uBAAA;MAAA,gBAAAC,mBAAAzS,iBAAA;QAAA,QAAAwS,uBAAA,KAAAA,uBAAA,GAr1B8EpX,EAAE,CAAAkR,qBAAA,CAq1BQgG,UAAU,IAAAtS,iBAAA,IAAVsS,UAAU;MAAA;IAAA,IAAqD;EAAE;EAC3K;IAAS,IAAI,CAACd,IAAI,kBAt1B8EpW,EAAE,CAAAqW,iBAAA;MAAAtR,IAAA,EAs1BJmS,UAAU;MAAAlS,SAAA;MAAA6H,QAAA;MAAAC,YAAA,WAAAwK,wBAAAhU,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAt1BRtD,EAAE,CAAAiN,WAAA,iBAs1BJ1J,GAAA,CAAAsK,SAAA,CAAU,CAAD,CAAC;QAAA;MAAA;MAAAR,QAAA,GAt1BRrN,EAAE,CAAAsN,kBAAA,CAs1B+H,CACzN;QAAEC,OAAO,EAAEI,iBAAiB;QAAEH,WAAW,EAAE0J;MAAW,CAAC,CAC1D,GAx1B2FlX,EAAE,CAAAuQ,0BAAA;MAAAgG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAa,oBAAAjU,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFtD,EAAE,CAAA4W,cAAA,YA01B/C,CAAC,YAEE,CAAC,YACmB,CAAC;UA71BqB5W,EAAE,CAAAwD,SAAA,YA81BhB,CAAC;UA91BaxD,EAAE,CAAA8W,YAAA,CA+1BzF,CAAC;UA/1BsF9W,EAAE,CAAAwX,mBAAA,IAAAnU,iCAAA,MAg2B1E,CAAC;UAh2BuErD,EAAE,CAAA8W,YAAA,CAw2B3F,CAAC,CACH,CAAC;QAAA;QAAA,IAAAxT,EAAA;UAz2B0FtD,EAAE,CAAAkN,WAAA,uBAAA3J,GAAA,CAAAK,GAAA,CAAAmF,WAAA,EA01BhD,CAAC;UA11B6C/I,EAAE,CAAA8D,SAAA,CA41B7C,CAAC;UA51B0C9D,EAAE,CAAAkN,WAAA,wBAAA3J,GAAA,CAAAK,GAAA,CAAAmF,WAAA,EA41B7C,CAAC;UA51B0C/I,EAAE,CAAA8D,SAAA,CA61BzB,CAAC;UA71BsB9D,EAAE,CAAA2D,sBAAA,wBAAAJ,GAAA,CAAAK,GAAA,CAAAwE,UAAA,EA61BzB,CAAC;UA71BsBpI,EAAE,CAAA8D,SAAA,CA81BvB,CAAC;UA91BoB9D,EAAE,CAAA2D,sBAAA,wBAAAJ,GAAA,CAAAK,GAAA,CAAAyE,UAAA,EA81BvB,CAAC;UA91BoBrI,EAAE,CAAA8D,SAAA,CAu2B9F,CAAC;UAv2B2F9D,EAAE,CAAAyX,aAAA,CAAAlU,GAAA,CAAAK,GAAA,CAAAkF,OAAA,WAu2B9F,CAAC;QAAA;MAAA;MAAA4O,YAAA,GAG27F5G,eAAe,EAA8D8D,eAAe,EAA8DY,eAAe;MAAAuB,MAAA;MAAAC,eAAA;IAAA,EAA+I;EAAE;AAC9vG;AACA;EAAA,QAAA/R,SAAA,oBAAAA,SAAA,KA52BoGjF,EAAE,CAAAkF,iBAAA,CA42BXgS,UAAU,EAAc,CAAC;IACxGnS,IAAI,EAAE9D,SAAS;IACfkE,IAAI,EAAE,CAAC;MAAEE,QAAQ,EAAE,aAAa;MAAEqR,QAAQ,EAAE;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEiB,OAAO,EAAE,CAAC7G,eAAe,EAAE8D,eAAe,EAAEY,eAAe,CAAC;MAAE9H,SAAS,EAAE,CACtD;QAAEH,OAAO,EAAEI,iBAAiB;QAAEH,WAAW,EAAE0J;MAAW,CAAC,CAC1D;MAAEzJ,IAAI,EAAE;QACL,sBAAsB,EAAE;MAC5B,CAAC;MAAEuJ,eAAe,EAAE9V,uBAAuB,CAAC+V,MAAM;MAAEF,MAAM,EAAE,CAAC,ujDAAujD,EAAE,g0CAAg0C;IAAE,CAAC;EACr8F,CAAC,CAAC;AAAA;AACV,MAAMa,UAAU,SAAShK,gBAAgB,CAAC;EACtC,IAAImC,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACnM,GAAG,CAACyG,QAAQ,CAACxD,UAAU;EACvC;EACA,IAAI8J,oBAAoBA,CAAA,EAAG;IACvB;IACA,OAAOkH,IAAI,CAACC,GAAG,CAAC,IAAI,CAAClU,GAAG,CAACyG,QAAQ,CAAC5D,UAAU,CAAC;EACjD;EACAjC,WAAWA,CAAA,EAAG;IACVzD,MAAM,CAAC,MAAM;MACT,IAAI,IAAI,CAAC6C,GAAG,CAAC2G,SAAS,CAAC,CAAC,KAAK,KAAK,EAAE;QAChC,IAAI,CAACwN,cAAc,GAAG,CAACtP,QAAQ,EAAEqH,SAAS,KAAK,EAAEA,SAAS,GAAGrH,QAAQ,CAAC;MAC1E,CAAC,MACI;QACD,IAAI,CAACsP,cAAc,GAAItP,QAAQ,IAAKA,QAAQ;MAChD;IACJ,CAAC,CAAC;IACF,KAAK,CAAC,CAAC;IACP,IAAI,CAACoL,OAAO,GAAG5T,MAAM,CAACkR,gBAAgB,CAAC;IACvC,IAAI,CAACxC,kBAAkB,GAAG,MAAM;IAChC,IAAI,CAACiE,gBAAgB,GAAG,OAAO;IAC/B,IAAI,CAACrE,YAAY,GAAG,aAAa;IACjC,IAAI,CAACiF,cAAc,GAAG,SAAS;IAC/B,IAAI,CAACtE,cAAc,GAAG,SAAS;IAC/B,IAAI,CAACiE,IAAI,GAAG,GAAG;EACnB;EACAzG,QAAQA,CAACsL,IAAI,EAAEjS,QAAQ,EAAE;IACrB,OAAOvD,IAAI,CAAC,IAAI,CAACoB,GAAG,CAAC8I,QAAQ,CAAC;MAAEsL,IAAI;MAAEjS;IAAS,CAAC,CAAC,CAAC;EACtD;EACA6N,eAAeA,CAACjM,KAAK,EAAEmI,SAAS,EAAE;IAC9B,IAAI,CAAClM,GAAG,CAACyG,QAAQ,CAACzC,SAAS,CAAC,IAAI,CAACmQ,cAAc,CAACpQ,KAAK,EAAEmI,SAAS,CAAC,CAAC;EACtE;EACA;IAAS,IAAI,CAACpL,IAAI,YAAAuT,mBAAArT,iBAAA;MAAA,YAAAA,iBAAA,IAAwFgT,UAAU;IAAA,CAAmD;EAAE;EACzK;IAAS,IAAI,CAACxB,IAAI,kBAv6B8EpW,EAAE,CAAAqW,iBAAA;MAAAtR,IAAA,EAu6BJ6S,UAAU;MAAA5S,SAAA;MAAA6H,QAAA;MAAAC,YAAA,WAAAoL,wBAAA5U,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAv6BRtD,EAAE,CAAAgN,WAAA,QAu6BJzJ,GAAA,CAAAK,GAAA,CAAA2G,SAAA,CAAc,CAAC;UAv6BbvK,EAAE,CAAAiN,WAAA,iBAu6BJ1J,GAAA,CAAAsK,SAAA,CAAU,CAAD,CAAC;QAAA;MAAA;MAAAR,QAAA,GAv6BRrN,EAAE,CAAAsN,kBAAA,CAu6B8J,CACxP;QAAEC,OAAO,EAAEI,iBAAiB;QAAEH,WAAW,EAAEoK;MAAW,CAAC,CAC1D,GAz6B2F5X,EAAE,CAAAuQ,0BAAA;MAAAgG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAyB,oBAAA7U,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFtD,EAAE,CAAA4W,cAAA,YA26B/C,CAAC,YAEE,CAAC,YACmB,CAAC;UA96BqB5W,EAAE,CAAAwD,SAAA,YA+6BhB,CAAC;UA/6BaxD,EAAE,CAAA8W,YAAA,CAg7BzF,CAAC;UAh7BsF9W,EAAE,CAAAwX,mBAAA,IAAAzT,iCAAA,MAi7B1E,CAAC;UAj7BuE/D,EAAE,CAAA8W,YAAA,CAy7B3F,CAAC,CACH,CAAC;QAAA;QAAA,IAAAxT,EAAA;UA17B0FtD,EAAE,CAAAkN,WAAA,uBAAA3J,GAAA,CAAAK,GAAA,CAAAmF,WAAA,EA26BhD,CAAC;UA36B6C/I,EAAE,CAAA8D,SAAA,CA66B7C,CAAC;UA76B0C9D,EAAE,CAAAkN,WAAA,wBAAA3J,GAAA,CAAAK,GAAA,CAAAmF,WAAA,EA66B7C,CAAC;UA76B0C/I,EAAE,CAAA8D,SAAA,CA86BzB,CAAC;UA96BsB9D,EAAE,CAAA2D,sBAAA,wBAAAJ,GAAA,CAAAK,GAAA,CAAAwE,UAAA,EA86BzB,CAAC;UA96BsBpI,EAAE,CAAA8D,SAAA,CA+6BvB,CAAC;UA/6BoB9D,EAAE,CAAA2D,sBAAA,wBAAAJ,GAAA,CAAAK,GAAA,CAAAyE,UAAA,EA+6BvB,CAAC;UA/6BoBrI,EAAE,CAAA8D,SAAA,CAw7B9F,CAAC;UAx7B2F9D,EAAE,CAAAyX,aAAA,CAAAlU,GAAA,CAAAK,GAAA,CAAAkF,OAAA,WAw7B9F,CAAC;QAAA;MAAA;MAAA4O,YAAA,GAGohHlH,eAAe,EAA8DiE,eAAe,EAA8De,eAAe;MAAAuB,MAAA,GAAA/S,GAAA;MAAAgT,eAAA;IAAA,EAA+I;EAAE;AACv1H;AACA;EAAA,QAAA/R,SAAA,oBAAAA,SAAA,KA77BoGjF,EAAE,CAAAkF,iBAAA,CA67BX0S,UAAU,EAAc,CAAC;IACxG7S,IAAI,EAAE9D,SAAS;IACfkE,IAAI,EAAE,CAAC;MAAEE,QAAQ,EAAE,aAAa;MAAEqR,QAAQ,EAAE;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEiB,OAAO,EAAE,CAACnH,eAAe,EAAEiE,eAAe,EAAEe,eAAe,CAAC;MAAE9H,SAAS,EAAE,CACtD;QAAEH,OAAO,EAAEI,iBAAiB;QAAEH,WAAW,EAAEoK;MAAW,CAAC,CAC1D;MAAEnK,IAAI,EAAE;QACL,YAAY,EAAE,iBAAiB;QAC/B,sBAAsB,EAAE;MAC5B,CAAC;MAAEuJ,eAAe,EAAE9V,uBAAuB,CAAC+V,MAAM;MAAEF,MAAM,EAAE,CAAC,ujDAAujD,EAAE,y5DAAy5D;IAAE,CAAC;EAC9hH,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMqB,UAAU,CAAC;EACb5T,WAAWA,CAAA,EAAG;IACV,IAAI,CAACZ,GAAG,GAAG3D,MAAM,CAACiI,YAAY,CAAC;EACnC;EACA;IAAS,IAAI,CAACxD,IAAI,YAAA2T,mBAAAzT,iBAAA;MAAA,YAAAA,iBAAA,IAAwFwT,UAAU;IAAA,CAAmD;EAAE;EACzK;IAAS,IAAI,CAAChC,IAAI,kBA99B8EpW,EAAE,CAAAqW,iBAAA;MAAAtR,IAAA,EA89BJqT,UAAU;MAAApT,SAAA;MAAAuR,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAA4B,oBAAAhV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA99BRtD,EAAE,CAAAwX,mBAAA,IAAAvT,iCAAA,qBA+9BzE,CAAC;UA/9BsEjE,EAAE,CAAAwX,mBAAA,IAAAtT,iCAAA,qBAk+BvE,CAAC;QAAA;QAAA,IAAAZ,EAAA;UAl+BoEtD,EAAE,CAAAyX,aAAA,CAAAlU,GAAA,CAAAK,GAAA,CAAAqH,YAAA,WAi+BlG,CAAC;UAj+B+FjL,EAAE,CAAA8D,SAAA,CAo+BlG,CAAC;UAp+B+F9D,EAAE,CAAAyX,aAAA,CAAAlU,GAAA,CAAAK,GAAA,CAAAsH,cAAA,WAo+BlG,CAAC;QAAA;MAAA;MAAAwM,YAAA,GACiGE,UAAU,EAAwDV,UAAU;MAAAH,MAAA;MAAAC,eAAA;IAAA,EAAmF;EAAE;AACvQ;AACA;EAAA,QAAA/R,SAAA,oBAAAA,SAAA,KAv+BoGjF,EAAE,CAAAkF,iBAAA,CAu+BXkT,UAAU,EAAc,CAAC;IACxGrT,IAAI,EAAE9D,SAAS;IACfkE,IAAI,EAAE,CAAC;MAAEE,QAAQ,EAAE,YAAY;MAAE2R,eAAe,EAAE9V,uBAAuB,CAAC+V,MAAM;MAAEU,OAAO,EAAE,CAACC,UAAU,EAAEV,UAAU,CAAC;MAAER,QAAQ,EAAE;AAC3I;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEK,MAAM,EAAE,CAAC,2BAA2B;IAAE,CAAC;EACpC,CAAC,CAAC;AAAA;AAEV,MAAMwB,WAAW,SAAS5O,eAAe,CAAC;EACtCnF,WAAWA,CAAA,EAAG;IACVzD,MAAM,CAAC,MAAM;MACT,MAAMyX,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC/T,aAAa;MAC1D3D,SAAS,CAAC,MAAM;QACZ,IAAI,CAACuJ,QAAQ,CAACtD,IAAI,CAAC,IAAI,CAACtC,aAAa,EAAE+T,cAAc,CAAC;MAC1D,CAAC,CAAC;IACN,CAAC,CAAC;IACF,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,cAAc,GAAGrX,SAAS,CAAC4U,QAAQ,CAAC,gBAAgB,CAAC;IAC1D,IAAI,CAAC0C,WAAW,GAAGtX,SAAS,CAAC4U,QAAQ,CAACqC,UAAU,CAAC;EACrD;EACA;IAAS,IAAI,CAAC1T,IAAI,YAAAgU,oBAAA9T,iBAAA;MAAA,YAAAA,iBAAA,IAAwF2T,WAAW;IAAA,CAAmD;EAAE;EAC1K;IAAS,IAAI,CAACnC,IAAI,kBAhgC8EpW,EAAE,CAAAqW,iBAAA;MAAAtR,IAAA,EAggCJwT,WAAW;MAAAvT,SAAA;MAAA2T,SAAA,WAAAC,kBAAAtV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhgCTtD,EAAE,CAAA6Y,iBAAA,CAAAtV,GAAA,CAAAiV,cAAA,EAAArU,GAAA;UAAFnE,EAAE,CAAA6Y,iBAAA,CAAAtV,GAAA,CAAAkV,WAAA,EAmgC6FL,UAAU;QAAA;QAAA,IAAA9U,EAAA;UAngCzGtD,EAAE,CAAA8Y,cAAA;QAAA;MAAA;MAAAC,QAAA;MAAA1L,QAAA,GAAFrN,EAAE,CAAAsN,kBAAA,CAggC0F,CACpL;QAAEC,OAAO,EAAErF,YAAY;QAAEsF,WAAW,EAAE+K;MAAY,CAAC,EACnDnS,eAAe,CAClB,GAngC2FpG,EAAE,CAAAuQ,0BAAA;MAAAyI,kBAAA,EAAA5U,GAAA;MAAAmS,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAuC,qBAAA3V,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFtD,EAAE,CAAAkZ,eAAA;UAAFlZ,EAAE,CAAA4W,cAAA,kBAogC9E,CAAC;UApgC2E5W,EAAE,CAAAmZ,YAAA,EAqgCpF,CAAC;UArgCiFnZ,EAAE,CAAAwD,SAAA,gBAsgCpF,CAAC;UAtgCiFxD,EAAE,CAAA8W,YAAA,CAugC7F,CAAC;QAAA;MAAA;MAAAY,YAAA,GACmpbU,UAAU;MAAArB,MAAA;MAAAC,eAAA;IAAA,EAAkF;EAAE;AAC3vb;AACA;EAAA,QAAA/R,SAAA,oBAAAA,SAAA,KA1gCoGjF,EAAE,CAAAkF,iBAAA,CA0gCXqT,WAAW,EAAc,CAAC;IACzGxT,IAAI,EAAE9D,SAAS;IACfkE,IAAI,EAAE,CAAC;MAAEE,QAAQ,EAAE,sCAAsC;MAAE0T,QAAQ,EAAE,aAAa;MAAEpB,OAAO,EAAE,CAACS,UAAU,CAAC;MAAE1B,QAAQ,EAAE;AACjI;AACA;AACA;AACA;AACA,GAAG;MAAEM,eAAe,EAAE9V,uBAAuB,CAAC+V,MAAM;MAAEvJ,SAAS,EAAE,CACzC;QAAEH,OAAO,EAAErF,YAAY;QAAEsF,WAAW,EAAE+K;MAAY,CAAC,EACnDnS,eAAe,CAClB;MAAE2Q,MAAM,EAAE,CAAC,klbAAklb;IAAE,CAAC;EAC7mb,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMqC,cAAc,SAASzP,eAAe,CAAC;EACzCnF,WAAWA,CAAA,EAAG;IACV;IACA;IACAzD,MAAM,CAAC,MAAM;MACT,MAAMiG,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC,CAAC;MAC9C,MAAML,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAAC,CAAC;MAC1D,MAAMO,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC,CAAC;MAC1C,MAAMmS,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC,CAAC;MAC1C,MAAMC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAAC,CAAC;MACtD,MAAMC,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC,CAAC;MACtCzY,SAAS,CAAC,MAAM;QACZ,IAAI,CAAC,IAAI,CAAC0Y,QAAQ,EAAE;UAChB,MAAM/G,KAAK,GAAG4G,aAAa,IAAIC,mBAAmB,IAAIC,WAAW;UACjE,IAAI9G,KAAK,EAAE;YACPD,OAAO,CAACC,KAAK,CAACA,KAAK,CAAC;UACxB,CAAC,MACI;YACD,IAAI,CAACgH,UAAU,CAACzS,eAAe,EAAEL,qBAAqB,EAAEO,aAAa,CAAC;UAC1E;QACJ;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,KAAK,CAAC,CAAC;IACP,IAAI,CAACwS,QAAQ,GAAGzZ,MAAM,CAACmB,SAAS,CAAC;IACjC,IAAI,CAACuY,MAAM,GAAG1Z,MAAM,CAACoB,cAAc,CAAC;IACpC,IAAI,CAACoX,WAAW,GAAGrY,MAAM,CAAC,IAAI,CAAC;IAC/B;AACR;AACA;IACQ,IAAI,CAACwZ,gBAAgB,GAAGpZ,KAAK,CAAC,CAAC;IAC/B;AACR;AACA;IACQ,IAAI,CAACqZ,sBAAsB,GAAGrZ,KAAK,CAAC,CAAC;IACrC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACsZ,cAAc,GAAGtZ,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACwG,eAAe,GAAG1F,YAAY,CAAC;MAChC6S,MAAM,EAAE,IAAI,CAACyF,gBAAgB;MAC7B;MACAG,WAAW,EAAG1U,QAAQ,IAAK,IAAI,CAAC2U,UAAU,CAAC3U,QAAQ,CAAC,IAAI,IAAI,CAAC4U,cAAc,CAAC,CAAC,EAAExV;IACnF,CAAC,CAAC;IACF,IAAI,CAAC4U,aAAa,GAAG1Y,QAAQ,CAAC,MAAM;MAChC,OAAO,CAAC,IAAI,CAACqG,eAAe,CAAC,CAAC,GACxB,iFAAiF,IAAI,CAAC4S,gBAAgB,CAAC,CAAC,GAAG,GAC3G,IAAI;IACd,CAAC,CAAC;IACF,IAAI,CAACjT,qBAAqB,GAAGrF,YAAY,CAAC;MACtC6S,MAAM,EAAE,IAAI,CAAC0F,sBAAsB;MACnCE,WAAW,EAAG1U,QAAQ,IAAK,IAAI,CAAC2U,UAAU,CAAC3U,QAAQ;IACvD,CAAC,CAAC;IACF,IAAI,CAACiU,mBAAmB,GAAG3Y,QAAQ,CAAC,MAAM;MACtC,OAAO,CAAC,IAAI,CAACgG,qBAAqB,CAAC,CAAC,IAAI,IAAI,CAACkT,sBAAsB,CAAC,CAAC,GAC/D,+EAA+E,IAAI,CAACA,sBAAsB,CAAC,CAAC,GAAG,GAC/G,IAAI;IACd,CAAC,CAAC;IACF,IAAI,CAAC3S,aAAa,GAAG5F,YAAY,CAAC;MAC9B6S,MAAM,EAAE,IAAI,CAAC2F,cAAc;MAC3BC,WAAW,EAAG1U,QAAQ,IAAK,IAAI,CAAC2U,UAAU,CAAC3U,QAAQ;IACvD,CAAC,CAAC;IACF,IAAI,CAACkU,WAAW,GAAG5Y,QAAQ,CAAC,MAAM;MAC9B,OAAO,CAAC,IAAI,CAACuG,aAAa,CAAC,CAAC,IAAI,IAAI,CAAC4S,cAAc,CAAC,CAAC,GAC/C,sEAAsE,IAAI,CAACA,cAAc,CAAC,CAAC,GAAG,GAC9F,IAAI;IACd,CAAC,CAAC;IACF;AACR;AACA;IACQ,IAAI,CAACG,cAAc,GAAG1Y,YAAY,CAACgD,cAAc,EAAE;MAAE2V,WAAW,EAAE;IAAK,CAAC,CAAC;EAC7E;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACT,MAAM,CAACU,UAAU,CAAC,IAAI,CAACD,cAAc,CAACE,QAAQ,CAAC;MACpD,IAAI,CAACF,cAAc,CAACG,OAAO,CAAC,CAAC;IACjC;EACJ;EACAd,UAAUA,CAACzS,eAAe,EAAEL,qBAAqB,EAAEO,aAAa,EAAE;IAC9D,IAAI,IAAI,CAACsS,QAAQ,EAAE;MACf;MACA,IAAI,CAACxS,eAAe,CAACQ,GAAG,CAACR,eAAe,CAAC;MACzC,IAAI,CAACL,qBAAqB,CAACa,GAAG,CAACb,qBAAqB,CAAC;MACrD,IAAI,CAACO,aAAa,CAACM,GAAG,CAACN,aAAa,CAAC;IACzC;IACA;IACA,IAAI,CAACA,aAAa,IAAI,CAACP,qBAAqB,EAAE;MAC1CA,qBAAqB,GAAG,IAAI,CAAC+S,QAAQ,CAAC3H,aAAa,CAAC,KAAK,CAAC;MAC1D;MACA,MAAMyI,UAAU,GAAGC,KAAK,CAACjY,IAAI,CAACwE,eAAe,CAACwT,UAAU,CAAC;MACzDA,UAAU,CAACE,OAAO,CAAEC,IAAI,IAAK,IAAI,CAACjB,QAAQ,CAACnH,WAAW,CAAC5L,qBAAqB,EAAEgU,IAAI,CAAC,CAAC;MACpF;MACA,IAAI,CAACjB,QAAQ,CAACnH,WAAW,CAACvL,eAAe,EAAEL,qBAAqB,CAAC;IACrE;IACA;IACA,IAAIA,qBAAqB,EAAE;MACvB;MACA,IAAI,CAAC0D,QAAQ,CAACtD,IAAI,CAACC,eAAe,EAAEL,qBAAqB,EAAEO,aAAa,CAAC;MACzE;MACA,IAAI,CAAC0T,iBAAiB,CAAC,CAAC;IAC5B;EACJ;EACAA,iBAAiBA,CAAA,EAAG;IAChB;IACA,IAAI,CAACR,cAAc,GAAG5Y,eAAe,CAAC4W,UAAU,EAAE;MAC9CyC,mBAAmB,EAAE,IAAI,CAAClB,MAAM,CAACmB,QAAQ;MACzCC,eAAe,EAAEtZ,QAAQ,CAACuZ,MAAM,CAAC;QAAEtN,SAAS,EAAE,CAAC;UAAEH,OAAO,EAAErF,YAAY;UAAE+S,QAAQ,EAAE;QAAK,CAAC;MAAE,CAAC;IAC/F,CAAC,CAAC;IACF;IACA,IAAI,CAACvB,QAAQ,CAACnH,WAAW,CAAC,IAAI,CAAClI,QAAQ,CAAC1D,qBAAqB,EAAE,IAAI,CAACyT,cAAc,CAACc,QAAQ,CAACzW,aAAa,CAAC;IAC1G;IACA,IAAI,CAACkV,MAAM,CAACwB,UAAU,CAAC,IAAI,CAACf,cAAc,CAACE,QAAQ,CAAC;IACpD;IACA,IAAI,CAAC7B,WAAW,CAACjR,GAAG,CAAC,IAAI,CAAC4S,cAAc,CAACgB,QAAQ,CAAC;EACtD;EACApB,UAAUA,CAAC3U,QAAQ,EAAE;IACjB,OAAOA,QAAQ,GAAG,IAAI,CAACZ,aAAa,CAAC4W,aAAa,CAAChW,QAAQ,CAAC,GAAG,IAAI;EACvE;EACA;IAAS,IAAI,CAACX,IAAI,YAAA4W,uBAAA1W,iBAAA;MAAA,YAAAA,iBAAA,IAAwFwU,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAAChD,IAAI,kBAjpC8EpW,EAAE,CAAAqW,iBAAA;MAAAtR,IAAA,EAipCJqU,cAAc;MAAApU,SAAA;MAAAuW,cAAA,WAAAC,8BAAAlY,EAAA,EAAAC,GAAA,EAAAkY,QAAA;QAAA,IAAAnY,EAAA;UAjpCZtD,EAAE,CAAA0b,oBAAA,CAAAD,QAAA,EAAAlY,GAAA,CAAA0W,cAAA,EAqpCvB1V,cAAc;QAAA;QAAA,IAAAjB,EAAA;UArpCOtD,EAAE,CAAA8Y,cAAA;QAAA;MAAA;MAAA6C,SAAA,sBAipCwlB,MAAM;MAAA9O,QAAA;MAAAC,YAAA,WAAA8O,4BAAAtY,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjpChmBtD,EAAE,CAAAkN,WAAA,mCAipCJ,IAAa,CAAC;QAAA;MAAA;MAAAC,MAAA;QAAAyM,gBAAA;QAAAC,sBAAA;QAAAC,cAAA;MAAA;MAAAf,QAAA;MAAA1L,QAAA,GAjpCZrN,EAAE,CAAAsN,kBAAA,CAipC+qB,CACzwB;QAAEC,OAAO,EAAErF,YAAY;QAAEsF,WAAW,EAAE4L;MAAe,CAAC,EACtD;QAAE7L,OAAO,EAAE5D,eAAe;QAAE6D,WAAW,EAAE+K;MAAY,CAAC,EACtDnS,eAAe,CAClB,GArpC2FpG,EAAE,CAAAuQ,0BAAA;MAAA+F,KAAA,EAAAjS,GAAA;MAAA2U,kBAAA,EAAA5U,GAAA;MAAAmS,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAAmF,wBAAAvY,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFtD,EAAE,CAAAkZ,eAAA;UAAFlZ,EAAE,CAAAmZ,YAAA,EAqpCsH,CAAC;QAAA;MAAA;MAAApC,MAAA,GAAAzS,GAAA;MAAA0S,eAAA;IAAA,EAAsqb;EAAE;AACr4b;AACA;EAAA,QAAA/R,SAAA,oBAAAA,SAAA,KAvpCoGjF,EAAE,CAAAkF,iBAAA,CAupCXkU,cAAc,EAAc,CAAC;IAC5GrU,IAAI,EAAE9D,SAAS;IACfkE,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE,gCAAgC;MAAE0T,QAAQ,EAAE,aAAa;MAAErC,QAAQ,EAAE,eAAe;MAAEM,eAAe,EAAE9V,uBAAuB,CAAC+V,MAAM;MAAExJ,IAAI,EAAE;QACtK;QACA;QACA;QACAqO,eAAe,EAAE,MAAM;QACvB,wCAAwC,EAAE;MAC9C,CAAC;MAAEpO,SAAS,EAAE,CACV;QAAEH,OAAO,EAAErF,YAAY;QAAEsF,WAAW,EAAE4L;MAAe,CAAC,EACtD;QAAE7L,OAAO,EAAE5D,eAAe;QAAE6D,WAAW,EAAE+K;MAAY,CAAC,EACtDnS,eAAe,CAClB;MAAE2Q,MAAM,EAAE,CAAC,klbAAklb;IAAE,CAAC;EAC7mb,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMgF,cAAc,CAAC;EACjBvX,WAAWA,CAAA,EAAG;IACV,IAAI,CAACwX,SAAS,GAAG/b,MAAM,CAACmZ,cAAc,EAAE;MAAE9O,IAAI,EAAE;IAAK,CAAC,CAAC;IACvD,IAAI,CAACR,IAAI,GAAG7J,MAAM,CAACM,MAAM,CAAC;IAC1B,IAAI,CAAC0b,eAAe,GAAGhc,MAAM,CAACkD,eAAe,CAAC;IAC9C,IAAI,CAAC+Y,cAAc,GAAG1b,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACwb,SAAS,CAACxC,QAAQ,GAAG,IAAI;IAC9B,IAAI2C,IAAI;IACRpb,MAAM,CAAE6K,SAAS,IAAK;MAClB,MAAM7E,IAAI,GAAG,IAAI,CAACiV,SAAS,CAAC3R,QAAQ,CAAChE,WAAW,CAAC,CAAC;MAClD,MAAMuT,gBAAgB,GAAG,IAAI,CAACoC,SAAS,CAACpC,gBAAgB,CAAC,CAAC;MAC1D,MAAMC,sBAAsB,GAAG,IAAI,CAACmC,SAAS,CAACnC,sBAAsB,CAAC,CAAC;MACtE,MAAMC,cAAc,GAAG,IAAI,CAACkC,SAAS,CAAClC,cAAc,CAAC,CAAC;MACtD,MAAMoC,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC;MAC5Cpb,SAAS,CAAC,MAAM;QACZ,IAAIkG,eAAe;QACnB,IAAIL,qBAAqB;QACzB,IAAI,CAACmD,IAAI,CAACoC,iBAAiB,CAAC,MAAM;UAC9B;UACA;UACAiQ,IAAI,GAAG,IAAI,CAACF,eAAe,CAAC9P,OAAO,CAAC,IAAI,CAAC6P,SAAS,CAACvX,aAAa,CAAC,CAACuB,IAAI,CAACnE,YAAY,CAAC,GAAG,EAAE,IAAI,EAAE;YAC3FoE,OAAO,EAAE,IAAI;YACbC,QAAQ,EAAE;UACd,CAAC,CAAC,CAAC,CAACkG,SAAS,CAAC,MAAM;YAChB;YACApF,eAAe,GAAG,IAAI,CAACgV,SAAS,CAACvX,aAAa,CAAC4W,aAAa,CAACzB,gBAAgB,CAAC;YAC9E;YACAjT,qBAAqB,GAAG,IAAI,CAACqV,SAAS,CAACvX,aAAa,CAAC4W,aAAa,CAACxB,sBAAsB,CAAC;YAC1F,IAAI,CAAC/P,IAAI,CAACuC,GAAG,CAAC,MAAM;cAChB,IAAI,CAACtF,IAAI,IAAIC,eAAe,IAAIL,qBAAqB,EAAE;gBACnD;gBACA,IAAIO,aAAa;gBACjB,IAAI4S,cAAc,EAAE;kBAChB5S,aAAa,GAAG,IAAI,CAAC8U,SAAS,CAACvX,aAAa,CAAC4W,aAAa,CAACvB,cAAc,CAAC;gBAC9E;gBACA,IAAI,CAACkC,SAAS,CAACvC,UAAU,CAACzS,eAAe,EAAEL,qBAAqB,EAAEO,aAAa,CAAC;cACpF,CAAC,MACI,IAAI,CAACF,eAAe,IAAI,CAACL,qBAAqB,EAAE;gBACjD,IAAI,CAACqV,SAAS,CAAC3R,QAAQ,CAAC5C,KAAK,CAAC,CAAC;cACnC;YACJ,CAAC,CAAC;YACF,IAAIyU,cAAc,KAAK,MAAM,EAAE;cAC3BC,IAAI,CAAC3P,WAAW,CAAC,CAAC;YACtB;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;QACFZ,SAAS,CAAC,MAAMuQ,IAAI,EAAE3P,WAAW,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC9H,IAAI,YAAA0X,uBAAAxX,iBAAA;MAAA,YAAAA,iBAAA,IAAwFmX,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAAClX,IAAI,kBAztC8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EAytCJgX,cAAc;MAAA/W,SAAA;MAAAmI,MAAA;QAAA+O,cAAA;MAAA;IAAA,EAAgQ;EAAE;AAClX;AACA;EAAA,QAAAjX,SAAA,oBAAAA,SAAA,KA3tCoGjF,EAAE,CAAAkF,iBAAA,CA2tCX6W,cAAc,EAAc,CAAC;IAC5GhX,IAAI,EAAE5E,SAAS;IACfgF,IAAI,EAAE,CAAC;MACCC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMgX,UAAU,CAAC;EACb7X,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqF,oBAAoB,GAAG5J,MAAM,CAAC6C,oBAAoB,CAAC;IACxD,IAAI,CAACkZ,SAAS,GAAG/b,MAAM,CAACmZ,cAAc,EAAE;MAAE9O,IAAI,EAAE;IAAK,CAAC,CAAC;IACvD,IAAI,CAACR,IAAI,GAAG7J,MAAM,CAACM,MAAM,CAAC;IAC1B;AACR;AACA;IACQ,IAAI,CAAC+b,eAAe,GAAGlc,MAAM,CAAC,CAAC,CAAC,CAAC;IACjC,IAAI+b,IAAI;IACRpb,MAAM,CAAE6K,SAAS,IAAK;MAClB,MAAM1E,aAAa,GAAG,IAAI,CAAC8U,SAAS,CAAC9U,aAAa,CAAC,CAAC;MACpD,MAAMP,qBAAqB,GAAG,IAAI,CAACqV,SAAS,CAACrV,qBAAqB,CAAC,CAAC;MACpE,MAAMkF,gBAAgB,GAAG,IAAI,CAACmQ,SAAS,CAACrT,kBAAkB,CAAC,CAAC;MAC5D,MAAMC,aAAa,GAAG,IAAI,CAACoT,SAAS,CAACpT,aAAa,CAAC,CAAC;MACpD9H,SAAS,CAAC,MAAM;QACZ,IAAI,CAAC8H,aAAa,IAAIjC,qBAAqB,IAAIO,aAAa,EAAE;UAC1D;UACA,IAAI,CAAC4C,IAAI,CAACoC,iBAAiB,CAAC,MAAM;YAC9BiQ,IAAI,GAAGtW,kBAAkB,CAAC,IAAI,CAACgE,oBAAoB,CAACsC,OAAO,CAACxF,qBAAqB,CAAC,EAAEkF,gBAAgB,CAAC,CAAC7F,IAAI,CAAChE,GAAG,CAAEsH,OAAO,IAAKD,mBAAmB,CAACC,OAAO,EAAE3C,qBAAqB,CAAC,CAAC,CAAC,CAACyF,SAAS,CAAC,MAAM;cAC9L,IAAI,CAACtC,IAAI,CAACuC,GAAG,CAAC,MAAM;gBAChB;gBACAN,qBAAqB,CAAC,MAAM;kBACxB,IAAI,CAACuQ,eAAe,CAAC9U,GAAG,CAAC;oBACrBqD,KAAK,EAAElE,qBAAqB,CAACJ,WAAW;oBACxCuE,MAAM,EAAEnE,qBAAqB,CAACL;kBAClC,CAAC,CAAC;gBACN,CAAC,CAAC;cACN,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,CAAC;QACN;QACAsF,SAAS,CAAC,MAAMuQ,IAAI,EAAE3P,WAAW,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC9H,IAAI,YAAA6X,mBAAA3X,iBAAA;MAAA,YAAAA,iBAAA,IAAwFyX,UAAU;IAAA,CAAmD;EAAE;EACzK;IAAS,IAAI,CAACxX,IAAI,kBAxwC8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EAwwCJsX,UAAU;MAAArX,SAAA;MAAA6H,QAAA;MAAAC,YAAA,WAAA0P,wBAAAlZ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxwCRtD,EAAE,CAAAiN,WAAA,mBAwwCJ1J,GAAA,CAAA+Y,eAAA,CAAgB,CAAC,CAAAzR,KAAR,CAAC,oBAAVtH,GAAA,CAAA+Y,eAAA,CAAgB,CAAC,CAAAxR,MAAR,CAAC;QAAA;MAAA;IAAA,EAA+N;EAAE;AAC7U;AACA;EAAA,QAAA7F,SAAA,oBAAAA,SAAA,KA1wCoGjF,EAAE,CAAAkF,iBAAA,CA0wCXmX,UAAU,EAAc,CAAC;IACxGtX,IAAI,EAAE5E,SAAS;IACfgF,IAAI,EAAE,CAAC;MACCC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,4CAA4C;MACtDoI,IAAI,EAAE;QACF,wBAAwB,EAAE,yBAAyB;QACnD,yBAAyB,EAAE;MAC/B;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMgP,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAAC/X,IAAI,YAAAgY,0BAAA9X,iBAAA;MAAA,YAAAA,iBAAA,IAAwF6X,iBAAiB;IAAA,CAAkD;EAAE;EAC/K;IAAS,IAAI,CAACE,IAAI,kBAxxC8E3c,EAAE,CAAA4c,gBAAA;MAAA7X,IAAA,EAwxCS0X;IAAiB,EAQtG;EAAE;EACxB;IAAS,IAAI,CAACI,IAAI,kBAjyC8E7c,EAAE,CAAA8c,gBAAA,IAiyC6B;EAAE;AACrI;AACA;EAAA,QAAA7X,SAAA,oBAAAA,SAAA,KAnyCoGjF,EAAE,CAAAkF,iBAAA,CAmyCXuX,iBAAiB,EAAc,CAAC;IAC/G1X,IAAI,EAAErD,QAAQ;IACdyD,IAAI,EAAE,CAAC;MACCwS,OAAO,EAAE,CACLY,WAAW,EACXhU,cAAc,EACd6U,cAAc,EACd2C,cAAc,EACdM,UAAU,CACb;MACDU,OAAO,EAAE,CACLxE,WAAW,EACXhU,cAAc,EACd6U,cAAc,EACd2C,cAAc,EACdM,UAAU;IAElB,CAAC;EACT,CAAC,CAAC;AAAA;AACV,SAASW,uBAAuBA,CAACpT,OAAO,EAAE;EACtC,OAAO,CACH;IACI2D,OAAO,EAAErE,oBAAoB;IAC7B+R,QAAQ,EAAE;MAAE,GAAG9S,cAAc;MAAE,GAAGyB;IAAQ;EAC9C,CAAC,CACJ;AACL;AACA,SAASqT,wBAAwBA,CAACC,GAAG,EAAE;EACnC,OAAOvb,wBAAwB,CAAC,CAC5B;IACI4L,OAAO,EAAEnE,qBAAqB;IAC9B6R,QAAQ,EAAEiC;EACd,CAAC,CACJ,CAAC;AACN;;AAEA;AACA;AACA;;AAEA,SAASnB,cAAc,EAAE7T,YAAY,EAAEgB,oBAAoB,EAAEE,qBAAqB,EAAEmP,WAAW,EAAEa,cAAc,EAAEqD,iBAAiB,EAAElY,cAAc,EAAE0E,qBAAqB,EAAEoT,UAAU,EAAEhT,mBAAmB,EAAE2T,uBAAuB,EAAEC,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}