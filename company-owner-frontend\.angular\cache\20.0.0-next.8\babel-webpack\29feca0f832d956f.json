{"ast": null, "code": "import { SharedModule } from 'src/app/theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../theme/shared/components/card/card.component\";\nexport default class SamplePageComponent {\n  static {\n    this.ɵfac = function SamplePageComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SamplePageComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SamplePageComponent,\n      selectors: [[\"app-sample-page\"]],\n      decls: 5,\n      vars: 0,\n      consts: [[1, \"row\"], [1, \"col-sm-12\"], [\"cardTitle\", \"Hello Card\"]],\n      template: function SamplePageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"app-card\", 2)(3, \"p\");\n          i0.ɵɵtext(4, \" \\\"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\\\" \");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      dependencies: [SharedModule, i1.CardComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "SamplePageComponent", "selectors", "decls", "vars", "consts", "template", "SamplePageComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "i1", "CardComponent", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\extra\\sample-page\\sample-page.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\extra\\sample-page\\sample-page.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\r\n\r\n@Component({\r\n  selector: 'app-sample-page',\r\n  standalone: true,\r\n  imports: [SharedModule],\r\n  templateUrl: './sample-page.component.html',\r\n  styleUrls: ['./sample-page.component.scss']\r\n})\r\nexport default class SamplePageComponent {}\r\n", "<div class=\"row\">\r\n  <div class=\"col-sm-12\">\r\n    <app-card cardTitle=\"Hello Card\">\r\n      <p>\r\n        \"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim\r\n        ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in\r\n        reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt\r\n        in culpa qui officia deserunt mollit anim id est laborum.\"\r\n      </p>\r\n    </app-card>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,oCAAoC;;;AASjE,eAAc,MAAOC,mBAAmB;;;uCAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPlCE,EAHN,CAAAC,cAAA,aAAiB,aACQ,kBACY,QAC5B;UACDD,EAAA,CAAAE,MAAA,0cAIF;UAGNF,EAHM,CAAAG,YAAA,EAAI,EACK,EACP,EACF;;;qBDLMb,YAAY,EAAAc,EAAA,CAAAC,aAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}