{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AdminComponent } from './theme/layout/admin/admin.component';\nimport { GuestComponent } from './theme/layout/guest/guest.component';\nimport { AuthGuard } from './core/guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: AdminComponent,\n  children: [{\n    path: '',\n    redirectTo: 'dashboard',\n    pathMatch: 'full'\n  }, {\n    path: 'basic',\n    loadChildren: () => import('./demo/ui-elements/ui-basic/ui-basic.module').then(m => m.UiBasicModule)\n  }, {\n    path: 'forms',\n    loadChildren: () => import('./demo/pages/form-elements/form-elements.module').then(m => m.FormElementsModule)\n  }, {\n    path: 'tables',\n    loadChildren: () => import('./demo/pages/tables/tables.module').then(m => m.TablesModule)\n  }, {\n    path: 'apexchart',\n    loadComponent: () => import('./demo/pages/core-chart/apex-chart/apex-chart.component')\n  }, {\n    path: 'sample-page',\n    loadComponent: () => import('./demo/extra/sample-page/sample-page.component')\n  }, {\n    path: 'auth',\n    loadChildren: () => import('./demo/pages/authentication/authentication.module').then(m => m.AuthenticationModule)\n  }, {\n    path: 'dashboard',\n    loadChildren: () => import('./demo/dashboard/dashboard.component').then(m => m.DashboardComponent),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'questions',\n    loadChildren: () => import('./demo/pages/questions/question.module').then(m => m.QuestionModule),\n    canActivate: [AuthGuard],\n    data: {\n      roles: ['ProductOwner']\n    }\n  }\n  // {\n  //   path: 'company-questions',\n  //   loadChildren: () => import('./demo/pages/company-questions/company-questions.module').then(m => m.CompanyQuestionsModule),\n  //   canActivate: [AuthGuard],\n  //   data: { roles: ['ProductOwner'] }\n  // }\n  ]\n}, {\n  path: '',\n  component: GuestComponent,\n  children: [{\n    path: 'auth',\n    loadChildren: () => import('./demo/pages/authentication/authentication.module').then(m => m.AuthenticationModule)\n  }]\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AdminComponent", "GuestComponent", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "component", "children", "redirectTo", "pathMatch", "loadChildren", "then", "m", "UiBasicModule", "FormElementsModule", "TablesModule", "loadComponent", "AuthenticationModule", "DashboardComponent", "canActivate", "QuestionModule", "data", "roles", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\nimport { AdminComponent } from './theme/layout/admin/admin.component';\r\nimport { GuestComponent } from './theme/layout/guest/guest.component';\r\nimport { AuthGuard } from './core/guards/auth.guard';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: AdminComponent,\r\n    children: [\r\n      {\r\n        path: '',\r\n        redirectTo: 'dashboard',\r\n        pathMatch: 'full'\r\n      },\r\n      {\r\n        path: 'basic',\r\n        loadChildren: () => import('./demo/ui-elements/ui-basic/ui-basic.module').then((m) => m.UiBasicModule)\r\n      },\r\n      {\r\n        path: 'forms',\r\n        loadChildren: () => import('./demo/pages/form-elements/form-elements.module').then((m) => m.FormElementsModule)\r\n      },\r\n      {\r\n        path: 'tables',\r\n        loadChildren: () => import('./demo/pages/tables/tables.module').then((m) => m.TablesModule)\r\n      },\r\n      {\r\n        path: 'apexchart',\r\n        loadComponent: () => import('./demo/pages/core-chart/apex-chart/apex-chart.component')\r\n      },\r\n      {\r\n        path: 'sample-page',\r\n        loadComponent: () => import('./demo/extra/sample-page/sample-page.component')\r\n      },\r\n\r\n      {\r\n        path: 'auth',\r\n        loadChildren: () => import('./demo/pages/authentication/authentication.module').then(m => m.AuthenticationModule)\r\n      },\r\n\r\n      {\r\n        path: 'dashboard',\r\n        loadChildren: () => import('./demo/dashboard/dashboard.component').then(m => m.DashboardComponent),\r\n        canActivate: [AuthGuard]\r\n      },\r\n\r\n      {\r\n        path: 'questions',\r\n        loadChildren: () => import('./demo/pages/questions/question.module').then(m => m.QuestionModule),\r\n        canActivate: [AuthGuard],\r\n        data: { roles: ['ProductOwner'] }\r\n      },\r\n      // {\r\n      //   path: 'company-questions',\r\n      //   loadChildren: () => import('./demo/pages/company-questions/company-questions.module').then(m => m.CompanyQuestionsModule),\r\n      //   canActivate: [AuthGuard],\r\n      //   data: { roles: ['ProductOwner'] }\r\n      // }\r\n    ]\r\n  },\r\n  {\r\n    path: '',\r\n    component: GuestComponent,\r\n    children: [\r\n      {\r\n        path: 'auth',\r\n        loadChildren: () => import('./demo/pages/authentication/authentication.module').then((m) => m.AuthenticationModule)\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AppRoutingModule { }"], "mappings": "AACA,SAAiBA,YAAY,QAAQ,iBAAiB;AACtD,SAASC,cAAc,QAAQ,sCAAsC;AACrE,SAASC,cAAc,QAAQ,sCAAsC;AACrE,SAASC,SAAS,QAAQ,0BAA0B;;;AAEpD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEL,cAAc;EACzBM,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRG,UAAU,EAAE,WAAW;IACvBC,SAAS,EAAE;GACZ,EACD;IACEJ,IAAI,EAAE,OAAO;IACbK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6CAA6C,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,aAAa;GACtG,EACD;IACER,IAAI,EAAE,OAAO;IACbK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,iDAAiD,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACE,kBAAkB;GAC/G,EACD;IACET,IAAI,EAAE,QAAQ;IACdK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACG,YAAY;GAC3F,EACD;IACEV,IAAI,EAAE,WAAW;IACjBW,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,yDAAyD;GACtF,EACD;IACEX,IAAI,EAAE,aAAa;IACnBW,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,gDAAgD;GAC7E,EAED;IACEX,IAAI,EAAE,MAAM;IACZK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mDAAmD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,oBAAoB;GACjH,EAED;IACEZ,IAAI,EAAE,WAAW;IACjBK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,kBAAkB,CAAC;IAClGC,WAAW,EAAE,CAAChB,SAAS;GACxB,EAED;IACEE,IAAI,EAAE,WAAW;IACjBK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,cAAc,CAAC;IAChGD,WAAW,EAAE,CAAChB,SAAS,CAAC;IACxBkB,IAAI,EAAE;MAAEC,KAAK,EAAE,CAAC,cAAc;IAAC;;EAEjC;EACA;EACA;EACA;EACA;EACA;EAAA;CAEH,EACD;EACEjB,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ,cAAc;EACzBK,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,MAAM;IACZK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mDAAmD,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACK,oBAAoB;GACnH;CAEJ,CACF;AAMD,OAAM,MAAOM,gBAAgB;;;uCAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBvB,YAAY,CAACwB,OAAO,CAACpB,MAAM,CAAC,EAC5BJ,YAAY;IAAA;EAAA;;;2EAEXuB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAA1B,YAAA;IAAA2B,OAAA,GAFjB3B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}