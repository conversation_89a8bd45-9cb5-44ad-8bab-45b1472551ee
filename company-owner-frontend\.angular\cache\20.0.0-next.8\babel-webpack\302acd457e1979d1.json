{"ast": null, "code": "/* eslint-disable promise/prefer-await-to-then */\n\nconst methodMap = [['requestFullscreen', 'exitFullscreen', 'fullscreenElement', 'fullscreenEnabled', 'fullscreenchange', 'fullscreenerror'],\n// New WebKit\n['webkitRequestFullscreen', 'webkitExitFullscreen', 'webkitFullscreenElement', 'webkitFullscreenEnabled', 'webkitfullscreenchange', 'webkitfullscreenerror'],\n// Old WebKit\n['webkitRequestFullScreen', 'webkitCancelFullScreen', 'webkitCurrentFullScreenElement', 'webkitCancelFullScreen', 'webkitfullscreenchange', 'webkitfullscreenerror'], ['mozRequestFullScreen', 'mozCancelFullScreen', 'mozFullScreenElement', 'mozFullScreenEnabled', 'mozfullscreenchange', 'mozfullscreenerror'], ['msRequestFullscreen', 'msExitFullscreen', 'msFullscreenElement', 'msFullscreenEnabled', 'MSFullscreenChange', 'MSFullscreenError']];\nconst nativeAPI = (() => {\n  if (typeof document === 'undefined') {\n    return false;\n  }\n  const unprefixedMethods = methodMap[0];\n  const returnValue = {};\n  for (const methodList of methodMap) {\n    const exitFullscreenMethod = methodList?.[1];\n    if (exitFullscreenMethod in document) {\n      for (const [index, method] of methodList.entries()) {\n        returnValue[unprefixedMethods[index]] = method;\n      }\n      return returnValue;\n    }\n  }\n  return false;\n})();\nconst eventNameMap = {\n  change: nativeAPI.fullscreenchange,\n  error: nativeAPI.fullscreenerror\n};\n\n// eslint-disable-next-line import/no-mutable-exports\nlet screenfull = {\n  // eslint-disable-next-line default-param-last\n  request(element = document.documentElement, options) {\n    return new Promise((resolve, reject) => {\n      const onFullScreenEntered = () => {\n        screenfull.off('change', onFullScreenEntered);\n        resolve();\n      };\n      screenfull.on('change', onFullScreenEntered);\n      const returnPromise = element[nativeAPI.requestFullscreen](options);\n      if (returnPromise instanceof Promise) {\n        returnPromise.then(onFullScreenEntered).catch(reject);\n      }\n    });\n  },\n  exit() {\n    return new Promise((resolve, reject) => {\n      if (!screenfull.isFullscreen) {\n        resolve();\n        return;\n      }\n      const onFullScreenExit = () => {\n        screenfull.off('change', onFullScreenExit);\n        resolve();\n      };\n      screenfull.on('change', onFullScreenExit);\n      const returnPromise = document[nativeAPI.exitFullscreen]();\n      if (returnPromise instanceof Promise) {\n        returnPromise.then(onFullScreenExit).catch(reject);\n      }\n    });\n  },\n  toggle(element, options) {\n    return screenfull.isFullscreen ? screenfull.exit() : screenfull.request(element, options);\n  },\n  onchange(callback) {\n    screenfull.on('change', callback);\n  },\n  onerror(callback) {\n    screenfull.on('error', callback);\n  },\n  on(event, callback) {\n    const eventName = eventNameMap[event];\n    if (eventName) {\n      document.addEventListener(eventName, callback, false);\n    }\n  },\n  off(event, callback) {\n    const eventName = eventNameMap[event];\n    if (eventName) {\n      document.removeEventListener(eventName, callback, false);\n    }\n  },\n  raw: nativeAPI\n};\nObject.defineProperties(screenfull, {\n  isFullscreen: {\n    get: () => Boolean(document[nativeAPI.fullscreenElement])\n  },\n  element: {\n    enumerable: true,\n    get: () => document[nativeAPI.fullscreenElement] ?? undefined\n  },\n  isEnabled: {\n    enumerable: true,\n    // Coerce to boolean in case of old WebKit.\n    get: () => Boolean(document[nativeAPI.fullscreenEnabled])\n  }\n});\nif (!nativeAPI) {\n  screenfull = {\n    isEnabled: false\n  };\n}\nexport default screenfull;", "map": {"version": 3, "names": ["methodMap", "nativeAPI", "document", "unprefixedMethods", "returnValue", "methodList", "exitFullscreenMethod", "index", "method", "entries", "eventNameMap", "change", "fullscreenchange", "error", "fullscreenerror", "screenfull", "request", "element", "documentElement", "options", "Promise", "resolve", "reject", "onFullScreenEntered", "off", "on", "returnPromise", "requestFullscreen", "then", "catch", "exit", "isFullscreen", "onFullScreenExit", "exitFullscreen", "toggle", "onchange", "callback", "onerror", "event", "eventName", "addEventListener", "removeEventListener", "raw", "Object", "defineProperties", "get", "Boolean", "fullscreenElement", "enumerable", "undefined", "isEnabled", "fullscreenEnabled"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/screenfull/index.js"], "sourcesContent": ["/* eslint-disable promise/prefer-await-to-then */\n\nconst methodMap = [\n\t[\n\t\t'requestFullscreen',\n\t\t'exitFullscreen',\n\t\t'fullscreenElement',\n\t\t'fullscreenEnabled',\n\t\t'fullscreenchange',\n\t\t'fullscreenerror',\n\t],\n\t// New WebKit\n\t[\n\t\t'webkitRequestFullscreen',\n\t\t'webkitExitFullscreen',\n\t\t'webkitFullscreenElement',\n\t\t'webkitFullscreenEnabled',\n\t\t'webkitfullscreenchange',\n\t\t'webkitfullscreenerror',\n\n\t],\n\t// Old WebKit\n\t[\n\t\t'webkitRequestFullScreen',\n\t\t'webkitCancelFullScreen',\n\t\t'webkitCurrentFullScreenElement',\n\t\t'webkitCancelFullScreen',\n\t\t'webkitfullscreenchange',\n\t\t'webkitfullscreenerror',\n\n\t],\n\t[\n\t\t'mozRequestFullScreen',\n\t\t'mozCancelFullScreen',\n\t\t'mozFullScreenElement',\n\t\t'mozFullScreenEnabled',\n\t\t'mozfullscreenchange',\n\t\t'mozfullscreenerror',\n\t],\n\t[\n\t\t'msRequestFullscreen',\n\t\t'msExitFullscreen',\n\t\t'msFullscreenElement',\n\t\t'msFullscreenEnabled',\n\t\t'MSFullscreenChange',\n\t\t'MSFullscreenError',\n\t],\n];\n\nconst nativeAPI = (() => {\n\tif (typeof document === 'undefined') {\n\t\treturn false;\n\t}\n\n\tconst unprefixedMethods = methodMap[0];\n\tconst returnValue = {};\n\n\tfor (const methodList of methodMap) {\n\t\tconst exitFullscreenMethod = methodList?.[1];\n\t\tif (exitFullscreenMethod in document) {\n\t\t\tfor (const [index, method] of methodList.entries()) {\n\t\t\t\treturnValue[unprefixedMethods[index]] = method;\n\t\t\t}\n\n\t\t\treturn returnValue;\n\t\t}\n\t}\n\n\treturn false;\n})();\n\nconst eventNameMap = {\n\tchange: nativeAPI.fullscreenchange,\n\terror: nativeAPI.fullscreenerror,\n};\n\n// eslint-disable-next-line import/no-mutable-exports\nlet screenfull = {\n\t// eslint-disable-next-line default-param-last\n\trequest(element = document.documentElement, options) {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tconst onFullScreenEntered = () => {\n\t\t\t\tscreenfull.off('change', onFullScreenEntered);\n\t\t\t\tresolve();\n\t\t\t};\n\n\t\t\tscreenfull.on('change', onFullScreenEntered);\n\n\t\t\tconst returnPromise = element[nativeAPI.requestFullscreen](options);\n\n\t\t\tif (returnPromise instanceof Promise) {\n\t\t\t\treturnPromise.then(onFullScreenEntered).catch(reject);\n\t\t\t}\n\t\t});\n\t},\n\texit() {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tif (!screenfull.isFullscreen) {\n\t\t\t\tresolve();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst onFullScreenExit = () => {\n\t\t\t\tscreenfull.off('change', onFullScreenExit);\n\t\t\t\tresolve();\n\t\t\t};\n\n\t\t\tscreenfull.on('change', onFullScreenExit);\n\n\t\t\tconst returnPromise = document[nativeAPI.exitFullscreen]();\n\n\t\t\tif (returnPromise instanceof Promise) {\n\t\t\t\treturnPromise.then(onFullScreenExit).catch(reject);\n\t\t\t}\n\t\t});\n\t},\n\ttoggle(element, options) {\n\t\treturn screenfull.isFullscreen ? screenfull.exit() : screenfull.request(element, options);\n\t},\n\tonchange(callback) {\n\t\tscreenfull.on('change', callback);\n\t},\n\tonerror(callback) {\n\t\tscreenfull.on('error', callback);\n\t},\n\ton(event, callback) {\n\t\tconst eventName = eventNameMap[event];\n\t\tif (eventName) {\n\t\t\tdocument.addEventListener(eventName, callback, false);\n\t\t}\n\t},\n\toff(event, callback) {\n\t\tconst eventName = eventNameMap[event];\n\t\tif (eventName) {\n\t\t\tdocument.removeEventListener(eventName, callback, false);\n\t\t}\n\t},\n\traw: nativeAPI,\n};\n\nObject.defineProperties(screenfull, {\n\tisFullscreen: {\n\t\tget: () => Boolean(document[nativeAPI.fullscreenElement]),\n\t},\n\telement: {\n\t\tenumerable: true,\n\t\tget: () => document[nativeAPI.fullscreenElement] ?? undefined,\n\t},\n\tisEnabled: {\n\t\tenumerable: true,\n\t\t// Coerce to boolean in case of old WebKit.\n\t\tget: () => Boolean(document[nativeAPI.fullscreenEnabled]),\n\t},\n});\n\nif (!nativeAPI) {\n\tscreenfull = {isEnabled: false};\n}\n\nexport default screenfull;\n"], "mappings": "AAAA;;AAEA,MAAMA,SAAS,GAAG,CACjB,CACC,mBAAmB,EACnB,gBAAgB,EAChB,mBAAmB,EACnB,mBAAmB,EACnB,kBAAkB,EAClB,iBAAiB,CACjB;AACD;AACA,CACC,yBAAyB,EACzB,sBAAsB,EACtB,yBAAyB,EACzB,yBAAyB,EACzB,wBAAwB,EACxB,uBAAuB,CAEvB;AACD;AACA,CACC,yBAAyB,EACzB,wBAAwB,EACxB,gCAAgC,EAChC,wBAAwB,EACxB,wBAAwB,EACxB,uBAAuB,CAEvB,EACD,CACC,sBAAsB,EACtB,qBAAqB,EACrB,sBAAsB,EACtB,sBAAsB,EACtB,qBAAqB,EACrB,oBAAoB,CACpB,EACD,CACC,qBAAqB,EACrB,kBAAkB,EAClB,qBAAqB,EACrB,qBAAqB,EACrB,oBAAoB,EACpB,mBAAmB,CACnB,CACD;AAED,MAAMC,SAAS,GAAG,CAAC,MAAM;EACxB,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;IACpC,OAAO,KAAK;EACb;EAEA,MAAMC,iBAAiB,GAAGH,SAAS,CAAC,CAAC,CAAC;EACtC,MAAMI,WAAW,GAAG,CAAC,CAAC;EAEtB,KAAK,MAAMC,UAAU,IAAIL,SAAS,EAAE;IACnC,MAAMM,oBAAoB,GAAGD,UAAU,GAAG,CAAC,CAAC;IAC5C,IAAIC,oBAAoB,IAAIJ,QAAQ,EAAE;MACrC,KAAK,MAAM,CAACK,KAAK,EAAEC,MAAM,CAAC,IAAIH,UAAU,CAACI,OAAO,CAAC,CAAC,EAAE;QACnDL,WAAW,CAACD,iBAAiB,CAACI,KAAK,CAAC,CAAC,GAAGC,MAAM;MAC/C;MAEA,OAAOJ,WAAW;IACnB;EACD;EAEA,OAAO,KAAK;AACb,CAAC,EAAE,CAAC;AAEJ,MAAMM,YAAY,GAAG;EACpBC,MAAM,EAAEV,SAAS,CAACW,gBAAgB;EAClCC,KAAK,EAAEZ,SAAS,CAACa;AAClB,CAAC;;AAED;AACA,IAAIC,UAAU,GAAG;EAChB;EACAC,OAAOA,CAACC,OAAO,GAAGf,QAAQ,CAACgB,eAAe,EAAEC,OAAO,EAAE;IACpD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACvC,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;QACjCR,UAAU,CAACS,GAAG,CAAC,QAAQ,EAAED,mBAAmB,CAAC;QAC7CF,OAAO,CAAC,CAAC;MACV,CAAC;MAEDN,UAAU,CAACU,EAAE,CAAC,QAAQ,EAAEF,mBAAmB,CAAC;MAE5C,MAAMG,aAAa,GAAGT,OAAO,CAAChB,SAAS,CAAC0B,iBAAiB,CAAC,CAACR,OAAO,CAAC;MAEnE,IAAIO,aAAa,YAAYN,OAAO,EAAE;QACrCM,aAAa,CAACE,IAAI,CAACL,mBAAmB,CAAC,CAACM,KAAK,CAACP,MAAM,CAAC;MACtD;IACD,CAAC,CAAC;EACH,CAAC;EACDQ,IAAIA,CAAA,EAAG;IACN,OAAO,IAAIV,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACvC,IAAI,CAACP,UAAU,CAACgB,YAAY,EAAE;QAC7BV,OAAO,CAAC,CAAC;QACT;MACD;MAEA,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;QAC9BjB,UAAU,CAACS,GAAG,CAAC,QAAQ,EAAEQ,gBAAgB,CAAC;QAC1CX,OAAO,CAAC,CAAC;MACV,CAAC;MAEDN,UAAU,CAACU,EAAE,CAAC,QAAQ,EAAEO,gBAAgB,CAAC;MAEzC,MAAMN,aAAa,GAAGxB,QAAQ,CAACD,SAAS,CAACgC,cAAc,CAAC,CAAC,CAAC;MAE1D,IAAIP,aAAa,YAAYN,OAAO,EAAE;QACrCM,aAAa,CAACE,IAAI,CAACI,gBAAgB,CAAC,CAACH,KAAK,CAACP,MAAM,CAAC;MACnD;IACD,CAAC,CAAC;EACH,CAAC;EACDY,MAAMA,CAACjB,OAAO,EAAEE,OAAO,EAAE;IACxB,OAAOJ,UAAU,CAACgB,YAAY,GAAGhB,UAAU,CAACe,IAAI,CAAC,CAAC,GAAGf,UAAU,CAACC,OAAO,CAACC,OAAO,EAAEE,OAAO,CAAC;EAC1F,CAAC;EACDgB,QAAQA,CAACC,QAAQ,EAAE;IAClBrB,UAAU,CAACU,EAAE,CAAC,QAAQ,EAAEW,QAAQ,CAAC;EAClC,CAAC;EACDC,OAAOA,CAACD,QAAQ,EAAE;IACjBrB,UAAU,CAACU,EAAE,CAAC,OAAO,EAAEW,QAAQ,CAAC;EACjC,CAAC;EACDX,EAAEA,CAACa,KAAK,EAAEF,QAAQ,EAAE;IACnB,MAAMG,SAAS,GAAG7B,YAAY,CAAC4B,KAAK,CAAC;IACrC,IAAIC,SAAS,EAAE;MACdrC,QAAQ,CAACsC,gBAAgB,CAACD,SAAS,EAAEH,QAAQ,EAAE,KAAK,CAAC;IACtD;EACD,CAAC;EACDZ,GAAGA,CAACc,KAAK,EAAEF,QAAQ,EAAE;IACpB,MAAMG,SAAS,GAAG7B,YAAY,CAAC4B,KAAK,CAAC;IACrC,IAAIC,SAAS,EAAE;MACdrC,QAAQ,CAACuC,mBAAmB,CAACF,SAAS,EAAEH,QAAQ,EAAE,KAAK,CAAC;IACzD;EACD,CAAC;EACDM,GAAG,EAAEzC;AACN,CAAC;AAED0C,MAAM,CAACC,gBAAgB,CAAC7B,UAAU,EAAE;EACnCgB,YAAY,EAAE;IACbc,GAAG,EAAEA,CAAA,KAAMC,OAAO,CAAC5C,QAAQ,CAACD,SAAS,CAAC8C,iBAAiB,CAAC;EACzD,CAAC;EACD9B,OAAO,EAAE;IACR+B,UAAU,EAAE,IAAI;IAChBH,GAAG,EAAEA,CAAA,KAAM3C,QAAQ,CAACD,SAAS,CAAC8C,iBAAiB,CAAC,IAAIE;EACrD,CAAC;EACDC,SAAS,EAAE;IACVF,UAAU,EAAE,IAAI;IAChB;IACAH,GAAG,EAAEA,CAAA,KAAMC,OAAO,CAAC5C,QAAQ,CAACD,SAAS,CAACkD,iBAAiB,CAAC;EACzD;AACD,CAAC,CAAC;AAEF,IAAI,CAAClD,SAAS,EAAE;EACfc,UAAU,GAAG;IAACmC,SAAS,EAAE;EAAK,CAAC;AAChC;AAEA,eAAenC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}