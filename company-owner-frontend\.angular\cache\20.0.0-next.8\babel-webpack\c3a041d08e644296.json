{"ast": null, "code": "import { BehaviorSubject, throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthService {\n  constructor(http) {\n    this.http = http;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    // Load user from localStorage on startup\n    const user = this.getUserFromStorage();\n    if (user) {\n      this.currentUserSubject.next(user);\n      this.startRefreshTokenTimer();\n    }\n  }\n  get currentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  login(email, password) {\n    return this.http.post(`${environment.apiUrl}/product-owner-auth/login`, {\n      email,\n      password\n    }).pipe(map(response => {\n      // Store user details and tokens in local storage\n      this.storeUserData(response);\n      this.currentUserSubject.next(response.user);\n      this.startRefreshTokenTimer();\n      return response;\n    }), catchError(error => {\n      console.error('Login error:', error);\n      return throwError(() => error);\n    }));\n  }\n  logout() {\n    localStorage.removeItem('user');\n    localStorage.removeItem('tokens');\n    this.currentUserSubject.next(null);\n    this.stopRefreshTokenTimer();\n  }\n  refreshToken() {\n    const tokens = this.getTokensFromStorage();\n    if (!tokens?.refresh_token) {\n      return throwError(() => new Error('No refresh token available'));\n    }\n    return this.http.post(`${environment.apiUrl}/product-owner-auth/refresh-token`, {\n      refreshToken: tokens.refresh_token\n    }).pipe(map(response => {\n      this.storeUserData(response);\n      this.currentUserSubject.next(response.user);\n      this.startRefreshTokenTimer();\n      return response;\n    }), catchError(error => {\n      this.logout();\n      return throwError(() => error);\n    }));\n  }\n  isLoggedIn() {\n    return !!this.currentUserValue;\n  }\n  hasRole(role) {\n    return this.currentUserValue?.role === role;\n  }\n  getAccessToken() {\n    const tokens = this.getTokensFromStorage();\n    return tokens?.access_token || null;\n  }\n  storeUserData(response) {\n    localStorage.setItem('user', JSON.stringify(response.user));\n    localStorage.setItem('tokens', JSON.stringify({\n      access_token: response.access_token,\n      refresh_token: response.refresh_token\n    }));\n  }\n  getUserFromStorage() {\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n  }\n  getTokensFromStorage() {\n    const tokensStr = localStorage.getItem('tokens');\n    return tokensStr ? JSON.parse(tokensStr) : null;\n  }\n  startRefreshTokenTimer() {\n    const tokens = this.getTokensFromStorage();\n    if (!tokens?.access_token) return;\n    const jwtToken = JSON.parse(atob(tokens.access_token.split('.')[1]));\n    const expires = new Date(jwtToken.exp * 1000);\n    const timeout = expires.getTime() - Date.now() - 60 * 1000;\n    this.refreshTokenTimeout = setTimeout(() => this.refreshToken().subscribe(), timeout);\n  }\n  stopRefreshTokenTimer() {\n    clearTimeout(this.refreshTokenTimeout);\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "catchError", "map", "environment", "AuthService", "constructor", "http", "currentUserSubject", "currentUser$", "asObservable", "user", "getUserFromStorage", "next", "startRefreshTokenTimer", "currentUserValue", "value", "login", "email", "password", "post", "apiUrl", "pipe", "response", "storeUserData", "error", "console", "logout", "localStorage", "removeItem", "stopRefreshTokenTimer", "refreshToken", "tokens", "getTokensFromStorage", "refresh_token", "Error", "isLoggedIn", "hasRole", "role", "getAccessToken", "access_token", "setItem", "JSON", "stringify", "userStr", "getItem", "parse", "tokensStr", "jwtToken", "atob", "split", "expires", "Date", "exp", "timeout", "getTime", "now", "refreshTokenTimeout", "setTimeout", "subscribe", "clearTimeout", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["// src/app/core/services/auth.service.ts\r\nimport { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\r\nimport { catchError, map, tap } from 'rxjs/operators';\r\nimport { environment } from '../../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthService {\r\n  private currentUserSubject = new BehaviorSubject<any>(null);\r\n  public currentUser$ = this.currentUserSubject.asObservable();\r\n  private refreshTokenTimeout: any;\r\n\r\n  constructor(private http: HttpClient) {\r\n    // Load user from localStorage on startup\r\n    const user = this.getUserFromStorage();\r\n    if (user) {\r\n      this.currentUserSubject.next(user);\r\n      this.startRefreshTokenTimer();\r\n    }\r\n  }\r\n\r\n  public get currentUserValue(): any {\r\n    return this.currentUserSubject.value;\r\n  }\r\n\r\n  login(email: string, password: string): Observable<any> {\r\n    return this.http.post<any>(`${environment.apiUrl}/product-owner-auth/login`, { email, password })\r\n      .pipe(\r\n        map(response => {\r\n          // Store user details and tokens in local storage\r\n          this.storeUserData(response);\r\n          this.currentUserSubject.next(response.user);\r\n          this.startRefreshTokenTimer();\r\n          return response;\r\n        }),\r\n        catchError(error => {\r\n          console.error('Login error:', error);\r\n          return throwError(() => error);\r\n        })\r\n      );\r\n  }\r\n\r\n  logout() {\r\n    localStorage.removeItem('user');\r\n    localStorage.removeItem('tokens');\r\n    this.currentUserSubject.next(null);\r\n    this.stopRefreshTokenTimer();\r\n  }\r\n\r\n  refreshToken(): Observable<any> {\r\n    const tokens = this.getTokensFromStorage();\r\n    if (!tokens?.refresh_token) {\r\n      return throwError(() => new Error('No refresh token available'));\r\n    }\r\n\r\n    return this.http.post<any>(`${environment.apiUrl}/product-owner-auth/refresh-token`, {\r\n      refreshToken: tokens.refresh_token\r\n    }).pipe(\r\n      map(response => {\r\n        this.storeUserData(response);\r\n        this.currentUserSubject.next(response.user);\r\n        this.startRefreshTokenTimer();\r\n        return response;\r\n      }),\r\n      catchError(error => {\r\n        this.logout();\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  isLoggedIn(): boolean {\r\n    return !!this.currentUserValue;\r\n  }\r\n\r\n  hasRole(role: string): boolean {\r\n    return this.currentUserValue?.role === role;\r\n  }\r\n\r\n  getAccessToken(): string | null {\r\n    const tokens = this.getTokensFromStorage();\r\n    return tokens?.access_token || null;\r\n  }\r\n\r\n  private storeUserData(response: any) {\r\n    localStorage.setItem('user', JSON.stringify(response.user));\r\n    localStorage.setItem('tokens', JSON.stringify({\r\n      access_token: response.access_token,\r\n      refresh_token: response.refresh_token\r\n    }));\r\n  }\r\n\r\n  private getUserFromStorage(): any {\r\n    const userStr = localStorage.getItem('user');\r\n    return userStr ? JSON.parse(userStr) : null;\r\n  }\r\n\r\n  private getTokensFromStorage(): any {\r\n    const tokensStr = localStorage.getItem('tokens');\r\n    return tokensStr ? JSON.parse(tokensStr) : null;\r\n  }\r\n\r\n  private startRefreshTokenTimer() {\r\n    const tokens = this.getTokensFromStorage();\r\n    if (!tokens?.access_token) return;\r\n\r\n    const jwtToken = JSON.parse(atob(tokens.access_token.split('.')[1]));\r\n    const expires = new Date(jwtToken.exp * 1000);\r\n    \r\n    const timeout = expires.getTime() - Date.now() - (60 * 1000);\r\n    this.refreshTokenTimeout = setTimeout(() => this.refreshToken().subscribe(), timeout);\r\n  }\r\n\r\n  private stopRefreshTokenTimer() {\r\n    clearTimeout(this.refreshTokenTimeout);\r\n  }\r\n}"], "mappings": "AAGA,SAASA,eAAe,EAAcC,UAAU,QAAQ,MAAM;AAC9D,SAASC,UAAU,EAAEC,GAAG,QAAa,gBAAgB;AACrD,SAASC,WAAW,QAAQ,mCAAmC;;;AAK/D,OAAM,MAAOC,WAAW;EAKtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJhB,KAAAC,kBAAkB,GAAG,IAAIR,eAAe,CAAM,IAAI,CAAC;IACpD,KAAAS,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IAI1D;IACA,MAAMC,IAAI,GAAG,IAAI,CAACC,kBAAkB,EAAE;IACtC,IAAID,IAAI,EAAE;MACR,IAAI,CAACH,kBAAkB,CAACK,IAAI,CAACF,IAAI,CAAC;MAClC,IAAI,CAACG,sBAAsB,EAAE;IAC/B;EACF;EAEA,IAAWC,gBAAgBA,CAAA;IACzB,OAAO,IAAI,CAACP,kBAAkB,CAACQ,KAAK;EACtC;EAEAC,KAAKA,CAACC,KAAa,EAAEC,QAAgB;IACnC,OAAO,IAAI,CAACZ,IAAI,CAACa,IAAI,CAAM,GAAGhB,WAAW,CAACiB,MAAM,2BAA2B,EAAE;MAAEH,KAAK;MAAEC;IAAQ,CAAE,CAAC,CAC9FG,IAAI,CACHnB,GAAG,CAACoB,QAAQ,IAAG;MACb;MACA,IAAI,CAACC,aAAa,CAACD,QAAQ,CAAC;MAC5B,IAAI,CAACf,kBAAkB,CAACK,IAAI,CAACU,QAAQ,CAACZ,IAAI,CAAC;MAC3C,IAAI,CAACG,sBAAsB,EAAE;MAC7B,OAAOS,QAAQ;IACjB,CAAC,CAAC,EACFrB,UAAU,CAACuB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,OAAOxB,UAAU,CAAC,MAAMwB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEAE,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,QAAQ,CAAC;IACjC,IAAI,CAACrB,kBAAkB,CAACK,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACiB,qBAAqB,EAAE;EAC9B;EAEAC,YAAYA,CAAA;IACV,MAAMC,MAAM,GAAG,IAAI,CAACC,oBAAoB,EAAE;IAC1C,IAAI,CAACD,MAAM,EAAEE,aAAa,EAAE;MAC1B,OAAOjC,UAAU,CAAC,MAAM,IAAIkC,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE;IAEA,OAAO,IAAI,CAAC5B,IAAI,CAACa,IAAI,CAAM,GAAGhB,WAAW,CAACiB,MAAM,mCAAmC,EAAE;MACnFU,YAAY,EAAEC,MAAM,CAACE;KACtB,CAAC,CAACZ,IAAI,CACLnB,GAAG,CAACoB,QAAQ,IAAG;MACb,IAAI,CAACC,aAAa,CAACD,QAAQ,CAAC;MAC5B,IAAI,CAACf,kBAAkB,CAACK,IAAI,CAACU,QAAQ,CAACZ,IAAI,CAAC;MAC3C,IAAI,CAACG,sBAAsB,EAAE;MAC7B,OAAOS,QAAQ;IACjB,CAAC,CAAC,EACFrB,UAAU,CAACuB,KAAK,IAAG;MACjB,IAAI,CAACE,MAAM,EAAE;MACb,OAAO1B,UAAU,CAAC,MAAMwB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEAW,UAAUA,CAAA;IACR,OAAO,CAAC,CAAC,IAAI,CAACrB,gBAAgB;EAChC;EAEAsB,OAAOA,CAACC,IAAY;IAClB,OAAO,IAAI,CAACvB,gBAAgB,EAAEuB,IAAI,KAAKA,IAAI;EAC7C;EAEAC,cAAcA,CAAA;IACZ,MAAMP,MAAM,GAAG,IAAI,CAACC,oBAAoB,EAAE;IAC1C,OAAOD,MAAM,EAAEQ,YAAY,IAAI,IAAI;EACrC;EAEQhB,aAAaA,CAACD,QAAa;IACjCK,YAAY,CAACa,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACpB,QAAQ,CAACZ,IAAI,CAAC,CAAC;IAC3DiB,YAAY,CAACa,OAAO,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAC;MAC5CH,YAAY,EAAEjB,QAAQ,CAACiB,YAAY;MACnCN,aAAa,EAAEX,QAAQ,CAACW;KACzB,CAAC,CAAC;EACL;EAEQtB,kBAAkBA,CAAA;IACxB,MAAMgC,OAAO,GAAGhB,YAAY,CAACiB,OAAO,CAAC,MAAM,CAAC;IAC5C,OAAOD,OAAO,GAAGF,IAAI,CAACI,KAAK,CAACF,OAAO,CAAC,GAAG,IAAI;EAC7C;EAEQX,oBAAoBA,CAAA;IAC1B,MAAMc,SAAS,GAAGnB,YAAY,CAACiB,OAAO,CAAC,QAAQ,CAAC;IAChD,OAAOE,SAAS,GAAGL,IAAI,CAACI,KAAK,CAACC,SAAS,CAAC,GAAG,IAAI;EACjD;EAEQjC,sBAAsBA,CAAA;IAC5B,MAAMkB,MAAM,GAAG,IAAI,CAACC,oBAAoB,EAAE;IAC1C,IAAI,CAACD,MAAM,EAAEQ,YAAY,EAAE;IAE3B,MAAMQ,QAAQ,GAAGN,IAAI,CAACI,KAAK,CAACG,IAAI,CAACjB,MAAM,CAACQ,YAAY,CAACU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACJ,QAAQ,CAACK,GAAG,GAAG,IAAI,CAAC;IAE7C,MAAMC,OAAO,GAAGH,OAAO,CAACI,OAAO,EAAE,GAAGH,IAAI,CAACI,GAAG,EAAE,GAAI,EAAE,GAAG,IAAK;IAC5D,IAAI,CAACC,mBAAmB,GAAGC,UAAU,CAAC,MAAM,IAAI,CAAC3B,YAAY,EAAE,CAAC4B,SAAS,EAAE,EAAEL,OAAO,CAAC;EACvF;EAEQxB,qBAAqBA,CAAA;IAC3B8B,YAAY,CAAC,IAAI,CAACH,mBAAmB,CAAC;EACxC;;;uCA5GWpD,WAAW,EAAAwD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAX3D,WAAW;MAAA4D,OAAA,EAAX5D,WAAW,CAAA6D,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}