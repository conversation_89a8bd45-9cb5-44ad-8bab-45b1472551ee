// src/app/demo/pages/questions/question-list/question-list.component.ts
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { QuestionService } from '../../../../core/services/question.service';
import { Question } from '../../../../core/models/question.model';

@Component({
  selector: 'app-questions-list',
  templateUrl: './questions-list.component.html',
  styleUrls: ['./questions-list.component.scss']
})
export class QuestionsListComponent implements OnInit {
  questions: Question[] = [];
  loading = false;
  error = '';

  constructor(
    private questionService: QuestionService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadQuestions();
  }

  loadQuestions(): void {
    this.loading = true;
    this.questionService.getQuestions()
      .subscribe({
        next: (data) => {
          this.questions = data;
          this.loading = false;
        },
        error: (error) => {
          this.error = 'Failed to load questions';
          this.loading = false;
          console.error('Error loading questions:', error);
        }
      });
  }

  createQuestion(): void {
    this.router.navigate(['/questions/new']);
  }

  editQuestion(id: string): void {
    this.router.navigate(['/questions/edit', id]);
  }
}
