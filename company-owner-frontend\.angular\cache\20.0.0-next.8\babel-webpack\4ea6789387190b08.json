{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport default class BreadcrumbPagingComponent {\n  static {\n    this.ɵfac = function BreadcrumbPagingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BreadcrumbPagingComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BreadcrumbPagingComponent,\n      selectors: [[\"app-breadcrumb-paging\"]],\n      decls: 242,\n      vars: 0,\n      consts: [[1, \"row\"], [1, \"col-sm-6\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-body\"], [\"aria-label\", \"breadcrumb\"], [1, \"breadcrumb\"], [\"aria-current\", \"page\", 1, \"breadcrumb-item\", \"active\"], [1, \"breadcrumb-item\"], [\"href\", \"javascript:\"], [1, \"feather\", \"icon-home\"], [1, \"feather\", \"icon-home\", \"fs-6\"], [\"aria-label\", \"breadcrumb\", 2, \"--bs-breadcrumb-divider\", \"'>'\"], [\"aria-label\", \"breadcrumb\", 2, \"--bs-breadcrumb-divider\", \"url(\\n              \\\"data:image/svg + xml,\\n              %3Csvgxmlns='http://www.w3.org/2000/svg'width='8'height='8'%3E%3Cpathd='M2.5 0L1 1.5 3.5 4 1 6.5 2.5 8l4-4-4-4z'fill='currentColor'/%3E%3C/svg%3E\\\"\\n            )\"], [1, \"col-sm-12\"], [\"aria-label\", \"Page navigation example\"], [1, \"pagination\"], [1, \"page-item\"], [\"href\", \"javascript:\", 1, \"page-link\"], [1, \"mt-5\"], [\"href\", \"javascript:\", \"aria-label\", \"Previous\", 1, \"page-link\"], [\"aria-hidden\", \"true\"], [1, \"sr-only\"], [\"href\", \"javascript:\", \"aria-label\", \"Next\", 1, \"page-link\"], [\"aria-label\", \"...\"], [1, \"page-item\", \"disabled\"], [1, \"page-link\"], [1, \"page-item\", \"active\"], [\"aria-label\", \"...\", 1, \"mb-4\"], [1, \"pagination\", \"pagination-lg\"], [\"href\", \"javascript:\", \"tabindex\", \"-1\", 1, \"page-link\"], [1, \"pagination\", \"pagination-sm\"], [1, \"pagination\", \"justify-content-center\"], [1, \"pagination\", \"justify-content-end\"]],\n      template: function BreadcrumbPagingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h5\");\n          i0.ɵɵtext(5, \"Breadcrumb\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"nav\", 5)(8, \"ol\", 6)(9, \"li\", 7);\n          i0.ɵɵtext(10, \"Home\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"nav\", 5)(12, \"ol\", 6)(13, \"li\", 8)(14, \"a\", 9);\n          i0.ɵɵtext(15, \"Home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"li\", 7);\n          i0.ɵɵtext(17, \"Library\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"nav\", 5)(19, \"ol\", 6)(20, \"li\", 8)(21, \"a\", 9);\n          i0.ɵɵtext(22, \"Home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"li\", 8)(24, \"a\", 9);\n          i0.ɵɵtext(25, \"Library\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"li\", 7);\n          i0.ɵɵtext(27, \"Data\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(28, \"div\", 1)(29, \"div\", 2)(30, \"div\", 3)(31, \"h5\");\n          i0.ɵɵtext(32, \"Breadcrumb Icon\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 4)(34, \"nav\", 5)(35, \"ol\", 6)(36, \"li\", 7);\n          i0.ɵɵelement(37, \"i\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"nav\", 5)(39, \"ol\", 6)(40, \"li\", 8)(41, \"a\", 9);\n          i0.ɵɵelement(42, \"i\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"li\", 7);\n          i0.ɵɵtext(44, \"Library\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"nav\", 5)(46, \"ol\", 6)(47, \"li\", 8)(48, \"a\", 9);\n          i0.ɵɵelement(49, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"li\", 8)(51, \"a\", 9);\n          i0.ɵɵtext(52, \"Library\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"li\", 7);\n          i0.ɵɵtext(54, \"Data\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(55, \"div\", 1)(56, \"div\", 2)(57, \"div\", 3)(58, \"h5\");\n          i0.ɵɵtext(59, \"Breadcrumb Dividers [ character ]\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 4)(61, \"nav\", 12)(62, \"ol\", 6)(63, \"li\", 7);\n          i0.ɵɵelement(64, \"i\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(65, \"nav\", 12)(66, \"ol\", 6)(67, \"li\", 8)(68, \"a\", 9);\n          i0.ɵɵelement(69, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"li\", 7);\n          i0.ɵɵtext(71, \"Library\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(72, \"nav\", 12)(73, \"ol\", 6)(74, \"li\", 8)(75, \"a\", 9);\n          i0.ɵɵelement(76, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"li\", 8)(78, \"a\", 9);\n          i0.ɵɵtext(79, \"Library\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"li\", 7);\n          i0.ɵɵtext(81, \"Data\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(82, \"div\", 1)(83, \"div\", 2)(84, \"div\", 3)(85, \"h5\");\n          i0.ɵɵtext(86, \"Breadcrumb Dividers [ embedded SVG icon ]\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 4)(88, \"nav\", 13)(89, \"ol\", 6)(90, \"li\", 7);\n          i0.ɵɵelement(91, \"i\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(92, \"nav\", 13)(93, \"ol\", 6)(94, \"li\", 8)(95, \"a\", 9);\n          i0.ɵɵelement(96, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(97, \"li\", 7);\n          i0.ɵɵtext(98, \"Library\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(99, \"nav\", 13)(100, \"ol\", 6)(101, \"li\", 8)(102, \"a\", 9);\n          i0.ɵɵelement(103, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(104, \"li\", 8)(105, \"a\", 9);\n          i0.ɵɵtext(106, \"Library\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(107, \"li\", 7);\n          i0.ɵɵtext(108, \"Data\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(109, \"div\", 14)(110, \"div\", 2)(111, \"div\", 3)(112, \"h5\");\n          i0.ɵɵtext(113, \"Pagination\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(114, \"div\", 4)(115, \"nav\", 15)(116, \"ul\", 16)(117, \"li\", 17)(118, \"a\", 18);\n          i0.ɵɵtext(119, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(120, \"li\", 17)(121, \"a\", 18);\n          i0.ɵɵtext(122, \"1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(123, \"li\", 17)(124, \"a\", 18);\n          i0.ɵɵtext(125, \"2\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(126, \"li\", 17)(127, \"a\", 18);\n          i0.ɵɵtext(128, \"3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(129, \"li\", 17)(130, \"a\", 18);\n          i0.ɵɵtext(131, \"Next\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(132, \"h5\", 19);\n          i0.ɵɵtext(133, \"Working with Icons\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(134, \"hr\");\n          i0.ɵɵelementStart(135, \"nav\", 15)(136, \"ul\", 16)(137, \"li\", 17)(138, \"a\", 20)(139, \"span\", 21);\n          i0.ɵɵtext(140, \"\\u00AB\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(141, \"span\", 22);\n          i0.ɵɵtext(142, \"Previous\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(143, \"li\", 17)(144, \"a\", 18);\n          i0.ɵɵtext(145, \"1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(146, \"li\", 17)(147, \"a\", 18);\n          i0.ɵɵtext(148, \"2\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(149, \"li\", 17)(150, \"a\", 18);\n          i0.ɵɵtext(151, \"3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(152, \"li\", 17)(153, \"a\", 23)(154, \"span\", 21);\n          i0.ɵɵtext(155, \"\\u00BB\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(156, \"span\", 22);\n          i0.ɵɵtext(157, \"Next\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(158, \"h5\", 19);\n          i0.ɵɵtext(159, \"Disabled and Active States\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(160, \"hr\");\n          i0.ɵɵelementStart(161, \"nav\", 24)(162, \"ul\", 16)(163, \"li\", 25)(164, \"span\", 26);\n          i0.ɵɵtext(165, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(166, \"li\", 17)(167, \"a\", 18);\n          i0.ɵɵtext(168, \"1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(169, \"li\", 27)(170, \"span\", 26);\n          i0.ɵɵtext(171, \" 2 \");\n          i0.ɵɵelementStart(172, \"span\", 22);\n          i0.ɵɵtext(173, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(174, \"li\", 17)(175, \"a\", 18);\n          i0.ɵɵtext(176, \"3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(177, \"li\", 17)(178, \"a\", 18);\n          i0.ɵɵtext(179, \"Next\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(180, \"h5\", 19);\n          i0.ɵɵtext(181, \"Sizing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(182, \"hr\");\n          i0.ɵɵelementStart(183, \"nav\", 28)(184, \"ul\", 29)(185, \"li\", 25)(186, \"a\", 30);\n          i0.ɵɵtext(187, \"1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(188, \"li\", 17)(189, \"a\", 18);\n          i0.ɵɵtext(190, \"2\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(191, \"li\", 17)(192, \"a\", 18);\n          i0.ɵɵtext(193, \"3\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(194, \"nav\", 24)(195, \"ul\", 31)(196, \"li\", 25)(197, \"a\", 30);\n          i0.ɵɵtext(198, \"1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(199, \"li\", 17)(200, \"a\", 18);\n          i0.ɵɵtext(201, \"2\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(202, \"li\", 17)(203, \"a\", 18);\n          i0.ɵɵtext(204, \"3\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(205, \"h5\", 19);\n          i0.ɵɵtext(206, \"Alignment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(207, \"hr\");\n          i0.ɵɵelementStart(208, \"nav\", 15)(209, \"ul\", 32)(210, \"li\", 25)(211, \"a\", 30);\n          i0.ɵɵtext(212, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(213, \"li\", 17)(214, \"a\", 18);\n          i0.ɵɵtext(215, \"1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(216, \"li\", 17)(217, \"a\", 18);\n          i0.ɵɵtext(218, \"2\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(219, \"li\", 17)(220, \"a\", 18);\n          i0.ɵɵtext(221, \"3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(222, \"li\", 17)(223, \"a\", 18);\n          i0.ɵɵtext(224, \"Next\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(225, \"nav\", 15)(226, \"ul\", 33)(227, \"li\", 25)(228, \"a\", 30);\n          i0.ɵɵtext(229, \"Previous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(230, \"li\", 17)(231, \"a\", 18);\n          i0.ɵɵtext(232, \"1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(233, \"li\", 17)(234, \"a\", 18);\n          i0.ɵɵtext(235, \"2\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(236, \"li\", 17)(237, \"a\", 18);\n          i0.ɵɵtext(238, \"3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(239, \"li\", 17)(240, \"a\", 18);\n          i0.ɵɵtext(241, \"Next\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n      },\n      styles: [\"a[_ngcontent-%COMP%], \\n.btn-link[_ngcontent-%COMP%] {\\n  --bs-link-color: $pc-primary;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZGVtby91aS1lbGVtZW50cy91aS1iYXNpYy9icmVhZGNydW1iLXBhZ2luZy9icmVhZGNydW1iLXBhZ2luZy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7RUFFRSw0QkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiYSxcclxuLmJ0bi1saW5rIHtcclxuICAtLWJzLWxpbmstY29sb3I6ICRwYy1wcmltYXJ5O1xyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BreadcrumbPagingComponent", "selectors", "decls", "vars", "consts", "template", "BreadcrumbPagingComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\ui-elements\\ui-basic\\breadcrumb-paging\\breadcrumb-paging.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\ui-elements\\ui-basic\\breadcrumb-paging\\breadcrumb-paging.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-breadcrumb-paging',\r\n  standalone: true,\r\n  imports: [],\r\n  templateUrl: './breadcrumb-paging.component.html',\r\n  styleUrls: ['./breadcrumb-paging.component.scss']\r\n})\r\nexport default class BreadcrumbPagingComponent {}\r\n", "<div class=\"row\">\r\n  <!-- [ Breadcrumbs & Pagination ] start -->\r\n  <div class=\"col-sm-6\">\r\n    <div class=\"card\">\r\n      <div class=\"card-header\">\r\n        <h5>Breadcrumb</h5>\r\n      </div>\r\n      <div class=\"card-body\">\r\n        <nav aria-label=\"breadcrumb\">\r\n          <ol class=\"breadcrumb\">\r\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">Home</li>\r\n          </ol>\r\n        </nav>\r\n        <nav aria-label=\"breadcrumb\">\r\n          <ol class=\"breadcrumb\">\r\n            <li class=\"breadcrumb-item\"><a href=\"javascript:\">Home</a></li>\r\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">Library</li>\r\n          </ol>\r\n        </nav>\r\n        <nav aria-label=\"breadcrumb\">\r\n          <ol class=\"breadcrumb\">\r\n            <li class=\"breadcrumb-item\"><a href=\"javascript:\">Home</a></li>\r\n            <li class=\"breadcrumb-item\"><a href=\"javascript:\">Library</a></li>\r\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">Data</li>\r\n          </ol>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"col-sm-6\">\r\n    <div class=\"card\">\r\n      <div class=\"card-header\">\r\n        <h5>Breadcrumb Icon</h5>\r\n      </div>\r\n      <div class=\"card-body\">\r\n        <nav aria-label=\"breadcrumb\">\r\n          <ol class=\"breadcrumb\">\r\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">\r\n              <i class=\"feather icon-home\"></i>\r\n            </li>\r\n          </ol>\r\n        </nav>\r\n        <nav aria-label=\"breadcrumb\">\r\n          <ol class=\"breadcrumb\">\r\n            <li class=\"breadcrumb-item\">\r\n              <a href=\"javascript:\"><i class=\"feather icon-home fs-6\"></i></a>\r\n            </li>\r\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">Library</li>\r\n          </ol>\r\n        </nav>\r\n        <nav aria-label=\"breadcrumb\">\r\n          <ol class=\"breadcrumb\">\r\n            <li class=\"breadcrumb-item\">\r\n              <a href=\"javascript:\"><i class=\"feather icon-home\"></i></a>\r\n            </li>\r\n            <li class=\"breadcrumb-item\"><a href=\"javascript:\">Library</a></li>\r\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">Data</li>\r\n          </ol>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"col-sm-6\">\r\n    <div class=\"card\">\r\n      <div class=\"card-header\">\r\n        <h5>Breadcrumb Dividers [ character ]</h5>\r\n      </div>\r\n      <div class=\"card-body\">\r\n        <nav aria-label=\"breadcrumb\" style=\"--bs-breadcrumb-divider: '>'\">\r\n          <ol class=\"breadcrumb\">\r\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">\r\n              <i class=\"feather icon-home\"></i>\r\n            </li>\r\n          </ol>\r\n        </nav>\r\n        <nav aria-label=\"breadcrumb\" style=\"--bs-breadcrumb-divider: '>'\">\r\n          <ol class=\"breadcrumb\">\r\n            <li class=\"breadcrumb-item\">\r\n              <a href=\"javascript:\"><i class=\"feather icon-home\"></i></a>\r\n            </li>\r\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">Library</li>\r\n          </ol>\r\n        </nav>\r\n        <nav aria-label=\"breadcrumb\" style=\"--bs-breadcrumb-divider: '>'\">\r\n          <ol class=\"breadcrumb\">\r\n            <li class=\"breadcrumb-item\">\r\n              <a href=\"javascript:\"><i class=\"feather icon-home\"></i></a>\r\n            </li>\r\n            <li class=\"breadcrumb-item\"><a href=\"javascript:\">Library</a></li>\r\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">Data</li>\r\n          </ol>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"col-sm-6\">\r\n    <div class=\"card\">\r\n      <div class=\"card-header\">\r\n        <h5>Breadcrumb Dividers [ embedded SVG icon ]</h5>\r\n      </div>\r\n      <div class=\"card-body\">\r\n        <nav\r\n          aria-label=\"breadcrumb\"\r\n          style=\"\r\n            --bs-breadcrumb-divider: url(\r\n              &#34;data:image/svg + xml,\r\n              %3Csvgxmlns='http://www.w3.org/2000/svg'width='8'height='8'%3E%3Cpathd='M2.5 0L1 1.5 3.5 4 1 6.5 2.5 8l4-4-4-4z'fill='currentColor'/%3E%3C/svg%3E&#34;\r\n            );\r\n          \"\r\n        >\r\n          <ol class=\"breadcrumb\">\r\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">\r\n              <i class=\"feather icon-home\"></i>\r\n            </li>\r\n          </ol>\r\n        </nav>\r\n        <nav\r\n          aria-label=\"breadcrumb\"\r\n          style=\"\r\n            --bs-breadcrumb-divider: url(\r\n              &#34;data:image/svg + xml,\r\n              %3Csvgxmlns='http://www.w3.org/2000/svg'width='8'height='8'%3E%3Cpathd='M2.5 0L1 1.5 3.5 4 1 6.5 2.5 8l4-4-4-4z'fill='currentColor'/%3E%3C/svg%3E&#34;\r\n            );\r\n          \"\r\n        >\r\n          <ol class=\"breadcrumb\">\r\n            <li class=\"breadcrumb-item\">\r\n              <a href=\"javascript:\"><i class=\"feather icon-home\"></i></a>\r\n            </li>\r\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">Library</li>\r\n          </ol>\r\n        </nav>\r\n        <nav\r\n          aria-label=\"breadcrumb\"\r\n          style=\"\r\n            --bs-breadcrumb-divider: url(\r\n              &#34;data:image/svg + xml,\r\n              %3Csvgxmlns='http://www.w3.org/2000/svg'width='8'height='8'%3E%3Cpathd='M2.5 0L1 1.5 3.5 4 1 6.5 2.5 8l4-4-4-4z'fill='currentColor'/%3E%3C/svg%3E&#34;\r\n            );\r\n          \"\r\n        >\r\n          <ol class=\"breadcrumb\">\r\n            <li class=\"breadcrumb-item\">\r\n              <a href=\"javascript:\"><i class=\"feather icon-home\"></i></a>\r\n            </li>\r\n            <li class=\"breadcrumb-item\"><a href=\"javascript:\">Library</a></li>\r\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">Data</li>\r\n          </ol>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"col-sm-12\">\r\n    <div class=\"card\">\r\n      <div class=\"card-header\">\r\n        <h5>Pagination</h5>\r\n      </div>\r\n      <div class=\"card-body\">\r\n        <nav aria-label=\"Page navigation example\">\r\n          <ul class=\"pagination\">\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">Previous</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">1</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">2</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">3</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">Next</a>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n        <h5 class=\"mt-5\">Working with Icons</h5>\r\n        <hr />\r\n        <nav aria-label=\"Page navigation example\">\r\n          <ul class=\"pagination\">\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\" aria-label=\"Previous\">\r\n                <span aria-hidden=\"true\">&laquo;</span>\r\n                <span class=\"sr-only\">Previous</span>\r\n              </a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">1</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">2</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">3</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\" aria-label=\"Next\">\r\n                <span aria-hidden=\"true\">&raquo;</span>\r\n                <span class=\"sr-only\">Next</span>\r\n              </a>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n        <h5 class=\"mt-5\">Disabled and Active States</h5>\r\n        <hr />\r\n        <nav aria-label=\"...\">\r\n          <ul class=\"pagination\">\r\n            <li class=\"page-item disabled\">\r\n              <span class=\"page-link\">Previous</span>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">1</a>\r\n            </li>\r\n            <li class=\"page-item active\">\r\n              <span class=\"page-link\">\r\n                2\r\n                <span class=\"sr-only\">(current)</span>\r\n              </span>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">3</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">Next</a>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n        <h5 class=\"mt-5\">Sizing</h5>\r\n        <hr />\r\n        <nav class=\"mb-4\" aria-label=\"...\">\r\n          <ul class=\"pagination pagination-lg\">\r\n            <li class=\"page-item disabled\">\r\n              <a class=\"page-link\" href=\"javascript:\" tabindex=\"-1\">1</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">2</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">3</a>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n        <nav aria-label=\"...\">\r\n          <ul class=\"pagination pagination-sm\">\r\n            <li class=\"page-item disabled\">\r\n              <a class=\"page-link\" href=\"javascript:\" tabindex=\"-1\">1</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">2</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">3</a>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n        <h5 class=\"mt-5\">Alignment</h5>\r\n        <hr />\r\n        <nav aria-label=\"Page navigation example\">\r\n          <ul class=\"pagination justify-content-center\">\r\n            <li class=\"page-item disabled\">\r\n              <a class=\"page-link\" href=\"javascript:\" tabindex=\"-1\">Previous</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">1</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">2</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">3</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">Next</a>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n        <nav aria-label=\"Page navigation example\">\r\n          <ul class=\"pagination justify-content-end\">\r\n            <li class=\"page-item disabled\">\r\n              <a class=\"page-link\" href=\"javascript:\" tabindex=\"-1\">Previous</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">1</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">2</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">3</a>\r\n            </li>\r\n            <li class=\"page-item\">\r\n              <a class=\"page-link\" href=\"javascript:\">Next</a>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <!-- [ Breadcrumbs & Pagination ] end -->\r\n</div>\r\n"], "mappings": ";AASA,eAAc,MAAOA,yBAAyB;;;uCAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCJtCE,EALR,CAAAC,cAAA,aAAiB,aAEO,aACF,aACS,SACnB;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAChBF,EADgB,CAAAG,YAAA,EAAK,EACf;UAIAH,EAHN,CAAAC,cAAA,aAAuB,aACQ,YACJ,YACkC;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAE/DF,EAF+D,CAAAG,YAAA,EAAK,EAC7D,EACD;UAG0BH,EAFhC,CAAAC,cAAA,cAA6B,aACJ,aACO,YAAsB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UAC/DH,EAAA,CAAAC,cAAA,aAAuD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAElEF,EAFkE,CAAAG,YAAA,EAAK,EAChE,EACD;UAG0BH,EAFhC,CAAAC,cAAA,cAA6B,aACJ,aACO,YAAsB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UACnCH,EAA5B,CAAAC,cAAA,aAA4B,YAAsB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UAClEH,EAAA,CAAAC,cAAA,aAAuD;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAKrEF,EALqE,CAAAG,YAAA,EAAK,EAC7D,EACD,EACF,EACF,EACF;UAIAH,EAHN,CAAAC,cAAA,cAAsB,cACF,cACS,UACnB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UACrBF,EADqB,CAAAG,YAAA,EAAK,EACpB;UAIAH,EAHN,CAAAC,cAAA,cAAuB,cACQ,aACJ,aACkC;UACrDD,EAAA,CAAAI,SAAA,aAAiC;UAGvCJ,EAFI,CAAAG,YAAA,EAAK,EACF,EACD;UAIAH,EAHN,CAAAC,cAAA,cAA6B,aACJ,aACO,YACJ;UAAAD,EAAA,CAAAI,SAAA,aAAsC;UAC9DJ,EAD8D,CAAAG,YAAA,EAAI,EAC7D;UACLH,EAAA,CAAAC,cAAA,aAAuD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAElEF,EAFkE,CAAAG,YAAA,EAAK,EAChE,EACD;UAIAH,EAHN,CAAAC,cAAA,cAA6B,aACJ,aACO,YACJ;UAAAD,EAAA,CAAAI,SAAA,aAAiC;UACzDJ,EADyD,CAAAG,YAAA,EAAI,EACxD;UACuBH,EAA5B,CAAAC,cAAA,aAA4B,YAAsB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UAClEH,EAAA,CAAAC,cAAA,aAAuD;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAKrEF,EALqE,CAAAG,YAAA,EAAK,EAC7D,EACD,EACF,EACF,EACF;UAIAH,EAHN,CAAAC,cAAA,cAAsB,cACF,cACS,UACnB;UAAAD,EAAA,CAAAE,MAAA,yCAAiC;UACvCF,EADuC,CAAAG,YAAA,EAAK,EACtC;UAIAH,EAHN,CAAAC,cAAA,cAAuB,eAC6C,aACzC,aACkC;UACrDD,EAAA,CAAAI,SAAA,aAAiC;UAGvCJ,EAFI,CAAAG,YAAA,EAAK,EACF,EACD;UAIAH,EAHN,CAAAC,cAAA,eAAkE,aACzC,aACO,YACJ;UAAAD,EAAA,CAAAI,SAAA,aAAiC;UACzDJ,EADyD,CAAAG,YAAA,EAAI,EACxD;UACLH,EAAA,CAAAC,cAAA,aAAuD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAElEF,EAFkE,CAAAG,YAAA,EAAK,EAChE,EACD;UAIAH,EAHN,CAAAC,cAAA,eAAkE,aACzC,aACO,YACJ;UAAAD,EAAA,CAAAI,SAAA,aAAiC;UACzDJ,EADyD,CAAAG,YAAA,EAAI,EACxD;UACuBH,EAA5B,CAAAC,cAAA,aAA4B,YAAsB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UAClEH,EAAA,CAAAC,cAAA,aAAuD;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAKrEF,EALqE,CAAAG,YAAA,EAAK,EAC7D,EACD,EACF,EACF,EACF;UAIAH,EAHN,CAAAC,cAAA,cAAsB,cACF,cACS,UACnB;UAAAD,EAAA,CAAAE,MAAA,iDAAyC;UAC/CF,EAD+C,CAAAG,YAAA,EAAK,EAC9C;UAYAH,EAXN,CAAAC,cAAA,cAAuB,eASpB,aACwB,aACkC;UACrDD,EAAA,CAAAI,SAAA,aAAiC;UAGvCJ,EAFI,CAAAG,YAAA,EAAK,EACF,EACD;UAYAH,EAXN,CAAAC,cAAA,eAQC,aACwB,aACO,YACJ;UAAAD,EAAA,CAAAI,SAAA,aAAiC;UACzDJ,EADyD,CAAAG,YAAA,EAAI,EACxD;UACLH,EAAA,CAAAC,cAAA,aAAuD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAElEF,EAFkE,CAAAG,YAAA,EAAK,EAChE,EACD;UAYAH,EAXN,CAAAC,cAAA,eAQC,cACwB,cACO,aACJ;UAAAD,EAAA,CAAAI,SAAA,cAAiC;UACzDJ,EADyD,CAAAG,YAAA,EAAI,EACxD;UACuBH,EAA5B,CAAAC,cAAA,cAA4B,aAAsB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UAClEH,EAAA,CAAAC,cAAA,cAAuD;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAKrEF,EALqE,CAAAG,YAAA,EAAK,EAC7D,EACD,EACF,EACF,EACF;UAIAH,EAHN,CAAAC,cAAA,gBAAuB,eACH,eACS,WACnB;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAChBF,EADgB,CAAAG,YAAA,EAAK,EACf;UAKEH,EAJR,CAAAC,cAAA,eAAuB,gBACqB,eACjB,eACC,cACoB;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAClDF,EADkD,CAAAG,YAAA,EAAI,EACjD;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAGlDF,EAHkD,CAAAG,YAAA,EAAI,EAC7C,EACF,EACD;UACNH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,2BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAAI,SAAA,WAAM;UAKEJ,EAJR,CAAAC,cAAA,gBAA0C,eACjB,eACC,cAC0C,iBACnC;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvCH,EAAA,CAAAC,cAAA,iBAAsB;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAElCF,EAFkC,CAAAG,YAAA,EAAO,EACnC,EACD;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAGDH,EAFJ,CAAAC,cAAA,eAAsB,cACsC,iBAC/B;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvCH,EAAA,CAAAC,cAAA,iBAAsB;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAIlCF,EAJkC,CAAAG,YAAA,EAAO,EAC/B,EACD,EACF,EACD;UACNH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,mCAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChDH,EAAA,CAAAI,SAAA,WAAM;UAIAJ,EAHN,CAAAC,cAAA,gBAAsB,eACG,eACU,iBACL;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAClCF,EADkC,CAAAG,YAAA,EAAO,EACpC;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAA6B,iBACH;UACtBD,EAAA,CAAAE,MAAA,YACA;UAAAF,EAAA,CAAAC,cAAA,iBAAsB;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAEnCF,EAFmC,CAAAG,YAAA,EAAO,EACjC,EACJ;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAGlDF,EAHkD,CAAAG,YAAA,EAAI,EAC7C,EACF,EACD;UACNH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAI,SAAA,WAAM;UAIAJ,EAHN,CAAAC,cAAA,gBAAmC,eACI,eACJ,cACyB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UACzDF,EADyD,CAAAG,YAAA,EAAI,EACxD;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAG/CF,EAH+C,CAAAG,YAAA,EAAI,EAC1C,EACF,EACD;UAIAH,EAHN,CAAAC,cAAA,gBAAsB,eACiB,eACJ,cACyB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UACzDF,EADyD,CAAAG,YAAA,EAAI,EACxD;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAG/CF,EAH+C,CAAAG,YAAA,EAAI,EAC1C,EACF,EACD;UACNH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAI,SAAA,WAAM;UAIAJ,EAHN,CAAAC,cAAA,gBAA0C,eACM,eACb,cACyB;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAChEF,EADgE,CAAAG,YAAA,EAAI,EAC/D;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAGlDF,EAHkD,CAAAG,YAAA,EAAI,EAC7C,EACF,EACD;UAIAH,EAHN,CAAAC,cAAA,gBAA0C,eACG,eACV,cACyB;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAChEF,EADgE,CAAAG,YAAA,EAAI,EAC/D;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EAC1C;UAEHH,EADF,CAAAC,cAAA,eAAsB,cACoB;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAQ1DF,EAR0D,CAAAG,YAAA,EAAI,EAC7C,EACF,EACD,EACF,EACF,EACF,EAEF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}