{"ast": null, "code": "/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { AnimationGroupPlayer, NoopAnimationPlayer, AUTO_STYLE, ɵPRE_STYLE as _PRE_STYLE, AnimationMetadataType, sequence } from './private_export-CacKMzxJ.mjs';\nimport { ɵRuntimeError as _RuntimeError } from '@angular/core';\nconst LINE_START = '\\n - ';\nfunction invalidTimingValue(exp) {\n  return new _RuntimeError(3000 /* RuntimeErrorCode.INVALID_TIMING_VALUE */, ngDevMode && `The provided timing value \"${exp}\" is invalid.`);\n}\nfunction negativeStepValue() {\n  return new _RuntimeError(3100 /* RuntimeErrorCode.NEGATIVE_STEP_VALUE */, ngDevMode && 'Duration values below 0 are not allowed for this animation step.');\n}\nfunction negativeDelayValue() {\n  return new _RuntimeError(3101 /* RuntimeErrorCode.NEGATIVE_DELAY_VALUE */, ngDevMode && 'Delay values below 0 are not allowed for this animation step.');\n}\nfunction invalidStyleParams(varName) {\n  return new _RuntimeError(3001 /* RuntimeErrorCode.INVALID_STYLE_PARAMS */, ngDevMode && `Unable to resolve the local animation param ${varName} in the given list of values`);\n}\nfunction invalidParamValue(varName) {\n  return new _RuntimeError(3003 /* RuntimeErrorCode.INVALID_PARAM_VALUE */, ngDevMode && `Please provide a value for the animation param ${varName}`);\n}\nfunction invalidNodeType(nodeType) {\n  return new _RuntimeError(3004 /* RuntimeErrorCode.INVALID_NODE_TYPE */, ngDevMode && `Unable to resolve animation metadata node #${nodeType}`);\n}\nfunction invalidCssUnitValue(userProvidedProperty, value) {\n  return new _RuntimeError(3005 /* RuntimeErrorCode.INVALID_CSS_UNIT_VALUE */, ngDevMode && `Please provide a CSS unit value for ${userProvidedProperty}:${value}`);\n}\nfunction invalidTrigger() {\n  return new _RuntimeError(3006 /* RuntimeErrorCode.INVALID_TRIGGER */, ngDevMode && \"animation triggers cannot be prefixed with an `@` sign (e.g. trigger('@foo', [...]))\");\n}\nfunction invalidDefinition() {\n  return new _RuntimeError(3007 /* RuntimeErrorCode.INVALID_DEFINITION */, ngDevMode && 'only state() and transition() definitions can sit inside of a trigger()');\n}\nfunction invalidState(metadataName, missingSubs) {\n  return new _RuntimeError(3008 /* RuntimeErrorCode.INVALID_STATE */, ngDevMode && `state(\"${metadataName}\", ...) must define default values for all the following style substitutions: ${missingSubs.join(', ')}`);\n}\nfunction invalidStyleValue(value) {\n  return new _RuntimeError(3002 /* RuntimeErrorCode.INVALID_STYLE_VALUE */, ngDevMode && `The provided style string value ${value} is not allowed.`);\n}\nfunction invalidParallelAnimation(prop, firstStart, firstEnd, secondStart, secondEnd) {\n  return new _RuntimeError(3010 /* RuntimeErrorCode.INVALID_PARALLEL_ANIMATION */, ngDevMode && `The CSS property \"${prop}\" that exists between the times of \"${firstStart}ms\" and \"${firstEnd}ms\" is also being animated in a parallel animation between the times of \"${secondStart}ms\" and \"${secondEnd}ms\"`);\n}\nfunction invalidKeyframes() {\n  return new _RuntimeError(3011 /* RuntimeErrorCode.INVALID_KEYFRAMES */, ngDevMode && `keyframes() must be placed inside of a call to animate()`);\n}\nfunction invalidOffset() {\n  return new _RuntimeError(3012 /* RuntimeErrorCode.INVALID_OFFSET */, ngDevMode && `Please ensure that all keyframe offsets are between 0 and 1`);\n}\nfunction keyframeOffsetsOutOfOrder() {\n  return new _RuntimeError(3200 /* RuntimeErrorCode.KEYFRAME_OFFSETS_OUT_OF_ORDER */, ngDevMode && `Please ensure that all keyframe offsets are in order`);\n}\nfunction keyframesMissingOffsets() {\n  return new _RuntimeError(3202 /* RuntimeErrorCode.KEYFRAMES_MISSING_OFFSETS */, ngDevMode && `Not all style() steps within the declared keyframes() contain offsets`);\n}\nfunction invalidStagger() {\n  return new _RuntimeError(3013 /* RuntimeErrorCode.INVALID_STAGGER */, ngDevMode && `stagger() can only be used inside of query()`);\n}\nfunction invalidQuery(selector) {\n  return new _RuntimeError(3014 /* RuntimeErrorCode.INVALID_QUERY */, ngDevMode && `\\`query(\"${selector}\")\\` returned zero elements. (Use \\`query(\"${selector}\", { optional: true })\\` if you wish to allow this.)`);\n}\nfunction invalidExpression(expr) {\n  return new _RuntimeError(3015 /* RuntimeErrorCode.INVALID_EXPRESSION */, ngDevMode && `The provided transition expression \"${expr}\" is not supported`);\n}\nfunction invalidTransitionAlias(alias) {\n  return new _RuntimeError(3016 /* RuntimeErrorCode.INVALID_TRANSITION_ALIAS */, ngDevMode && `The transition alias value \"${alias}\" is not supported`);\n}\nfunction validationFailed(errors) {\n  return new _RuntimeError(3500 /* RuntimeErrorCode.VALIDATION_FAILED */, ngDevMode && `animation validation failed:\\n${errors.map(err => err.message).join('\\n')}`);\n}\nfunction buildingFailed(errors) {\n  return new _RuntimeError(3501 /* RuntimeErrorCode.BUILDING_FAILED */, ngDevMode && `animation building failed:\\n${errors.map(err => err.message).join('\\n')}`);\n}\nfunction triggerBuildFailed(name, errors) {\n  return new _RuntimeError(3404 /* RuntimeErrorCode.TRIGGER_BUILD_FAILED */, ngDevMode && `The animation trigger \"${name}\" has failed to build due to the following errors:\\n - ${errors.map(err => err.message).join('\\n - ')}`);\n}\nfunction animationFailed(errors) {\n  return new _RuntimeError(3502 /* RuntimeErrorCode.ANIMATION_FAILED */, ngDevMode && `Unable to animate due to the following errors:${LINE_START}${errors.map(err => err.message).join(LINE_START)}`);\n}\nfunction registerFailed(errors) {\n  return new _RuntimeError(3503 /* RuntimeErrorCode.REGISTRATION_FAILED */, ngDevMode && `Unable to build the animation due to the following errors: ${errors.map(err => err.message).join('\\n')}`);\n}\nfunction missingOrDestroyedAnimation() {\n  return new _RuntimeError(3300 /* RuntimeErrorCode.MISSING_OR_DESTROYED_ANIMATION */, ngDevMode && \"The requested animation doesn't exist or has already been destroyed\");\n}\nfunction createAnimationFailed(errors) {\n  return new _RuntimeError(3504 /* RuntimeErrorCode.CREATE_ANIMATION_FAILED */, ngDevMode && `Unable to create the animation due to the following errors:${errors.map(err => err.message).join('\\n')}`);\n}\nfunction missingPlayer(id) {\n  return new _RuntimeError(3301 /* RuntimeErrorCode.MISSING_PLAYER */, ngDevMode && `Unable to find the timeline player referenced by ${id}`);\n}\nfunction missingTrigger(phase, name) {\n  return new _RuntimeError(3302 /* RuntimeErrorCode.MISSING_TRIGGER */, ngDevMode && `Unable to listen on the animation trigger event \"${phase}\" because the animation trigger \"${name}\" doesn\\'t exist!`);\n}\nfunction missingEvent(name) {\n  return new _RuntimeError(3303 /* RuntimeErrorCode.MISSING_EVENT */, ngDevMode && `Unable to listen on the animation trigger \"${name}\" because the provided event is undefined!`);\n}\nfunction unsupportedTriggerEvent(phase, name) {\n  return new _RuntimeError(3400 /* RuntimeErrorCode.UNSUPPORTED_TRIGGER_EVENT */, ngDevMode && `The provided animation trigger event \"${phase}\" for the animation trigger \"${name}\" is not supported!`);\n}\nfunction unregisteredTrigger(name) {\n  return new _RuntimeError(3401 /* RuntimeErrorCode.UNREGISTERED_TRIGGER */, ngDevMode && `The provided animation trigger \"${name}\" has not been registered!`);\n}\nfunction triggerTransitionsFailed(errors) {\n  return new _RuntimeError(3402 /* RuntimeErrorCode.TRIGGER_TRANSITIONS_FAILED */, ngDevMode && `Unable to process animations due to the following failed trigger transitions\\n ${errors.map(err => err.message).join('\\n')}`);\n}\nfunction transitionFailed(name, errors) {\n  return new _RuntimeError(3505 /* RuntimeErrorCode.TRANSITION_FAILED */, ngDevMode && `@${name} has failed due to:\\n ${errors.map(err => err.message).join('\\n- ')}`);\n}\n\n/**\n * Set of all animatable CSS properties\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties\n */\nconst ANIMATABLE_PROP_SET = new Set(['-moz-outline-radius', '-moz-outline-radius-bottomleft', '-moz-outline-radius-bottomright', '-moz-outline-radius-topleft', '-moz-outline-radius-topright', '-ms-grid-columns', '-ms-grid-rows', '-webkit-line-clamp', '-webkit-text-fill-color', '-webkit-text-stroke', '-webkit-text-stroke-color', 'accent-color', 'all', 'backdrop-filter', 'background', 'background-color', 'background-position', 'background-size', 'block-size', 'border', 'border-block-end', 'border-block-end-color', 'border-block-end-width', 'border-block-start', 'border-block-start-color', 'border-block-start-width', 'border-bottom', 'border-bottom-color', 'border-bottom-left-radius', 'border-bottom-right-radius', 'border-bottom-width', 'border-color', 'border-end-end-radius', 'border-end-start-radius', 'border-image-outset', 'border-image-slice', 'border-image-width', 'border-inline-end', 'border-inline-end-color', 'border-inline-end-width', 'border-inline-start', 'border-inline-start-color', 'border-inline-start-width', 'border-left', 'border-left-color', 'border-left-width', 'border-radius', 'border-right', 'border-right-color', 'border-right-width', 'border-start-end-radius', 'border-start-start-radius', 'border-top', 'border-top-color', 'border-top-left-radius', 'border-top-right-radius', 'border-top-width', 'border-width', 'bottom', 'box-shadow', 'caret-color', 'clip', 'clip-path', 'color', 'column-count', 'column-gap', 'column-rule', 'column-rule-color', 'column-rule-width', 'column-width', 'columns', 'filter', 'flex', 'flex-basis', 'flex-grow', 'flex-shrink', 'font', 'font-size', 'font-size-adjust', 'font-stretch', 'font-variation-settings', 'font-weight', 'gap', 'grid-column-gap', 'grid-gap', 'grid-row-gap', 'grid-template-columns', 'grid-template-rows', 'height', 'inline-size', 'input-security', 'inset', 'inset-block', 'inset-block-end', 'inset-block-start', 'inset-inline', 'inset-inline-end', 'inset-inline-start', 'left', 'letter-spacing', 'line-clamp', 'line-height', 'margin', 'margin-block-end', 'margin-block-start', 'margin-bottom', 'margin-inline-end', 'margin-inline-start', 'margin-left', 'margin-right', 'margin-top', 'mask', 'mask-border', 'mask-position', 'mask-size', 'max-block-size', 'max-height', 'max-inline-size', 'max-lines', 'max-width', 'min-block-size', 'min-height', 'min-inline-size', 'min-width', 'object-position', 'offset', 'offset-anchor', 'offset-distance', 'offset-path', 'offset-position', 'offset-rotate', 'opacity', 'order', 'outline', 'outline-color', 'outline-offset', 'outline-width', 'padding', 'padding-block-end', 'padding-block-start', 'padding-bottom', 'padding-inline-end', 'padding-inline-start', 'padding-left', 'padding-right', 'padding-top', 'perspective', 'perspective-origin', 'right', 'rotate', 'row-gap', 'scale', 'scroll-margin', 'scroll-margin-block', 'scroll-margin-block-end', 'scroll-margin-block-start', 'scroll-margin-bottom', 'scroll-margin-inline', 'scroll-margin-inline-end', 'scroll-margin-inline-start', 'scroll-margin-left', 'scroll-margin-right', 'scroll-margin-top', 'scroll-padding', 'scroll-padding-block', 'scroll-padding-block-end', 'scroll-padding-block-start', 'scroll-padding-bottom', 'scroll-padding-inline', 'scroll-padding-inline-end', 'scroll-padding-inline-start', 'scroll-padding-left', 'scroll-padding-right', 'scroll-padding-top', 'scroll-snap-coordinate', 'scroll-snap-destination', 'scrollbar-color', 'shape-image-threshold', 'shape-margin', 'shape-outside', 'tab-size', 'text-decoration', 'text-decoration-color', 'text-decoration-thickness', 'text-emphasis', 'text-emphasis-color', 'text-indent', 'text-shadow', 'text-underline-offset', 'top', 'transform', 'transform-origin', 'translate', 'vertical-align', 'visibility', 'width', 'word-spacing', 'z-index', 'zoom']);\nfunction optimizeGroupPlayer(players) {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n    case 1:\n      return players[0];\n    default:\n      return new AnimationGroupPlayer(players);\n  }\n}\nfunction normalizeKeyframes$1(normalizer, keyframes, preStyles = new Map(), postStyles = new Map()) {\n  const errors = [];\n  const normalizedKeyframes = [];\n  let previousOffset = -1;\n  let previousKeyframe = null;\n  keyframes.forEach(kf => {\n    const offset = kf.get('offset');\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe = isSameOffset && previousKeyframe || new Map();\n    kf.forEach((val, prop) => {\n      let normalizedProp = prop;\n      let normalizedValue = val;\n      if (prop !== 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n        switch (normalizedValue) {\n          case _PRE_STYLE:\n            normalizedValue = preStyles.get(prop);\n            break;\n          case AUTO_STYLE:\n            normalizedValue = postStyles.get(prop);\n            break;\n          default:\n            normalizedValue = normalizer.normalizeStyleValue(prop, normalizedProp, normalizedValue, errors);\n            break;\n        }\n      }\n      normalizedKeyframe.set(normalizedProp, normalizedValue);\n    });\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n  if (errors.length) {\n    throw animationFailed(errors);\n  }\n  return normalizedKeyframes;\n}\nfunction listenOnPlayer(player, eventName, event, callback) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player)));\n      break;\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player)));\n      break;\n    case 'destroy':\n      player.onDestroy(() => callback(event && copyAnimationEvent(event, 'destroy', player)));\n      break;\n  }\n}\nfunction copyAnimationEvent(e, phaseName, player) {\n  const totalTime = player.totalTime;\n  const disabled = player.disabled ? true : false;\n  const event = makeAnimationEvent(e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName, totalTime == undefined ? e.totalTime : totalTime, disabled);\n  const data = e['_data'];\n  if (data != null) {\n    event['_data'] = data;\n  }\n  return event;\n}\nfunction makeAnimationEvent(element, triggerName, fromState, toState, phaseName = '', totalTime = 0, disabled) {\n  return {\n    element,\n    triggerName,\n    fromState,\n    toState,\n    phaseName,\n    totalTime,\n    disabled: !!disabled\n  };\n}\nfunction getOrSetDefaultValue(map, key, defaultValue) {\n  let value = map.get(key);\n  if (!value) {\n    map.set(key, value = defaultValue);\n  }\n  return value;\n}\nfunction parseTimelineCommand(command) {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.slice(separatorPos + 1);\n  return [id, action];\n}\nconst documentElement = /* @__PURE__ */(() => typeof document === 'undefined' ? null : document.documentElement)();\nfunction getParentElement(element) {\n  const parent = element.parentNode || element.host || null; // consider host to support shadow DOM\n  if (parent === documentElement) {\n    return null;\n  }\n  return parent;\n}\nfunction containsVendorPrefix(prop) {\n  // Webkit is the only real popular vendor prefix nowadays\n  // cc: http://shouldiprefix.com/\n  return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n}\nlet _CACHED_BODY = null;\nlet _IS_WEBKIT = false;\nfunction validateStyleProperty(prop) {\n  if (!_CACHED_BODY) {\n    _CACHED_BODY = getBodyNode() || {};\n    _IS_WEBKIT = _CACHED_BODY.style ? 'WebkitAppearance' in _CACHED_BODY.style : false;\n  }\n  let result = true;\n  if (_CACHED_BODY.style && !containsVendorPrefix(prop)) {\n    result = prop in _CACHED_BODY.style;\n    if (!result && _IS_WEBKIT) {\n      const camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.slice(1);\n      result = camelProp in _CACHED_BODY.style;\n    }\n  }\n  return result;\n}\nfunction validateWebAnimatableStyleProperty(prop) {\n  return ANIMATABLE_PROP_SET.has(prop);\n}\nfunction getBodyNode() {\n  if (typeof document != 'undefined') {\n    return document.body;\n  }\n  return null;\n}\nfunction containsElement(elm1, elm2) {\n  while (elm2) {\n    if (elm2 === elm1) {\n      return true;\n    }\n    elm2 = getParentElement(elm2);\n  }\n  return false;\n}\nfunction invokeQuery(element, selector, multi) {\n  if (multi) {\n    return Array.from(element.querySelectorAll(selector));\n  }\n  const elem = element.querySelector(selector);\n  return elem ? [elem] : [];\n}\nconst ONE_SECOND = 1000;\nconst SUBSTITUTION_EXPR_START = '{{';\nconst SUBSTITUTION_EXPR_END = '}}';\nconst ENTER_CLASSNAME = 'ng-enter';\nconst LEAVE_CLASSNAME = 'ng-leave';\nconst NG_TRIGGER_CLASSNAME = 'ng-trigger';\nconst NG_TRIGGER_SELECTOR = '.ng-trigger';\nconst NG_ANIMATING_CLASSNAME = 'ng-animating';\nconst NG_ANIMATING_SELECTOR = '.ng-animating';\nfunction resolveTimingValue(value) {\n  if (typeof value == 'number') return value;\n  const matches = value.match(/^(-?[\\.\\d]+)(m?s)/);\n  if (!matches || matches.length < 2) return 0;\n  return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\nfunction _convertTimeValueToMS(value, unit) {\n  switch (unit) {\n    case 's':\n      return value * ONE_SECOND;\n    default:\n      // ms or something else\n      return value;\n  }\n}\nfunction resolveTiming(timings, errors, allowNegativeValues) {\n  return timings.hasOwnProperty('duration') ? timings : parseTimeExpression(timings, errors, allowNegativeValues);\n}\nfunction parseTimeExpression(exp, errors, allowNegativeValues) {\n  const regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n  let duration;\n  let delay = 0;\n  let easing = '';\n  if (typeof exp === 'string') {\n    const matches = exp.match(regex);\n    if (matches === null) {\n      errors.push(invalidTimingValue(exp));\n      return {\n        duration: 0,\n        delay: 0,\n        easing: ''\n      };\n    }\n    duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n    const delayMatch = matches[3];\n    if (delayMatch != null) {\n      delay = _convertTimeValueToMS(parseFloat(delayMatch), matches[4]);\n    }\n    const easingVal = matches[5];\n    if (easingVal) {\n      easing = easingVal;\n    }\n  } else {\n    duration = exp;\n  }\n  if (!allowNegativeValues) {\n    let containsErrors = false;\n    let startIndex = errors.length;\n    if (duration < 0) {\n      errors.push(negativeStepValue());\n      containsErrors = true;\n    }\n    if (delay < 0) {\n      errors.push(negativeDelayValue());\n      containsErrors = true;\n    }\n    if (containsErrors) {\n      errors.splice(startIndex, 0, invalidTimingValue(exp));\n    }\n  }\n  return {\n    duration,\n    delay,\n    easing\n  };\n}\nfunction normalizeKeyframes(keyframes) {\n  if (!keyframes.length) {\n    return [];\n  }\n  if (keyframes[0] instanceof Map) {\n    return keyframes;\n  }\n  return keyframes.map(kf => new Map(Object.entries(kf)));\n}\nfunction normalizeStyles(styles) {\n  return Array.isArray(styles) ? new Map(...styles) : new Map(styles);\n}\nfunction setStyles(element, styles, formerStyles) {\n  styles.forEach((val, prop) => {\n    const camelProp = dashCaseToCamelCase(prop);\n    if (formerStyles && !formerStyles.has(prop)) {\n      formerStyles.set(prop, element.style[camelProp]);\n    }\n    element.style[camelProp] = val;\n  });\n}\nfunction eraseStyles(element, styles) {\n  styles.forEach((_, prop) => {\n    const camelProp = dashCaseToCamelCase(prop);\n    element.style[camelProp] = '';\n  });\n}\nfunction normalizeAnimationEntry(steps) {\n  if (Array.isArray(steps)) {\n    if (steps.length == 1) return steps[0];\n    return sequence(steps);\n  }\n  return steps;\n}\nfunction validateStyleParams(value, options, errors) {\n  const params = options.params || {};\n  const matches = extractStyleParams(value);\n  if (matches.length) {\n    matches.forEach(varName => {\n      if (!params.hasOwnProperty(varName)) {\n        errors.push(invalidStyleParams(varName));\n      }\n    });\n  }\n}\nconst PARAM_REGEX = /* @__PURE__ */new RegExp(`${SUBSTITUTION_EXPR_START}\\\\s*(.+?)\\\\s*${SUBSTITUTION_EXPR_END}`, 'g');\nfunction extractStyleParams(value) {\n  let params = [];\n  if (typeof value === 'string') {\n    let match;\n    while (match = PARAM_REGEX.exec(value)) {\n      params.push(match[1]);\n    }\n    PARAM_REGEX.lastIndex = 0;\n  }\n  return params;\n}\nfunction interpolateParams(value, params, errors) {\n  const original = `${value}`;\n  const str = original.replace(PARAM_REGEX, (_, varName) => {\n    let localVal = params[varName];\n    // this means that the value was never overridden by the data passed in by the user\n    if (localVal == null) {\n      errors.push(invalidParamValue(varName));\n      localVal = '';\n    }\n    return localVal.toString();\n  });\n  // we do this to assert that numeric values stay as they are\n  return str == original ? value : str;\n}\nconst DASH_CASE_REGEXP = /-+([a-z0-9])/g;\nfunction dashCaseToCamelCase(input) {\n  return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\nfunction camelCaseToDashCase(input) {\n  return input.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\nfunction allowPreviousPlayerStylesMerge(duration, delay) {\n  return duration === 0 || delay === 0;\n}\nfunction balancePreviousStylesIntoKeyframes(element, keyframes, previousStyles) {\n  if (previousStyles.size && keyframes.length) {\n    let startingKeyframe = keyframes[0];\n    let missingStyleProps = [];\n    previousStyles.forEach((val, prop) => {\n      if (!startingKeyframe.has(prop)) {\n        missingStyleProps.push(prop);\n      }\n      startingKeyframe.set(prop, val);\n    });\n    if (missingStyleProps.length) {\n      for (let i = 1; i < keyframes.length; i++) {\n        let kf = keyframes[i];\n        missingStyleProps.forEach(prop => kf.set(prop, computeStyle(element, prop)));\n      }\n    }\n  }\n  return keyframes;\n}\nfunction visitDslNode(visitor, node, context) {\n  switch (node.type) {\n    case AnimationMetadataType.Trigger:\n      return visitor.visitTrigger(node, context);\n    case AnimationMetadataType.State:\n      return visitor.visitState(node, context);\n    case AnimationMetadataType.Transition:\n      return visitor.visitTransition(node, context);\n    case AnimationMetadataType.Sequence:\n      return visitor.visitSequence(node, context);\n    case AnimationMetadataType.Group:\n      return visitor.visitGroup(node, context);\n    case AnimationMetadataType.Animate:\n      return visitor.visitAnimate(node, context);\n    case AnimationMetadataType.Keyframes:\n      return visitor.visitKeyframes(node, context);\n    case AnimationMetadataType.Style:\n      return visitor.visitStyle(node, context);\n    case AnimationMetadataType.Reference:\n      return visitor.visitReference(node, context);\n    case AnimationMetadataType.AnimateChild:\n      return visitor.visitAnimateChild(node, context);\n    case AnimationMetadataType.AnimateRef:\n      return visitor.visitAnimateRef(node, context);\n    case AnimationMetadataType.Query:\n      return visitor.visitQuery(node, context);\n    case AnimationMetadataType.Stagger:\n      return visitor.visitStagger(node, context);\n    default:\n      throw invalidNodeType(node.type);\n  }\n}\nfunction computeStyle(element, prop) {\n  return window.getComputedStyle(element)[prop];\n}\nexport { ENTER_CLASSNAME, LEAVE_CLASSNAME, NG_ANIMATING_CLASSNAME, NG_ANIMATING_SELECTOR, NG_TRIGGER_CLASSNAME, NG_TRIGGER_SELECTOR, SUBSTITUTION_EXPR_START, allowPreviousPlayerStylesMerge, balancePreviousStylesIntoKeyframes, buildingFailed, camelCaseToDashCase, computeStyle, containsElement, createAnimationFailed, dashCaseToCamelCase, eraseStyles, extractStyleParams, getOrSetDefaultValue, getParentElement, interpolateParams, invalidCssUnitValue, invalidDefinition, invalidExpression, invalidKeyframes, invalidOffset, invalidParallelAnimation, invalidQuery, invalidStagger, invalidState, invalidStyleValue, invalidTransitionAlias, invalidTrigger, invokeQuery, keyframeOffsetsOutOfOrder, keyframesMissingOffsets, listenOnPlayer, makeAnimationEvent, missingEvent, missingOrDestroyedAnimation, missingPlayer, missingTrigger, normalizeAnimationEntry, normalizeKeyframes$1 as normalizeKeyframes, normalizeKeyframes as normalizeKeyframes$1, normalizeStyles, optimizeGroupPlayer, parseTimelineCommand, registerFailed, resolveTiming, resolveTimingValue, setStyles, transitionFailed, triggerBuildFailed, triggerTransitionsFailed, unregisteredTrigger, unsupportedTriggerEvent, validateStyleParams, validateStyleProperty, validateWebAnimatableStyleProperty, validationFailed, visitDslNode };", "map": {"version": 3, "names": ["AnimationGroupPlayer", "NoopAnimationPlayer", "AUTO_STYLE", "ɵPRE_STYLE", "_PRE_STYLE", "AnimationMetadataType", "sequence", "ɵRuntimeError", "_RuntimeError", "LINE_START", "invalidT<PERSON>ing<PERSON><PERSON><PERSON>", "exp", "ngDevMode", "negativeStepValue", "negativeDelayValue", "invalidStyleParams", "varName", "invalidParamV<PERSON>ue", "invalidNodeType", "nodeType", "invalidCssUnitValue", "userProvidedProperty", "value", "invalid<PERSON><PERSON>ger", "invalidDefinition", "invalidState", "metadataName", "missingSubs", "join", "invalidStyleV<PERSON>ue", "invalidParallelAnimation", "prop", "firstStart", "firstEnd", "secondStart", "secondEnd", "invalidKeyframes", "invalidOffset", "keyframeOffsetsOutOfOrder", "keyframesMissingOffsets", "invalidStagger", "<PERSON><PERSON><PERSON><PERSON>", "selector", "invalidExpression", "expr", "invalidTransitionAlias", "alias", "validationFailed", "errors", "map", "err", "message", "buildingFailed", "triggerBuildFailed", "name", "animationFailed", "registerFailed", "missingOrDestroyedAnimation", "createAnimationFailed", "missingPlayer", "id", "missing<PERSON><PERSON>ger", "phase", "missingEvent", "unsupportedTriggerEvent", "unregisteredTrigger", "triggerTransitionsFailed", "transitionFailed", "ANIMATABLE_PROP_SET", "Set", "optimizeGroupPlayer", "players", "length", "normalizeKeyframes$1", "normalizer", "keyframes", "preStyles", "Map", "postStyles", "normalizedKeyframes", "previousOffset", "previousKeyframe", "for<PERSON>ach", "kf", "offset", "get", "isSameOffset", "normalizedKeyframe", "val", "normalizedProp", "normalizedValue", "normalizePropertyName", "normalizeStyleValue", "set", "push", "listenOnPlayer", "player", "eventName", "event", "callback", "onStart", "copyAnimationEvent", "onDone", "onDestroy", "e", "phaseName", "totalTime", "disabled", "makeAnimationEvent", "element", "triggerName", "fromState", "toState", "undefined", "data", "getOrSetDefaultValue", "key", "defaultValue", "parseTimelineCommand", "command", "separatorPos", "indexOf", "substring", "action", "slice", "documentElement", "document", "getParentElement", "parent", "parentNode", "host", "containsVendorPrefix", "_CACHED_BODY", "_IS_WEBKIT", "validateStyleProperty", "getBodyNode", "style", "result", "camelProp", "char<PERSON>t", "toUpperCase", "validateWebAnimatableStyleProperty", "has", "body", "containsElement", "elm1", "elm2", "invoke<PERSON><PERSON>y", "multi", "Array", "from", "querySelectorAll", "elem", "querySelector", "ONE_SECOND", "SUBSTITUTION_EXPR_START", "SUBSTITUTION_EXPR_END", "ENTER_CLASSNAME", "LEAVE_CLASSNAME", "NG_TRIGGER_CLASSNAME", "NG_TRIGGER_SELECTOR", "NG_ANIMATING_CLASSNAME", "NG_ANIMATING_SELECTOR", "resolveTimingValue", "matches", "match", "_convertTimeValueToMS", "parseFloat", "unit", "resolveTiming", "timings", "allowNegativeValues", "hasOwnProperty", "parseTimeExpression", "regex", "duration", "delay", "easing", "delayMatch", "easingVal", "containsErrors", "startIndex", "splice", "normalizeKeyframes", "Object", "entries", "normalizeStyles", "styles", "isArray", "setStyles", "formerStyles", "dashCaseToCamelCase", "eraseStyles", "_", "normalizeAnimationEntry", "steps", "validateStyleParams", "options", "params", "extractStyleParams", "PARAM_REGEX", "RegExp", "exec", "lastIndex", "interpolateParams", "original", "str", "replace", "localVal", "toString", "DASH_CASE_REGEXP", "input", "m", "camelCaseToDashCase", "toLowerCase", "allowPreviousPlayerStylesMerge", "balancePreviousStylesIntoKeyframes", "previousStyles", "size", "startingKeyframe", "missingStyleProps", "i", "computeStyle", "visitDslNode", "visitor", "node", "context", "type", "<PERSON><PERSON>", "visitTrigger", "State", "visitState", "Transition", "visitTransition", "Sequence", "visitSequence", "Group", "visitGroup", "Animate", "visitAnimate", "Keyframes", "visitKeyframes", "Style", "visitStyle", "Reference", "visitReference", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visitAnimateChild", "AnimateRef", "visitAnimateRef", "Query", "visit<PERSON><PERSON><PERSON>", "Stagger", "visitStagger", "window", "getComputedStyle"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/@angular/animations/fesm2022/util-DN3Vao_r.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { AnimationGroupPlayer, NoopAnimationPlayer, AUTO_STYLE, ɵPRE_STYLE as _PRE_STYLE, AnimationMetadataType, sequence } from './private_export-CacKMzxJ.mjs';\nimport { ɵRuntimeError as _RuntimeError } from '@angular/core';\n\nconst LINE_START = '\\n - ';\nfunction invalidTimingValue(exp) {\n    return new _RuntimeError(3000 /* RuntimeErrorCode.INVALID_TIMING_VALUE */, ngDevMode && `The provided timing value \"${exp}\" is invalid.`);\n}\nfunction negativeStepValue() {\n    return new _RuntimeError(3100 /* RuntimeErrorCode.NEGATIVE_STEP_VALUE */, ngDevMode && 'Duration values below 0 are not allowed for this animation step.');\n}\nfunction negativeDelayValue() {\n    return new _RuntimeError(3101 /* RuntimeErrorCode.NEGATIVE_DELAY_VALUE */, ngDevMode && 'Delay values below 0 are not allowed for this animation step.');\n}\nfunction invalidStyleParams(varName) {\n    return new _RuntimeError(3001 /* RuntimeErrorCode.INVALID_STYLE_PARAMS */, ngDevMode &&\n        `Unable to resolve the local animation param ${varName} in the given list of values`);\n}\nfunction invalidParamValue(varName) {\n    return new _RuntimeError(3003 /* RuntimeErrorCode.INVALID_PARAM_VALUE */, ngDevMode && `Please provide a value for the animation param ${varName}`);\n}\nfunction invalidNodeType(nodeType) {\n    return new _RuntimeError(3004 /* RuntimeErrorCode.INVALID_NODE_TYPE */, ngDevMode && `Unable to resolve animation metadata node #${nodeType}`);\n}\nfunction invalidCssUnitValue(userProvidedProperty, value) {\n    return new _RuntimeError(3005 /* RuntimeErrorCode.INVALID_CSS_UNIT_VALUE */, ngDevMode && `Please provide a CSS unit value for ${userProvidedProperty}:${value}`);\n}\nfunction invalidTrigger() {\n    return new _RuntimeError(3006 /* RuntimeErrorCode.INVALID_TRIGGER */, ngDevMode &&\n        \"animation triggers cannot be prefixed with an `@` sign (e.g. trigger('@foo', [...]))\");\n}\nfunction invalidDefinition() {\n    return new _RuntimeError(3007 /* RuntimeErrorCode.INVALID_DEFINITION */, ngDevMode && 'only state() and transition() definitions can sit inside of a trigger()');\n}\nfunction invalidState(metadataName, missingSubs) {\n    return new _RuntimeError(3008 /* RuntimeErrorCode.INVALID_STATE */, ngDevMode &&\n        `state(\"${metadataName}\", ...) must define default values for all the following style substitutions: ${missingSubs.join(', ')}`);\n}\nfunction invalidStyleValue(value) {\n    return new _RuntimeError(3002 /* RuntimeErrorCode.INVALID_STYLE_VALUE */, ngDevMode && `The provided style string value ${value} is not allowed.`);\n}\nfunction invalidParallelAnimation(prop, firstStart, firstEnd, secondStart, secondEnd) {\n    return new _RuntimeError(3010 /* RuntimeErrorCode.INVALID_PARALLEL_ANIMATION */, ngDevMode &&\n        `The CSS property \"${prop}\" that exists between the times of \"${firstStart}ms\" and \"${firstEnd}ms\" is also being animated in a parallel animation between the times of \"${secondStart}ms\" and \"${secondEnd}ms\"`);\n}\nfunction invalidKeyframes() {\n    return new _RuntimeError(3011 /* RuntimeErrorCode.INVALID_KEYFRAMES */, ngDevMode && `keyframes() must be placed inside of a call to animate()`);\n}\nfunction invalidOffset() {\n    return new _RuntimeError(3012 /* RuntimeErrorCode.INVALID_OFFSET */, ngDevMode && `Please ensure that all keyframe offsets are between 0 and 1`);\n}\nfunction keyframeOffsetsOutOfOrder() {\n    return new _RuntimeError(3200 /* RuntimeErrorCode.KEYFRAME_OFFSETS_OUT_OF_ORDER */, ngDevMode && `Please ensure that all keyframe offsets are in order`);\n}\nfunction keyframesMissingOffsets() {\n    return new _RuntimeError(3202 /* RuntimeErrorCode.KEYFRAMES_MISSING_OFFSETS */, ngDevMode && `Not all style() steps within the declared keyframes() contain offsets`);\n}\nfunction invalidStagger() {\n    return new _RuntimeError(3013 /* RuntimeErrorCode.INVALID_STAGGER */, ngDevMode && `stagger() can only be used inside of query()`);\n}\nfunction invalidQuery(selector) {\n    return new _RuntimeError(3014 /* RuntimeErrorCode.INVALID_QUERY */, ngDevMode &&\n        `\\`query(\"${selector}\")\\` returned zero elements. (Use \\`query(\"${selector}\", { optional: true })\\` if you wish to allow this.)`);\n}\nfunction invalidExpression(expr) {\n    return new _RuntimeError(3015 /* RuntimeErrorCode.INVALID_EXPRESSION */, ngDevMode && `The provided transition expression \"${expr}\" is not supported`);\n}\nfunction invalidTransitionAlias(alias) {\n    return new _RuntimeError(3016 /* RuntimeErrorCode.INVALID_TRANSITION_ALIAS */, ngDevMode && `The transition alias value \"${alias}\" is not supported`);\n}\nfunction validationFailed(errors) {\n    return new _RuntimeError(3500 /* RuntimeErrorCode.VALIDATION_FAILED */, ngDevMode && `animation validation failed:\\n${errors.map((err) => err.message).join('\\n')}`);\n}\nfunction buildingFailed(errors) {\n    return new _RuntimeError(3501 /* RuntimeErrorCode.BUILDING_FAILED */, ngDevMode && `animation building failed:\\n${errors.map((err) => err.message).join('\\n')}`);\n}\nfunction triggerBuildFailed(name, errors) {\n    return new _RuntimeError(3404 /* RuntimeErrorCode.TRIGGER_BUILD_FAILED */, ngDevMode &&\n        `The animation trigger \"${name}\" has failed to build due to the following errors:\\n - ${errors\n            .map((err) => err.message)\n            .join('\\n - ')}`);\n}\nfunction animationFailed(errors) {\n    return new _RuntimeError(3502 /* RuntimeErrorCode.ANIMATION_FAILED */, ngDevMode &&\n        `Unable to animate due to the following errors:${LINE_START}${errors\n            .map((err) => err.message)\n            .join(LINE_START)}`);\n}\nfunction registerFailed(errors) {\n    return new _RuntimeError(3503 /* RuntimeErrorCode.REGISTRATION_FAILED */, ngDevMode &&\n        `Unable to build the animation due to the following errors: ${errors\n            .map((err) => err.message)\n            .join('\\n')}`);\n}\nfunction missingOrDestroyedAnimation() {\n    return new _RuntimeError(3300 /* RuntimeErrorCode.MISSING_OR_DESTROYED_ANIMATION */, ngDevMode && \"The requested animation doesn't exist or has already been destroyed\");\n}\nfunction createAnimationFailed(errors) {\n    return new _RuntimeError(3504 /* RuntimeErrorCode.CREATE_ANIMATION_FAILED */, ngDevMode &&\n        `Unable to create the animation due to the following errors:${errors\n            .map((err) => err.message)\n            .join('\\n')}`);\n}\nfunction missingPlayer(id) {\n    return new _RuntimeError(3301 /* RuntimeErrorCode.MISSING_PLAYER */, ngDevMode && `Unable to find the timeline player referenced by ${id}`);\n}\nfunction missingTrigger(phase, name) {\n    return new _RuntimeError(3302 /* RuntimeErrorCode.MISSING_TRIGGER */, ngDevMode &&\n        `Unable to listen on the animation trigger event \"${phase}\" because the animation trigger \"${name}\" doesn\\'t exist!`);\n}\nfunction missingEvent(name) {\n    return new _RuntimeError(3303 /* RuntimeErrorCode.MISSING_EVENT */, ngDevMode &&\n        `Unable to listen on the animation trigger \"${name}\" because the provided event is undefined!`);\n}\nfunction unsupportedTriggerEvent(phase, name) {\n    return new _RuntimeError(3400 /* RuntimeErrorCode.UNSUPPORTED_TRIGGER_EVENT */, ngDevMode &&\n        `The provided animation trigger event \"${phase}\" for the animation trigger \"${name}\" is not supported!`);\n}\nfunction unregisteredTrigger(name) {\n    return new _RuntimeError(3401 /* RuntimeErrorCode.UNREGISTERED_TRIGGER */, ngDevMode && `The provided animation trigger \"${name}\" has not been registered!`);\n}\nfunction triggerTransitionsFailed(errors) {\n    return new _RuntimeError(3402 /* RuntimeErrorCode.TRIGGER_TRANSITIONS_FAILED */, ngDevMode &&\n        `Unable to process animations due to the following failed trigger transitions\\n ${errors\n            .map((err) => err.message)\n            .join('\\n')}`);\n}\nfunction transitionFailed(name, errors) {\n    return new _RuntimeError(3505 /* RuntimeErrorCode.TRANSITION_FAILED */, ngDevMode && `@${name} has failed due to:\\n ${errors.map((err) => err.message).join('\\n- ')}`);\n}\n\n/**\n * Set of all animatable CSS properties\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties\n */\nconst ANIMATABLE_PROP_SET = new Set([\n    '-moz-outline-radius',\n    '-moz-outline-radius-bottomleft',\n    '-moz-outline-radius-bottomright',\n    '-moz-outline-radius-topleft',\n    '-moz-outline-radius-topright',\n    '-ms-grid-columns',\n    '-ms-grid-rows',\n    '-webkit-line-clamp',\n    '-webkit-text-fill-color',\n    '-webkit-text-stroke',\n    '-webkit-text-stroke-color',\n    'accent-color',\n    'all',\n    'backdrop-filter',\n    'background',\n    'background-color',\n    'background-position',\n    'background-size',\n    'block-size',\n    'border',\n    'border-block-end',\n    'border-block-end-color',\n    'border-block-end-width',\n    'border-block-start',\n    'border-block-start-color',\n    'border-block-start-width',\n    'border-bottom',\n    'border-bottom-color',\n    'border-bottom-left-radius',\n    'border-bottom-right-radius',\n    'border-bottom-width',\n    'border-color',\n    'border-end-end-radius',\n    'border-end-start-radius',\n    'border-image-outset',\n    'border-image-slice',\n    'border-image-width',\n    'border-inline-end',\n    'border-inline-end-color',\n    'border-inline-end-width',\n    'border-inline-start',\n    'border-inline-start-color',\n    'border-inline-start-width',\n    'border-left',\n    'border-left-color',\n    'border-left-width',\n    'border-radius',\n    'border-right',\n    'border-right-color',\n    'border-right-width',\n    'border-start-end-radius',\n    'border-start-start-radius',\n    'border-top',\n    'border-top-color',\n    'border-top-left-radius',\n    'border-top-right-radius',\n    'border-top-width',\n    'border-width',\n    'bottom',\n    'box-shadow',\n    'caret-color',\n    'clip',\n    'clip-path',\n    'color',\n    'column-count',\n    'column-gap',\n    'column-rule',\n    'column-rule-color',\n    'column-rule-width',\n    'column-width',\n    'columns',\n    'filter',\n    'flex',\n    'flex-basis',\n    'flex-grow',\n    'flex-shrink',\n    'font',\n    'font-size',\n    'font-size-adjust',\n    'font-stretch',\n    'font-variation-settings',\n    'font-weight',\n    'gap',\n    'grid-column-gap',\n    'grid-gap',\n    'grid-row-gap',\n    'grid-template-columns',\n    'grid-template-rows',\n    'height',\n    'inline-size',\n    'input-security',\n    'inset',\n    'inset-block',\n    'inset-block-end',\n    'inset-block-start',\n    'inset-inline',\n    'inset-inline-end',\n    'inset-inline-start',\n    'left',\n    'letter-spacing',\n    'line-clamp',\n    'line-height',\n    'margin',\n    'margin-block-end',\n    'margin-block-start',\n    'margin-bottom',\n    'margin-inline-end',\n    'margin-inline-start',\n    'margin-left',\n    'margin-right',\n    'margin-top',\n    'mask',\n    'mask-border',\n    'mask-position',\n    'mask-size',\n    'max-block-size',\n    'max-height',\n    'max-inline-size',\n    'max-lines',\n    'max-width',\n    'min-block-size',\n    'min-height',\n    'min-inline-size',\n    'min-width',\n    'object-position',\n    'offset',\n    'offset-anchor',\n    'offset-distance',\n    'offset-path',\n    'offset-position',\n    'offset-rotate',\n    'opacity',\n    'order',\n    'outline',\n    'outline-color',\n    'outline-offset',\n    'outline-width',\n    'padding',\n    'padding-block-end',\n    'padding-block-start',\n    'padding-bottom',\n    'padding-inline-end',\n    'padding-inline-start',\n    'padding-left',\n    'padding-right',\n    'padding-top',\n    'perspective',\n    'perspective-origin',\n    'right',\n    'rotate',\n    'row-gap',\n    'scale',\n    'scroll-margin',\n    'scroll-margin-block',\n    'scroll-margin-block-end',\n    'scroll-margin-block-start',\n    'scroll-margin-bottom',\n    'scroll-margin-inline',\n    'scroll-margin-inline-end',\n    'scroll-margin-inline-start',\n    'scroll-margin-left',\n    'scroll-margin-right',\n    'scroll-margin-top',\n    'scroll-padding',\n    'scroll-padding-block',\n    'scroll-padding-block-end',\n    'scroll-padding-block-start',\n    'scroll-padding-bottom',\n    'scroll-padding-inline',\n    'scroll-padding-inline-end',\n    'scroll-padding-inline-start',\n    'scroll-padding-left',\n    'scroll-padding-right',\n    'scroll-padding-top',\n    'scroll-snap-coordinate',\n    'scroll-snap-destination',\n    'scrollbar-color',\n    'shape-image-threshold',\n    'shape-margin',\n    'shape-outside',\n    'tab-size',\n    'text-decoration',\n    'text-decoration-color',\n    'text-decoration-thickness',\n    'text-emphasis',\n    'text-emphasis-color',\n    'text-indent',\n    'text-shadow',\n    'text-underline-offset',\n    'top',\n    'transform',\n    'transform-origin',\n    'translate',\n    'vertical-align',\n    'visibility',\n    'width',\n    'word-spacing',\n    'z-index',\n    'zoom',\n]);\n\nfunction optimizeGroupPlayer(players) {\n    switch (players.length) {\n        case 0:\n            return new NoopAnimationPlayer();\n        case 1:\n            return players[0];\n        default:\n            return new AnimationGroupPlayer(players);\n    }\n}\nfunction normalizeKeyframes$1(normalizer, keyframes, preStyles = new Map(), postStyles = new Map()) {\n    const errors = [];\n    const normalizedKeyframes = [];\n    let previousOffset = -1;\n    let previousKeyframe = null;\n    keyframes.forEach((kf) => {\n        const offset = kf.get('offset');\n        const isSameOffset = offset == previousOffset;\n        const normalizedKeyframe = (isSameOffset && previousKeyframe) || new Map();\n        kf.forEach((val, prop) => {\n            let normalizedProp = prop;\n            let normalizedValue = val;\n            if (prop !== 'offset') {\n                normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n                switch (normalizedValue) {\n                    case _PRE_STYLE:\n                        normalizedValue = preStyles.get(prop);\n                        break;\n                    case AUTO_STYLE:\n                        normalizedValue = postStyles.get(prop);\n                        break;\n                    default:\n                        normalizedValue = normalizer.normalizeStyleValue(prop, normalizedProp, normalizedValue, errors);\n                        break;\n                }\n            }\n            normalizedKeyframe.set(normalizedProp, normalizedValue);\n        });\n        if (!isSameOffset) {\n            normalizedKeyframes.push(normalizedKeyframe);\n        }\n        previousKeyframe = normalizedKeyframe;\n        previousOffset = offset;\n    });\n    if (errors.length) {\n        throw animationFailed(errors);\n    }\n    return normalizedKeyframes;\n}\nfunction listenOnPlayer(player, eventName, event, callback) {\n    switch (eventName) {\n        case 'start':\n            player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player)));\n            break;\n        case 'done':\n            player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player)));\n            break;\n        case 'destroy':\n            player.onDestroy(() => callback(event && copyAnimationEvent(event, 'destroy', player)));\n            break;\n    }\n}\nfunction copyAnimationEvent(e, phaseName, player) {\n    const totalTime = player.totalTime;\n    const disabled = player.disabled ? true : false;\n    const event = makeAnimationEvent(e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName, totalTime == undefined ? e.totalTime : totalTime, disabled);\n    const data = e['_data'];\n    if (data != null) {\n        event['_data'] = data;\n    }\n    return event;\n}\nfunction makeAnimationEvent(element, triggerName, fromState, toState, phaseName = '', totalTime = 0, disabled) {\n    return { element, triggerName, fromState, toState, phaseName, totalTime, disabled: !!disabled };\n}\nfunction getOrSetDefaultValue(map, key, defaultValue) {\n    let value = map.get(key);\n    if (!value) {\n        map.set(key, (value = defaultValue));\n    }\n    return value;\n}\nfunction parseTimelineCommand(command) {\n    const separatorPos = command.indexOf(':');\n    const id = command.substring(1, separatorPos);\n    const action = command.slice(separatorPos + 1);\n    return [id, action];\n}\nconst documentElement = /* @__PURE__ */ (() => typeof document === 'undefined' ? null : document.documentElement)();\nfunction getParentElement(element) {\n    const parent = element.parentNode || element.host || null; // consider host to support shadow DOM\n    if (parent === documentElement) {\n        return null;\n    }\n    return parent;\n}\nfunction containsVendorPrefix(prop) {\n    // Webkit is the only real popular vendor prefix nowadays\n    // cc: http://shouldiprefix.com/\n    return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n}\nlet _CACHED_BODY = null;\nlet _IS_WEBKIT = false;\nfunction validateStyleProperty(prop) {\n    if (!_CACHED_BODY) {\n        _CACHED_BODY = getBodyNode() || {};\n        _IS_WEBKIT = _CACHED_BODY.style ? 'WebkitAppearance' in _CACHED_BODY.style : false;\n    }\n    let result = true;\n    if (_CACHED_BODY.style && !containsVendorPrefix(prop)) {\n        result = prop in _CACHED_BODY.style;\n        if (!result && _IS_WEBKIT) {\n            const camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.slice(1);\n            result = camelProp in _CACHED_BODY.style;\n        }\n    }\n    return result;\n}\nfunction validateWebAnimatableStyleProperty(prop) {\n    return ANIMATABLE_PROP_SET.has(prop);\n}\nfunction getBodyNode() {\n    if (typeof document != 'undefined') {\n        return document.body;\n    }\n    return null;\n}\nfunction containsElement(elm1, elm2) {\n    while (elm2) {\n        if (elm2 === elm1) {\n            return true;\n        }\n        elm2 = getParentElement(elm2);\n    }\n    return false;\n}\nfunction invokeQuery(element, selector, multi) {\n    if (multi) {\n        return Array.from(element.querySelectorAll(selector));\n    }\n    const elem = element.querySelector(selector);\n    return elem ? [elem] : [];\n}\n\nconst ONE_SECOND = 1000;\nconst SUBSTITUTION_EXPR_START = '{{';\nconst SUBSTITUTION_EXPR_END = '}}';\nconst ENTER_CLASSNAME = 'ng-enter';\nconst LEAVE_CLASSNAME = 'ng-leave';\nconst NG_TRIGGER_CLASSNAME = 'ng-trigger';\nconst NG_TRIGGER_SELECTOR = '.ng-trigger';\nconst NG_ANIMATING_CLASSNAME = 'ng-animating';\nconst NG_ANIMATING_SELECTOR = '.ng-animating';\nfunction resolveTimingValue(value) {\n    if (typeof value == 'number')\n        return value;\n    const matches = value.match(/^(-?[\\.\\d]+)(m?s)/);\n    if (!matches || matches.length < 2)\n        return 0;\n    return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\nfunction _convertTimeValueToMS(value, unit) {\n    switch (unit) {\n        case 's':\n            return value * ONE_SECOND;\n        default: // ms or something else\n            return value;\n    }\n}\nfunction resolveTiming(timings, errors, allowNegativeValues) {\n    return timings.hasOwnProperty('duration')\n        ? timings\n        : parseTimeExpression(timings, errors, allowNegativeValues);\n}\nfunction parseTimeExpression(exp, errors, allowNegativeValues) {\n    const regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n    let duration;\n    let delay = 0;\n    let easing = '';\n    if (typeof exp === 'string') {\n        const matches = exp.match(regex);\n        if (matches === null) {\n            errors.push(invalidTimingValue(exp));\n            return { duration: 0, delay: 0, easing: '' };\n        }\n        duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n        const delayMatch = matches[3];\n        if (delayMatch != null) {\n            delay = _convertTimeValueToMS(parseFloat(delayMatch), matches[4]);\n        }\n        const easingVal = matches[5];\n        if (easingVal) {\n            easing = easingVal;\n        }\n    }\n    else {\n        duration = exp;\n    }\n    if (!allowNegativeValues) {\n        let containsErrors = false;\n        let startIndex = errors.length;\n        if (duration < 0) {\n            errors.push(negativeStepValue());\n            containsErrors = true;\n        }\n        if (delay < 0) {\n            errors.push(negativeDelayValue());\n            containsErrors = true;\n        }\n        if (containsErrors) {\n            errors.splice(startIndex, 0, invalidTimingValue(exp));\n        }\n    }\n    return { duration, delay, easing };\n}\nfunction normalizeKeyframes(keyframes) {\n    if (!keyframes.length) {\n        return [];\n    }\n    if (keyframes[0] instanceof Map) {\n        return keyframes;\n    }\n    return keyframes.map((kf) => new Map(Object.entries(kf)));\n}\nfunction normalizeStyles(styles) {\n    return Array.isArray(styles) ? new Map(...styles) : new Map(styles);\n}\nfunction setStyles(element, styles, formerStyles) {\n    styles.forEach((val, prop) => {\n        const camelProp = dashCaseToCamelCase(prop);\n        if (formerStyles && !formerStyles.has(prop)) {\n            formerStyles.set(prop, element.style[camelProp]);\n        }\n        element.style[camelProp] = val;\n    });\n}\nfunction eraseStyles(element, styles) {\n    styles.forEach((_, prop) => {\n        const camelProp = dashCaseToCamelCase(prop);\n        element.style[camelProp] = '';\n    });\n}\nfunction normalizeAnimationEntry(steps) {\n    if (Array.isArray(steps)) {\n        if (steps.length == 1)\n            return steps[0];\n        return sequence(steps);\n    }\n    return steps;\n}\nfunction validateStyleParams(value, options, errors) {\n    const params = options.params || {};\n    const matches = extractStyleParams(value);\n    if (matches.length) {\n        matches.forEach((varName) => {\n            if (!params.hasOwnProperty(varName)) {\n                errors.push(invalidStyleParams(varName));\n            }\n        });\n    }\n}\nconst PARAM_REGEX = /* @__PURE__ */ new RegExp(`${SUBSTITUTION_EXPR_START}\\\\s*(.+?)\\\\s*${SUBSTITUTION_EXPR_END}`, 'g');\nfunction extractStyleParams(value) {\n    let params = [];\n    if (typeof value === 'string') {\n        let match;\n        while ((match = PARAM_REGEX.exec(value))) {\n            params.push(match[1]);\n        }\n        PARAM_REGEX.lastIndex = 0;\n    }\n    return params;\n}\nfunction interpolateParams(value, params, errors) {\n    const original = `${value}`;\n    const str = original.replace(PARAM_REGEX, (_, varName) => {\n        let localVal = params[varName];\n        // this means that the value was never overridden by the data passed in by the user\n        if (localVal == null) {\n            errors.push(invalidParamValue(varName));\n            localVal = '';\n        }\n        return localVal.toString();\n    });\n    // we do this to assert that numeric values stay as they are\n    return str == original ? value : str;\n}\nconst DASH_CASE_REGEXP = /-+([a-z0-9])/g;\nfunction dashCaseToCamelCase(input) {\n    return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\nfunction camelCaseToDashCase(input) {\n    return input.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\nfunction allowPreviousPlayerStylesMerge(duration, delay) {\n    return duration === 0 || delay === 0;\n}\nfunction balancePreviousStylesIntoKeyframes(element, keyframes, previousStyles) {\n    if (previousStyles.size && keyframes.length) {\n        let startingKeyframe = keyframes[0];\n        let missingStyleProps = [];\n        previousStyles.forEach((val, prop) => {\n            if (!startingKeyframe.has(prop)) {\n                missingStyleProps.push(prop);\n            }\n            startingKeyframe.set(prop, val);\n        });\n        if (missingStyleProps.length) {\n            for (let i = 1; i < keyframes.length; i++) {\n                let kf = keyframes[i];\n                missingStyleProps.forEach((prop) => kf.set(prop, computeStyle(element, prop)));\n            }\n        }\n    }\n    return keyframes;\n}\nfunction visitDslNode(visitor, node, context) {\n    switch (node.type) {\n        case AnimationMetadataType.Trigger:\n            return visitor.visitTrigger(node, context);\n        case AnimationMetadataType.State:\n            return visitor.visitState(node, context);\n        case AnimationMetadataType.Transition:\n            return visitor.visitTransition(node, context);\n        case AnimationMetadataType.Sequence:\n            return visitor.visitSequence(node, context);\n        case AnimationMetadataType.Group:\n            return visitor.visitGroup(node, context);\n        case AnimationMetadataType.Animate:\n            return visitor.visitAnimate(node, context);\n        case AnimationMetadataType.Keyframes:\n            return visitor.visitKeyframes(node, context);\n        case AnimationMetadataType.Style:\n            return visitor.visitStyle(node, context);\n        case AnimationMetadataType.Reference:\n            return visitor.visitReference(node, context);\n        case AnimationMetadataType.AnimateChild:\n            return visitor.visitAnimateChild(node, context);\n        case AnimationMetadataType.AnimateRef:\n            return visitor.visitAnimateRef(node, context);\n        case AnimationMetadataType.Query:\n            return visitor.visitQuery(node, context);\n        case AnimationMetadataType.Stagger:\n            return visitor.visitStagger(node, context);\n        default:\n            throw invalidNodeType(node.type);\n    }\n}\nfunction computeStyle(element, prop) {\n    return window.getComputedStyle(element)[prop];\n}\n\nexport { ENTER_CLASSNAME, LEAVE_CLASSNAME, NG_ANIMATING_CLASSNAME, NG_ANIMATING_SELECTOR, NG_TRIGGER_CLASSNAME, NG_TRIGGER_SELECTOR, SUBSTITUTION_EXPR_START, allowPreviousPlayerStylesMerge, balancePreviousStylesIntoKeyframes, buildingFailed, camelCaseToDashCase, computeStyle, containsElement, createAnimationFailed, dashCaseToCamelCase, eraseStyles, extractStyleParams, getOrSetDefaultValue, getParentElement, interpolateParams, invalidCssUnitValue, invalidDefinition, invalidExpression, invalidKeyframes, invalidOffset, invalidParallelAnimation, invalidQuery, invalidStagger, invalidState, invalidStyleValue, invalidTransitionAlias, invalidTrigger, invokeQuery, keyframeOffsetsOutOfOrder, keyframesMissingOffsets, listenOnPlayer, makeAnimationEvent, missingEvent, missingOrDestroyedAnimation, missingPlayer, missingTrigger, normalizeAnimationEntry, normalizeKeyframes$1 as normalizeKeyframes, normalizeKeyframes as normalizeKeyframes$1, normalizeStyles, optimizeGroupPlayer, parseTimelineCommand, registerFailed, resolveTiming, resolveTimingValue, setStyles, transitionFailed, triggerBuildFailed, triggerTransitionsFailed, unregisteredTrigger, unsupportedTriggerEvent, validateStyleParams, validateStyleProperty, validateWebAnimatableStyleProperty, validationFailed, visitDslNode };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,oBAAoB,EAAEC,mBAAmB,EAAEC,UAAU,EAAEC,UAAU,IAAIC,UAAU,EAAEC,qBAAqB,EAAEC,QAAQ,QAAQ,+BAA+B;AAChK,SAASC,aAAa,IAAIC,aAAa,QAAQ,eAAe;AAE9D,MAAMC,UAAU,GAAG,OAAO;AAC1B,SAASC,kBAAkBA,CAACC,GAAG,EAAE;EAC7B,OAAO,IAAIH,aAAa,CAAC,IAAI,CAAC,6CAA6CI,SAAS,IAAI,8BAA8BD,GAAG,eAAe,CAAC;AAC7I;AACA,SAASE,iBAAiBA,CAAA,EAAG;EACzB,OAAO,IAAIL,aAAa,CAAC,IAAI,CAAC,4CAA4CI,SAAS,IAAI,kEAAkE,CAAC;AAC9J;AACA,SAASE,kBAAkBA,CAAA,EAAG;EAC1B,OAAO,IAAIN,aAAa,CAAC,IAAI,CAAC,6CAA6CI,SAAS,IAAI,+DAA+D,CAAC;AAC5J;AACA,SAASG,kBAAkBA,CAACC,OAAO,EAAE;EACjC,OAAO,IAAIR,aAAa,CAAC,IAAI,CAAC,6CAA6CI,SAAS,IAChF,+CAA+CI,OAAO,8BAA8B,CAAC;AAC7F;AACA,SAASC,iBAAiBA,CAACD,OAAO,EAAE;EAChC,OAAO,IAAIR,aAAa,CAAC,IAAI,CAAC,4CAA4CI,SAAS,IAAI,kDAAkDI,OAAO,EAAE,CAAC;AACvJ;AACA,SAASE,eAAeA,CAACC,QAAQ,EAAE;EAC/B,OAAO,IAAIX,aAAa,CAAC,IAAI,CAAC,0CAA0CI,SAAS,IAAI,8CAA8CO,QAAQ,EAAE,CAAC;AAClJ;AACA,SAASC,mBAAmBA,CAACC,oBAAoB,EAAEC,KAAK,EAAE;EACtD,OAAO,IAAId,aAAa,CAAC,IAAI,CAAC,+CAA+CI,SAAS,IAAI,uCAAuCS,oBAAoB,IAAIC,KAAK,EAAE,CAAC;AACrK;AACA,SAASC,cAAcA,CAAA,EAAG;EACtB,OAAO,IAAIf,aAAa,CAAC,IAAI,CAAC,wCAAwCI,SAAS,IAC3E,sFAAsF,CAAC;AAC/F;AACA,SAASY,iBAAiBA,CAAA,EAAG;EACzB,OAAO,IAAIhB,aAAa,CAAC,IAAI,CAAC,2CAA2CI,SAAS,IAAI,yEAAyE,CAAC;AACpK;AACA,SAASa,YAAYA,CAACC,YAAY,EAAEC,WAAW,EAAE;EAC7C,OAAO,IAAInB,aAAa,CAAC,IAAI,CAAC,sCAAsCI,SAAS,IACzE,UAAUc,YAAY,iFAAiFC,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACxI;AACA,SAASC,iBAAiBA,CAACP,KAAK,EAAE;EAC9B,OAAO,IAAId,aAAa,CAAC,IAAI,CAAC,4CAA4CI,SAAS,IAAI,mCAAmCU,KAAK,kBAAkB,CAAC;AACtJ;AACA,SAASQ,wBAAwBA,CAACC,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAE;EAClF,OAAO,IAAI3B,aAAa,CAAC,IAAI,CAAC,mDAAmDI,SAAS,IACtF,qBAAqBmB,IAAI,uCAAuCC,UAAU,YAAYC,QAAQ,4EAA4EC,WAAW,YAAYC,SAAS,KAAK,CAAC;AACxN;AACA,SAASC,gBAAgBA,CAAA,EAAG;EACxB,OAAO,IAAI5B,aAAa,CAAC,IAAI,CAAC,0CAA0CI,SAAS,IAAI,0DAA0D,CAAC;AACpJ;AACA,SAASyB,aAAaA,CAAA,EAAG;EACrB,OAAO,IAAI7B,aAAa,CAAC,IAAI,CAAC,uCAAuCI,SAAS,IAAI,6DAA6D,CAAC;AACpJ;AACA,SAAS0B,yBAAyBA,CAAA,EAAG;EACjC,OAAO,IAAI9B,aAAa,CAAC,IAAI,CAAC,sDAAsDI,SAAS,IAAI,sDAAsD,CAAC;AAC5J;AACA,SAAS2B,uBAAuBA,CAAA,EAAG;EAC/B,OAAO,IAAI/B,aAAa,CAAC,IAAI,CAAC,kDAAkDI,SAAS,IAAI,uEAAuE,CAAC;AACzK;AACA,SAAS4B,cAAcA,CAAA,EAAG;EACtB,OAAO,IAAIhC,aAAa,CAAC,IAAI,CAAC,wCAAwCI,SAAS,IAAI,8CAA8C,CAAC;AACtI;AACA,SAAS6B,YAAYA,CAACC,QAAQ,EAAE;EAC5B,OAAO,IAAIlC,aAAa,CAAC,IAAI,CAAC,sCAAsCI,SAAS,IACzE,YAAY8B,QAAQ,8CAA8CA,QAAQ,sDAAsD,CAAC;AACzI;AACA,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EAC7B,OAAO,IAAIpC,aAAa,CAAC,IAAI,CAAC,2CAA2CI,SAAS,IAAI,uCAAuCgC,IAAI,oBAAoB,CAAC;AAC1J;AACA,SAASC,sBAAsBA,CAACC,KAAK,EAAE;EACnC,OAAO,IAAItC,aAAa,CAAC,IAAI,CAAC,iDAAiDI,SAAS,IAAI,+BAA+BkC,KAAK,oBAAoB,CAAC;AACzJ;AACA,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAIxC,aAAa,CAAC,IAAI,CAAC,0CAA0CI,SAAS,IAAI,iCAAiCoC,MAAM,CAACC,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CAACvB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACxK;AACA,SAASwB,cAAcA,CAACJ,MAAM,EAAE;EAC5B,OAAO,IAAIxC,aAAa,CAAC,IAAI,CAAC,wCAAwCI,SAAS,IAAI,+BAA+BoC,MAAM,CAACC,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CAACvB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACpK;AACA,SAASyB,kBAAkBA,CAACC,IAAI,EAAEN,MAAM,EAAE;EACtC,OAAO,IAAIxC,aAAa,CAAC,IAAI,CAAC,6CAA6CI,SAAS,IAChF,0BAA0B0C,IAAI,0DAA0DN,MAAM,CACzFC,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CACzBvB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;AAC7B;AACA,SAAS2B,eAAeA,CAACP,MAAM,EAAE;EAC7B,OAAO,IAAIxC,aAAa,CAAC,IAAI,CAAC,yCAAyCI,SAAS,IAC5E,iDAAiDH,UAAU,GAAGuC,MAAM,CAC/DC,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CACzBvB,IAAI,CAACnB,UAAU,CAAC,EAAE,CAAC;AAChC;AACA,SAAS+C,cAAcA,CAACR,MAAM,EAAE;EAC5B,OAAO,IAAIxC,aAAa,CAAC,IAAI,CAAC,4CAA4CI,SAAS,IAC/E,8DAA8DoC,MAAM,CAC/DC,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CACzBvB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AAC1B;AACA,SAAS6B,2BAA2BA,CAAA,EAAG;EACnC,OAAO,IAAIjD,aAAa,CAAC,IAAI,CAAC,uDAAuDI,SAAS,IAAI,qEAAqE,CAAC;AAC5K;AACA,SAAS8C,qBAAqBA,CAACV,MAAM,EAAE;EACnC,OAAO,IAAIxC,aAAa,CAAC,IAAI,CAAC,gDAAgDI,SAAS,IACnF,8DAA8DoC,MAAM,CAC/DC,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CACzBvB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AAC1B;AACA,SAAS+B,aAAaA,CAACC,EAAE,EAAE;EACvB,OAAO,IAAIpD,aAAa,CAAC,IAAI,CAAC,uCAAuCI,SAAS,IAAI,oDAAoDgD,EAAE,EAAE,CAAC;AAC/I;AACA,SAASC,cAAcA,CAACC,KAAK,EAAER,IAAI,EAAE;EACjC,OAAO,IAAI9C,aAAa,CAAC,IAAI,CAAC,wCAAwCI,SAAS,IAC3E,oDAAoDkD,KAAK,oCAAoCR,IAAI,mBAAmB,CAAC;AAC7H;AACA,SAASS,YAAYA,CAACT,IAAI,EAAE;EACxB,OAAO,IAAI9C,aAAa,CAAC,IAAI,CAAC,sCAAsCI,SAAS,IACzE,8CAA8C0C,IAAI,4CAA4C,CAAC;AACvG;AACA,SAASU,uBAAuBA,CAACF,KAAK,EAAER,IAAI,EAAE;EAC1C,OAAO,IAAI9C,aAAa,CAAC,IAAI,CAAC,kDAAkDI,SAAS,IACrF,yCAAyCkD,KAAK,gCAAgCR,IAAI,qBAAqB,CAAC;AAChH;AACA,SAASW,mBAAmBA,CAACX,IAAI,EAAE;EAC/B,OAAO,IAAI9C,aAAa,CAAC,IAAI,CAAC,6CAA6CI,SAAS,IAAI,mCAAmC0C,IAAI,4BAA4B,CAAC;AAChK;AACA,SAASY,wBAAwBA,CAAClB,MAAM,EAAE;EACtC,OAAO,IAAIxC,aAAa,CAAC,IAAI,CAAC,mDAAmDI,SAAS,IACtF,kFAAkFoC,MAAM,CACnFC,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CACzBvB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AAC1B;AACA,SAASuC,gBAAgBA,CAACb,IAAI,EAAEN,MAAM,EAAE;EACpC,OAAO,IAAIxC,aAAa,CAAC,IAAI,CAAC,0CAA0CI,SAAS,IAAI,IAAI0C,IAAI,yBAAyBN,MAAM,CAACC,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CAACvB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;AAC1K;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMwC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAChC,qBAAqB,EACrB,gCAAgC,EAChC,iCAAiC,EACjC,6BAA6B,EAC7B,8BAA8B,EAC9B,kBAAkB,EAClB,eAAe,EACf,oBAAoB,EACpB,yBAAyB,EACzB,qBAAqB,EACrB,2BAA2B,EAC3B,cAAc,EACd,KAAK,EACL,iBAAiB,EACjB,YAAY,EACZ,kBAAkB,EAClB,qBAAqB,EACrB,iBAAiB,EACjB,YAAY,EACZ,QAAQ,EACR,kBAAkB,EAClB,wBAAwB,EACxB,wBAAwB,EACxB,oBAAoB,EACpB,0BAA0B,EAC1B,0BAA0B,EAC1B,eAAe,EACf,qBAAqB,EACrB,2BAA2B,EAC3B,4BAA4B,EAC5B,qBAAqB,EACrB,cAAc,EACd,uBAAuB,EACvB,yBAAyB,EACzB,qBAAqB,EACrB,oBAAoB,EACpB,oBAAoB,EACpB,mBAAmB,EACnB,yBAAyB,EACzB,yBAAyB,EACzB,qBAAqB,EACrB,2BAA2B,EAC3B,2BAA2B,EAC3B,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,eAAe,EACf,cAAc,EACd,oBAAoB,EACpB,oBAAoB,EACpB,yBAAyB,EACzB,2BAA2B,EAC3B,YAAY,EACZ,kBAAkB,EAClB,wBAAwB,EACxB,yBAAyB,EACzB,kBAAkB,EAClB,cAAc,EACd,QAAQ,EACR,YAAY,EACZ,aAAa,EACb,MAAM,EACN,WAAW,EACX,OAAO,EACP,cAAc,EACd,YAAY,EACZ,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACd,SAAS,EACT,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,WAAW,EACX,aAAa,EACb,MAAM,EACN,WAAW,EACX,kBAAkB,EAClB,cAAc,EACd,yBAAyB,EACzB,aAAa,EACb,KAAK,EACL,iBAAiB,EACjB,UAAU,EACV,cAAc,EACd,uBAAuB,EACvB,oBAAoB,EACpB,QAAQ,EACR,aAAa,EACb,gBAAgB,EAChB,OAAO,EACP,aAAa,EACb,iBAAiB,EACjB,mBAAmB,EACnB,cAAc,EACd,kBAAkB,EAClB,oBAAoB,EACpB,MAAM,EACN,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACb,QAAQ,EACR,kBAAkB,EAClB,oBAAoB,EACpB,eAAe,EACf,mBAAmB,EACnB,qBAAqB,EACrB,aAAa,EACb,cAAc,EACd,YAAY,EACZ,MAAM,EACN,aAAa,EACb,eAAe,EACf,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,WAAW,EACX,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,WAAW,EACX,iBAAiB,EACjB,QAAQ,EACR,eAAe,EACf,iBAAiB,EACjB,aAAa,EACb,iBAAiB,EACjB,eAAe,EACf,SAAS,EACT,OAAO,EACP,SAAS,EACT,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,SAAS,EACT,mBAAmB,EACnB,qBAAqB,EACrB,gBAAgB,EAChB,oBAAoB,EACpB,sBAAsB,EACtB,cAAc,EACd,eAAe,EACf,aAAa,EACb,aAAa,EACb,oBAAoB,EACpB,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,eAAe,EACf,qBAAqB,EACrB,yBAAyB,EACzB,2BAA2B,EAC3B,sBAAsB,EACtB,sBAAsB,EACtB,0BAA0B,EAC1B,4BAA4B,EAC5B,oBAAoB,EACpB,qBAAqB,EACrB,mBAAmB,EACnB,gBAAgB,EAChB,sBAAsB,EACtB,0BAA0B,EAC1B,4BAA4B,EAC5B,uBAAuB,EACvB,uBAAuB,EACvB,2BAA2B,EAC3B,6BAA6B,EAC7B,qBAAqB,EACrB,sBAAsB,EACtB,oBAAoB,EACpB,wBAAwB,EACxB,yBAAyB,EACzB,iBAAiB,EACjB,uBAAuB,EACvB,cAAc,EACd,eAAe,EACf,UAAU,EACV,iBAAiB,EACjB,uBAAuB,EACvB,2BAA2B,EAC3B,eAAe,EACf,qBAAqB,EACrB,aAAa,EACb,aAAa,EACb,uBAAuB,EACvB,KAAK,EACL,WAAW,EACX,kBAAkB,EAClB,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACP,cAAc,EACd,SAAS,EACT,MAAM,CACT,CAAC;AAEF,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EAClC,QAAQA,OAAO,CAACC,MAAM;IAClB,KAAK,CAAC;MACF,OAAO,IAAIvE,mBAAmB,CAAC,CAAC;IACpC,KAAK,CAAC;MACF,OAAOsE,OAAO,CAAC,CAAC,CAAC;IACrB;MACI,OAAO,IAAIvE,oBAAoB,CAACuE,OAAO,CAAC;EAChD;AACJ;AACA,SAASE,oBAAoBA,CAACC,UAAU,EAAEC,SAAS,EAAEC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC,EAAEC,UAAU,GAAG,IAAID,GAAG,CAAC,CAAC,EAAE;EAChG,MAAM7B,MAAM,GAAG,EAAE;EACjB,MAAM+B,mBAAmB,GAAG,EAAE;EAC9B,IAAIC,cAAc,GAAG,CAAC,CAAC;EACvB,IAAIC,gBAAgB,GAAG,IAAI;EAC3BN,SAAS,CAACO,OAAO,CAAEC,EAAE,IAAK;IACtB,MAAMC,MAAM,GAAGD,EAAE,CAACE,GAAG,CAAC,QAAQ,CAAC;IAC/B,MAAMC,YAAY,GAAGF,MAAM,IAAIJ,cAAc;IAC7C,MAAMO,kBAAkB,GAAID,YAAY,IAAIL,gBAAgB,IAAK,IAAIJ,GAAG,CAAC,CAAC;IAC1EM,EAAE,CAACD,OAAO,CAAC,CAACM,GAAG,EAAEzD,IAAI,KAAK;MACtB,IAAI0D,cAAc,GAAG1D,IAAI;MACzB,IAAI2D,eAAe,GAAGF,GAAG;MACzB,IAAIzD,IAAI,KAAK,QAAQ,EAAE;QACnB0D,cAAc,GAAGf,UAAU,CAACiB,qBAAqB,CAACF,cAAc,EAAEzC,MAAM,CAAC;QACzE,QAAQ0C,eAAe;UACnB,KAAKtF,UAAU;YACXsF,eAAe,GAAGd,SAAS,CAACS,GAAG,CAACtD,IAAI,CAAC;YACrC;UACJ,KAAK7B,UAAU;YACXwF,eAAe,GAAGZ,UAAU,CAACO,GAAG,CAACtD,IAAI,CAAC;YACtC;UACJ;YACI2D,eAAe,GAAGhB,UAAU,CAACkB,mBAAmB,CAAC7D,IAAI,EAAE0D,cAAc,EAAEC,eAAe,EAAE1C,MAAM,CAAC;YAC/F;QACR;MACJ;MACAuC,kBAAkB,CAACM,GAAG,CAACJ,cAAc,EAAEC,eAAe,CAAC;IAC3D,CAAC,CAAC;IACF,IAAI,CAACJ,YAAY,EAAE;MACfP,mBAAmB,CAACe,IAAI,CAACP,kBAAkB,CAAC;IAChD;IACAN,gBAAgB,GAAGM,kBAAkB;IACrCP,cAAc,GAAGI,MAAM;EAC3B,CAAC,CAAC;EACF,IAAIpC,MAAM,CAACwB,MAAM,EAAE;IACf,MAAMjB,eAAe,CAACP,MAAM,CAAC;EACjC;EACA,OAAO+B,mBAAmB;AAC9B;AACA,SAASgB,cAAcA,CAACC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EACxD,QAAQF,SAAS;IACb,KAAK,OAAO;MACRD,MAAM,CAACI,OAAO,CAAC,MAAMD,QAAQ,CAACD,KAAK,IAAIG,kBAAkB,CAACH,KAAK,EAAE,OAAO,EAAEF,MAAM,CAAC,CAAC,CAAC;MACnF;IACJ,KAAK,MAAM;MACPA,MAAM,CAACM,MAAM,CAAC,MAAMH,QAAQ,CAACD,KAAK,IAAIG,kBAAkB,CAACH,KAAK,EAAE,MAAM,EAAEF,MAAM,CAAC,CAAC,CAAC;MACjF;IACJ,KAAK,SAAS;MACVA,MAAM,CAACO,SAAS,CAAC,MAAMJ,QAAQ,CAACD,KAAK,IAAIG,kBAAkB,CAACH,KAAK,EAAE,SAAS,EAAEF,MAAM,CAAC,CAAC,CAAC;MACvF;EACR;AACJ;AACA,SAASK,kBAAkBA,CAACG,CAAC,EAAEC,SAAS,EAAET,MAAM,EAAE;EAC9C,MAAMU,SAAS,GAAGV,MAAM,CAACU,SAAS;EAClC,MAAMC,QAAQ,GAAGX,MAAM,CAACW,QAAQ,GAAG,IAAI,GAAG,KAAK;EAC/C,MAAMT,KAAK,GAAGU,kBAAkB,CAACJ,CAAC,CAACK,OAAO,EAAEL,CAAC,CAACM,WAAW,EAAEN,CAAC,CAACO,SAAS,EAAEP,CAAC,CAACQ,OAAO,EAAEP,SAAS,IAAID,CAAC,CAACC,SAAS,EAAEC,SAAS,IAAIO,SAAS,GAAGT,CAAC,CAACE,SAAS,GAAGA,SAAS,EAAEC,QAAQ,CAAC;EACxK,MAAMO,IAAI,GAAGV,CAAC,CAAC,OAAO,CAAC;EACvB,IAAIU,IAAI,IAAI,IAAI,EAAE;IACdhB,KAAK,CAAC,OAAO,CAAC,GAAGgB,IAAI;EACzB;EACA,OAAOhB,KAAK;AAChB;AACA,SAASU,kBAAkBA,CAACC,OAAO,EAAEC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEP,SAAS,GAAG,EAAE,EAAEC,SAAS,GAAG,CAAC,EAAEC,QAAQ,EAAE;EAC3G,OAAO;IAAEE,OAAO;IAAEC,WAAW;IAAEC,SAAS;IAAEC,OAAO;IAAEP,SAAS;IAAEC,SAAS;IAAEC,QAAQ,EAAE,CAAC,CAACA;EAAS,CAAC;AACnG;AACA,SAASQ,oBAAoBA,CAAClE,GAAG,EAAEmE,GAAG,EAAEC,YAAY,EAAE;EAClD,IAAI/F,KAAK,GAAG2B,GAAG,CAACoC,GAAG,CAAC+B,GAAG,CAAC;EACxB,IAAI,CAAC9F,KAAK,EAAE;IACR2B,GAAG,CAAC4C,GAAG,CAACuB,GAAG,EAAG9F,KAAK,GAAG+F,YAAa,CAAC;EACxC;EACA,OAAO/F,KAAK;AAChB;AACA,SAASgG,oBAAoBA,CAACC,OAAO,EAAE;EACnC,MAAMC,YAAY,GAAGD,OAAO,CAACE,OAAO,CAAC,GAAG,CAAC;EACzC,MAAM7D,EAAE,GAAG2D,OAAO,CAACG,SAAS,CAAC,CAAC,EAAEF,YAAY,CAAC;EAC7C,MAAMG,MAAM,GAAGJ,OAAO,CAACK,KAAK,CAACJ,YAAY,GAAG,CAAC,CAAC;EAC9C,OAAO,CAAC5D,EAAE,EAAE+D,MAAM,CAAC;AACvB;AACA,MAAME,eAAe,GAAG,eAAgB,CAAC,MAAM,OAAOC,QAAQ,KAAK,WAAW,GAAG,IAAI,GAAGA,QAAQ,CAACD,eAAe,EAAE,CAAC;AACnH,SAASE,gBAAgBA,CAAClB,OAAO,EAAE;EAC/B,MAAMmB,MAAM,GAAGnB,OAAO,CAACoB,UAAU,IAAIpB,OAAO,CAACqB,IAAI,IAAI,IAAI,CAAC,CAAC;EAC3D,IAAIF,MAAM,KAAKH,eAAe,EAAE;IAC5B,OAAO,IAAI;EACf;EACA,OAAOG,MAAM;AACjB;AACA,SAASG,oBAAoBA,CAACpG,IAAI,EAAE;EAChC;EACA;EACA,OAAOA,IAAI,CAAC2F,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC;AAC5C;AACA,IAAIU,YAAY,GAAG,IAAI;AACvB,IAAIC,UAAU,GAAG,KAAK;AACtB,SAASC,qBAAqBA,CAACvG,IAAI,EAAE;EACjC,IAAI,CAACqG,YAAY,EAAE;IACfA,YAAY,GAAGG,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC;IAClCF,UAAU,GAAGD,YAAY,CAACI,KAAK,GAAG,kBAAkB,IAAIJ,YAAY,CAACI,KAAK,GAAG,KAAK;EACtF;EACA,IAAIC,MAAM,GAAG,IAAI;EACjB,IAAIL,YAAY,CAACI,KAAK,IAAI,CAACL,oBAAoB,CAACpG,IAAI,CAAC,EAAE;IACnD0G,MAAM,GAAG1G,IAAI,IAAIqG,YAAY,CAACI,KAAK;IACnC,IAAI,CAACC,MAAM,IAAIJ,UAAU,EAAE;MACvB,MAAMK,SAAS,GAAG,QAAQ,GAAG3G,IAAI,CAAC4G,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG7G,IAAI,CAAC6F,KAAK,CAAC,CAAC,CAAC;MACzEa,MAAM,GAAGC,SAAS,IAAIN,YAAY,CAACI,KAAK;IAC5C;EACJ;EACA,OAAOC,MAAM;AACjB;AACA,SAASI,kCAAkCA,CAAC9G,IAAI,EAAE;EAC9C,OAAOqC,mBAAmB,CAAC0E,GAAG,CAAC/G,IAAI,CAAC;AACxC;AACA,SAASwG,WAAWA,CAAA,EAAG;EACnB,IAAI,OAAOT,QAAQ,IAAI,WAAW,EAAE;IAChC,OAAOA,QAAQ,CAACiB,IAAI;EACxB;EACA,OAAO,IAAI;AACf;AACA,SAASC,eAAeA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACjC,OAAOA,IAAI,EAAE;IACT,IAAIA,IAAI,KAAKD,IAAI,EAAE;MACf,OAAO,IAAI;IACf;IACAC,IAAI,GAAGnB,gBAAgB,CAACmB,IAAI,CAAC;EACjC;EACA,OAAO,KAAK;AAChB;AACA,SAASC,WAAWA,CAACtC,OAAO,EAAEnE,QAAQ,EAAE0G,KAAK,EAAE;EAC3C,IAAIA,KAAK,EAAE;IACP,OAAOC,KAAK,CAACC,IAAI,CAACzC,OAAO,CAAC0C,gBAAgB,CAAC7G,QAAQ,CAAC,CAAC;EACzD;EACA,MAAM8G,IAAI,GAAG3C,OAAO,CAAC4C,aAAa,CAAC/G,QAAQ,CAAC;EAC5C,OAAO8G,IAAI,GAAG,CAACA,IAAI,CAAC,GAAG,EAAE;AAC7B;AAEA,MAAME,UAAU,GAAG,IAAI;AACvB,MAAMC,uBAAuB,GAAG,IAAI;AACpC,MAAMC,qBAAqB,GAAG,IAAI;AAClC,MAAMC,eAAe,GAAG,UAAU;AAClC,MAAMC,eAAe,GAAG,UAAU;AAClC,MAAMC,oBAAoB,GAAG,YAAY;AACzC,MAAMC,mBAAmB,GAAG,aAAa;AACzC,MAAMC,sBAAsB,GAAG,cAAc;AAC7C,MAAMC,qBAAqB,GAAG,eAAe;AAC7C,SAASC,kBAAkBA,CAAC7I,KAAK,EAAE;EAC/B,IAAI,OAAOA,KAAK,IAAI,QAAQ,EACxB,OAAOA,KAAK;EAChB,MAAM8I,OAAO,GAAG9I,KAAK,CAAC+I,KAAK,CAAC,mBAAmB,CAAC;EAChD,IAAI,CAACD,OAAO,IAAIA,OAAO,CAAC5F,MAAM,GAAG,CAAC,EAC9B,OAAO,CAAC;EACZ,OAAO8F,qBAAqB,CAACC,UAAU,CAACH,OAAO,CAAC,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;AACpE;AACA,SAASE,qBAAqBA,CAAChJ,KAAK,EAAEkJ,IAAI,EAAE;EACxC,QAAQA,IAAI;IACR,KAAK,GAAG;MACJ,OAAOlJ,KAAK,GAAGoI,UAAU;IAC7B;MAAS;MACL,OAAOpI,KAAK;EACpB;AACJ;AACA,SAASmJ,aAAaA,CAACC,OAAO,EAAE1H,MAAM,EAAE2H,mBAAmB,EAAE;EACzD,OAAOD,OAAO,CAACE,cAAc,CAAC,UAAU,CAAC,GACnCF,OAAO,GACPG,mBAAmB,CAACH,OAAO,EAAE1H,MAAM,EAAE2H,mBAAmB,CAAC;AACnE;AACA,SAASE,mBAAmBA,CAAClK,GAAG,EAAEqC,MAAM,EAAE2H,mBAAmB,EAAE;EAC3D,MAAMG,KAAK,GAAG,0EAA0E;EACxF,IAAIC,QAAQ;EACZ,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,MAAM,GAAG,EAAE;EACf,IAAI,OAAOtK,GAAG,KAAK,QAAQ,EAAE;IACzB,MAAMyJ,OAAO,GAAGzJ,GAAG,CAAC0J,KAAK,CAACS,KAAK,CAAC;IAChC,IAAIV,OAAO,KAAK,IAAI,EAAE;MAClBpH,MAAM,CAAC8C,IAAI,CAACpF,kBAAkB,CAACC,GAAG,CAAC,CAAC;MACpC,OAAO;QAAEoK,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAG,CAAC;IAChD;IACAF,QAAQ,GAAGT,qBAAqB,CAACC,UAAU,CAACH,OAAO,CAAC,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;IACpE,MAAMc,UAAU,GAAGd,OAAO,CAAC,CAAC,CAAC;IAC7B,IAAIc,UAAU,IAAI,IAAI,EAAE;MACpBF,KAAK,GAAGV,qBAAqB,CAACC,UAAU,CAACW,UAAU,CAAC,EAAEd,OAAO,CAAC,CAAC,CAAC,CAAC;IACrE;IACA,MAAMe,SAAS,GAAGf,OAAO,CAAC,CAAC,CAAC;IAC5B,IAAIe,SAAS,EAAE;MACXF,MAAM,GAAGE,SAAS;IACtB;EACJ,CAAC,MACI;IACDJ,QAAQ,GAAGpK,GAAG;EAClB;EACA,IAAI,CAACgK,mBAAmB,EAAE;IACtB,IAAIS,cAAc,GAAG,KAAK;IAC1B,IAAIC,UAAU,GAAGrI,MAAM,CAACwB,MAAM;IAC9B,IAAIuG,QAAQ,GAAG,CAAC,EAAE;MACd/H,MAAM,CAAC8C,IAAI,CAACjF,iBAAiB,CAAC,CAAC,CAAC;MAChCuK,cAAc,GAAG,IAAI;IACzB;IACA,IAAIJ,KAAK,GAAG,CAAC,EAAE;MACXhI,MAAM,CAAC8C,IAAI,CAAChF,kBAAkB,CAAC,CAAC,CAAC;MACjCsK,cAAc,GAAG,IAAI;IACzB;IACA,IAAIA,cAAc,EAAE;MAChBpI,MAAM,CAACsI,MAAM,CAACD,UAAU,EAAE,CAAC,EAAE3K,kBAAkB,CAACC,GAAG,CAAC,CAAC;IACzD;EACJ;EACA,OAAO;IAAEoK,QAAQ;IAAEC,KAAK;IAAEC;EAAO,CAAC;AACtC;AACA,SAASM,kBAAkBA,CAAC5G,SAAS,EAAE;EACnC,IAAI,CAACA,SAAS,CAACH,MAAM,EAAE;IACnB,OAAO,EAAE;EACb;EACA,IAAIG,SAAS,CAAC,CAAC,CAAC,YAAYE,GAAG,EAAE;IAC7B,OAAOF,SAAS;EACpB;EACA,OAAOA,SAAS,CAAC1B,GAAG,CAAEkC,EAAE,IAAK,IAAIN,GAAG,CAAC2G,MAAM,CAACC,OAAO,CAACtG,EAAE,CAAC,CAAC,CAAC;AAC7D;AACA,SAASuG,eAAeA,CAACC,MAAM,EAAE;EAC7B,OAAOtC,KAAK,CAACuC,OAAO,CAACD,MAAM,CAAC,GAAG,IAAI9G,GAAG,CAAC,GAAG8G,MAAM,CAAC,GAAG,IAAI9G,GAAG,CAAC8G,MAAM,CAAC;AACvE;AACA,SAASE,SAASA,CAAChF,OAAO,EAAE8E,MAAM,EAAEG,YAAY,EAAE;EAC9CH,MAAM,CAACzG,OAAO,CAAC,CAACM,GAAG,EAAEzD,IAAI,KAAK;IAC1B,MAAM2G,SAAS,GAAGqD,mBAAmB,CAAChK,IAAI,CAAC;IAC3C,IAAI+J,YAAY,IAAI,CAACA,YAAY,CAAChD,GAAG,CAAC/G,IAAI,CAAC,EAAE;MACzC+J,YAAY,CAACjG,GAAG,CAAC9D,IAAI,EAAE8E,OAAO,CAAC2B,KAAK,CAACE,SAAS,CAAC,CAAC;IACpD;IACA7B,OAAO,CAAC2B,KAAK,CAACE,SAAS,CAAC,GAAGlD,GAAG;EAClC,CAAC,CAAC;AACN;AACA,SAASwG,WAAWA,CAACnF,OAAO,EAAE8E,MAAM,EAAE;EAClCA,MAAM,CAACzG,OAAO,CAAC,CAAC+G,CAAC,EAAElK,IAAI,KAAK;IACxB,MAAM2G,SAAS,GAAGqD,mBAAmB,CAAChK,IAAI,CAAC;IAC3C8E,OAAO,CAAC2B,KAAK,CAACE,SAAS,CAAC,GAAG,EAAE;EACjC,CAAC,CAAC;AACN;AACA,SAASwD,uBAAuBA,CAACC,KAAK,EAAE;EACpC,IAAI9C,KAAK,CAACuC,OAAO,CAACO,KAAK,CAAC,EAAE;IACtB,IAAIA,KAAK,CAAC3H,MAAM,IAAI,CAAC,EACjB,OAAO2H,KAAK,CAAC,CAAC,CAAC;IACnB,OAAO7L,QAAQ,CAAC6L,KAAK,CAAC;EAC1B;EACA,OAAOA,KAAK;AAChB;AACA,SAASC,mBAAmBA,CAAC9K,KAAK,EAAE+K,OAAO,EAAErJ,MAAM,EAAE;EACjD,MAAMsJ,MAAM,GAAGD,OAAO,CAACC,MAAM,IAAI,CAAC,CAAC;EACnC,MAAMlC,OAAO,GAAGmC,kBAAkB,CAACjL,KAAK,CAAC;EACzC,IAAI8I,OAAO,CAAC5F,MAAM,EAAE;IAChB4F,OAAO,CAAClF,OAAO,CAAElE,OAAO,IAAK;MACzB,IAAI,CAACsL,MAAM,CAAC1B,cAAc,CAAC5J,OAAO,CAAC,EAAE;QACjCgC,MAAM,CAAC8C,IAAI,CAAC/E,kBAAkB,CAACC,OAAO,CAAC,CAAC;MAC5C;IACJ,CAAC,CAAC;EACN;AACJ;AACA,MAAMwL,WAAW,GAAG,eAAgB,IAAIC,MAAM,CAAC,GAAG9C,uBAAuB,gBAAgBC,qBAAqB,EAAE,EAAE,GAAG,CAAC;AACtH,SAAS2C,kBAAkBA,CAACjL,KAAK,EAAE;EAC/B,IAAIgL,MAAM,GAAG,EAAE;EACf,IAAI,OAAOhL,KAAK,KAAK,QAAQ,EAAE;IAC3B,IAAI+I,KAAK;IACT,OAAQA,KAAK,GAAGmC,WAAW,CAACE,IAAI,CAACpL,KAAK,CAAC,EAAG;MACtCgL,MAAM,CAACxG,IAAI,CAACuE,KAAK,CAAC,CAAC,CAAC,CAAC;IACzB;IACAmC,WAAW,CAACG,SAAS,GAAG,CAAC;EAC7B;EACA,OAAOL,MAAM;AACjB;AACA,SAASM,iBAAiBA,CAACtL,KAAK,EAAEgL,MAAM,EAAEtJ,MAAM,EAAE;EAC9C,MAAM6J,QAAQ,GAAG,GAAGvL,KAAK,EAAE;EAC3B,MAAMwL,GAAG,GAAGD,QAAQ,CAACE,OAAO,CAACP,WAAW,EAAE,CAACP,CAAC,EAAEjL,OAAO,KAAK;IACtD,IAAIgM,QAAQ,GAAGV,MAAM,CAACtL,OAAO,CAAC;IAC9B;IACA,IAAIgM,QAAQ,IAAI,IAAI,EAAE;MAClBhK,MAAM,CAAC8C,IAAI,CAAC7E,iBAAiB,CAACD,OAAO,CAAC,CAAC;MACvCgM,QAAQ,GAAG,EAAE;IACjB;IACA,OAAOA,QAAQ,CAACC,QAAQ,CAAC,CAAC;EAC9B,CAAC,CAAC;EACF;EACA,OAAOH,GAAG,IAAID,QAAQ,GAAGvL,KAAK,GAAGwL,GAAG;AACxC;AACA,MAAMI,gBAAgB,GAAG,eAAe;AACxC,SAASnB,mBAAmBA,CAACoB,KAAK,EAAE;EAChC,OAAOA,KAAK,CAACJ,OAAO,CAACG,gBAAgB,EAAE,CAAC,GAAGE,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,CAACxE,WAAW,CAAC,CAAC,CAAC;AACxE;AACA,SAASyE,mBAAmBA,CAACF,KAAK,EAAE;EAChC,OAAOA,KAAK,CAACJ,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAACO,WAAW,CAAC,CAAC;AAClE;AACA,SAASC,8BAA8BA,CAACxC,QAAQ,EAAEC,KAAK,EAAE;EACrD,OAAOD,QAAQ,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC;AACxC;AACA,SAASwC,kCAAkCA,CAAC3G,OAAO,EAAElC,SAAS,EAAE8I,cAAc,EAAE;EAC5E,IAAIA,cAAc,CAACC,IAAI,IAAI/I,SAAS,CAACH,MAAM,EAAE;IACzC,IAAImJ,gBAAgB,GAAGhJ,SAAS,CAAC,CAAC,CAAC;IACnC,IAAIiJ,iBAAiB,GAAG,EAAE;IAC1BH,cAAc,CAACvI,OAAO,CAAC,CAACM,GAAG,EAAEzD,IAAI,KAAK;MAClC,IAAI,CAAC4L,gBAAgB,CAAC7E,GAAG,CAAC/G,IAAI,CAAC,EAAE;QAC7B6L,iBAAiB,CAAC9H,IAAI,CAAC/D,IAAI,CAAC;MAChC;MACA4L,gBAAgB,CAAC9H,GAAG,CAAC9D,IAAI,EAAEyD,GAAG,CAAC;IACnC,CAAC,CAAC;IACF,IAAIoI,iBAAiB,CAACpJ,MAAM,EAAE;MAC1B,KAAK,IAAIqJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlJ,SAAS,CAACH,MAAM,EAAEqJ,CAAC,EAAE,EAAE;QACvC,IAAI1I,EAAE,GAAGR,SAAS,CAACkJ,CAAC,CAAC;QACrBD,iBAAiB,CAAC1I,OAAO,CAAEnD,IAAI,IAAKoD,EAAE,CAACU,GAAG,CAAC9D,IAAI,EAAE+L,YAAY,CAACjH,OAAO,EAAE9E,IAAI,CAAC,CAAC,CAAC;MAClF;IACJ;EACJ;EACA,OAAO4C,SAAS;AACpB;AACA,SAASoJ,YAAYA,CAACC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAE;EAC1C,QAAQD,IAAI,CAACE,IAAI;IACb,KAAK9N,qBAAqB,CAAC+N,OAAO;MAC9B,OAAOJ,OAAO,CAACK,YAAY,CAACJ,IAAI,EAAEC,OAAO,CAAC;IAC9C,KAAK7N,qBAAqB,CAACiO,KAAK;MAC5B,OAAON,OAAO,CAACO,UAAU,CAACN,IAAI,EAAEC,OAAO,CAAC;IAC5C,KAAK7N,qBAAqB,CAACmO,UAAU;MACjC,OAAOR,OAAO,CAACS,eAAe,CAACR,IAAI,EAAEC,OAAO,CAAC;IACjD,KAAK7N,qBAAqB,CAACqO,QAAQ;MAC/B,OAAOV,OAAO,CAACW,aAAa,CAACV,IAAI,EAAEC,OAAO,CAAC;IAC/C,KAAK7N,qBAAqB,CAACuO,KAAK;MAC5B,OAAOZ,OAAO,CAACa,UAAU,CAACZ,IAAI,EAAEC,OAAO,CAAC;IAC5C,KAAK7N,qBAAqB,CAACyO,OAAO;MAC9B,OAAOd,OAAO,CAACe,YAAY,CAACd,IAAI,EAAEC,OAAO,CAAC;IAC9C,KAAK7N,qBAAqB,CAAC2O,SAAS;MAChC,OAAOhB,OAAO,CAACiB,cAAc,CAAChB,IAAI,EAAEC,OAAO,CAAC;IAChD,KAAK7N,qBAAqB,CAAC6O,KAAK;MAC5B,OAAOlB,OAAO,CAACmB,UAAU,CAAClB,IAAI,EAAEC,OAAO,CAAC;IAC5C,KAAK7N,qBAAqB,CAAC+O,SAAS;MAChC,OAAOpB,OAAO,CAACqB,cAAc,CAACpB,IAAI,EAAEC,OAAO,CAAC;IAChD,KAAK7N,qBAAqB,CAACiP,YAAY;MACnC,OAAOtB,OAAO,CAACuB,iBAAiB,CAACtB,IAAI,EAAEC,OAAO,CAAC;IACnD,KAAK7N,qBAAqB,CAACmP,UAAU;MACjC,OAAOxB,OAAO,CAACyB,eAAe,CAACxB,IAAI,EAAEC,OAAO,CAAC;IACjD,KAAK7N,qBAAqB,CAACqP,KAAK;MAC5B,OAAO1B,OAAO,CAAC2B,UAAU,CAAC1B,IAAI,EAAEC,OAAO,CAAC;IAC5C,KAAK7N,qBAAqB,CAACuP,OAAO;MAC9B,OAAO5B,OAAO,CAAC6B,YAAY,CAAC5B,IAAI,EAAEC,OAAO,CAAC;IAC9C;MACI,MAAMhN,eAAe,CAAC+M,IAAI,CAACE,IAAI,CAAC;EACxC;AACJ;AACA,SAASL,YAAYA,CAACjH,OAAO,EAAE9E,IAAI,EAAE;EACjC,OAAO+N,MAAM,CAACC,gBAAgB,CAAClJ,OAAO,CAAC,CAAC9E,IAAI,CAAC;AACjD;AAEA,SAAS8H,eAAe,EAAEC,eAAe,EAAEG,sBAAsB,EAAEC,qBAAqB,EAAEH,oBAAoB,EAAEC,mBAAmB,EAAEL,uBAAuB,EAAE4D,8BAA8B,EAAEC,kCAAkC,EAAEpK,cAAc,EAAEiK,mBAAmB,EAAES,YAAY,EAAE9E,eAAe,EAAEtF,qBAAqB,EAAEqI,mBAAmB,EAAEC,WAAW,EAAEO,kBAAkB,EAAEpF,oBAAoB,EAAEY,gBAAgB,EAAE6E,iBAAiB,EAAExL,mBAAmB,EAAEI,iBAAiB,EAAEmB,iBAAiB,EAAEP,gBAAgB,EAAEC,aAAa,EAAEP,wBAAwB,EAAEW,YAAY,EAAED,cAAc,EAAEf,YAAY,EAAEI,iBAAiB,EAAEgB,sBAAsB,EAAEtB,cAAc,EAAE4H,WAAW,EAAE7G,yBAAyB,EAAEC,uBAAuB,EAAEwD,cAAc,EAAEa,kBAAkB,EAAE7C,YAAY,EAAEN,2BAA2B,EAAEE,aAAa,EAAEE,cAAc,EAAEqI,uBAAuB,EAAEzH,oBAAoB,IAAI8G,kBAAkB,EAAEA,kBAAkB,IAAI9G,oBAAoB,EAAEiH,eAAe,EAAEpH,mBAAmB,EAAEgD,oBAAoB,EAAE9D,cAAc,EAAEiH,aAAa,EAAEN,kBAAkB,EAAE0B,SAAS,EAAE1H,gBAAgB,EAAEd,kBAAkB,EAAEa,wBAAwB,EAAED,mBAAmB,EAAED,uBAAuB,EAAEoI,mBAAmB,EAAE9D,qBAAqB,EAAEO,kCAAkC,EAAE9F,gBAAgB,EAAEgL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}