{"ast": null, "code": "// angular import\nimport { output } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { CommonModule } from '@angular/common';\n// project import\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\nimport { NavLeftComponent } from './nav-left/nav-left.component';\nimport { NavRightComponent } from './nav-right/nav-right.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nconst _c0 = () => [\"/dashboard/default\"];\nconst _c1 = a0 => ({\n  on: a0\n});\nexport class NavBarComponent {\n  // constructor\n  constructor() {\n    // public props\n    this.NavCollapsedMob = output();\n    this.navCollapsedMob = false;\n    this.headerStyle = '';\n    this.menuClass = false;\n    this.collapseStyle = 'none';\n  }\n  // public method\n  toggleMobOption() {\n    this.menuClass = !this.menuClass;\n    this.headerStyle = this.menuClass ? 'none' : '';\n    this.collapseStyle = this.menuClass ? 'block' : 'none';\n  }\n  // this is for eslint rule\n  handleKeyDown(event) {\n    if (event.key === 'Escape') {\n      this.closeMenu();\n    }\n  }\n  closeMenu() {\n    if (document.querySelector('app-navigation.pcoded-navbar').classList.contains('mob-open')) {\n      document.querySelector('app-navigation.pcoded-navbar').classList.remove('mob-open');\n    }\n  }\n  static {\n    this.ɵfac = function NavBarComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NavBarComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavBarComponent,\n      selectors: [[\"app-nav-bar\"]],\n      outputs: {\n        NavCollapsedMob: \"NavCollapsedMob\"\n      },\n      decls: 15,\n      vars: 11,\n      consts: [[1, \"m-header\"], [\"id\", \"mobile-collapse1\", \"href\", \"javascript:\", 1, \"mobile-menu\", 3, \"click\"], [1, \"b-brand\", 3, \"routerLink\"], [1, \"b-bg\"], [1, \"feather\", \"icon-trending-up\"], [1, \"b-title\"], [\"id\", \"mobile-header\", \"href\", \"javascript:\", 1, \"mobile-menu\", 3, \"click\", \"ngClass\"], [1, \"feather\", \"icon-more-horizontal\"], [1, \"collapse\", \"navbar-collapse\", \"px-4\"], [1, \"me-auto\"], [1, \"ms-auto\"], [\"tabindex\", \"0\", 1, \"pc-menu-overlay\", 3, \"click\", \"keydown\"]],\n      template: function NavBarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\")(1, \"div\", 0)(2, \"a\", 1);\n          i0.ɵɵlistener(\"click\", function NavBarComponent_Template_a_click_2_listener() {\n            return ctx.NavCollapsedMob.emit();\n          });\n          i0.ɵɵelement(3, \"span\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"a\", 2)(5, \"div\", 3);\n          i0.ɵɵelement(6, \"i\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 5);\n          i0.ɵɵtext(8, \"Datta Able\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"a\", 6);\n          i0.ɵɵlistener(\"click\", function NavBarComponent_Template_a_click_9_listener() {\n            return ctx.toggleMobOption();\n          });\n          i0.ɵɵelement(10, \"i\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8);\n          i0.ɵɵelement(12, \"app-nav-left\", 9)(13, \"app-nav-right\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 11);\n          i0.ɵɵlistener(\"click\", function NavBarComponent_Template_div_click_14_listener() {\n            return ctx.closeMenu();\n          })(\"keydown\", function NavBarComponent_Template_div_keydown_14_listener($event) {\n            return ctx.handleKeyDown($event);\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"display\", ctx.headerStyle);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(8, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c1, ctx.menuClass));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"display\", ctx.collapseStyle);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"display\", ctx.headerStyle);\n        }\n      },\n      dependencies: [SharedModule, i1.NgClass, NavLeftComponent, NavRightComponent, RouterModule, i2.RouterLink, CommonModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["output", "RouterModule", "CommonModule", "SharedModule", "NavLeftComponent", "NavRightComponent", "NavBarComponent", "constructor", "NavCollapsedMob", "navCollapsedMob", "headerStyle", "menuClass", "collapseStyle", "toggleMobOption", "handleKeyDown", "event", "key", "closeMenu", "document", "querySelector", "classList", "contains", "remove", "selectors", "outputs", "decls", "vars", "consts", "template", "NavBarComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵlistener", "NavBarComponent_Template_a_click_2_listener", "emit", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "NavBarComponent_Template_a_click_9_listener", "NavBarComponent_Template_div_click_14_listener", "NavBarComponent_Template_div_keydown_14_listener", "$event", "ɵɵadvance", "ɵɵstyleProp", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵpureFunction1", "_c1", "i1", "Ng<PERSON><PERSON>", "i2", "RouterLink", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\nav-bar\\nav-bar.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\nav-bar\\nav-bar.component.html"], "sourcesContent": ["// angular import\r\nimport { Component, output } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n// project import\r\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\r\nimport { NavLeftComponent } from './nav-left/nav-left.component';\r\nimport { NavRightComponent } from './nav-right/nav-right.component';\r\n\r\n@Component({\r\n  selector: 'app-nav-bar',\r\n  imports: [SharedModule, NavLeftComponent, NavRightComponent, RouterModule, CommonModule],\r\n  templateUrl: './nav-bar.component.html',\r\n  styleUrls: ['./nav-bar.component.scss']\r\n})\r\nexport class NavBarComponent {\r\n  // public props\r\n  readonly NavCollapsedMob = output();\r\n  navCollapsedMob;\r\n  headerStyle: string;\r\n  menuClass: boolean;\r\n  collapseStyle: string;\r\n\r\n  // constructor\r\n  constructor() {\r\n    this.navCollapsedMob = false;\r\n    this.headerStyle = '';\r\n    this.menuClass = false;\r\n    this.collapseStyle = 'none';\r\n  }\r\n\r\n  // public method\r\n  toggleMobOption() {\r\n    this.menuClass = !this.menuClass;\r\n    this.headerStyle = this.menuClass ? 'none' : '';\r\n    this.collapseStyle = this.menuClass ? 'block' : 'none';\r\n  }\r\n\r\n  // this is for eslint rule\r\n  handleKeyDown(event: KeyboardEvent): void {\r\n    if (event.key === 'Escape') {\r\n      this.closeMenu();\r\n    }\r\n  }\r\n\r\n  closeMenu() {\r\n    if (document.querySelector('app-navigation.pcoded-navbar').classList.contains('mob-open')) {\r\n      document.querySelector('app-navigation.pcoded-navbar').classList.remove('mob-open');\r\n    }\r\n  }\r\n}\r\n", "<header>\r\n  <div class=\"m-header\" [style.display]=\"this.headerStyle\">\r\n    <a class=\"mobile-menu\" id=\"mobile-collapse1\" href=\"javascript:\" (click)=\"this.NavCollapsedMob.emit()\"><span></span></a>\r\n    <a [routerLink]=\"['/dashboard/default']\" class=\"b-brand\">\r\n      <div class=\"b-bg\">\r\n        <i class=\"feather icon-trending-up\"></i>\r\n      </div>\r\n      <span class=\"b-title\">Datta Able</span>\r\n    </a>\r\n  </div>\r\n  <a class=\"mobile-menu\" [ngClass]=\"{ on: this.menuClass }\" id=\"mobile-header\" href=\"javascript:\" (click)=\"toggleMobOption()\">\r\n    <i class=\"feather icon-more-horizontal\"></i>\r\n  </a>\r\n  <div class=\"collapse navbar-collapse px-4\" [style.display]=\"this.collapseStyle\">\r\n    <app-nav-left class=\"me-auto\" [style.display]=\"this.headerStyle\" />\r\n    <app-nav-right class=\"ms-auto\" />\r\n  </div>\r\n</header>\r\n<div class=\"pc-menu-overlay\" (click)=\"closeMenu()\" (keydown)=\"handleKeyDown($event)\" tabindex=\"0\"></div>\r\n"], "mappings": "AAAA;AACA,SAAoBA,MAAM,QAAQ,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,YAAY,QAAQ,oCAAoC;AACjE,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,iBAAiB,QAAQ,iCAAiC;;;;;;;;AAQnE,OAAM,MAAOC,eAAe;EAQ1B;EACAC,YAAA;IARA;IACS,KAAAC,eAAe,GAAGR,MAAM,EAAE;IAQjC,IAAI,CAACS,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,aAAa,GAAG,MAAM;EAC7B;EAEA;EACAC,eAAeA,CAAA;IACb,IAAI,CAACF,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAChC,IAAI,CAACD,WAAW,GAAG,IAAI,CAACC,SAAS,GAAG,MAAM,GAAG,EAAE;IAC/C,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,SAAS,GAAG,OAAO,GAAG,MAAM;EACxD;EAEA;EACAG,aAAaA,CAACC,KAAoB;IAChC,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC1B,IAAI,CAACC,SAAS,EAAE;IAClB;EACF;EAEAA,SAASA,CAAA;IACP,IAAIC,QAAQ,CAACC,aAAa,CAAC,8BAA8B,CAAC,CAACC,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACzFH,QAAQ,CAACC,aAAa,CAAC,8BAA8B,CAAC,CAACC,SAAS,CAACE,MAAM,CAAC,UAAU,CAAC;IACrF;EACF;;;uCAlCWhB,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAiB,SAAA;MAAAC,OAAA;QAAAhB,eAAA;MAAA;MAAAiB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdxBE,EAFJ,CAAAC,cAAA,aAAQ,aACmD,WAC+C;UAAtCD,EAAA,CAAAE,UAAA,mBAAAC,4CAAA;YAAA,OAASJ,GAAA,CAAAvB,eAAA,CAAA4B,IAAA,EAA2B;UAAA,EAAC;UAACJ,EAAA,CAAAK,SAAA,WAAa;UAAAL,EAAA,CAAAM,YAAA,EAAI;UAErHN,EADF,CAAAC,cAAA,WAAyD,aACrC;UAChBD,EAAA,CAAAK,SAAA,WAAwC;UAC1CL,EAAA,CAAAM,YAAA,EAAM;UACNN,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAO,MAAA,iBAAU;UAEpCP,EAFoC,CAAAM,YAAA,EAAO,EACrC,EACA;UACNN,EAAA,CAAAC,cAAA,WAA4H;UAA5BD,EAAA,CAAAE,UAAA,mBAAAM,4CAAA;YAAA,OAAST,GAAA,CAAAlB,eAAA,EAAiB;UAAA,EAAC;UACzHmB,EAAA,CAAAK,SAAA,YAA4C;UAC9CL,EAAA,CAAAM,YAAA,EAAI;UACJN,EAAA,CAAAC,cAAA,cAAgF;UAE9ED,EADA,CAAAK,SAAA,uBAAmE,yBAClC;UAErCL,EADE,CAAAM,YAAA,EAAM,EACC;UACTN,EAAA,CAAAC,cAAA,eAAkG;UAA/CD,EAAtB,CAAAE,UAAA,mBAAAO,+CAAA;YAAA,OAASV,GAAA,CAAAd,SAAA,EAAW;UAAA,EAAC,qBAAAyB,iDAAAC,MAAA;YAAA,OAAYZ,GAAA,CAAAjB,aAAA,CAAA6B,MAAA,CAAqB;UAAA,EAAC;UAAcX,EAAA,CAAAM,YAAA,EAAM;;;UAjBhFN,EAAA,CAAAY,SAAA,EAAkC;UAAlCZ,EAAA,CAAAa,WAAA,YAAAd,GAAA,CAAArB,WAAA,CAAkC;UAEnDsB,EAAA,CAAAY,SAAA,GAAqC;UAArCZ,EAAA,CAAAc,UAAA,eAAAd,EAAA,CAAAe,eAAA,IAAAC,GAAA,EAAqC;UAOnBhB,EAAA,CAAAY,SAAA,GAAkC;UAAlCZ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAiB,eAAA,IAAAC,GAAA,EAAAnB,GAAA,CAAApB,SAAA,EAAkC;UAGdqB,EAAA,CAAAY,SAAA,GAAoC;UAApCZ,EAAA,CAAAa,WAAA,YAAAd,GAAA,CAAAnB,aAAA,CAAoC;UAC/CoB,EAAA,CAAAY,SAAA,EAAkC;UAAlCZ,EAAA,CAAAa,WAAA,YAAAd,GAAA,CAAArB,WAAA,CAAkC;;;qBDFxDP,YAAY,EAAAgD,EAAA,CAAAC,OAAA,EAAEhD,gBAAgB,EAAEC,iBAAiB,EAAEJ,YAAY,EAAAoD,EAAA,CAAAC,UAAA,EAAEpD,YAAY;MAAAqD,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}