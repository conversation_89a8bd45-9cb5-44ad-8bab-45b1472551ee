{"ast": null, "code": "import { __decorate } from \"tslib\";\n// src/app/demo/pages/questions/question-form/question-form.component.ts\nimport { Component } from '@angular/core';\nimport { Validators } from '@angular/forms';\nlet QuestionFormComponent = class QuestionFormComponent {\n  constructor(fb, route, router, questionService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.questionService = questionService;\n    this.questionId = null;\n    this.isEditMode = false;\n    this.loading = false;\n    this.submitting = false;\n    this.error = '';\n    this.questionForm = this.fb.group({\n      question_text: ['', [Validators.required, Validators.minLength(5)]],\n      options: this.fb.array([])\n    });\n  }\n  ngOnInit() {\n    this.questionId = this.route.snapshot.paramMap.get('id');\n    this.isEditMode = !!this.questionId;\n    if (this.isEditMode && this.questionId) {\n      this.loadQuestion(this.questionId);\n    } else {\n      // Add at least two options for a new question\n      this.addOption();\n      this.addOption();\n    }\n  }\n  get options() {\n    return this.questionForm.get('options');\n  }\n  addOption() {\n    this.options.push(this.fb.group({\n      option_text: ['', Validators.required],\n      option_value: [0, [Validators.required, Validators.min(0)]]\n    }));\n  }\n  removeOption(index) {\n    if (this.options.length > 2) {\n      this.options.removeAt(index);\n    } else {\n      alert('A question must have at least 2 options');\n    }\n  }\n  loadQuestion(id) {\n    this.loading = true;\n    this.questionService.getQuestion(id).subscribe({\n      next: question => {\n        this.questionForm.patchValue({\n          question_text: question.question_text\n        });\n        // Clear default options\n        while (this.options.length) {\n          this.options.removeAt(0);\n        }\n        // Add existing options\n        question.options.forEach(option => {\n          this.options.push(this.fb.group({\n            id: [option.id],\n            option_text: [option.option_text, Validators.required],\n            option_value: [option.option_value, [Validators.required, Validators.min(0)]]\n          }));\n        });\n        this.loading = false;\n      },\n      error: error => {\n        this.error = 'Failed to load question';\n        this.loading = false;\n        console.error('Error loading question:', error);\n      }\n    });\n  }\n  onSubmit() {\n    if (this.questionForm.invalid) {\n      return;\n    }\n    this.submitting = true;\n    const questionData = this.questionForm.value;\n    if (this.isEditMode && this.questionId) {\n      this.questionService.updateQuestion(this.questionId, questionData).subscribe({\n        next: () => {\n          this.router.navigate(['/questions']);\n        },\n        error: error => {\n          this.error = 'Failed to update question';\n          this.submitting = false;\n          console.error('Error updating question:', error);\n        }\n      });\n    } else {\n      this.questionService.createQuestion(questionData).subscribe({\n        next: () => {\n          this.router.navigate(['/questions']);\n        },\n        error: error => {\n          this.error = 'Failed to create question';\n          this.submitting = false;\n          console.error('Error creating question:', error);\n        }\n      });\n    }\n  }\n};\nQuestionFormComponent = __decorate([Component({\n  selector: 'app-question-form',\n  templateUrl: './question-form.component.html',\n  styleUrls: ['./question-form.component.scss']\n})], QuestionFormComponent);\nexport { QuestionFormComponent };", "map": {"version": 3, "names": ["Component", "Validators", "QuestionFormComponent", "constructor", "fb", "route", "router", "questionService", "questionId", "isEditMode", "loading", "submitting", "error", "questionForm", "group", "question_text", "required", "<PERSON><PERSON><PERSON><PERSON>", "options", "array", "ngOnInit", "snapshot", "paramMap", "get", "loadQuestion", "addOption", "push", "option_text", "option_value", "min", "removeOption", "index", "length", "removeAt", "alert", "id", "getQuestion", "subscribe", "next", "question", "patchValue", "for<PERSON>ach", "option", "console", "onSubmit", "invalid", "questionData", "value", "updateQuestion", "navigate", "createQuestion", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\questions\\questions-form\\questions-form.component.ts"], "sourcesContent": ["// src/app/demo/pages/questions/question-form/question-form.component.ts\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { QuestionService } from '../../../../core/services/question.service';\r\nimport { Question } from '../../../../core/models/question.model';\r\n\r\n@Component({\r\n  selector: 'app-question-form',\r\n  templateUrl: './question-form.component.html',\r\n  styleUrls: ['./question-form.component.scss']\r\n})\r\nexport class QuestionFormComponent implements OnInit {\r\n  questionForm: FormGroup;\r\n  questionId: string | null = null;\r\n  isEditMode = false;\r\n  loading = false;\r\n  submitting = false;\r\n  error = '';\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private questionService: QuestionService\r\n  ) {\r\n    this.questionForm = this.fb.group({\r\n      question_text: ['', [Validators.required, Validators.minLength(5)]],\r\n      options: this.fb.array([])\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.questionId = this.route.snapshot.paramMap.get('id');\r\n    this.isEditMode = !!this.questionId;\r\n\r\n    if (this.isEditMode && this.questionId) {\r\n      this.loadQuestion(this.questionId);\r\n    } else {\r\n      // Add at least two options for a new question\r\n      this.addOption();\r\n      this.addOption();\r\n    }\r\n  }\r\n\r\n  get options(): FormArray {\r\n    return this.questionForm.get('options') as FormArray;\r\n  }\r\n\r\n  addOption(): void {\r\n    this.options.push(this.fb.group({\r\n      option_text: ['', Validators.required],\r\n      option_value: [0, [Validators.required, Validators.min(0)]]\r\n    }));\r\n  }\r\n\r\n  removeOption(index: number): void {\r\n    if (this.options.length > 2) {\r\n      this.options.removeAt(index);\r\n    } else {\r\n      alert('A question must have at least 2 options');\r\n    }\r\n  }\r\n\r\n  loadQuestion(id: string): void {\r\n    this.loading = true;\r\n    this.questionService.getQuestion(id)\r\n      .subscribe({\r\n        next: question => {\r\n          this.questionForm.patchValue({\r\n            question_text: question.question_text\r\n          });\r\n\r\n          // Clear default options\r\n          while (this.options.length) {\r\n            this.options.removeAt(0);\r\n          }\r\n\r\n          // Add existing options\r\n          question.options.forEach(option => {\r\n            this.options.push(this.fb.group({\r\n              id: [option.id],\r\n              option_text: [option.option_text, Validators.required],\r\n              option_value: [option.option_value, [Validators.required, Validators.min(0)]]\r\n            }));\r\n          });\r\n\r\n          this.loading = false;\r\n        },\r\n        error: error => {\r\n          this.error = 'Failed to load question';\r\n          this.loading = false;\r\n          console.error('Error loading question:', error);\r\n        }\r\n      });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.questionForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.submitting = true;\r\n    const questionData: Question = this.questionForm.value;\r\n\r\n    if (this.isEditMode && this.questionId) {\r\n      this.questionService.updateQuestion(this.questionId, questionData)\r\n        .subscribe({\r\n          next: () => {\r\n            this.router.navigate(['/questions']);\r\n          },\r\n          error: error => {\r\n            this.error = 'Failed to update question';\r\n            this.submitting = false;\r\n            console.error('Error updating question:', error);\r\n          }\r\n        });\r\n    } else {\r\n      this.questionService.createQuestion(questionData)\r\n        .subscribe({\r\n          next: () => {\r\n            this.router.navigate(['/questions']);\r\n          },\r\n          error: error => {\r\n            this.error = 'Failed to create question';\r\n            this.submitting = false;\r\n            console.error('Error creating question:', error);\r\n          }\r\n        });\r\n    }\r\n  }\r\n}"], "mappings": ";AAAA;AACA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAA4CC,UAAU,QAAQ,gBAAgB;AAUvE,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAQhCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,eAAgC;IAHhC,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IAVzB,KAAAC,UAAU,GAAkB,IAAI;IAChC,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,KAAK,GAAG,EAAE;IAQR,IAAI,CAACC,YAAY,GAAG,IAAI,CAACT,EAAE,CAACU,KAAK,CAAC;MAChCC,aAAa,EAAE,CAAC,EAAE,EAAE,CAACd,UAAU,CAACe,QAAQ,EAAEf,UAAU,CAACgB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACnEC,OAAO,EAAE,IAAI,CAACd,EAAE,CAACe,KAAK,CAAC,EAAE;KAC1B,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACZ,UAAU,GAAG,IAAI,CAACH,KAAK,CAACgB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACxD,IAAI,CAACd,UAAU,GAAG,CAAC,CAAC,IAAI,CAACD,UAAU;IAEnC,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACD,UAAU,EAAE;MACtC,IAAI,CAACgB,YAAY,CAAC,IAAI,CAAChB,UAAU,CAAC;IACpC,CAAC,MAAM;MACL;MACA,IAAI,CAACiB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,EAAE;IAClB;EACF;EAEA,IAAIP,OAAOA,CAAA;IACT,OAAO,IAAI,CAACL,YAAY,CAACU,GAAG,CAAC,SAAS,CAAc;EACtD;EAEAE,SAASA,CAAA;IACP,IAAI,CAACP,OAAO,CAACQ,IAAI,CAAC,IAAI,CAACtB,EAAE,CAACU,KAAK,CAAC;MAC9Ba,WAAW,EAAE,CAAC,EAAE,EAAE1B,UAAU,CAACe,QAAQ,CAAC;MACtCY,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC3B,UAAU,CAACe,QAAQ,EAAEf,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3D,CAAC,CAAC;EACL;EAEAC,YAAYA,CAACC,KAAa;IACxB,IAAI,IAAI,CAACb,OAAO,CAACc,MAAM,GAAG,CAAC,EAAE;MAC3B,IAAI,CAACd,OAAO,CAACe,QAAQ,CAACF,KAAK,CAAC;IAC9B,CAAC,MAAM;MACLG,KAAK,CAAC,yCAAyC,CAAC;IAClD;EACF;EAEAV,YAAYA,CAACW,EAAU;IACrB,IAAI,CAACzB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACH,eAAe,CAAC6B,WAAW,CAACD,EAAE,CAAC,CACjCE,SAAS,CAAC;MACTC,IAAI,EAAEC,QAAQ,IAAG;QACf,IAAI,CAAC1B,YAAY,CAAC2B,UAAU,CAAC;UAC3BzB,aAAa,EAAEwB,QAAQ,CAACxB;SACzB,CAAC;QAEF;QACA,OAAO,IAAI,CAACG,OAAO,CAACc,MAAM,EAAE;UAC1B,IAAI,CAACd,OAAO,CAACe,QAAQ,CAAC,CAAC,CAAC;QAC1B;QAEA;QACAM,QAAQ,CAACrB,OAAO,CAACuB,OAAO,CAACC,MAAM,IAAG;UAChC,IAAI,CAACxB,OAAO,CAACQ,IAAI,CAAC,IAAI,CAACtB,EAAE,CAACU,KAAK,CAAC;YAC9BqB,EAAE,EAAE,CAACO,MAAM,CAACP,EAAE,CAAC;YACfR,WAAW,EAAE,CAACe,MAAM,CAACf,WAAW,EAAE1B,UAAU,CAACe,QAAQ,CAAC;YACtDY,YAAY,EAAE,CAACc,MAAM,CAACd,YAAY,EAAE,CAAC3B,UAAU,CAACe,QAAQ,EAAEf,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;WAC7E,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAACnB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDE,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACA,KAAK,GAAG,yBAAyB;QACtC,IAAI,CAACF,OAAO,GAAG,KAAK;QACpBiC,OAAO,CAAC/B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;KACD,CAAC;EACN;EAEAgC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC/B,YAAY,CAACgC,OAAO,EAAE;MAC7B;IACF;IAEA,IAAI,CAAClC,UAAU,GAAG,IAAI;IACtB,MAAMmC,YAAY,GAAa,IAAI,CAACjC,YAAY,CAACkC,KAAK;IAEtD,IAAI,IAAI,CAACtC,UAAU,IAAI,IAAI,CAACD,UAAU,EAAE;MACtC,IAAI,CAACD,eAAe,CAACyC,cAAc,CAAC,IAAI,CAACxC,UAAU,EAAEsC,YAAY,CAAC,CAC/DT,SAAS,CAAC;QACTC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAChC,MAAM,CAAC2C,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDrC,KAAK,EAAEA,KAAK,IAAG;UACb,IAAI,CAACA,KAAK,GAAG,2BAA2B;UACxC,IAAI,CAACD,UAAU,GAAG,KAAK;UACvBgC,OAAO,CAAC/B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;OACD,CAAC;IACN,CAAC,MAAM;MACL,IAAI,CAACL,eAAe,CAAC2C,cAAc,CAACJ,YAAY,CAAC,CAC9CT,SAAS,CAAC;QACTC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAChC,MAAM,CAAC2C,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDrC,KAAK,EAAEA,KAAK,IAAG;UACb,IAAI,CAACA,KAAK,GAAG,2BAA2B;UACxC,IAAI,CAACD,UAAU,GAAG,KAAK;UACvBgC,OAAO,CAAC/B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;OACD,CAAC;IACN;EACF;CACD;AAvHYV,qBAAqB,GAAAiD,UAAA,EALjCnD,SAAS,CAAC;EACToD,QAAQ,EAAE,mBAAmB;EAC7BC,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,CAAC,gCAAgC;CAC7C,CAAC,C,EACWpD,qBAAqB,CAuHjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}