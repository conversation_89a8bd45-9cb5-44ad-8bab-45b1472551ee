{"ast": null, "code": "/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { assertInInjectionContext, inject, DestroyRef, RuntimeError, Injector, assertNotInReactiveContext, signal, PendingTasks } from './root_effect_scheduler-BZMWiScf.mjs';\nimport { getOutputDestroyRef, effect, untracked, computed, resource } from './resource-DhKtse7l.mjs';\nimport './primitives/di.mjs';\nimport './signal-B6pMq7KS.mjs';\nimport '@angular/core/primitives/di';\nimport '@angular/core/primitives/signals';\nimport './untracked-Bz5WMeU1.mjs';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @publicApi\n */\nfunction takeUntilDestroyed(destroyRef) {\n  if (!destroyRef) {\n    assertInInjectionContext(takeUntilDestroyed);\n    destroyRef = inject(DestroyRef);\n  }\n  const destroyed$ = new Observable(observer => {\n    const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n    return unregisterFn;\n  });\n  return source => {\n    return source.pipe(takeUntil(destroyed$));\n  };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n  source;\n  destroyed = false;\n  destroyRef = inject(DestroyRef);\n  constructor(source) {\n    this.source = source;\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n  subscribe(callbackFn) {\n    if (this.destroyed) {\n      throw new RuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected subscription to destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.');\n    }\n    // Stop yielding more values when the directive/component is already destroyed.\n    const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n      next: value => callbackFn(value)\n    });\n    return {\n      unsubscribe: () => subscription.unsubscribe()\n    };\n  }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @publicApi\n */\nfunction outputFromObservable(observable, opts) {\n  ngDevMode && assertInInjectionContext(outputFromObservable);\n  return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @publicApi\n */\nfunction outputToObservable(ref) {\n  const destroyRef = getOutputDestroyRef(ref);\n  return new Observable(observer => {\n    // Complete the observable upon directive/component destroy.\n    // Note: May be `undefined` if an `EventEmitter` is declared outside\n    // of an injection context.\n    destroyRef?.onDestroy(() => observer.complete());\n    const subscription = ref.subscribe(v => observer.next(v));\n    return () => subscription.unsubscribe();\n  });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @publicApi\n */\nfunction toObservable(source, options) {\n  !options?.injector && assertInInjectionContext(toObservable);\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject(1);\n  const watcher = effect(() => {\n    let value;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {\n    injector,\n    manualCleanup: true\n  });\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n  return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n */\nfunction toSignal(source, options) {\n  typeof ngDevMode !== 'undefined' && ngDevMode && assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' + 'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n  const requiresCleanup = !options?.manualCleanup;\n  requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n  const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n  const equal = makeToSignalEqual(options?.equal);\n  // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n  // the same - the returned signal gives values of type `T`.\n  let state;\n  if (options?.requireSync) {\n    // Initially the signal is in a `NoValue` state.\n    state = signal({\n      kind: 0 /* StateKind.NoValue */\n    }, {\n      equal\n    });\n  } else {\n    // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n    state = signal({\n      kind: 1 /* StateKind.Value */,\n      value: options?.initialValue\n    }, {\n      equal\n    });\n  }\n  // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n  // this, we would subscribe to the observable outside of the current reactive context, avoiding\n  // that side-effect signal reads/writes are attribute to the current consumer. The current\n  // consumer only needs to be notified when the `state` signal changes through the observable\n  // subscription. Additional context (related to async pipe):\n  // https://github.com/angular/angular/pull/50522.\n  const sub = source.subscribe({\n    next: value => state.set({\n      kind: 1 /* StateKind.Value */,\n      value\n    }),\n    error: error => {\n      state.set({\n        kind: 2 /* StateKind.Error */,\n        error\n      });\n    }\n    // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n    // \"complete\".\n  });\n  if (options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n    throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n  }\n  // Unsubscribe when the current context is destroyed, if requested.\n  cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n  // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n  // to either values or errors.\n  return computed(() => {\n    const current = state();\n    switch (current.kind) {\n      case 1 /* StateKind.Value */:\n        return current.value;\n      case 2 /* StateKind.Error */:\n        throw current.error;\n      case 0 /* StateKind.NoValue */:\n        // This shouldn't really happen because the error is thrown on creation.\n        throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n  }, {\n    equal: options?.equal\n  });\n}\nfunction makeToSignalEqual(userEquality = Object.is) {\n  return (a, b) => a.kind === 1 /* StateKind.Value */ && b.kind === 1 /* StateKind.Value */ && userEquality(a.value, b.value);\n}\n\n/**\n * Operator which makes the application unstable until the observable emits, completes, errors, or is unsubscribed.\n *\n * Use this operator in observables whose subscriptions are important for rendering and should be included in SSR serialization.\n *\n * @param injector The `Injector` to use during creation. If this is not provided, the current injection context will be used instead (via `inject`).\n *\n * @developerPreview\n */\nfunction pendingUntilEvent(injector) {\n  if (injector === undefined) {\n    assertInInjectionContext(pendingUntilEvent);\n    injector = inject(Injector);\n  }\n  const taskService = injector.get(PendingTasks);\n  return sourceObservable => {\n    return new Observable(originalSubscriber => {\n      // create a new task on subscription\n      const removeTask = taskService.add();\n      let cleanedUp = false;\n      function cleanupTask() {\n        if (cleanedUp) {\n          return;\n        }\n        removeTask();\n        cleanedUp = true;\n      }\n      const innerSubscription = sourceObservable.subscribe({\n        next: v => {\n          originalSubscriber.next(v);\n          cleanupTask();\n        },\n        complete: () => {\n          originalSubscriber.complete();\n          cleanupTask();\n        },\n        error: e => {\n          originalSubscriber.error(e);\n          cleanupTask();\n        }\n      });\n      innerSubscription.add(() => {\n        originalSubscriber.unsubscribe();\n        cleanupTask();\n      });\n      return innerSubscription;\n    });\n  };\n}\nfunction rxResource(opts) {\n  opts?.injector || assertInInjectionContext(rxResource);\n  return resource({\n    ...opts,\n    loader: undefined,\n    stream: params => {\n      let sub;\n      // Track the abort listener so it can be removed if the Observable completes (as a memory\n      // optimization).\n      const onAbort = () => sub.unsubscribe();\n      params.abortSignal.addEventListener('abort', onAbort);\n      // Start off stream as undefined.\n      const stream = signal({\n        value: undefined\n      });\n      let resolve;\n      const promise = new Promise(r => resolve = r);\n      function send(value) {\n        stream.set(value);\n        resolve?.(stream);\n        resolve = undefined;\n      }\n      sub = opts.loader(params).subscribe({\n        next: value => send({\n          value\n        }),\n        error: error => send({\n          error\n        }),\n        complete: () => {\n          if (resolve) {\n            send({\n              error: new Error('Resource completed before producing a value')\n            });\n          }\n          params.abortSignal.removeEventListener('abort', onAbort);\n        }\n      });\n      return promise;\n    }\n  });\n}\nexport { outputFromObservable, outputToObservable, pendingUntilEvent, rxResource, takeUntilDestroyed, toObservable, toSignal };", "map": {"version": 3, "names": ["Observable", "ReplaySubject", "takeUntil", "assertInInjectionContext", "inject", "DestroyRef", "RuntimeError", "Injector", "assertNotInReactiveContext", "signal", "PendingTasks", "getOutputDestroyRef", "effect", "untracked", "computed", "resource", "takeUntilDestroyed", "destroyRef", "destroyed$", "observer", "unregisterFn", "onDestroy", "next", "bind", "source", "pipe", "OutputFromObservableRef", "destroyed", "constructor", "subscribe", "callbackFn", "ngDevMode", "subscription", "value", "unsubscribe", "outputFromObservable", "observable", "opts", "outputToObservable", "ref", "complete", "v", "toObservable", "options", "injector", "subject", "watcher", "err", "error", "manualCleanup", "get", "destroy", "asObservable", "toSignal", "requiresCleanup", "cleanupRef", "equal", "makeToSignalEqual", "state", "requireSync", "kind", "initialValue", "sub", "set", "current", "userEquality", "Object", "is", "a", "b", "pendingUntilEvent", "undefined", "taskService", "sourceObservable", "originalSubscriber", "removeTask", "add", "cleanedUp", "cleanupTask", "innerSubscription", "e", "rxResource", "loader", "stream", "params", "onAbort", "abortSignal", "addEventListener", "resolve", "promise", "Promise", "r", "send", "Error", "removeEventListener"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/@angular/core/fesm2022/rxjs-interop.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { assertInInjectionContext, inject, DestroyRef, RuntimeError, Injector, assertNotInReactiveContext, signal, PendingTasks } from './root_effect_scheduler-BZMWiScf.mjs';\nimport { getOutputDestroyRef, effect, untracked, computed, resource } from './resource-DhKtse7l.mjs';\nimport './primitives/di.mjs';\nimport './signal-B6pMq7KS.mjs';\nimport '@angular/core/primitives/di';\nimport '@angular/core/primitives/signals';\nimport './untracked-Bz5WMeU1.mjs';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @publicApi\n */\nfunction takeUntilDestroyed(destroyRef) {\n    if (!destroyRef) {\n        assertInInjectionContext(takeUntilDestroyed);\n        destroyRef = inject(DestroyRef);\n    }\n    const destroyed$ = new Observable((observer) => {\n        const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n        return unregisterFn;\n    });\n    return (source) => {\n        return source.pipe(takeUntil(destroyed$));\n    };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n    source;\n    destroyed = false;\n    destroyRef = inject(DestroyRef);\n    constructor(source) {\n        this.source = source;\n        this.destroyRef.onDestroy(() => {\n            this.destroyed = true;\n        });\n    }\n    subscribe(callbackFn) {\n        if (this.destroyed) {\n            throw new RuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected subscription to destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.');\n        }\n        // Stop yielding more values when the directive/component is already destroyed.\n        const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n            next: (value) => callbackFn(value),\n        });\n        return {\n            unsubscribe: () => subscription.unsubscribe(),\n        };\n    }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @publicApi\n */\nfunction outputFromObservable(observable, opts) {\n    ngDevMode && assertInInjectionContext(outputFromObservable);\n    return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @publicApi\n */\nfunction outputToObservable(ref) {\n    const destroyRef = getOutputDestroyRef(ref);\n    return new Observable((observer) => {\n        // Complete the observable upon directive/component destroy.\n        // Note: May be `undefined` if an `EventEmitter` is declared outside\n        // of an injection context.\n        destroyRef?.onDestroy(() => observer.complete());\n        const subscription = ref.subscribe((v) => observer.next(v));\n        return () => subscription.unsubscribe();\n    });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @publicApi\n */\nfunction toObservable(source, options) {\n    !options?.injector && assertInInjectionContext(toObservable);\n    const injector = options?.injector ?? inject(Injector);\n    const subject = new ReplaySubject(1);\n    const watcher = effect(() => {\n        let value;\n        try {\n            value = source();\n        }\n        catch (err) {\n            untracked(() => subject.error(err));\n            return;\n        }\n        untracked(() => subject.next(value));\n    }, { injector, manualCleanup: true });\n    injector.get(DestroyRef).onDestroy(() => {\n        watcher.destroy();\n        subject.complete();\n    });\n    return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n */\nfunction toSignal(source, options) {\n    typeof ngDevMode !== 'undefined' &&\n        ngDevMode &&\n        assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' +\n            'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n    const requiresCleanup = !options?.manualCleanup;\n    requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n    const cleanupRef = requiresCleanup\n        ? (options?.injector?.get(DestroyRef) ?? inject(DestroyRef))\n        : null;\n    const equal = makeToSignalEqual(options?.equal);\n    // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n    // the same - the returned signal gives values of type `T`.\n    let state;\n    if (options?.requireSync) {\n        // Initially the signal is in a `NoValue` state.\n        state = signal({ kind: 0 /* StateKind.NoValue */ }, { equal });\n    }\n    else {\n        // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n        state = signal({ kind: 1 /* StateKind.Value */, value: options?.initialValue }, { equal });\n    }\n    // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n    // this, we would subscribe to the observable outside of the current reactive context, avoiding\n    // that side-effect signal reads/writes are attribute to the current consumer. The current\n    // consumer only needs to be notified when the `state` signal changes through the observable\n    // subscription. Additional context (related to async pipe):\n    // https://github.com/angular/angular/pull/50522.\n    const sub = source.subscribe({\n        next: (value) => state.set({ kind: 1 /* StateKind.Value */, value }),\n        error: (error) => {\n            state.set({ kind: 2 /* StateKind.Error */, error });\n        },\n        // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n        // \"complete\".\n    });\n    if (options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n        throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n    // Unsubscribe when the current context is destroyed, if requested.\n    cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n    // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n    // to either values or errors.\n    return computed(() => {\n        const current = state();\n        switch (current.kind) {\n            case 1 /* StateKind.Value */:\n                return current.value;\n            case 2 /* StateKind.Error */:\n                throw current.error;\n            case 0 /* StateKind.NoValue */:\n                // This shouldn't really happen because the error is thrown on creation.\n                throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n        }\n    }, { equal: options?.equal });\n}\nfunction makeToSignalEqual(userEquality = Object.is) {\n    return (a, b) => a.kind === 1 /* StateKind.Value */ && b.kind === 1 /* StateKind.Value */ && userEquality(a.value, b.value);\n}\n\n/**\n * Operator which makes the application unstable until the observable emits, completes, errors, or is unsubscribed.\n *\n * Use this operator in observables whose subscriptions are important for rendering and should be included in SSR serialization.\n *\n * @param injector The `Injector` to use during creation. If this is not provided, the current injection context will be used instead (via `inject`).\n *\n * @developerPreview\n */\nfunction pendingUntilEvent(injector) {\n    if (injector === undefined) {\n        assertInInjectionContext(pendingUntilEvent);\n        injector = inject(Injector);\n    }\n    const taskService = injector.get(PendingTasks);\n    return (sourceObservable) => {\n        return new Observable((originalSubscriber) => {\n            // create a new task on subscription\n            const removeTask = taskService.add();\n            let cleanedUp = false;\n            function cleanupTask() {\n                if (cleanedUp) {\n                    return;\n                }\n                removeTask();\n                cleanedUp = true;\n            }\n            const innerSubscription = sourceObservable.subscribe({\n                next: (v) => {\n                    originalSubscriber.next(v);\n                    cleanupTask();\n                },\n                complete: () => {\n                    originalSubscriber.complete();\n                    cleanupTask();\n                },\n                error: (e) => {\n                    originalSubscriber.error(e);\n                    cleanupTask();\n                },\n            });\n            innerSubscription.add(() => {\n                originalSubscriber.unsubscribe();\n                cleanupTask();\n            });\n            return innerSubscription;\n        });\n    };\n}\n\nfunction rxResource(opts) {\n    opts?.injector || assertInInjectionContext(rxResource);\n    return resource({\n        ...opts,\n        loader: undefined,\n        stream: (params) => {\n            let sub;\n            // Track the abort listener so it can be removed if the Observable completes (as a memory\n            // optimization).\n            const onAbort = () => sub.unsubscribe();\n            params.abortSignal.addEventListener('abort', onAbort);\n            // Start off stream as undefined.\n            const stream = signal({ value: undefined });\n            let resolve;\n            const promise = new Promise((r) => (resolve = r));\n            function send(value) {\n                stream.set(value);\n                resolve?.(stream);\n                resolve = undefined;\n            }\n            sub = opts.loader(params).subscribe({\n                next: (value) => send({ value }),\n                error: (error) => send({ error }),\n                complete: () => {\n                    if (resolve) {\n                        send({ error: new Error('Resource completed before producing a value') });\n                    }\n                    params.abortSignal.removeEventListener('abort', onAbort);\n                },\n            });\n            return promise;\n        },\n    });\n}\n\nexport { outputFromObservable, outputToObservable, pendingUntilEvent, rxResource, takeUntilDestroyed, toObservable, toSignal };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,UAAU,EAAEC,aAAa,QAAQ,MAAM;AAChD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,wBAAwB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,0BAA0B,EAAEC,MAAM,EAAEC,YAAY,QAAQ,sCAAsC;AAC7K,SAASC,mBAAmB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,yBAAyB;AACpG,OAAO,qBAAqB;AAC5B,OAAO,uBAAuB;AAC9B,OAAO,6BAA6B;AACpC,OAAO,kCAAkC;AACzC,OAAO,0BAA0B;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,UAAU,EAAE;EACpC,IAAI,CAACA,UAAU,EAAE;IACbd,wBAAwB,CAACa,kBAAkB,CAAC;IAC5CC,UAAU,GAAGb,MAAM,CAACC,UAAU,CAAC;EACnC;EACA,MAAMa,UAAU,GAAG,IAAIlB,UAAU,CAAEmB,QAAQ,IAAK;IAC5C,MAAMC,YAAY,GAAGH,UAAU,CAACI,SAAS,CAACF,QAAQ,CAACG,IAAI,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAAC;IACvE,OAAOC,YAAY;EACvB,CAAC,CAAC;EACF,OAAQI,MAAM,IAAK;IACf,OAAOA,MAAM,CAACC,IAAI,CAACvB,SAAS,CAACgB,UAAU,CAAC,CAAC;EAC7C,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,uBAAuB,CAAC;EAC1BF,MAAM;EACNG,SAAS,GAAG,KAAK;EACjBV,UAAU,GAAGb,MAAM,CAACC,UAAU,CAAC;EAC/BuB,WAAWA,CAACJ,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACP,UAAU,CAACI,SAAS,CAAC,MAAM;MAC5B,IAAI,CAACM,SAAS,GAAG,IAAI;IACzB,CAAC,CAAC;EACN;EACAE,SAASA,CAACC,UAAU,EAAE;IAClB,IAAI,IAAI,CAACH,SAAS,EAAE;MAChB,MAAM,IAAIrB,YAAY,CAAC,GAAG,CAAC,8CAA8CyB,SAAS,IAC9E,oDAAoD,GAChD,8CAA8C,CAAC;IAC3D;IACA;IACA,MAAMC,YAAY,GAAG,IAAI,CAACR,MAAM,CAACC,IAAI,CAACT,kBAAkB,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAACY,SAAS,CAAC;MACjFP,IAAI,EAAGW,KAAK,IAAKH,UAAU,CAACG,KAAK;IACrC,CAAC,CAAC;IACF,OAAO;MACHC,WAAW,EAAEA,CAAA,KAAMF,YAAY,CAACE,WAAW,CAAC;IAChD,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACC,UAAU,EAAEC,IAAI,EAAE;EAC5CN,SAAS,IAAI5B,wBAAwB,CAACgC,oBAAoB,CAAC;EAC3D,OAAO,IAAIT,uBAAuB,CAACU,UAAU,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,kBAAkBA,CAACC,GAAG,EAAE;EAC7B,MAAMtB,UAAU,GAAGN,mBAAmB,CAAC4B,GAAG,CAAC;EAC3C,OAAO,IAAIvC,UAAU,CAAEmB,QAAQ,IAAK;IAChC;IACA;IACA;IACAF,UAAU,EAAEI,SAAS,CAAC,MAAMF,QAAQ,CAACqB,QAAQ,CAAC,CAAC,CAAC;IAChD,MAAMR,YAAY,GAAGO,GAAG,CAACV,SAAS,CAAEY,CAAC,IAAKtB,QAAQ,CAACG,IAAI,CAACmB,CAAC,CAAC,CAAC;IAC3D,OAAO,MAAMT,YAAY,CAACE,WAAW,CAAC,CAAC;EAC3C,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,YAAYA,CAAClB,MAAM,EAAEmB,OAAO,EAAE;EACnC,CAACA,OAAO,EAAEC,QAAQ,IAAIzC,wBAAwB,CAACuC,YAAY,CAAC;EAC5D,MAAME,QAAQ,GAAGD,OAAO,EAAEC,QAAQ,IAAIxC,MAAM,CAACG,QAAQ,CAAC;EACtD,MAAMsC,OAAO,GAAG,IAAI5C,aAAa,CAAC,CAAC,CAAC;EACpC,MAAM6C,OAAO,GAAGlC,MAAM,CAAC,MAAM;IACzB,IAAIqB,KAAK;IACT,IAAI;MACAA,KAAK,GAAGT,MAAM,CAAC,CAAC;IACpB,CAAC,CACD,OAAOuB,GAAG,EAAE;MACRlC,SAAS,CAAC,MAAMgC,OAAO,CAACG,KAAK,CAACD,GAAG,CAAC,CAAC;MACnC;IACJ;IACAlC,SAAS,CAAC,MAAMgC,OAAO,CAACvB,IAAI,CAACW,KAAK,CAAC,CAAC;EACxC,CAAC,EAAE;IAAEW,QAAQ;IAAEK,aAAa,EAAE;EAAK,CAAC,CAAC;EACrCL,QAAQ,CAACM,GAAG,CAAC7C,UAAU,CAAC,CAACgB,SAAS,CAAC,MAAM;IACrCyB,OAAO,CAACK,OAAO,CAAC,CAAC;IACjBN,OAAO,CAACL,QAAQ,CAAC,CAAC;EACtB,CAAC,CAAC;EACF,OAAOK,OAAO,CAACO,YAAY,CAAC,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAC7B,MAAM,EAAEmB,OAAO,EAAE;EAC/B,OAAOZ,SAAS,KAAK,WAAW,IAC5BA,SAAS,IACTvB,0BAA0B,CAAC6C,QAAQ,EAAE,2DAA2D,GAC5F,oGAAoG,CAAC;EAC7G,MAAMC,eAAe,GAAG,CAACX,OAAO,EAAEM,aAAa;EAC/CK,eAAe,IAAI,CAACX,OAAO,EAAEC,QAAQ,IAAIzC,wBAAwB,CAACkD,QAAQ,CAAC;EAC3E,MAAME,UAAU,GAAGD,eAAe,GAC3BX,OAAO,EAAEC,QAAQ,EAAEM,GAAG,CAAC7C,UAAU,CAAC,IAAID,MAAM,CAACC,UAAU,CAAC,GACzD,IAAI;EACV,MAAMmD,KAAK,GAAGC,iBAAiB,CAACd,OAAO,EAAEa,KAAK,CAAC;EAC/C;EACA;EACA,IAAIE,KAAK;EACT,IAAIf,OAAO,EAAEgB,WAAW,EAAE;IACtB;IACAD,KAAK,GAAGjD,MAAM,CAAC;MAAEmD,IAAI,EAAE,CAAC,CAAC;IAAwB,CAAC,EAAE;MAAEJ;IAAM,CAAC,CAAC;EAClE,CAAC,MACI;IACD;IACAE,KAAK,GAAGjD,MAAM,CAAC;MAAEmD,IAAI,EAAE,CAAC,CAAC;MAAuB3B,KAAK,EAAEU,OAAO,EAAEkB;IAAa,CAAC,EAAE;MAAEL;IAAM,CAAC,CAAC;EAC9F;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMM,GAAG,GAAGtC,MAAM,CAACK,SAAS,CAAC;IACzBP,IAAI,EAAGW,KAAK,IAAKyB,KAAK,CAACK,GAAG,CAAC;MAAEH,IAAI,EAAE,CAAC,CAAC;MAAuB3B;IAAM,CAAC,CAAC;IACpEe,KAAK,EAAGA,KAAK,IAAK;MACdU,KAAK,CAACK,GAAG,CAAC;QAAEH,IAAI,EAAE,CAAC,CAAC;QAAuBZ;MAAM,CAAC,CAAC;IACvD;IACA;IACA;EACJ,CAAC,CAAC;EACF,IAAIL,OAAO,EAAEgB,WAAW,IAAID,KAAK,CAAC,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,yBAAyB;IACpE,MAAM,IAAItD,YAAY,CAAC,GAAG,CAAC,wDAAwD,CAAC,OAAOyB,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC7H,qFAAqF,CAAC;EAC9F;EACA;EACAwB,UAAU,EAAElC,SAAS,CAACyC,GAAG,CAAC5B,WAAW,CAACX,IAAI,CAACuC,GAAG,CAAC,CAAC;EAChD;EACA;EACA,OAAOhD,QAAQ,CAAC,MAAM;IAClB,MAAMkD,OAAO,GAAGN,KAAK,CAAC,CAAC;IACvB,QAAQM,OAAO,CAACJ,IAAI;MAChB,KAAK,CAAC,CAAC;QACH,OAAOI,OAAO,CAAC/B,KAAK;MACxB,KAAK,CAAC,CAAC;QACH,MAAM+B,OAAO,CAAChB,KAAK;MACvB,KAAK,CAAC,CAAC;QACH;QACA,MAAM,IAAI1C,YAAY,CAAC,GAAG,CAAC,wDAAwD,CAAC,OAAOyB,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC7H,qFAAqF,CAAC;IAClG;EACJ,CAAC,EAAE;IAAEyB,KAAK,EAAEb,OAAO,EAAEa;EAAM,CAAC,CAAC;AACjC;AACA,SAASC,iBAAiBA,CAACQ,YAAY,GAAGC,MAAM,CAACC,EAAE,EAAE;EACjD,OAAO,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACR,IAAI,KAAK,CAAC,CAAC,yBAAyBS,CAAC,CAACT,IAAI,KAAK,CAAC,CAAC,yBAAyBK,YAAY,CAACG,CAAC,CAACnC,KAAK,EAAEoC,CAAC,CAACpC,KAAK,CAAC;AAC/H;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqC,iBAAiBA,CAAC1B,QAAQ,EAAE;EACjC,IAAIA,QAAQ,KAAK2B,SAAS,EAAE;IACxBpE,wBAAwB,CAACmE,iBAAiB,CAAC;IAC3C1B,QAAQ,GAAGxC,MAAM,CAACG,QAAQ,CAAC;EAC/B;EACA,MAAMiE,WAAW,GAAG5B,QAAQ,CAACM,GAAG,CAACxC,YAAY,CAAC;EAC9C,OAAQ+D,gBAAgB,IAAK;IACzB,OAAO,IAAIzE,UAAU,CAAE0E,kBAAkB,IAAK;MAC1C;MACA,MAAMC,UAAU,GAAGH,WAAW,CAACI,GAAG,CAAC,CAAC;MACpC,IAAIC,SAAS,GAAG,KAAK;MACrB,SAASC,WAAWA,CAAA,EAAG;QACnB,IAAID,SAAS,EAAE;UACX;QACJ;QACAF,UAAU,CAAC,CAAC;QACZE,SAAS,GAAG,IAAI;MACpB;MACA,MAAME,iBAAiB,GAAGN,gBAAgB,CAAC5C,SAAS,CAAC;QACjDP,IAAI,EAAGmB,CAAC,IAAK;UACTiC,kBAAkB,CAACpD,IAAI,CAACmB,CAAC,CAAC;UAC1BqC,WAAW,CAAC,CAAC;QACjB,CAAC;QACDtC,QAAQ,EAAEA,CAAA,KAAM;UACZkC,kBAAkB,CAAClC,QAAQ,CAAC,CAAC;UAC7BsC,WAAW,CAAC,CAAC;QACjB,CAAC;QACD9B,KAAK,EAAGgC,CAAC,IAAK;UACVN,kBAAkB,CAAC1B,KAAK,CAACgC,CAAC,CAAC;UAC3BF,WAAW,CAAC,CAAC;QACjB;MACJ,CAAC,CAAC;MACFC,iBAAiB,CAACH,GAAG,CAAC,MAAM;QACxBF,kBAAkB,CAACxC,WAAW,CAAC,CAAC;QAChC4C,WAAW,CAAC,CAAC;MACjB,CAAC,CAAC;MACF,OAAOC,iBAAiB;IAC5B,CAAC,CAAC;EACN,CAAC;AACL;AAEA,SAASE,UAAUA,CAAC5C,IAAI,EAAE;EACtBA,IAAI,EAAEO,QAAQ,IAAIzC,wBAAwB,CAAC8E,UAAU,CAAC;EACtD,OAAOlE,QAAQ,CAAC;IACZ,GAAGsB,IAAI;IACP6C,MAAM,EAAEX,SAAS;IACjBY,MAAM,EAAGC,MAAM,IAAK;MAChB,IAAItB,GAAG;MACP;MACA;MACA,MAAMuB,OAAO,GAAGA,CAAA,KAAMvB,GAAG,CAAC5B,WAAW,CAAC,CAAC;MACvCkD,MAAM,CAACE,WAAW,CAACC,gBAAgB,CAAC,OAAO,EAAEF,OAAO,CAAC;MACrD;MACA,MAAMF,MAAM,GAAG1E,MAAM,CAAC;QAAEwB,KAAK,EAAEsC;MAAU,CAAC,CAAC;MAC3C,IAAIiB,OAAO;MACX,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAEC,CAAC,IAAMH,OAAO,GAAGG,CAAE,CAAC;MACjD,SAASC,IAAIA,CAAC3D,KAAK,EAAE;QACjBkD,MAAM,CAACpB,GAAG,CAAC9B,KAAK,CAAC;QACjBuD,OAAO,GAAGL,MAAM,CAAC;QACjBK,OAAO,GAAGjB,SAAS;MACvB;MACAT,GAAG,GAAGzB,IAAI,CAAC6C,MAAM,CAACE,MAAM,CAAC,CAACvD,SAAS,CAAC;QAChCP,IAAI,EAAGW,KAAK,IAAK2D,IAAI,CAAC;UAAE3D;QAAM,CAAC,CAAC;QAChCe,KAAK,EAAGA,KAAK,IAAK4C,IAAI,CAAC;UAAE5C;QAAM,CAAC,CAAC;QACjCR,QAAQ,EAAEA,CAAA,KAAM;UACZ,IAAIgD,OAAO,EAAE;YACTI,IAAI,CAAC;cAAE5C,KAAK,EAAE,IAAI6C,KAAK,CAAC,6CAA6C;YAAE,CAAC,CAAC;UAC7E;UACAT,MAAM,CAACE,WAAW,CAACQ,mBAAmB,CAAC,OAAO,EAAET,OAAO,CAAC;QAC5D;MACJ,CAAC,CAAC;MACF,OAAOI,OAAO;IAClB;EACJ,CAAC,CAAC;AACN;AAEA,SAAStD,oBAAoB,EAAEG,kBAAkB,EAAEgC,iBAAiB,EAAEW,UAAU,EAAEjE,kBAAkB,EAAE0B,YAAY,EAAEW,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}