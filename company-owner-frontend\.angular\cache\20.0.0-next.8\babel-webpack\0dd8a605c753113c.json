{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { UiBasicRoutingModule } from './ui-basic-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class UiBasicModule {\n  static {\n    this.ɵfac = function UiBasicModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UiBasicModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: UiBasicModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, UiBasicRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(UiBasicModule, {\n    imports: [CommonModule, UiBasicRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "UiBasicRoutingModule", "UiBasicModule", "imports"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\ui-elements\\ui-basic\\ui-basic.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { UiBasicRoutingModule } from './ui-basic-routing.module';\r\n\r\n@NgModule({\r\n  declarations: [],\r\n  imports: [CommonModule, UiBasicRoutingModule]\r\n})\r\nexport class UiBasicModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,oBAAoB,QAAQ,2BAA2B;;AAMhE,OAAM,MAAOC,aAAa;;;uCAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBAFdF,YAAY,EAAEC,oBAAoB;IAAA;EAAA;;;2EAEjCC,aAAa;IAAAC,OAAA,GAFdH,YAAY,EAAEC,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}