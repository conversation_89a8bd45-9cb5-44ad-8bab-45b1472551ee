{"ast": null, "code": "/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n  return value != null && `${value}` !== 'false';\n}\nexport { coerceBooleanProperty as c };", "map": {"version": 3, "names": ["coerceBooleanProperty", "value", "c"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/@angular/cdk/fesm2022/boolean-property-DaaVhX5A.mjs"], "sourcesContent": ["/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n    return value != null && `${value}` !== 'false';\n}\n\nexport { coerceBooleanProperty as c };\n"], "mappings": "AAAA;AACA,SAASA,qBAAqBA,CAACC,KAAK,EAAE;EAClC,OAAOA,KAAK,IAAI,IAAI,IAAI,GAAGA,KAAK,EAAE,KAAK,OAAO;AAClD;AAEA,SAASD,qBAAqB,IAAIE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}