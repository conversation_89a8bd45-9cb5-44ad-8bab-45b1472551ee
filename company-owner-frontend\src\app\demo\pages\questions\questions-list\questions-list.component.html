<div class="row">
  <div class="col-sm-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between">
        <h5>Questions</h5>
        <button class="btn btn-primary" (click)="createQuestion()">Create Question</button>
      </div>
      <div class="card-body">
        <div *ngIf="loading" class="text-center">
          <p>Loading questions...</p>
        </div>
        
        <div *ngIf="error" class="alert alert-danger">
          {{ error }}
        </div>
        
        <div *ngIf="!loading && !error">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Question</th>
                  <th>Options</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let question of questions">
                  <td>{{ question.question_text }}</td>
                  <td>
                    <span *ngFor="let option of question.options; let last = last">
                      {{ option.option_text }} ({{ option.option_value }}){{ !last ? ', ' : '' }}
                    </span>
                  </td>
                  <td>
                    <button class="btn btn-sm btn-primary mr-2" (click)="editQuestion(question.id)">Edit</button>
                  </td>
                </tr>
                <tr *ngIf="questions.length === 0">
                  <td colspan="3" class="text-center">No questions found</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>