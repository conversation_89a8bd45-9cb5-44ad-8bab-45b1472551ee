{"ast": null, "code": "// angular import\nimport { inject, input } from '@angular/core';\nimport { Location } from '@angular/common';\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\nimport { NavItemComponent } from '../nav-item/nav-item.component';\nimport { NavCollapseComponent } from '../nav-collapse/nav-collapse.component';\nimport * as i0 from \"@angular/core\";\nfunction NavGroupComponent_Conditional_0_For_4_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-nav-collapse\", 2);\n  }\n  if (rf & 2) {\n    const items_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"item\", items_r1);\n  }\n}\nfunction NavGroupComponent_Conditional_0_For_4_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-nav-item\", 2);\n  }\n  if (rf & 2) {\n    const items_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"item\", items_r1);\n  }\n}\nfunction NavGroupComponent_Conditional_0_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, NavGroupComponent_Conditional_0_For_4_Conditional_0_Template, 1, 1, \"app-nav-collapse\", 2);\n    i0.ɵɵconditionalCreate(1, NavGroupComponent_Conditional_0_For_4_Conditional_1_Template, 1, 1, \"app-nav-item\", 2);\n  }\n  if (rf & 2) {\n    const items_r1 = ctx.$implicit;\n    i0.ɵɵconditional(items_r1.type === \"collapse\" ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(items_r1.type === \"item\" ? 1 : -1);\n  }\n}\nfunction NavGroupComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 0)(1, \"label\", 1);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵrepeaterCreate(3, NavGroupComponent_Conditional_0_For_4_Template, 2, 2, null, null, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.item().title);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r1.item().children);\n  }\n}\nexport class NavGroupComponent {\n  constructor() {\n    this.location = inject(Location);\n    // public props\n    this.item = input(undefined);\n  }\n  // life cycle event\n  ngOnInit() {\n    // at reload time active and trigger link\n    let current_url = this.location.path();\n    if (this.location['_baseHref']) {\n      current_url = this.location['_baseHref'] + this.location.path();\n    }\n    const link = \"a.nav-link[ href='\" + current_url + \"' ]\";\n    const ele = document.querySelector(link);\n    if (ele !== null && ele !== undefined) {\n      const parent = ele.parentElement;\n      const up_parent = parent.parentElement.parentElement;\n      const pre_parent = up_parent.parentElement;\n      const last_parent = up_parent.parentElement.parentElement.parentElement.parentElement;\n      if (parent.classList.contains('pcoded-hasmenu')) {\n        parent.classList.add('pcoded-trigger');\n        parent.classList.add('active');\n      } else if (up_parent.classList.contains('pcoded-hasmenu')) {\n        up_parent.classList.add('pcoded-trigger');\n        up_parent.classList.add('active');\n      } else if (pre_parent.classList.contains('pcoded-hasmenu')) {\n        pre_parent.classList.add('pcoded-trigger');\n        pre_parent.classList.add('active');\n      }\n      if (last_parent.classList.contains('pcoded-hasmenu')) {\n        last_parent.classList.add('pcoded-trigger');\n        if (pre_parent.classList.contains('pcoded-hasmenu')) {\n          pre_parent.classList.add('pcoded-trigger');\n        }\n        last_parent.classList.add('active');\n      }\n    }\n  }\n  static {\n    this.ɵfac = function NavGroupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NavGroupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavGroupComponent,\n      selectors: [[\"app-nav-group\"]],\n      inputs: {\n        item: [1, \"item\"]\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[1, \"nav-item\", \"pcoded-menu-caption\"], [\"for\", \"title\"], [3, \"item\"]],\n      template: function NavGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵconditionalCreate(0, NavGroupComponent_Conditional_0_Template, 5, 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(!ctx.item().hidden ? 0 : -1);\n        }\n      },\n      dependencies: [SharedModule, NavItemComponent, NavCollapseComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["inject", "input", "Location", "SharedModule", "NavItemComponent", "NavCollapseComponent", "i0", "ɵɵelement", "ɵɵproperty", "items_r1", "ɵɵconditionalCreate", "NavGroupComponent_Conditional_0_For_4_Conditional_0_Template", "NavGroupComponent_Conditional_0_For_4_Conditional_1_Template", "ɵɵconditional", "type", "ɵɵadvance", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵrepeaterCreate", "NavGroupComponent_Conditional_0_For_4_Template", "ɵɵrepeaterTrackByIdentity", "ɵɵtextInterpolate", "ctx_r1", "item", "title", "ɵɵrepeater", "children", "NavGroupComponent", "constructor", "location", "undefined", "ngOnInit", "current_url", "path", "link", "ele", "document", "querySelector", "parent", "parentElement", "up_parent", "pre_parent", "last_parent", "classList", "contains", "add", "selectors", "inputs", "decls", "vars", "consts", "template", "NavGroupComponent_Template", "rf", "ctx", "NavGroupComponent_Conditional_0_Template", "hidden", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\navigation\\nav-content\\nav-group\\nav-group.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\navigation\\nav-content\\nav-group\\nav-group.component.html"], "sourcesContent": ["// angular import\r\nimport { Component, OnInit, inject, input } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\n\r\n// project import\r\nimport { NavigationItem } from '../../navigation';\r\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\r\nimport { NavItemComponent } from '../nav-item/nav-item.component';\r\nimport { NavCollapseComponent } from '../nav-collapse/nav-collapse.component';\r\n\r\n@Component({\r\n  selector: 'app-nav-group',\r\n  imports: [SharedModule, NavItemComponent, NavCollapseComponent],\r\n  templateUrl: './nav-group.component.html',\r\n  styleUrls: ['./nav-group.component.scss']\r\n})\r\nexport class NavGroupComponent implements OnInit {\r\n  private location = inject(Location);\r\n\r\n  // public props\r\n  readonly item = input<NavigationItem>(undefined);\r\n\r\n  // life cycle event\r\n  ngOnInit() {\r\n    // at reload time active and trigger link\r\n    let current_url = this.location.path();\r\n    if (this.location['_baseHref']) {\r\n      current_url = this.location['_baseHref'] + this.location.path();\r\n    }\r\n    const link = \"a.nav-link[ href='\" + current_url + \"' ]\";\r\n    const ele = document.querySelector(link);\r\n    if (ele !== null && ele !== undefined) {\r\n      const parent = ele.parentElement;\r\n      const up_parent = parent.parentElement.parentElement;\r\n      const pre_parent = up_parent.parentElement;\r\n      const last_parent = up_parent.parentElement.parentElement.parentElement.parentElement;\r\n      if (parent.classList.contains('pcoded-hasmenu')) {\r\n        parent.classList.add('pcoded-trigger');\r\n        parent.classList.add('active');\r\n      } else if (up_parent.classList.contains('pcoded-hasmenu')) {\r\n        up_parent.classList.add('pcoded-trigger');\r\n        up_parent.classList.add('active');\r\n      } else if (pre_parent.classList.contains('pcoded-hasmenu')) {\r\n        pre_parent.classList.add('pcoded-trigger');\r\n        pre_parent.classList.add('active');\r\n      }\r\n\r\n      if (last_parent.classList.contains('pcoded-hasmenu')) {\r\n        last_parent.classList.add('pcoded-trigger');\r\n\r\n        if (pre_parent.classList.contains('pcoded-hasmenu')) {\r\n          pre_parent.classList.add('pcoded-trigger');\r\n        }\r\n        last_parent.classList.add('active');\r\n      }\r\n    }\r\n  }\r\n}\r\n", "@if (!item().hidden) {\r\n  <li class=\"nav-item pcoded-menu-caption\">\r\n    <label for=\"title\">{{ item().title }}</label>\r\n  </li>\r\n  @for (items of item().children; track items) {\r\n    @if (items.type === 'collapse') {\r\n      <app-nav-collapse [item]=\"items\" />\r\n    }\r\n    @if (items.type === 'item') {\r\n      <app-nav-item [item]=\"items\" />\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAAA;AACA,SAA4BA,MAAM,EAAEC,KAAK,QAAQ,eAAe;AAChE,SAASC,QAAQ,QAAQ,iBAAiB;AAI1C,SAASC,YAAY,QAAQ,oCAAoC;AACjE,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,oBAAoB,QAAQ,wCAAwC;;;;ICFvEC,EAAA,CAAAC,SAAA,0BAAmC;;;;IAAjBD,EAAA,CAAAE,UAAA,SAAAC,QAAA,CAAc;;;;;IAGhCH,EAAA,CAAAC,SAAA,sBAA+B;;;;IAAjBD,EAAA,CAAAE,UAAA,SAAAC,QAAA,CAAc;;;;;IAJ9BH,EAAA,CAAAI,mBAAA,IAAAC,4DAAA,8BAAiC;IAGjCL,EAAA,CAAAI,mBAAA,IAAAE,4DAAA,0BAA6B;;;;IAH7BN,EAAA,CAAAO,aAAA,CAAAJ,QAAA,CAAAK,IAAA,yBAEC;IACDR,EAAA,CAAAS,SAAA,EAEC;IAFDT,EAAA,CAAAO,aAAA,CAAAJ,QAAA,CAAAK,IAAA,qBAEC;;;;;IARDR,EADF,CAAAU,cAAA,YAAyC,eACpB;IAAAV,EAAA,CAAAW,MAAA,GAAkB;IACvCX,EADuC,CAAAY,YAAA,EAAQ,EAC1C;IACLZ,EAAA,CAAAa,gBAAA,IAAAC,8CAAA,oBAAAd,EAAA,CAAAe,yBAAA,CAOC;;;;IAToBf,EAAA,CAAAS,SAAA,GAAkB;IAAlBT,EAAA,CAAAgB,iBAAA,CAAAC,MAAA,CAAAC,IAAA,GAAAC,KAAA,CAAkB;IAEvCnB,EAAA,CAAAS,SAAA,EAOC;IAPDT,EAAA,CAAAoB,UAAA,CAAAH,MAAA,CAAAC,IAAA,EAAM,CAAAG,QAAA,CAOL;;;ADKH,OAAM,MAAOC,iBAAiB;EAN9BC,YAAA;IAOU,KAAAC,QAAQ,GAAG9B,MAAM,CAACE,QAAQ,CAAC;IAEnC;IACS,KAAAsB,IAAI,GAAGvB,KAAK,CAAiB8B,SAAS,CAAC;;EAEhD;EACAC,QAAQA,CAAA;IACN;IACA,IAAIC,WAAW,GAAG,IAAI,CAACH,QAAQ,CAACI,IAAI,EAAE;IACtC,IAAI,IAAI,CAACJ,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC9BG,WAAW,GAAG,IAAI,CAACH,QAAQ,CAAC,WAAW,CAAC,GAAG,IAAI,CAACA,QAAQ,CAACI,IAAI,EAAE;IACjE;IACA,MAAMC,IAAI,GAAG,oBAAoB,GAAGF,WAAW,GAAG,KAAK;IACvD,MAAMG,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAACH,IAAI,CAAC;IACxC,IAAIC,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKL,SAAS,EAAE;MACrC,MAAMQ,MAAM,GAAGH,GAAG,CAACI,aAAa;MAChC,MAAMC,SAAS,GAAGF,MAAM,CAACC,aAAa,CAACA,aAAa;MACpD,MAAME,UAAU,GAAGD,SAAS,CAACD,aAAa;MAC1C,MAAMG,WAAW,GAAGF,SAAS,CAACD,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa;MACrF,IAAID,MAAM,CAACK,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QAC/CN,MAAM,CAACK,SAAS,CAACE,GAAG,CAAC,gBAAgB,CAAC;QACtCP,MAAM,CAACK,SAAS,CAACE,GAAG,CAAC,QAAQ,CAAC;MAChC,CAAC,MAAM,IAAIL,SAAS,CAACG,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QACzDJ,SAAS,CAACG,SAAS,CAACE,GAAG,CAAC,gBAAgB,CAAC;QACzCL,SAAS,CAACG,SAAS,CAACE,GAAG,CAAC,QAAQ,CAAC;MACnC,CAAC,MAAM,IAAIJ,UAAU,CAACE,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QAC1DH,UAAU,CAACE,SAAS,CAACE,GAAG,CAAC,gBAAgB,CAAC;QAC1CJ,UAAU,CAACE,SAAS,CAACE,GAAG,CAAC,QAAQ,CAAC;MACpC;MAEA,IAAIH,WAAW,CAACC,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QACpDF,WAAW,CAACC,SAAS,CAACE,GAAG,CAAC,gBAAgB,CAAC;QAE3C,IAAIJ,UAAU,CAACE,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;UACnDH,UAAU,CAACE,SAAS,CAACE,GAAG,CAAC,gBAAgB,CAAC;QAC5C;QACAH,WAAW,CAACC,SAAS,CAACE,GAAG,CAAC,QAAQ,CAAC;MACrC;IACF;EACF;;;uCAxCWlB,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAmB,SAAA;MAAAC,MAAA;QAAAxB,IAAA;MAAA;MAAAyB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChB9BhD,EAAA,CAAAI,mBAAA,IAAA8C,wCAAA,OAAsB;;;UAAtBlD,EAAA,CAAAO,aAAA,EAAA0C,GAAA,CAAA/B,IAAA,GAAAiC,MAAA,UAYC;;;qBDAWtD,YAAY,EAAEC,gBAAgB,EAAEC,oBAAoB;MAAAqD,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}