{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CommonModule } from '@angular/common';\n// project import\nimport { NavBarComponent } from './nav-bar/nav-bar.component';\nimport { NavigationComponent } from './navigation/navigation.component';\nimport { ConfigurationComponent } from 'src/app/theme/layout/admin/configuration/configuration.component';\nimport { BreadcrumbsComponent } from '../../shared/components/breadcrumbs/breadcrumbs.component';\nimport { Footer } from './footer/footer';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nconst _c0 = (a0, a1) => ({\n  \"navbar-collapsed\": a0,\n  \"mob-open\": a1\n});\nconst _c1 = a0 => ({\n  \"navbar-collapsed\": a0\n});\nexport class AdminComponent {\n  // constructor\n  constructor() {\n    this.windowWidth = window.innerWidth;\n    this.navCollapsedMob = false;\n  }\n  // public method\n  navMobClick() {\n    if (this.navCollapsedMob && !document.querySelector('app-navigation.pcoded-navbar').classList.contains('mob-open')) {\n      this.navCollapsedMob = !this.navCollapsedMob;\n      setTimeout(() => {\n        this.navCollapsedMob = !this.navCollapsedMob;\n      }, 100);\n    } else {\n      this.navCollapsedMob = !this.navCollapsedMob;\n    }\n  }\n  // this is for eslint rule\n  handleKeyDown(event) {\n    if (event.key === 'Escape') {\n      this.closeMenu();\n    }\n  }\n  closeMenu() {\n    if (document.querySelector('app-navigation.pcoded-navbar').classList.contains('mob-open')) {\n      document.querySelector('app-navigation.pcoded-navbar').classList.remove('mob-open');\n    }\n  }\n  static {\n    this.ɵfac = function AdminComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminComponent,\n      selectors: [[\"app-admin\"]],\n      decls: 13,\n      vars: 7,\n      consts: [[1, \"pcoded-navbar\", 3, \"NavCollapse\", \"NavCollapsedMob\", \"ngClass\"], [1, \"navbar\", \"pcoded-header\", \"navbar-expand-lg\", \"navbar-light\", 3, \"NavCollapsedMob\"], [1, \"pcoded-main-container\"], [1, \"pcoded-wrapper\"], [1, \"pcoded-content\"], [1, \"pcoded-inner-content\"], [1, \"main-body\"], [1, \"page-wrapper\"], [\"tabindex\", \"0\", 1, \"pc-menu-overlay\", 3, \"click\", \"keydown\"], [3, \"ngClass\"]],\n      template: function AdminComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"app-navigation\", 0);\n          i0.ɵɵlistener(\"NavCollapse\", function AdminComponent_Template_app_navigation_NavCollapse_0_listener() {\n            return ctx.navCollapsed = !ctx.navCollapsed;\n          })(\"NavCollapsedMob\", function AdminComponent_Template_app_navigation_NavCollapsedMob_0_listener() {\n            return ctx.navMobClick();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1, \"app-nav-bar\", 1);\n          i0.ɵɵlistener(\"NavCollapsedMob\", function AdminComponent_Template_app_nav_bar_NavCollapsedMob_1_listener() {\n            return ctx.navMobClick();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"app-breadcrumb\");\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7);\n          i0.ɵɵelement(9, \"router-outlet\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(10, \"div\", 8);\n          i0.ɵɵlistener(\"click\", function AdminComponent_Template_div_click_10_listener() {\n            return ctx.closeMenu();\n          })(\"keydown\", function AdminComponent_Template_div_keydown_10_listener($event) {\n            return ctx.handleKeyDown($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(11, \"app-footer\", 9)(12, \"app-configuration\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c0, ctx.navCollapsed, ctx.navCollapsedMob));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, ctx.navCollapsed));\n        }\n      },\n      dependencies: [NavBarComponent, NavigationComponent, RouterModule, i1.RouterOutlet, CommonModule, i2.NgClass, ConfigurationComponent, BreadcrumbsComponent, Footer],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterModule", "CommonModule", "NavBarComponent", "NavigationComponent", "ConfigurationComponent", "BreadcrumbsComponent", "Footer", "AdminComponent", "constructor", "windowWidth", "window", "innerWidth", "navCollapsedMob", "navMobClick", "document", "querySelector", "classList", "contains", "setTimeout", "handleKeyDown", "event", "key", "closeMenu", "remove", "selectors", "decls", "vars", "consts", "template", "AdminComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵlistener", "AdminComponent_Template_app_navigation_NavCollapse_0_listener", "navCollapsed", "AdminComponent_Template_app_navigation_NavCollapsedMob_0_listener", "ɵɵelementEnd", "AdminComponent_Template_app_nav_bar_NavCollapsedMob_1_listener", "ɵɵelement", "AdminComponent_Template_div_click_10_listener", "AdminComponent_Template_div_keydown_10_listener", "$event", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "ɵɵadvance", "ɵɵpureFunction1", "_c1", "i1", "RouterOutlet", "i2", "Ng<PERSON><PERSON>", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\admin.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\admin.component.html"], "sourcesContent": ["// angular import\r\nimport { Component } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n// project import\r\nimport { NavBarComponent } from './nav-bar/nav-bar.component';\r\nimport { NavigationComponent } from './navigation/navigation.component';\r\nimport { ConfigurationComponent } from 'src/app/theme/layout/admin/configuration/configuration.component';\r\nimport { BreadcrumbsComponent } from '../../shared/components/breadcrumbs/breadcrumbs.component';\r\nimport { Footer } from './footer/footer';\r\n\r\n@Component({\r\n  selector: 'app-admin',\r\n  imports: [NavBarComponent, NavigationComponent, RouterModule, CommonModule, ConfigurationComponent, BreadcrumbsComponent, Footer],\r\n  templateUrl: './admin.component.html',\r\n  styleUrls: ['./admin.component.scss']\r\n})\r\nexport class AdminComponent {\r\n  // public props\r\n  navCollapsed;\r\n  navCollapsedMob: boolean;\r\n  windowWidth: number;\r\n\r\n  // constructor\r\n  constructor() {\r\n    this.windowWidth = window.innerWidth;\r\n    this.navCollapsedMob = false;\r\n  }\r\n\r\n  // public method\r\n  navMobClick() {\r\n    if (this.navCollapsedMob && !document.querySelector('app-navigation.pcoded-navbar').classList.contains('mob-open')) {\r\n      this.navCollapsedMob = !this.navCollapsedMob;\r\n      setTimeout(() => {\r\n        this.navCollapsedMob = !this.navCollapsedMob;\r\n      }, 100);\r\n    } else {\r\n      this.navCollapsedMob = !this.navCollapsedMob;\r\n    }\r\n  }\r\n\r\n  // this is for eslint rule\r\n  handleKeyDown(event: KeyboardEvent): void {\r\n    if (event.key === 'Escape') {\r\n      this.closeMenu();\r\n    }\r\n  }\r\n\r\n  closeMenu() {\r\n    if (document.querySelector('app-navigation.pcoded-navbar').classList.contains('mob-open')) {\r\n      document.querySelector('app-navigation.pcoded-navbar').classList.remove('mob-open');\r\n    }\r\n  }\r\n}\r\n", "<app-navigation\r\n  class=\"pcoded-navbar\"\r\n  [ngClass]=\"{\r\n    'navbar-collapsed': navCollapsed,\r\n    'mob-open': navCollapsedMob\r\n  }\"\r\n  (NavCollapse)=\"this.navCollapsed = !this.navCollapsed\"\r\n  (NavCollapsedMob)=\"navMobClick()\"\r\n/>\r\n<app-nav-bar class=\"navbar pcoded-header navbar-expand-lg navbar-light\" (NavCollapsedMob)=\"navMobClick()\" />\r\n<div class=\"pcoded-main-container\">\r\n  <div class=\"pcoded-wrapper\">\r\n    <div class=\"pcoded-content\">\r\n      <div class=\"pcoded-inner-content\">\r\n        <app-breadcrumb />\r\n        <div class=\"main-body\">\r\n          <div class=\"page-wrapper\">\r\n            <router-outlet />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"pc-menu-overlay\" (click)=\"closeMenu()\" (keydown)=\"handleKeyDown($event)\" tabindex=\"0\"></div>\r\n</div>\r\n<app-footer [ngClass]=\"{ 'navbar-collapsed': navCollapsed }\" />\r\n<app-configuration />\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,sBAAsB,QAAQ,kEAAkE;AACzG,SAASC,oBAAoB,QAAQ,2DAA2D;AAChG,SAASC,MAAM,QAAQ,iBAAiB;;;;;;;;;;;AAQxC,OAAM,MAAOC,cAAc;EAMzB;EACAC,YAAA;IACE,IAAI,CAACC,WAAW,GAAGC,MAAM,CAACC,UAAU;IACpC,IAAI,CAACC,eAAe,GAAG,KAAK;EAC9B;EAEA;EACAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACD,eAAe,IAAI,CAACE,QAAQ,CAACC,aAAa,CAAC,8BAA8B,CAAC,CAACC,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MAClH,IAAI,CAACL,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;MAC5CM,UAAU,CAAC,MAAK;QACd,IAAI,CAACN,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;MAC9C,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACL,IAAI,CAACA,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC9C;EACF;EAEA;EACAO,aAAaA,CAACC,KAAoB;IAChC,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC1B,IAAI,CAACC,SAAS,EAAE;IAClB;EACF;EAEAA,SAASA,CAAA;IACP,IAAIR,QAAQ,CAACC,aAAa,CAAC,8BAA8B,CAAC,CAACC,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACzFH,QAAQ,CAACC,aAAa,CAAC,8BAA8B,CAAC,CAACC,SAAS,CAACO,MAAM,CAAC,UAAU,CAAC;IACrF;EACF;;;uCAnCWhB,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAiB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClB3BE,EAAA,CAAAC,cAAA,wBAQE;UADAD,EADA,CAAAE,UAAA,yBAAAC,8DAAA;YAAA,OAAAJ,GAAA,CAAAK,YAAA,IAAAL,GAAA,CAAAK,YAAA;UAAA,EAAsD,6BAAAC,kEAAA;YAAA,OACnCN,GAAA,CAAAlB,WAAA,EAAa;UAAA,EAAC;UAPnCmB,EAAA,CAAAM,YAAA,EAQE;UACFN,EAAA,CAAAC,cAAA,qBAA4G;UAApCD,EAAA,CAAAE,UAAA,6BAAAK,+DAAA;YAAA,OAAmBR,GAAA,CAAAlB,WAAA,EAAa;UAAA,EAAC;UAAzGmB,EAAA,CAAAM,YAAA,EAA4G;UAItGN,EAHN,CAAAC,cAAA,aAAmC,aACL,aACE,aACQ;UAChCD,EAAA,CAAAQ,SAAA,qBAAkB;UAEhBR,EADF,CAAAC,cAAA,aAAuB,aACK;UACxBD,EAAA,CAAAQ,SAAA,oBAAiB;UAK3BR,EAJQ,CAAAM,YAAA,EAAM,EACF,EACF,EACF,EACF;UACNN,EAAA,CAAAC,cAAA,cAAkG;UAA/CD,EAAtB,CAAAE,UAAA,mBAAAO,8CAAA;YAAA,OAASV,GAAA,CAAAT,SAAA,EAAW;UAAA,EAAC,qBAAAoB,gDAAAC,MAAA;YAAA,OAAYZ,GAAA,CAAAZ,aAAA,CAAAwB,MAAA,CAAqB;UAAA,EAAC;UACtFX,EADoG,CAAAM,YAAA,EAAM,EACpG;UAENN,EADA,CAAAQ,SAAA,qBAA+D,yBAC1C;;;UAxBnBR,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAa,eAAA,IAAAC,GAAA,EAAAf,GAAA,CAAAK,YAAA,EAAAL,GAAA,CAAAnB,eAAA,EAGE;UAoBQoB,EAAA,CAAAe,SAAA,IAAgD;UAAhDf,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAlB,GAAA,CAAAK,YAAA,EAAgD;;;qBDXhDlC,eAAe,EAAEC,mBAAmB,EAAEH,YAAY,EAAAkD,EAAA,CAAAC,YAAA,EAAElD,YAAY,EAAAmD,EAAA,CAAAC,OAAA,EAAEjD,sBAAsB,EAAEC,oBAAoB,EAAEC,MAAM;MAAAgD,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}