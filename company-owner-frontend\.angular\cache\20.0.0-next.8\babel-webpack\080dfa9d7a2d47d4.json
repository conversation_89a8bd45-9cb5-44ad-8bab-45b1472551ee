{"ast": null, "code": "import { SharedModule } from 'src/app/theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../theme/shared/components/card/card.component\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nexport default class BasicButtonComponent {\n  static {\n    this.ɵfac = function BasicButtonComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BasicButtonComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BasicButtonComponent,\n      selectors: [[\"app-basic-button\"]],\n      decls: 1076,\n      vars: 78,\n      consts: [[1, \"row\"], [1, \"col-sm-12\"], [\"cardTitle\", \"Default\", 3, \"options\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\"], [\"type\", \"button\", 1, \"btn\", \"btn-warning\"], [\"type\", \"button\", 1, \"btn\", \"btn-info\"], [\"type\", \"button\", 1, \"btn\", \"btn-dark\"], [\"type\", \"button\", 1, \"btn\", \"btn-link\"], [1, \"col-md-12\"], [\"cardTitle\", \"Outline\", 3, \"options\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-warning\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-info\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-dark\"], [\"cardTitle\", \"Square Button\", 3, \"options\"], [\"type\", \"button\", 1, \"btn\", \"btn-square\", \"btn-primary\"], [\"type\", \"button\", 1, \"btn\", \"btn-square\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-square\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-square\", \"btn-danger\"], [\"type\", \"button\", 1, \"btn\", \"btn-square\", \"btn-warning\"], [\"type\", \"button\", 1, \"btn\", \"btn-square\", \"btn-info\"], [\"type\", \"button\", 1, \"btn\", \"btn-square\", \"btn-dark\"], [\"cardTitle\", \"Disabled Button\", 3, \"options\"], [\"type\", \"button\", 1, \"btn\", \"disabled\", \"btn-primary\"], [\"type\", \"button\", 1, \"btn\", \"disabled\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"disabled\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"disabled\", \"btn-danger\"], [\"type\", \"button\", 1, \"btn\", \"disabled\", \"btn-warning\"], [\"type\", \"button\", 1, \"btn\", \"disabled\", \"btn-info\"], [\"type\", \"button\", 1, \"btn\", \"disabled\", \"btn-dark\"], [\"cardTitle\", \"Rounded Button\", 3, \"options\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-primary\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-danger\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-warning\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-info\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-dark\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-outline-primary\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-outline-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-outline-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-outline-danger\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-outline-warning\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-outline-info\"], [\"type\", \"button\", 1, \"btn\", \"btn-rounded\", \"btn-outline-dark\"], [\"cardTitle\", \"Glow Button\", 3, \"options\"], [\"type\", \"button\", \"title\", \"btn btn-glow-primary btn-primary\", \"data-toggle\", \"tooltip\", 1, \"btn\", \"btn-glow-primary\", \"btn-primary\"], [\"type\", \"button\", \"title\", \"btn btn-glow-secondary btn-secondary\", \"data-toggle\", \"tooltip\", 1, \"btn\", \"btn-glow-secondary\", \"btn-secondary\"], [\"type\", \"button\", \"title\", \"btn btn-glow-success btn-success\", \"data-toggle\", \"tooltip\", 1, \"btn\", \"btn-glow-success\", \"btn-success\"], [\"type\", \"button\", \"title\", \"btn btn-glow-danger btn-danger\", \"data-toggle\", \"tooltip\", 1, \"btn\", \"btn-glow-danger\", \"btn-danger\"], [\"type\", \"button\", \"title\", \"btn btn-glow-warning btn-warning\", \"data-toggle\", \"tooltip\", 1, \"btn\", \"btn-glow-warning\", \"btn-warning\"], [\"type\", \"button\", \"title\", \"btn btn-glow-info btn-info\", \"data-toggle\", \"tooltip\", 1, \"btn\", \"btn-glow-info\", \"btn-info\"], [\"type\", \"button\", \"title\", \"btn btn-glow-dark btn-dark\", \"data-toggle\", \"tooltip\", 1, \"btn\", \"btn-glow-dark\", \"btn-dark\"], [\"cardTitle\", \"Shadow Button\", 3, \"options\"], [\"type\", \"button\", 1, \"btn\", \"shadow-1\", \"btn-primary\"], [\"type\", \"button\", 1, \"btn\", \"shadow-2\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"shadow-3\", \"btn-danger\"], [\"type\", \"button\", 1, \"btn\", \"shadow-4\", \"btn-warning\"], [\"type\", \"button\", 1, \"btn\", \"shadow-5\", \"btn-info\"], [1, \"col-md-6\"], [\"cardTitle\", \"Sizes [ Large ]\", 3, \"options\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-lg\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-lg\"], [\"cardTitle\", \"Sizes [ small ]\", 3, \"options\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\"], [\"cardTitle\", \"Checkbox button groups\", 3, \"options\"], [\"role\", \"group\", 1, \"btn-group\"], [\"type\", \"checkbox\", \"id\", \"btncheck1\", \"autocomplete\", \"off\", 1, \"btn-check\"], [\"for\", \"btncheck1\", 1, \"btn\", \"btn-outline-secondary\"], [\"type\", \"checkbox\", \"id\", \"btncheck2\", \"autocomplete\", \"off\", 1, \"btn-check\"], [\"for\", \"btncheck2\", 1, \"btn\", \"btn-outline-secondary\"], [\"type\", \"checkbox\", \"id\", \"btncheck3\", \"autocomplete\", \"off\", 1, \"btn-check\"], [\"for\", \"btncheck3\", 1, \"btn\", \"btn-outline-secondary\"], [\"type\", \"checkbox\", \"id\", \"btnchecklite1\", \"autocomplete\", \"off\", \"checked\", \"\", 1, \"btn-check\"], [\"for\", \"btnchecklite1\", 1, \"btn\", \"btn-light-secondary\"], [\"type\", \"checkbox\", \"id\", \"btnchecklite2\", \"autocomplete\", \"off\", \"checked\", \"\", 1, \"btn-check\"], [\"for\", \"btnchecklite2\", 1, \"btn\", \"btn-light-secondary\"], [\"type\", \"checkbox\", \"id\", \"btnchecklite3\", \"autocomplete\", \"off\", \"checked\", \"\", 1, \"btn-check\"], [\"for\", \"btnchecklite3\", 1, \"btn\", \"btn-light-secondary\"], [\"type\", \"checkbox\", \"id\", \"btnchecklitecol1\", \"autocomplete\", \"off\", \"checked\", \"\", 1, \"btn-check\"], [\"for\", \"btnchecklitecol1\", 1, \"btn\", \"btn-light-primary\"], [\"type\", \"checkbox\", \"id\", \"btnchecklitecol2\", \"autocomplete\", \"off\", \"checked\", \"\", 1, \"btn-check\"], [\"for\", \"btnchecklitecol2\", 1, \"btn\", \"btn-light-success\"], [\"type\", \"checkbox\", \"id\", \"btnchecklitecol3\", \"autocomplete\", \"off\", \"checked\", \"\", 1, \"btn-check\"], [\"for\", \"btnchecklitecol3\", 1, \"btn\", \"btn-light-danger\"], [\"cardTitle\", \"Radio button groups\", 3, \"options\"], [\"type\", \"radio\", \"id\", \"btnrdo1\", \"autocomplete\", \"off\", \"name\", \"btnradio1\", 1, \"btn-check\"], [\"for\", \"btnrdo1\", 1, \"btn\", \"btn-outline-secondary\"], [\"type\", \"radio\", \"id\", \"btnrdo2\", \"autocomplete\", \"off\", \"name\", \"btnradio2\", 1, \"btn-check\"], [\"for\", \"btnrdo2\", 1, \"btn\", \"btn-outline-secondary\"], [\"type\", \"radio\", \"id\", \"btnrdo3\", \"autocomplete\", \"off\", \"name\", \"btnradio32\", 1, \"btn-check\"], [\"for\", \"btnrdo3\", 1, \"btn\", \"btn-outline-secondary\"], [\"type\", \"radio\", \"id\", \"btnrdolite1\", \"autocomplete\", \"off\", \"name\", \"btnradio1\", \"checked\", \"\", 1, \"btn-check\"], [\"for\", \"btnrdolite1\", 1, \"btn\", \"btn-light-secondary\"], [\"type\", \"radio\", \"id\", \"btnrdolite2\", \"autocomplete\", \"off\", \"name\", \"btnradio2\", \"checked\", \"\", 1, \"btn-check\"], [\"for\", \"btnrdolite2\", 1, \"btn\", \"btn-light-secondary\"], [\"type\", \"radio\", \"id\", \"btnrdolite3\", \"autocomplete\", \"off\", \"name\", \"btnradio23\", \"checked\", \"\", 1, \"btn-check\"], [\"for\", \"btnrdolite3\", 1, \"btn\", \"btn-light-secondary\"], [\"type\", \"radio\", \"id\", \"btnrdolitecol1\", \"autocomplete\", \"off\", \"name\", \"btnradio31\", \"checked\", \"\", 1, \"btn-check\"], [\"for\", \"btnrdolitecol1\", 1, \"btn\", \"btn-light-primary\"], [\"type\", \"radio\", \"id\", \"btnrdolitecol2\", \"autocomplete\", \"off\", \"name\", \"btnradio32\", \"checked\", \"\", 1, \"btn-check\"], [\"for\", \"btnrdolitecol2\", 1, \"btn\", \"btn-light-success\"], [\"type\", \"radio\", \"id\", \"btnrdolitecol3\", \"autocomplete\", \"off\", \"name\", \"btnradio33\", \"checked\", \"\", 1, \"btn-check\"], [\"for\", \"btnrdolitecol3\", 1, \"btn\", \"btn-light-danger\"], [\"cardTitle\", \"Buttons With Icon\", 3, \"options\"], [1, \"feather\", \"icon-thumbs-up\"], [1, \"feather\", \"icon-camera\"], [1, \"feather\", \"icon-check-circle\"], [1, \"feather\", \"icon-slash\"], [1, \"feather\", \"icon-alert-triangle\"], [1, \"feather\", \"icon-info\"], [\"cardTitle\", \"Outline Icon Buttons\", 3, \"options\"], [\"cardTitle\", \"Only Icon\", 3, \"options\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-primary\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-danger\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-warning\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-info\"], [\"cardTitle\", \"Outline Icon\", 3, \"options\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-outline-primary\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-outline-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-outline-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-outline-danger\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-outline-warning\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-outline-info\"], [\"cardTitle\", \"Icon Button Rounded\", 3, \"options\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-rounded\", \"btn-primary\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-rounded\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-rounded\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-rounded\", \"btn-danger\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-rounded\", \"btn-warning\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-rounded\", \"btn-info\"], [\"cardTitle\", \"Icon Outline Button Rounded\", 3, \"options\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-rounded\", \"btn-outline-primary\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-rounded\", \"btn-outline-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-rounded\", \"btn-outline-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-rounded\", \"btn-outline-danger\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-rounded\", \"btn-outline-warning\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-rounded\", \"btn-outline-info\"], [\"cardTitle\", \"Basic Dropdown Button\", 3, \"options\"], [\"ngbDropdown\", \"\", 1, \"btn-group\", \"mb-2\", \"me-2\", 3, \"placement\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-primary\"], [\"ngbDropdownMenu\", \"\"], [\"href\", \"javascript:\", 1, \"dropdown-item\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-secondary\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-success\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-danger\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-warning\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-info\"], [\"cardTitle\", \"Split Dropdown Button\", 3, \"options\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-primary\", \"dropdown-toggle-split\"], [1, \"sr-only\"], [1, \"dropdown-divider\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-secondary\", \"dropdown-toggle-split\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-success\", \"dropdown-toggle-split\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-danger\", \"dropdown-toggle-split\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-warning\", \"dropdown-toggle-split\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-info\", \"dropdown-toggle-split\"], [\"cardTitle\", \"Basic Outline Dropdown Button\", 3, \"options\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-outline-primary\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-outline-success\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-outline-danger\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-outline-warning\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-outline-info\"], [\"cardTitle\", \"Split Outline Dropdown Button\", 3, \"options\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-outline-primary\", \"dropdown-toggle-split\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle-split\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-outline-success\", \"dropdown-toggle-split\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-outline-danger\", \"dropdown-toggle-split\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-outline-warning\", \"dropdown-toggle-split\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-outline-info\", \"dropdown-toggle-split\"], [\"cardTitle\", \"Basic Icon Dropdown\", 3, \"options\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-primary\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-secondary\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-success\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-danger\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-warning\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-info\"], [\"cardTitle\", \"Outline Icon Dropdown\", 3, \"options\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-outline-primary\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-outline-secondary\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-outline-success\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-outline-danger\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-outline-warning\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-outline-info\"], [\"cardTitle\", \"Basic Rounded Icon Dropdown\", 3, \"options\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-rounded\", \"btn-primary\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-rounded\", \"btn-secondary\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-rounded\", \"btn-success\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-rounded\", \"btn-danger\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-rounded\", \"btn-warning\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-rounded\", \"btn-info\"], [\"cardTitle\", \"Outline Rounded Icon Dropdown\", 3, \"options\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-rounded\", \"btn-outline-primary\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-rounded\", \"btn-outline-secondary\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-rounded\", \"btn-outline-success\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-rounded\", \"btn-outline-danger\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-rounded\", \"btn-outline-warning\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"drp-icon\", \"btn-rounded\", \"btn-outline-info\"], [\"cardTitle\", \"Basic Button Group\", 3, \"options\"], [\"role\", \"group\", \"aria-label\", \"Basic example\", 1, \"btn-group\", \"mb-2\"], [\"cardTitle\", \"Button Toolbar\", 3, \"options\"], [1, \"btn-toolbar\"], [1, \"btn-group\", \"me-2\", \"mb-1\"], [1, \"btn-group\", \"mb-1\"], [\"cardTitle\", \"Button Toolbar Size\", 3, \"options\"], [1, \"col-xl-4\", \"col-md-6\", \"mb-2\"], [\"role\", \"group\", \"aria-label\", \"button groups\", 1, \"btn-group\"], [1, \"col-xl-4\", \"col-md-12\", \"mb-2\"], [\"role\", \"group\", \"aria-label\", \"button groups xl\", 1, \"btn-group\", \"btn-group-lg\"], [\"role\", \"group\", \"aria-label\", \"button groups sm\", 1, \"btn-group\", \"btn-group-sm\"], [\"cardTitle\", \"Nesting\", 3, \"options\"], [\"role\", \"group\", \"ngbDropdown\", \"\", 1, \"btn-group\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-secondary\"], [\"cardTitle\", \"Vertical Variation\", 3, \"options\"], [1, \"col-4\"], [\"role\", \"group\", \"aria-label\", \"Button group with nested dropdown\", 1, \"btn-group-vertical\"], [\"type\", \"button\", 1, \"btn\", \"m-0\", \"btn-secondary\"], [1, \"col-8\"], [\"ngbDropdown\", \"\"], [\"type\", \"button\", \"data-bs-toggle\", \"dropdown\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"m-0\", \"btn-secondary\", \"dropdown-toggle\"], [\"ngbDropdownMenu\", \"\", 1, \"dropdown-menu\"]],\n      template: function BasicButtonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"app-card\", 2)(3, \"p\");\n          i0.ɵɵtext(4, \" use class \");\n          i0.ɵɵelementStart(5, \"code\");\n          i0.ɵɵtext(6, \".btn-*\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \" in class \");\n          i0.ɵɵelementStart(8, \"code\");\n          i0.ɵɵtext(9, \".btn\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10, \" class to get various button \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 3);\n          i0.ɵɵtext(12, \"Primary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 4);\n          i0.ɵɵtext(14, \"Secondary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"button\", 5);\n          i0.ɵɵtext(16, \"Success\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 6);\n          i0.ɵɵtext(18, \"Danger\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 7);\n          i0.ɵɵtext(20, \"Warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 8);\n          i0.ɵɵtext(22, \"Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 9);\n          i0.ɵɵtext(24, \"Dark\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 10);\n          i0.ɵɵtext(26, \"Link\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 11)(28, \"app-card\", 12)(29, \"p\");\n          i0.ɵɵtext(30, \" use class \");\n          i0.ɵɵelementStart(31, \"code\");\n          i0.ɵɵtext(32, \".btn-outline-*\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(33, \" in class \");\n          i0.ɵɵelementStart(34, \"code\");\n          i0.ɵɵtext(35, \".btn\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(36, \" class to get various outline button \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"button\", 13);\n          i0.ɵɵtext(38, \"Primary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"button\", 14);\n          i0.ɵɵtext(40, \"Secondary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"button\", 15);\n          i0.ɵɵtext(42, \"Success\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"button\", 16);\n          i0.ɵɵtext(44, \"Danger\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"button\", 17);\n          i0.ɵɵtext(46, \"Warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"button\", 18);\n          i0.ɵɵtext(48, \"Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"button\", 19);\n          i0.ɵɵtext(50, \"Dark\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(51, \"div\", 11)(52, \"app-card\", 20)(53, \"p\");\n          i0.ɵɵtext(54, \" use \");\n          i0.ɵɵelementStart(55, \"code\");\n          i0.ɵɵtext(56, \".btn-square\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(57, \" in class \");\n          i0.ɵɵelementStart(58, \"code\");\n          i0.ɵɵtext(59, \".btn\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \" class to get square button \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"button\", 21);\n          i0.ɵɵtext(62, \"Primary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"button\", 22);\n          i0.ɵɵtext(64, \"Secondary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"button\", 23);\n          i0.ɵɵtext(66, \"Success\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"button\", 24);\n          i0.ɵɵtext(68, \"Danger\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"button\", 25);\n          i0.ɵɵtext(70, \"Warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"button\", 26);\n          i0.ɵɵtext(72, \"Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"button\", 27);\n          i0.ɵɵtext(74, \"Dark\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(75, \"div\", 11)(76, \"app-card\", 28)(77, \"p\");\n          i0.ɵɵtext(78, \" use \");\n          i0.ɵɵelementStart(79, \"code\");\n          i0.ɵɵtext(80, \".disabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(81, \" in class \");\n          i0.ɵɵelementStart(82, \"code\");\n          i0.ɵɵtext(83, \".btn\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(84, \" class to get Disabled button \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"button\", 29);\n          i0.ɵɵtext(86, \"Primary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"button\", 30);\n          i0.ɵɵtext(88, \"Secondary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"button\", 31);\n          i0.ɵɵtext(90, \"Success\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"button\", 32);\n          i0.ɵɵtext(92, \"Danger\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"button\", 33);\n          i0.ɵɵtext(94, \"Warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"button\", 34);\n          i0.ɵɵtext(96, \"Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"button\", 35);\n          i0.ɵɵtext(98, \"Dark\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(99, \"div\", 11)(100, \"app-card\", 36)(101, \"p\");\n          i0.ɵɵtext(102, \" use \");\n          i0.ɵɵelementStart(103, \"code\");\n          i0.ɵɵtext(104, \".btn-rounded\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(105, \" in class \");\n          i0.ɵɵelementStart(106, \"code\");\n          i0.ɵɵtext(107, \".btn\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(108, \" class to get Rounded button \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"button\", 37);\n          i0.ɵɵtext(110, \"Primary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"button\", 38);\n          i0.ɵɵtext(112, \"Secondary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"button\", 39);\n          i0.ɵɵtext(114, \"Success\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"button\", 40);\n          i0.ɵɵtext(116, \"Danger\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"button\", 41);\n          i0.ɵɵtext(118, \"Warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(119, \"button\", 42);\n          i0.ɵɵtext(120, \"Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(121, \"button\", 43);\n          i0.ɵɵtext(122, \"Dark\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(123, \"hr\");\n          i0.ɵɵelementStart(124, \"p\");\n          i0.ɵɵtext(125, \" use \");\n          i0.ɵɵelementStart(126, \"code\");\n          i0.ɵɵtext(127, \".btn-rounded\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(128, \" in class \");\n          i0.ɵɵelementStart(129, \"code\");\n          i0.ɵɵtext(130, \".btn-outline-*\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(131, \" class to get Rounded Outline button \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(132, \"button\", 44);\n          i0.ɵɵtext(133, \"Primary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(134, \"button\", 45);\n          i0.ɵɵtext(135, \"Secondary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(136, \"button\", 46);\n          i0.ɵɵtext(137, \"Success\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(138, \"button\", 47);\n          i0.ɵɵtext(139, \"Danger\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(140, \"button\", 48);\n          i0.ɵɵtext(141, \"Warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(142, \"button\", 49);\n          i0.ɵɵtext(143, \"Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(144, \"button\", 50);\n          i0.ɵɵtext(145, \"Dark\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(146, \"div\", 11)(147, \"app-card\", 51)(148, \"p\");\n          i0.ɵɵtext(149, \" use \");\n          i0.ɵɵelementStart(150, \"code\");\n          i0.ɵɵtext(151, \".btn-glow\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(152, \" in class \");\n          i0.ɵɵelementStart(153, \"code\");\n          i0.ɵɵtext(154, \".btn\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(155, \" class to get Glow button \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(156, \"button\", 52);\n          i0.ɵɵtext(157, \" Primary \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(158, \"button\", 53);\n          i0.ɵɵtext(159, \" Secondary \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(160, \"button\", 54);\n          i0.ɵɵtext(161, \" Success \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(162, \"button\", 55);\n          i0.ɵɵtext(163, \" Danger \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(164, \"button\", 56);\n          i0.ɵɵtext(165, \" Warning \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(166, \"button\", 57);\n          i0.ɵɵtext(167, \"Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(168, \"button\", 58);\n          i0.ɵɵtext(169, \"Dark\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(170, \"div\", 11)(171, \"app-card\", 59)(172, \"p\");\n          i0.ɵɵtext(173, \" use \");\n          i0.ɵɵelementStart(174, \"code\");\n          i0.ɵɵtext(175, \".shadow-[1 / 2 / 3 / 4 / 5]\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(176, \" in class \");\n          i0.ɵɵelementStart(177, \"code\");\n          i0.ɵɵtext(178, \".btn\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(179, \" class to get Shadow button \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(180, \"button\", 60);\n          i0.ɵɵtext(181, \".shadow-1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(182, \"button\", 61);\n          i0.ɵɵtext(183, \".shadow-2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(184, \"button\", 62);\n          i0.ɵɵtext(185, \".shadow-3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(186, \"button\", 63);\n          i0.ɵɵtext(187, \".shadow-4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(188, \"button\", 64);\n          i0.ɵɵtext(189, \".shadow-5\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(190, \"div\", 65)(191, \"app-card\", 66)(192, \"p\");\n          i0.ɵɵtext(193, \" use \");\n          i0.ɵɵelementStart(194, \"code\");\n          i0.ɵɵtext(195, \".btn-lg\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(196, \" in class \");\n          i0.ɵɵelementStart(197, \"code\");\n          i0.ɵɵtext(198, \".btn\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(199, \" class to get Large button \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(200, \"button\", 67);\n          i0.ɵɵtext(201, \"Large button\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(202, \"button\", 68);\n          i0.ɵɵtext(203, \"Large button\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(204, \"div\", 65)(205, \"app-card\", 69)(206, \"p\");\n          i0.ɵɵtext(207, \" use \");\n          i0.ɵɵelementStart(208, \"code\");\n          i0.ɵɵtext(209, \".btn-sm\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(210, \" in class \");\n          i0.ɵɵelementStart(211, \"code\");\n          i0.ɵɵtext(212, \".btn\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(213, \" class to get Small button \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(214, \"button\", 70);\n          i0.ɵɵtext(215, \"Small button\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(216, \"button\", 71);\n          i0.ɵɵtext(217, \"Small button\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(218, \"div\", 65)(219, \"app-card\", 72)(220, \"div\", 73);\n          i0.ɵɵelement(221, \"input\", 74);\n          i0.ɵɵelementStart(222, \"label\", 75);\n          i0.ɵɵtext(223, \"Checkbox 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(224, \"input\", 76);\n          i0.ɵɵelementStart(225, \"label\", 77);\n          i0.ɵɵtext(226, \"Checkbox 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(227, \"input\", 78);\n          i0.ɵɵelementStart(228, \"label\", 79);\n          i0.ɵɵtext(229, \"Checkbox 3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(230, \"hr\");\n          i0.ɵɵelementStart(231, \"div\", 73);\n          i0.ɵɵelement(232, \"input\", 80);\n          i0.ɵɵelementStart(233, \"label\", 81);\n          i0.ɵɵtext(234, \"Checkbox 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(235, \"input\", 82);\n          i0.ɵɵelementStart(236, \"label\", 83);\n          i0.ɵɵtext(237, \"Checkbox 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(238, \"input\", 84);\n          i0.ɵɵelementStart(239, \"label\", 85);\n          i0.ɵɵtext(240, \"Checkbox 3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(241, \"hr\");\n          i0.ɵɵelementStart(242, \"div\", 73);\n          i0.ɵɵelement(243, \"input\", 86);\n          i0.ɵɵelementStart(244, \"label\", 87);\n          i0.ɵɵtext(245, \"Checkbox 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(246, \"input\", 88);\n          i0.ɵɵelementStart(247, \"label\", 89);\n          i0.ɵɵtext(248, \"Checkbox 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(249, \"input\", 90);\n          i0.ɵɵelementStart(250, \"label\", 91);\n          i0.ɵɵtext(251, \"Checkbox 3\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(252, \"div\", 65)(253, \"app-card\", 92)(254, \"div\", 73);\n          i0.ɵɵelement(255, \"input\", 93);\n          i0.ɵɵelementStart(256, \"label\", 94);\n          i0.ɵɵtext(257, \"Radio 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(258, \"input\", 95);\n          i0.ɵɵelementStart(259, \"label\", 96);\n          i0.ɵɵtext(260, \"Radio 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(261, \"input\", 97);\n          i0.ɵɵelementStart(262, \"label\", 98);\n          i0.ɵɵtext(263, \"Radio 3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(264, \"hr\");\n          i0.ɵɵelementStart(265, \"div\", 73);\n          i0.ɵɵelement(266, \"input\", 99);\n          i0.ɵɵelementStart(267, \"label\", 100);\n          i0.ɵɵtext(268, \"Radio 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(269, \"input\", 101);\n          i0.ɵɵelementStart(270, \"label\", 102);\n          i0.ɵɵtext(271, \"Radio 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(272, \"input\", 103);\n          i0.ɵɵelementStart(273, \"label\", 104);\n          i0.ɵɵtext(274, \"Radio 3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(275, \"hr\");\n          i0.ɵɵelementStart(276, \"div\", 73);\n          i0.ɵɵelement(277, \"input\", 105);\n          i0.ɵɵelementStart(278, \"label\", 106);\n          i0.ɵɵtext(279, \"Radio 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(280, \"input\", 107);\n          i0.ɵɵelementStart(281, \"label\", 108);\n          i0.ɵɵtext(282, \"Radio 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(283, \"input\", 109);\n          i0.ɵɵelementStart(284, \"label\", 110);\n          i0.ɵɵtext(285, \"Radio 3\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(286, \"div\", 65)(287, \"app-card\", 111)(288, \"button\", 3);\n          i0.ɵɵelement(289, \"i\", 112);\n          i0.ɵɵtext(290, \" Primary \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(291, \"button\", 4);\n          i0.ɵɵelement(292, \"i\", 113);\n          i0.ɵɵtext(293, \" Secondary \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(294, \"button\", 5);\n          i0.ɵɵelement(295, \"i\", 114);\n          i0.ɵɵtext(296, \" Success \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(297, \"button\", 6);\n          i0.ɵɵelement(298, \"i\", 115);\n          i0.ɵɵtext(299, \" Danger \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(300, \"button\", 7);\n          i0.ɵɵelement(301, \"i\", 116);\n          i0.ɵɵtext(302, \" Warning \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(303, \"button\", 8);\n          i0.ɵɵelement(304, \"i\", 117);\n          i0.ɵɵtext(305, \" Info \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(306, \"div\", 65)(307, \"app-card\", 118)(308, \"button\", 13);\n          i0.ɵɵelement(309, \"i\", 112);\n          i0.ɵɵtext(310, \" Primary \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(311, \"button\", 14);\n          i0.ɵɵelement(312, \"i\", 113);\n          i0.ɵɵtext(313, \" Secondary \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(314, \"button\", 15);\n          i0.ɵɵelement(315, \"i\", 114);\n          i0.ɵɵtext(316, \" Success \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(317, \"button\", 16);\n          i0.ɵɵelement(318, \"i\", 115);\n          i0.ɵɵtext(319, \" Danger \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(320, \"button\", 17);\n          i0.ɵɵelement(321, \"i\", 116);\n          i0.ɵɵtext(322, \" Warning \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(323, \"button\", 18);\n          i0.ɵɵelement(324, \"i\", 117);\n          i0.ɵɵtext(325, \" Info \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(326, \"div\", 65)(327, \"app-card\", 119)(328, \"button\", 120);\n          i0.ɵɵelement(329, \"i\", 112);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(330, \"button\", 121);\n          i0.ɵɵelement(331, \"i\", 113);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(332, \"button\", 122);\n          i0.ɵɵelement(333, \"i\", 114);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(334, \"button\", 123);\n          i0.ɵɵelement(335, \"i\", 115);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(336, \"button\", 124);\n          i0.ɵɵelement(337, \"i\", 116);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(338, \"button\", 125);\n          i0.ɵɵelement(339, \"i\", 117);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(340, \"div\", 65)(341, \"app-card\", 126)(342, \"button\", 127);\n          i0.ɵɵelement(343, \"i\", 112);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(344, \"button\", 128);\n          i0.ɵɵelement(345, \"i\", 113);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(346, \"button\", 129);\n          i0.ɵɵelement(347, \"i\", 114);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(348, \"button\", 130);\n          i0.ɵɵelement(349, \"i\", 115);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(350, \"button\", 131);\n          i0.ɵɵelement(351, \"i\", 116);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(352, \"button\", 132);\n          i0.ɵɵelement(353, \"i\", 117);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(354, \"div\", 65)(355, \"app-card\", 133)(356, \"button\", 134);\n          i0.ɵɵelement(357, \"i\", 112);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(358, \"button\", 135);\n          i0.ɵɵelement(359, \"i\", 113);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(360, \"button\", 136);\n          i0.ɵɵelement(361, \"i\", 114);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(362, \"button\", 137);\n          i0.ɵɵelement(363, \"i\", 115);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(364, \"button\", 138);\n          i0.ɵɵelement(365, \"i\", 116);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(366, \"button\", 139);\n          i0.ɵɵelement(367, \"i\", 117);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(368, \"div\", 65)(369, \"app-card\", 140)(370, \"button\", 141);\n          i0.ɵɵelement(371, \"i\", 112);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(372, \"button\", 142);\n          i0.ɵɵelement(373, \"i\", 113);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(374, \"button\", 143);\n          i0.ɵɵelement(375, \"i\", 114);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(376, \"button\", 144);\n          i0.ɵɵelement(377, \"i\", 115);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(378, \"button\", 145);\n          i0.ɵɵelement(379, \"i\", 116);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(380, \"button\", 146);\n          i0.ɵɵelement(381, \"i\", 117);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(382, \"div\", 1)(383, \"app-card\", 147)(384, \"p\");\n          i0.ɵɵtext(385, \" use \");\n          i0.ɵɵelementStart(386, \"code\");\n          i0.ɵɵtext(387, \"ngbDropdown\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(388, \"code\");\n          i0.ɵɵtext(389, \"ngbDropdownToggle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(390, \"code\");\n          i0.ɵɵtext(391, \"ngbDropdownMenu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(392, \" selector in proper way to get dropdown button \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(393, \"div\", 148)(394, \"button\", 149);\n          i0.ɵɵtext(395, \"Primary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(396, \"div\", 150)(397, \"a\", 151);\n          i0.ɵɵtext(398, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(399, \"a\", 151);\n          i0.ɵɵtext(400, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(401, \"a\", 151);\n          i0.ɵɵtext(402, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(403, \"div\", 148)(404, \"button\", 152);\n          i0.ɵɵtext(405, \"Secondary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(406, \"div\", 150)(407, \"a\", 151);\n          i0.ɵɵtext(408, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(409, \"a\", 151);\n          i0.ɵɵtext(410, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(411, \"a\", 151);\n          i0.ɵɵtext(412, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(413, \"div\", 148)(414, \"button\", 153);\n          i0.ɵɵtext(415, \"Success\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(416, \"div\", 150)(417, \"a\", 151);\n          i0.ɵɵtext(418, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(419, \"a\", 151);\n          i0.ɵɵtext(420, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(421, \"a\", 151);\n          i0.ɵɵtext(422, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(423, \"div\", 148)(424, \"button\", 154);\n          i0.ɵɵtext(425, \"Danger\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(426, \"div\", 150)(427, \"a\", 151);\n          i0.ɵɵtext(428, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(429, \"a\", 151);\n          i0.ɵɵtext(430, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(431, \"a\", 151);\n          i0.ɵɵtext(432, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(433, \"div\", 148)(434, \"button\", 155);\n          i0.ɵɵtext(435, \"Warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(436, \"div\", 150)(437, \"a\", 151);\n          i0.ɵɵtext(438, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(439, \"a\", 151);\n          i0.ɵɵtext(440, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(441, \"a\", 151);\n          i0.ɵɵtext(442, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(443, \"div\", 148)(444, \"button\", 156);\n          i0.ɵɵtext(445, \"Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(446, \"div\", 150)(447, \"a\", 151);\n          i0.ɵɵtext(448, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(449, \"a\", 151);\n          i0.ɵɵtext(450, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(451, \"a\", 151);\n          i0.ɵɵtext(452, \"Something else here\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(453, \"div\", 11)(454, \"app-card\", 157)(455, \"div\", 148)(456, \"button\", 3);\n          i0.ɵɵtext(457, \"Primary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(458, \"button\", 158)(459, \"span\", 159);\n          i0.ɵɵtext(460, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(461, \"div\", 150)(462, \"a\", 151);\n          i0.ɵɵtext(463, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(464, \"a\", 151);\n          i0.ɵɵtext(465, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(466, \"a\", 151);\n          i0.ɵɵtext(467, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(468, \"div\", 160);\n          i0.ɵɵelementStart(469, \"a\", 151);\n          i0.ɵɵtext(470, \"Separated link\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(471, \"div\", 148)(472, \"button\", 4);\n          i0.ɵɵtext(473, \"Secondary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(474, \"button\", 161)(475, \"span\", 159);\n          i0.ɵɵtext(476, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(477, \"div\", 150)(478, \"a\", 151);\n          i0.ɵɵtext(479, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(480, \"a\", 151);\n          i0.ɵɵtext(481, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(482, \"a\", 151);\n          i0.ɵɵtext(483, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(484, \"div\", 160);\n          i0.ɵɵelementStart(485, \"a\", 151);\n          i0.ɵɵtext(486, \"Separated link\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(487, \"div\", 148)(488, \"button\", 5);\n          i0.ɵɵtext(489, \"Success\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(490, \"button\", 162)(491, \"span\", 159);\n          i0.ɵɵtext(492, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(493, \"div\", 150)(494, \"a\", 151);\n          i0.ɵɵtext(495, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(496, \"a\", 151);\n          i0.ɵɵtext(497, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(498, \"a\", 151);\n          i0.ɵɵtext(499, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(500, \"div\", 160);\n          i0.ɵɵelementStart(501, \"a\", 151);\n          i0.ɵɵtext(502, \"Separated link\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(503, \"div\", 148)(504, \"button\", 6);\n          i0.ɵɵtext(505, \"Danger\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(506, \"button\", 163)(507, \"span\", 159);\n          i0.ɵɵtext(508, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(509, \"div\", 150)(510, \"a\", 151);\n          i0.ɵɵtext(511, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(512, \"a\", 151);\n          i0.ɵɵtext(513, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(514, \"a\", 151);\n          i0.ɵɵtext(515, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(516, \"div\", 160);\n          i0.ɵɵelementStart(517, \"a\", 151);\n          i0.ɵɵtext(518, \"Separated link\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(519, \"div\", 148)(520, \"button\", 7);\n          i0.ɵɵtext(521, \"Warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(522, \"button\", 164)(523, \"span\", 159);\n          i0.ɵɵtext(524, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(525, \"div\", 150)(526, \"a\", 151);\n          i0.ɵɵtext(527, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(528, \"a\", 151);\n          i0.ɵɵtext(529, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(530, \"a\", 151);\n          i0.ɵɵtext(531, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(532, \"div\", 160);\n          i0.ɵɵelementStart(533, \"a\", 151);\n          i0.ɵɵtext(534, \"Separated link\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(535, \"div\", 148)(536, \"button\", 8);\n          i0.ɵɵtext(537, \"Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(538, \"button\", 165)(539, \"span\", 159);\n          i0.ɵɵtext(540, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(541, \"div\", 150)(542, \"a\", 151);\n          i0.ɵɵtext(543, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(544, \"a\", 151);\n          i0.ɵɵtext(545, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(546, \"a\", 151);\n          i0.ɵɵtext(547, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(548, \"div\", 160);\n          i0.ɵɵelementStart(549, \"a\", 151);\n          i0.ɵɵtext(550, \"Separated link\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(551, \"div\", 11)(552, \"app-card\", 166)(553, \"div\", 148)(554, \"button\", 167);\n          i0.ɵɵtext(555, \"Primary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(556, \"div\", 150)(557, \"a\", 151);\n          i0.ɵɵtext(558, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(559, \"a\", 151);\n          i0.ɵɵtext(560, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(561, \"a\", 151);\n          i0.ɵɵtext(562, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(563, \"div\", 148)(564, \"button\", 168);\n          i0.ɵɵtext(565, \"Secondary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(566, \"div\", 150)(567, \"a\", 151);\n          i0.ɵɵtext(568, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(569, \"a\", 151);\n          i0.ɵɵtext(570, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(571, \"a\", 151);\n          i0.ɵɵtext(572, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(573, \"div\", 148)(574, \"button\", 169);\n          i0.ɵɵtext(575, \"Success\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(576, \"div\", 150)(577, \"a\", 151);\n          i0.ɵɵtext(578, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(579, \"a\", 151);\n          i0.ɵɵtext(580, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(581, \"a\", 151);\n          i0.ɵɵtext(582, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(583, \"div\", 148)(584, \"button\", 170);\n          i0.ɵɵtext(585, \"Danger\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(586, \"div\", 150)(587, \"a\", 151);\n          i0.ɵɵtext(588, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(589, \"a\", 151);\n          i0.ɵɵtext(590, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(591, \"a\", 151);\n          i0.ɵɵtext(592, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(593, \"div\", 148)(594, \"button\", 171);\n          i0.ɵɵtext(595, \"Warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(596, \"div\", 150)(597, \"a\", 151);\n          i0.ɵɵtext(598, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(599, \"a\", 151);\n          i0.ɵɵtext(600, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(601, \"a\", 151);\n          i0.ɵɵtext(602, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(603, \"div\", 148)(604, \"button\", 172);\n          i0.ɵɵtext(605, \"Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(606, \"div\", 150)(607, \"a\", 151);\n          i0.ɵɵtext(608, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(609, \"a\", 151);\n          i0.ɵɵtext(610, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(611, \"a\", 151);\n          i0.ɵɵtext(612, \"Something else here\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(613, \"div\", 11)(614, \"app-card\", 173)(615, \"div\", 148)(616, \"button\", 13);\n          i0.ɵɵtext(617, \"Primary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(618, \"button\", 174)(619, \"span\", 159);\n          i0.ɵɵtext(620, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(621, \"div\", 150)(622, \"a\", 151);\n          i0.ɵɵtext(623, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(624, \"a\", 151);\n          i0.ɵɵtext(625, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(626, \"a\", 151);\n          i0.ɵɵtext(627, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(628, \"div\", 160);\n          i0.ɵɵelementStart(629, \"a\", 151);\n          i0.ɵɵtext(630, \"Separated link\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(631, \"div\", 148)(632, \"button\", 14);\n          i0.ɵɵtext(633, \"Secondary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(634, \"button\", 175)(635, \"span\", 159);\n          i0.ɵɵtext(636, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(637, \"div\", 150)(638, \"a\", 151);\n          i0.ɵɵtext(639, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(640, \"a\", 151);\n          i0.ɵɵtext(641, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(642, \"a\", 151);\n          i0.ɵɵtext(643, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(644, \"div\", 160);\n          i0.ɵɵelementStart(645, \"a\", 151);\n          i0.ɵɵtext(646, \"Separated link\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(647, \"div\", 148)(648, \"button\", 15);\n          i0.ɵɵtext(649, \"Success\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(650, \"button\", 176)(651, \"span\", 159);\n          i0.ɵɵtext(652, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(653, \"div\", 150)(654, \"a\", 151);\n          i0.ɵɵtext(655, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(656, \"a\", 151);\n          i0.ɵɵtext(657, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(658, \"a\", 151);\n          i0.ɵɵtext(659, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(660, \"div\", 160);\n          i0.ɵɵelementStart(661, \"a\", 151);\n          i0.ɵɵtext(662, \"Separated link\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(663, \"div\", 148)(664, \"button\", 16);\n          i0.ɵɵtext(665, \"Danger\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(666, \"button\", 177)(667, \"span\", 159);\n          i0.ɵɵtext(668, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(669, \"div\", 150)(670, \"a\", 151);\n          i0.ɵɵtext(671, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(672, \"a\", 151);\n          i0.ɵɵtext(673, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(674, \"a\", 151);\n          i0.ɵɵtext(675, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(676, \"div\", 160);\n          i0.ɵɵelementStart(677, \"a\", 151);\n          i0.ɵɵtext(678, \"Separated link\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(679, \"div\", 148)(680, \"button\", 17);\n          i0.ɵɵtext(681, \"Warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(682, \"button\", 178)(683, \"span\", 159);\n          i0.ɵɵtext(684, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(685, \"div\", 150)(686, \"a\", 151);\n          i0.ɵɵtext(687, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(688, \"a\", 151);\n          i0.ɵɵtext(689, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(690, \"a\", 151);\n          i0.ɵɵtext(691, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(692, \"div\", 160);\n          i0.ɵɵelementStart(693, \"a\", 151);\n          i0.ɵɵtext(694, \"Separated link\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(695, \"div\", 148)(696, \"button\", 18);\n          i0.ɵɵtext(697, \"Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(698, \"button\", 179)(699, \"span\", 159);\n          i0.ɵɵtext(700, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(701, \"div\", 150)(702, \"a\", 151);\n          i0.ɵɵtext(703, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(704, \"a\", 151);\n          i0.ɵɵtext(705, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(706, \"a\", 151);\n          i0.ɵɵtext(707, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(708, \"div\", 160);\n          i0.ɵɵelementStart(709, \"a\", 151);\n          i0.ɵɵtext(710, \"Separated link\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(711, \"div\", 65)(712, \"app-card\", 180)(713, \"div\", 148)(714, \"button\", 181);\n          i0.ɵɵelement(715, \"i\", 112);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(716, \"div\", 150)(717, \"a\", 151);\n          i0.ɵɵtext(718, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(719, \"a\", 151);\n          i0.ɵɵtext(720, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(721, \"a\", 151);\n          i0.ɵɵtext(722, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(723, \"div\", 148)(724, \"button\", 182);\n          i0.ɵɵelement(725, \"i\", 113);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(726, \"div\", 150)(727, \"a\", 151);\n          i0.ɵɵtext(728, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(729, \"a\", 151);\n          i0.ɵɵtext(730, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(731, \"a\", 151);\n          i0.ɵɵtext(732, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(733, \"div\", 148)(734, \"button\", 183);\n          i0.ɵɵelement(735, \"i\", 114);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(736, \"div\", 150)(737, \"a\", 151);\n          i0.ɵɵtext(738, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(739, \"a\", 151);\n          i0.ɵɵtext(740, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(741, \"a\", 151);\n          i0.ɵɵtext(742, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(743, \"div\", 148)(744, \"button\", 184);\n          i0.ɵɵelement(745, \"i\", 115);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(746, \"div\", 150)(747, \"a\", 151);\n          i0.ɵɵtext(748, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(749, \"a\", 151);\n          i0.ɵɵtext(750, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(751, \"a\", 151);\n          i0.ɵɵtext(752, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(753, \"div\", 148)(754, \"button\", 185);\n          i0.ɵɵelement(755, \"i\", 116);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(756, \"div\", 150)(757, \"a\", 151);\n          i0.ɵɵtext(758, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(759, \"a\", 151);\n          i0.ɵɵtext(760, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(761, \"a\", 151);\n          i0.ɵɵtext(762, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(763, \"div\", 148)(764, \"button\", 186);\n          i0.ɵɵelement(765, \"i\", 117);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(766, \"div\", 150)(767, \"a\", 151);\n          i0.ɵɵtext(768, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(769, \"a\", 151);\n          i0.ɵɵtext(770, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(771, \"a\", 151);\n          i0.ɵɵtext(772, \"Something else here\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(773, \"div\", 65)(774, \"app-card\", 187)(775, \"div\", 148)(776, \"button\", 188);\n          i0.ɵɵelement(777, \"i\", 112);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(778, \"div\", 150)(779, \"a\", 151);\n          i0.ɵɵtext(780, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(781, \"a\", 151);\n          i0.ɵɵtext(782, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(783, \"a\", 151);\n          i0.ɵɵtext(784, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(785, \"div\", 148)(786, \"button\", 189);\n          i0.ɵɵelement(787, \"i\", 113);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(788, \"div\", 150)(789, \"a\", 151);\n          i0.ɵɵtext(790, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(791, \"a\", 151);\n          i0.ɵɵtext(792, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(793, \"a\", 151);\n          i0.ɵɵtext(794, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(795, \"div\", 148)(796, \"button\", 190);\n          i0.ɵɵelement(797, \"i\", 114);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(798, \"div\", 150)(799, \"a\", 151);\n          i0.ɵɵtext(800, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(801, \"a\", 151);\n          i0.ɵɵtext(802, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(803, \"a\", 151);\n          i0.ɵɵtext(804, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(805, \"div\", 148)(806, \"button\", 191);\n          i0.ɵɵelement(807, \"i\", 115);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(808, \"div\", 150)(809, \"a\", 151);\n          i0.ɵɵtext(810, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(811, \"a\", 151);\n          i0.ɵɵtext(812, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(813, \"a\", 151);\n          i0.ɵɵtext(814, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(815, \"div\", 148)(816, \"button\", 192);\n          i0.ɵɵelement(817, \"i\", 116);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(818, \"div\", 150)(819, \"a\", 151);\n          i0.ɵɵtext(820, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(821, \"a\", 151);\n          i0.ɵɵtext(822, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(823, \"a\", 151);\n          i0.ɵɵtext(824, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(825, \"div\", 148)(826, \"button\", 193);\n          i0.ɵɵelement(827, \"i\", 117);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(828, \"div\", 150)(829, \"a\", 151);\n          i0.ɵɵtext(830, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(831, \"a\", 151);\n          i0.ɵɵtext(832, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(833, \"a\", 151);\n          i0.ɵɵtext(834, \"Something else here\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(835, \"div\", 65)(836, \"app-card\", 194)(837, \"div\", 148)(838, \"button\", 195);\n          i0.ɵɵelement(839, \"i\", 112);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(840, \"div\", 150)(841, \"a\", 151);\n          i0.ɵɵtext(842, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(843, \"a\", 151);\n          i0.ɵɵtext(844, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(845, \"a\", 151);\n          i0.ɵɵtext(846, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(847, \"div\", 148)(848, \"button\", 196);\n          i0.ɵɵelement(849, \"i\", 113);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(850, \"div\", 150)(851, \"a\", 151);\n          i0.ɵɵtext(852, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(853, \"a\", 151);\n          i0.ɵɵtext(854, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(855, \"a\", 151);\n          i0.ɵɵtext(856, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(857, \"div\", 148)(858, \"button\", 197);\n          i0.ɵɵelement(859, \"i\", 114);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(860, \"div\", 150)(861, \"a\", 151);\n          i0.ɵɵtext(862, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(863, \"a\", 151);\n          i0.ɵɵtext(864, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(865, \"a\", 151);\n          i0.ɵɵtext(866, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(867, \"div\", 148)(868, \"button\", 198);\n          i0.ɵɵelement(869, \"i\", 115);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(870, \"div\", 150)(871, \"a\", 151);\n          i0.ɵɵtext(872, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(873, \"a\", 151);\n          i0.ɵɵtext(874, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(875, \"a\", 151);\n          i0.ɵɵtext(876, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(877, \"div\", 148)(878, \"button\", 199);\n          i0.ɵɵelement(879, \"i\", 116);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(880, \"div\", 150)(881, \"a\", 151);\n          i0.ɵɵtext(882, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(883, \"a\", 151);\n          i0.ɵɵtext(884, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(885, \"a\", 151);\n          i0.ɵɵtext(886, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(887, \"div\", 148)(888, \"button\", 200);\n          i0.ɵɵelement(889, \"i\", 117);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(890, \"div\", 150)(891, \"a\", 151);\n          i0.ɵɵtext(892, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(893, \"a\", 151);\n          i0.ɵɵtext(894, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(895, \"a\", 151);\n          i0.ɵɵtext(896, \"Something else here\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(897, \"div\", 65)(898, \"app-card\", 201)(899, \"div\", 148)(900, \"button\", 202);\n          i0.ɵɵelement(901, \"i\", 112);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(902, \"div\", 150)(903, \"a\", 151);\n          i0.ɵɵtext(904, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(905, \"a\", 151);\n          i0.ɵɵtext(906, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(907, \"a\", 151);\n          i0.ɵɵtext(908, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(909, \"div\", 148)(910, \"button\", 203);\n          i0.ɵɵelement(911, \"i\", 113);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(912, \"div\", 150)(913, \"a\", 151);\n          i0.ɵɵtext(914, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(915, \"a\", 151);\n          i0.ɵɵtext(916, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(917, \"a\", 151);\n          i0.ɵɵtext(918, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(919, \"div\", 148)(920, \"button\", 204);\n          i0.ɵɵelement(921, \"i\", 114);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(922, \"div\", 150)(923, \"a\", 151);\n          i0.ɵɵtext(924, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(925, \"a\", 151);\n          i0.ɵɵtext(926, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(927, \"a\", 151);\n          i0.ɵɵtext(928, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(929, \"div\", 148)(930, \"button\", 205);\n          i0.ɵɵelement(931, \"i\", 115);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(932, \"div\", 150)(933, \"a\", 151);\n          i0.ɵɵtext(934, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(935, \"a\", 151);\n          i0.ɵɵtext(936, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(937, \"a\", 151);\n          i0.ɵɵtext(938, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(939, \"div\", 148)(940, \"button\", 206);\n          i0.ɵɵelement(941, \"i\", 116);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(942, \"div\", 150)(943, \"a\", 151);\n          i0.ɵɵtext(944, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(945, \"a\", 151);\n          i0.ɵɵtext(946, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(947, \"a\", 151);\n          i0.ɵɵtext(948, \"Something else here\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(949, \"div\", 148)(950, \"button\", 207);\n          i0.ɵɵelement(951, \"i\", 117);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(952, \"div\", 150)(953, \"a\", 151);\n          i0.ɵɵtext(954, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(955, \"a\", 151);\n          i0.ɵɵtext(956, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(957, \"a\", 151);\n          i0.ɵɵtext(958, \"Something else here\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(959, \"div\", 65)(960, \"app-card\", 208)(961, \"div\", 209)(962, \"button\", 4);\n          i0.ɵɵtext(963, \"Left\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(964, \"button\", 4);\n          i0.ɵɵtext(965, \"Middle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(966, \"button\", 4);\n          i0.ɵɵtext(967, \"Right\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(968, \"div\", 65)(969, \"app-card\", 210)(970, \"div\", 211)(971, \"div\", 212)(972, \"button\", 4);\n          i0.ɵɵtext(973, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(974, \"button\", 4);\n          i0.ɵɵtext(975, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(976, \"button\", 4);\n          i0.ɵɵtext(977, \"3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(978, \"button\", 4);\n          i0.ɵɵtext(979, \"4\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(980, \"div\", 212)(981, \"button\", 4);\n          i0.ɵɵtext(982, \"5\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(983, \"button\", 4);\n          i0.ɵɵtext(984, \"6\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(985, \"button\", 4);\n          i0.ɵɵtext(986, \"7\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(987, \"div\", 213)(988, \"button\", 4);\n          i0.ɵɵtext(989, \"8\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(990, \"div\", 11)(991, \"app-card\", 214)(992, \"div\", 0)(993, \"div\", 215)(994, \"p\");\n          i0.ɵɵtext(995, \"this is default size\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(996, \"div\", 216)(997, \"button\", 4);\n          i0.ɵɵtext(998, \"Left\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(999, \"button\", 4);\n          i0.ɵɵtext(1000, \"Middle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1001, \"button\", 4);\n          i0.ɵɵtext(1002, \"Right\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(1003, \"div\", 217)(1004, \"p\");\n          i0.ɵɵtext(1005, \" use \");\n          i0.ɵɵelementStart(1006, \"code\");\n          i0.ɵɵtext(1007, \".btn-group-lg\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(1008, \" in class \");\n          i0.ɵɵelementStart(1009, \"code\");\n          i0.ɵɵtext(1010, \".btn-group\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(1011, \" class to get large size button group \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1012, \"div\", 218)(1013, \"button\", 4);\n          i0.ɵɵtext(1014, \"Left\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1015, \"button\", 4);\n          i0.ɵɵtext(1016, \"Middle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1017, \"button\", 4);\n          i0.ɵɵtext(1018, \"Right\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(1019, \"div\", 215)(1020, \"p\");\n          i0.ɵɵtext(1021, \" use \");\n          i0.ɵɵelementStart(1022, \"code\");\n          i0.ɵɵtext(1023, \".btn-group-sm\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(1024, \" in class \");\n          i0.ɵɵelementStart(1025, \"code\");\n          i0.ɵɵtext(1026, \".btn-group\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(1027, \" class to get small size button group \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1028, \"div\", 219)(1029, \"button\", 4);\n          i0.ɵɵtext(1030, \"Left\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1031, \"button\", 4);\n          i0.ɵɵtext(1032, \"Middle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1033, \"button\", 4);\n          i0.ɵɵtext(1034, \"Right\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(1035, \"div\", 65)(1036, \"app-card\", 220)(1037, \"div\", 73)(1038, \"button\", 4);\n          i0.ɵɵtext(1039, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1040, \"button\", 4);\n          i0.ɵɵtext(1041, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1042, \"div\", 221)(1043, \"button\", 222);\n          i0.ɵɵtext(1044, \"Dropdown\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1045, \"div\", 150)(1046, \"a\", 151);\n          i0.ɵɵtext(1047, \"Dropdown link\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1048, \"a\", 151);\n          i0.ɵɵtext(1049, \"Dropdown link\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(1050, \"div\", 65)(1051, \"app-card\", 223)(1052, \"div\", 0)(1053, \"div\", 224)(1054, \"div\", 225)(1055, \"button\", 226);\n          i0.ɵɵtext(1056, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1057, \"button\", 226);\n          i0.ɵɵtext(1058, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1059, \"button\", 226);\n          i0.ɵɵtext(1060, \"3\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(1061, \"div\", 227)(1062, \"div\", 225)(1063, \"button\", 226);\n          i0.ɵɵtext(1064, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1065, \"button\", 226);\n          i0.ɵɵtext(1066, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1067, \"div\", 73)(1068, \"div\", 228)(1069, \"button\", 229);\n          i0.ɵɵtext(1070, \" Dropdown \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1071, \"div\", 230)(1072, \"a\", 151);\n          i0.ɵɵtext(1073, \"Dropdown link\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1074, \"a\", 151);\n          i0.ɵɵtext(1075, \"Dropdown link\");\n          i0.ɵɵelementEnd()()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(47);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(45);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"options\", false);\n        }\n      },\n      dependencies: [SharedModule, i1.CardComponent, i2.NgbDropdown, i2.NgbDropdownToggle, i2.NgbDropdownMenu],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "BasicButtonComponent", "selectors", "decls", "vars", "consts", "template", "BasicButtonComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "i1", "CardComponent", "i2", "NgbDropdown", "NgbDropdownToggle", "NgbDropdownMenu", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\ui-elements\\ui-basic\\basic-button\\basic-button.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\ui-elements\\ui-basic\\basic-button\\basic-button.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\r\n\r\n@Component({\r\n  selector: 'app-basic-button',\r\n  standalone: true,\r\n  imports: [SharedModule],\r\n  templateUrl: './basic-button.component.html',\r\n  styleUrls: ['./basic-button.component.scss']\r\n})\r\nexport default class BasicButtonComponent {}\r\n", "<div class=\"row\">\r\n  <div class=\"col-sm-12\">\r\n    <app-card cardTitle=\"Default\" [options]=\"false\">\r\n      <p>\r\n        use class\r\n        <code>.btn-*</code>\r\n        in class\r\n        <code>.btn</code>\r\n        class to get various button\r\n      </p>\r\n      <button type=\"button\" class=\"btn btn-primary\">Primary</button>\r\n      <button type=\"button\" class=\"btn btn-secondary\">Secondary</button>\r\n      <button type=\"button\" class=\"btn btn-success\">Success</button>\r\n      <button type=\"button\" class=\"btn btn-danger\">Danger</button>\r\n      <button type=\"button\" class=\"btn btn-warning\">Warning</button>\r\n      <button type=\"button\" class=\"btn btn-info\">Info</button>\r\n      <button type=\"button\" class=\"btn btn-dark\">Dark</button>\r\n      <button type=\"button\" class=\"btn btn-link\">Link</button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-12\">\r\n    <app-card cardTitle=\"Outline\" [options]=\"false\">\r\n      <p>\r\n        use class\r\n        <code>.btn-outline-*</code>\r\n        in class\r\n        <code>.btn</code>\r\n        class to get various outline button\r\n      </p>\r\n      <button type=\"button\" class=\"btn btn-outline-primary\">Primary</button>\r\n      <button type=\"button\" class=\"btn btn-outline-secondary\">Secondary</button>\r\n      <button type=\"button\" class=\"btn btn-outline-success\">Success</button>\r\n      <button type=\"button\" class=\"btn btn-outline-danger\">Danger</button>\r\n      <button type=\"button\" class=\"btn btn-outline-warning\">Warning</button>\r\n      <button type=\"button\" class=\"btn btn-outline-info\">Info</button>\r\n      <button type=\"button\" class=\"btn btn-outline-dark\">Dark</button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-12\">\r\n    <app-card cardTitle=\"Square Button\" [options]=\"false\">\r\n      <p>\r\n        use\r\n        <code>.btn-square</code>\r\n        in class\r\n        <code>.btn</code>\r\n        class to get square button\r\n      </p>\r\n      <button type=\"button\" class=\"btn btn-square btn-primary\">Primary</button>\r\n      <button type=\"button\" class=\"btn btn-square btn-secondary\">Secondary</button>\r\n      <button type=\"button\" class=\"btn btn-square btn-success\">Success</button>\r\n      <button type=\"button\" class=\"btn btn-square btn-danger\">Danger</button>\r\n      <button type=\"button\" class=\"btn btn-square btn-warning\">Warning</button>\r\n      <button type=\"button\" class=\"btn btn-square btn-info\">Info</button>\r\n      <button type=\"button\" class=\"btn btn-square btn-dark\">Dark</button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-12\">\r\n    <app-card cardTitle=\"Disabled Button\" [options]=\"false\">\r\n      <p>\r\n        use\r\n        <code>.disabled</code>\r\n        in class\r\n        <code>.btn</code>\r\n        class to get Disabled button\r\n      </p>\r\n      <button type=\"button\" class=\"btn disabled btn-primary\">Primary</button>\r\n      <button type=\"button\" class=\"btn disabled btn-secondary\">Secondary</button>\r\n      <button type=\"button\" class=\"btn disabled btn-success\">Success</button>\r\n      <button type=\"button\" class=\"btn disabled btn-danger\">Danger</button>\r\n      <button type=\"button\" class=\"btn disabled btn-warning\">Warning</button>\r\n      <button type=\"button\" class=\"btn disabled btn-info\">Info</button>\r\n      <button type=\"button\" class=\"btn disabled btn-dark\">Dark</button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-12\">\r\n    <app-card cardTitle=\"Rounded Button\" [options]=\"false\">\r\n      <p>\r\n        use\r\n        <code>.btn-rounded</code>\r\n        in class\r\n        <code>.btn</code>\r\n        class to get Rounded button\r\n      </p>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-primary\">Primary</button>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-secondary\">Secondary</button>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-success\">Success</button>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-danger\">Danger</button>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-warning\">Warning</button>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-info\">Info</button>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-dark\">Dark</button>\r\n      <hr />\r\n      <p>\r\n        use\r\n        <code>.btn-rounded</code>\r\n        in class\r\n        <code>.btn-outline-*</code>\r\n        class to get Rounded Outline button\r\n      </p>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-outline-primary\">Primary</button>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-outline-secondary\">Secondary</button>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-outline-success\">Success</button>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-outline-danger\">Danger</button>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-outline-warning\">Warning</button>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-outline-info\">Info</button>\r\n      <button type=\"button\" class=\"btn btn-rounded btn-outline-dark\">Dark</button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-12\">\r\n    <app-card cardTitle=\"Glow Button\" [options]=\"false\">\r\n      <p>\r\n        use\r\n        <code>.btn-glow</code>\r\n        in class\r\n        <code>.btn</code>\r\n        class to get Glow button\r\n      </p>\r\n      <button type=\"button\" class=\"btn btn-glow-primary btn-primary\" title=\"btn btn-glow-primary btn-primary\" data-toggle=\"tooltip\">\r\n        Primary\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-glow-secondary btn-secondary\" title=\"btn btn-glow-secondary btn-secondary\" data-toggle=\"tooltip\">\r\n        Secondary\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-glow-success btn-success\" title=\"btn btn-glow-success btn-success\" data-toggle=\"tooltip\">\r\n        Success\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-glow-danger btn-danger\" title=\"btn btn-glow-danger btn-danger\" data-toggle=\"tooltip\">\r\n        Danger\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-glow-warning btn-warning\" title=\"btn btn-glow-warning btn-warning\" data-toggle=\"tooltip\">\r\n        Warning\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-glow-info btn-info\" title=\"btn btn-glow-info btn-info\" data-toggle=\"tooltip\">Info</button>\r\n      <button type=\"button\" class=\"btn btn-glow-dark btn-dark\" title=\"btn btn-glow-dark btn-dark\" data-toggle=\"tooltip\">Dark</button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-12\">\r\n    <app-card cardTitle=\"Shadow Button\" [options]=\"false\">\r\n      <p>\r\n        use\r\n        <code>.shadow-[1 / 2 / 3 / 4 / 5]</code>\r\n        in class\r\n        <code>.btn</code>\r\n        class to get Shadow button\r\n      </p>\r\n      <button type=\"button\" class=\"btn shadow-1 btn-primary\">.shadow-1</button>\r\n      <button type=\"button\" class=\"btn shadow-2 btn-success\">.shadow-2</button>\r\n      <button type=\"button\" class=\"btn shadow-3 btn-danger\">.shadow-3</button>\r\n      <button type=\"button\" class=\"btn shadow-4 btn-warning\">.shadow-4</button>\r\n      <button type=\"button\" class=\"btn shadow-5 btn-info\">.shadow-5</button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Sizes [ Large ]\" [options]=\"false\">\r\n      <p>\r\n        use\r\n        <code>.btn-lg</code>\r\n        in class\r\n        <code>.btn</code>\r\n        class to get Large button\r\n      </p>\r\n      <button type=\"button\" class=\"btn btn-primary btn-lg\">Large button</button>\r\n      <button type=\"button\" class=\"btn btn-secondary btn-lg\">Large button</button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Sizes [ small ]\" [options]=\"false\">\r\n      <p>\r\n        use\r\n        <code>.btn-sm</code>\r\n        in class\r\n        <code>.btn</code>\r\n        class to get Small button\r\n      </p>\r\n      <button type=\"button\" class=\"btn btn-primary btn-sm\">Small button</button>\r\n      <button type=\"button\" class=\"btn btn-secondary btn-sm\">Small button</button>\r\n    </app-card>\r\n  </div>\r\n  <!-- [ Checkbox button groups ] start -->\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Checkbox button groups\" [options]=\"false\">\r\n      <div class=\"btn-group\" role=\"group\">\r\n        <input type=\"checkbox\" class=\"btn-check\" id=\"btncheck1\" autocomplete=\"off\" />\r\n        <label class=\"btn btn-outline-secondary\" for=\"btncheck1\">Checkbox 1</label>\r\n        <input type=\"checkbox\" class=\"btn-check\" id=\"btncheck2\" autocomplete=\"off\" />\r\n        <label class=\"btn btn-outline-secondary\" for=\"btncheck2\">Checkbox 2</label>\r\n        <input type=\"checkbox\" class=\"btn-check\" id=\"btncheck3\" autocomplete=\"off\" />\r\n        <label class=\"btn btn-outline-secondary\" for=\"btncheck3\">Checkbox 3</label>\r\n      </div>\r\n      <hr />\r\n      <div class=\"btn-group\" role=\"group\">\r\n        <input type=\"checkbox\" class=\"btn-check\" id=\"btnchecklite1\" autocomplete=\"off\" checked />\r\n        <label class=\"btn btn-light-secondary\" for=\"btnchecklite1\">Checkbox 1</label>\r\n        <input type=\"checkbox\" class=\"btn-check\" id=\"btnchecklite2\" autocomplete=\"off\" checked />\r\n        <label class=\"btn btn-light-secondary\" for=\"btnchecklite2\">Checkbox 2</label>\r\n        <input type=\"checkbox\" class=\"btn-check\" id=\"btnchecklite3\" autocomplete=\"off\" checked />\r\n        <label class=\"btn btn-light-secondary\" for=\"btnchecklite3\">Checkbox 3</label>\r\n      </div>\r\n      <hr />\r\n      <div class=\"btn-group\" role=\"group\">\r\n        <input type=\"checkbox\" class=\"btn-check\" id=\"btnchecklitecol1\" autocomplete=\"off\" checked />\r\n        <label class=\"btn btn-light-primary\" for=\"btnchecklitecol1\">Checkbox 1</label>\r\n        <input type=\"checkbox\" class=\"btn-check\" id=\"btnchecklitecol2\" autocomplete=\"off\" checked />\r\n        <label class=\"btn btn-light-success\" for=\"btnchecklitecol2\">Checkbox 2</label>\r\n        <input type=\"checkbox\" class=\"btn-check\" id=\"btnchecklitecol3\" autocomplete=\"off\" checked />\r\n        <label class=\"btn btn-light-danger\" for=\"btnchecklitecol3\">Checkbox 3</label>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <!-- [ Checkbox button groups ] end -->\r\n  <!-- [ radio button groups ] start -->\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Radio button groups\" [options]=\"false\">\r\n      <div class=\"btn-group\" role=\"group\">\r\n        <input type=\"radio\" class=\"btn-check\" id=\"btnrdo1\" autocomplete=\"off\" name=\"btnradio1\" />\r\n        <label class=\"btn btn-outline-secondary\" for=\"btnrdo1\">Radio 1</label>\r\n        <input type=\"radio\" class=\"btn-check\" id=\"btnrdo2\" autocomplete=\"off\" name=\"btnradio2\" />\r\n        <label class=\"btn btn-outline-secondary\" for=\"btnrdo2\">Radio 2</label>\r\n        <input type=\"radio\" class=\"btn-check\" id=\"btnrdo3\" autocomplete=\"off\" name=\"btnradio32\" />\r\n        <label class=\"btn btn-outline-secondary\" for=\"btnrdo3\">Radio 3</label>\r\n      </div>\r\n      <hr />\r\n      <div class=\"btn-group\" role=\"group\">\r\n        <input type=\"radio\" class=\"btn-check\" id=\"btnrdolite1\" autocomplete=\"off\" name=\"btnradio1\" checked />\r\n        <label class=\"btn btn-light-secondary\" for=\"btnrdolite1\">Radio 1</label>\r\n        <input type=\"radio\" class=\"btn-check\" id=\"btnrdolite2\" autocomplete=\"off\" name=\"btnradio2\" checked />\r\n        <label class=\"btn btn-light-secondary\" for=\"btnrdolite2\">Radio 2</label>\r\n        <input type=\"radio\" class=\"btn-check\" id=\"btnrdolite3\" autocomplete=\"off\" name=\"btnradio23\" checked />\r\n        <label class=\"btn btn-light-secondary\" for=\"btnrdolite3\">Radio 3</label>\r\n      </div>\r\n      <hr />\r\n      <div class=\"btn-group\" role=\"group\">\r\n        <input type=\"radio\" class=\"btn-check\" id=\"btnrdolitecol1\" autocomplete=\"off\" name=\"btnradio31\" checked />\r\n        <label class=\"btn btn-light-primary\" for=\"btnrdolitecol1\">Radio 1</label>\r\n        <input type=\"radio\" class=\"btn-check\" id=\"btnrdolitecol2\" autocomplete=\"off\" name=\"btnradio32\" checked />\r\n        <label class=\"btn btn-light-success\" for=\"btnrdolitecol2\">Radio 2</label>\r\n        <input type=\"radio\" class=\"btn-check\" id=\"btnrdolitecol3\" autocomplete=\"off\" name=\"btnradio33\" checked />\r\n        <label class=\"btn btn-light-danger\" for=\"btnrdolitecol3\">Radio 3</label>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <!-- [ radio button groups ] end -->\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Buttons With Icon\" [options]=\"false\">\r\n      <button type=\"button\" class=\"btn btn-primary\">\r\n        <i class=\"feather icon-thumbs-up\"></i>\r\n        Primary\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-secondary\">\r\n        <i class=\"feather icon-camera\"></i>\r\n        Secondary\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-success\">\r\n        <i class=\"feather icon-check-circle\"></i>\r\n        Success\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-danger\">\r\n        <i class=\"feather icon-slash\"></i>\r\n        Danger\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-warning\">\r\n        <i class=\"feather icon-alert-triangle\"></i>\r\n        Warning\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-info\">\r\n        <i class=\"feather icon-info\"></i>\r\n        Info\r\n      </button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Outline Icon Buttons\" [options]=\"false\">\r\n      <button type=\"button\" class=\"btn btn-outline-primary\">\r\n        <i class=\"feather icon-thumbs-up\"></i>\r\n        Primary\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-outline-secondary\">\r\n        <i class=\"feather icon-camera\"></i>\r\n        Secondary\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-outline-success\">\r\n        <i class=\"feather icon-check-circle\"></i>\r\n        Success\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-outline-danger\">\r\n        <i class=\"feather icon-slash\"></i>\r\n        Danger\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-outline-warning\">\r\n        <i class=\"feather icon-alert-triangle\"></i>\r\n        Warning\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-outline-info\">\r\n        <i class=\"feather icon-info\"></i>\r\n        Info\r\n      </button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Only Icon\" [options]=\"false\">\r\n      <button type=\"button\" class=\"btn btn-icon btn-primary\">\r\n        <i class=\"feather icon-thumbs-up\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-secondary\">\r\n        <i class=\"feather icon-camera\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-success\">\r\n        <i class=\"feather icon-check-circle\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-danger\">\r\n        <i class=\"feather icon-slash\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-warning\">\r\n        <i class=\"feather icon-alert-triangle\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-info\">\r\n        <i class=\"feather icon-info\"></i>\r\n      </button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Outline Icon\" [options]=\"false\">\r\n      <button type=\"button\" class=\"btn btn-icon btn-outline-primary\">\r\n        <i class=\"feather icon-thumbs-up\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-outline-secondary\">\r\n        <i class=\"feather icon-camera\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-outline-success\">\r\n        <i class=\"feather icon-check-circle\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-outline-danger\">\r\n        <i class=\"feather icon-slash\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-outline-warning\">\r\n        <i class=\"feather icon-alert-triangle\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-outline-info\">\r\n        <i class=\"feather icon-info\"></i>\r\n      </button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Icon Button Rounded\" [options]=\"false\">\r\n      <button type=\"button\" class=\"btn btn-icon btn-rounded btn-primary\">\r\n        <i class=\"feather icon-thumbs-up\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-rounded btn-secondary\">\r\n        <i class=\"feather icon-camera\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-rounded btn-success\">\r\n        <i class=\"feather icon-check-circle\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-rounded btn-danger\">\r\n        <i class=\"feather icon-slash\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-rounded btn-warning\">\r\n        <i class=\"feather icon-alert-triangle\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-rounded btn-info\">\r\n        <i class=\"feather icon-info\"></i>\r\n      </button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Icon Outline Button Rounded\" [options]=\"false\">\r\n      <button type=\"button\" class=\"btn btn-icon btn-rounded btn-outline-primary\">\r\n        <i class=\"feather icon-thumbs-up\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-rounded btn-outline-secondary\">\r\n        <i class=\"feather icon-camera\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-rounded btn-outline-success\">\r\n        <i class=\"feather icon-check-circle\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-rounded btn-outline-danger\">\r\n        <i class=\"feather icon-slash\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-rounded btn-outline-warning\">\r\n        <i class=\"feather icon-alert-triangle\"></i>\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-icon btn-rounded btn-outline-info\">\r\n        <i class=\"feather icon-info\"></i>\r\n      </button>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-sm-12\">\r\n    <app-card cardTitle=\"Basic Dropdown Button\" [options]=\"false\">\r\n      <p>\r\n        use\r\n        <code>ngbDropdown</code>\r\n        <code>ngbDropdownToggle</code>\r\n        <code>ngbDropdownMenu</code>\r\n        selector in proper way to get dropdown button\r\n      </p>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn btn-primary\" ngbDropdownToggle type=\"button\">Primary</button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn btn-secondary\" ngbDropdownToggle type=\"button\">Secondary</button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn btn-success\" ngbDropdownToggle type=\"button\">Success</button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn btn-danger\" ngbDropdownToggle type=\"button\">Danger</button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn btn-warning\" ngbDropdownToggle type=\"button\">Warning</button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn btn-info\" ngbDropdownToggle type=\"button\">Info</button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-12\">\r\n    <app-card cardTitle=\"Split Dropdown Button\" [options]=\"false\">\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button type=\"button\" class=\"btn btn-primary\">Primary</button>\r\n        <button type=\"button\" class=\"btn btn-primary dropdown-toggle-split\" ngbDropdownToggle>\r\n          <span class=\"sr-only\">Toggle Dropdown</span>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button type=\"button\" class=\"btn btn-secondary\">Secondary</button>\r\n        <button type=\"button\" class=\"btn btn-secondary dropdown-toggle-split\" ngbDropdownToggle>\r\n          <span class=\"sr-only\">Toggle Dropdown</span>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button type=\"button\" class=\"btn btn-success\">Success</button>\r\n        <button type=\"button\" class=\"btn btn-success dropdown-toggle-split\" ngbDropdownToggle>\r\n          <span class=\"sr-only\">Toggle Dropdown</span>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button type=\"button\" class=\"btn btn-danger\">Danger</button>\r\n        <button type=\"button\" class=\"btn btn-danger dropdown-toggle-split\" ngbDropdownToggle>\r\n          <span class=\"sr-only\">Toggle Dropdown</span>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button type=\"button\" class=\"btn btn-warning\">Warning</button>\r\n        <button type=\"button\" class=\"btn btn-warning dropdown-toggle-split\" ngbDropdownToggle>\r\n          <span class=\"sr-only\">Toggle Dropdown</span>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button type=\"button\" class=\"btn btn-info\">Info</button>\r\n        <button type=\"button\" class=\"btn btn-info dropdown-toggle-split\" ngbDropdownToggle>\r\n          <span class=\"sr-only\">Toggle Dropdown</span>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-12\">\r\n    <app-card cardTitle=\"Basic Outline Dropdown Button\" [options]=\"false\">\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn btn-outline-primary\" ngbDropdownToggle type=\"button\">Primary</button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn btn-outline-secondary\" ngbDropdownToggle type=\"button\">Secondary</button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn btn-outline-success\" ngbDropdownToggle type=\"button\">Success</button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn btn-outline-danger\" ngbDropdownToggle type=\"button\">Danger</button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn btn-outline-warning\" ngbDropdownToggle type=\"button\">Warning</button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn btn-outline-info\" ngbDropdownToggle type=\"button\">Info</button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-12\">\r\n    <app-card cardTitle=\"Split Outline Dropdown Button\" [options]=\"false\">\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button type=\"button\" class=\"btn btn-outline-primary\">Primary</button>\r\n        <button type=\"button\" class=\"btn btn-outline-primary dropdown-toggle-split\" ngbDropdownToggle>\r\n          <span class=\"sr-only\">Toggle Dropdown</span>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button type=\"button\" class=\"btn btn-outline-secondary\">Secondary</button>\r\n        <button type=\"button\" class=\"btn btn-outline-secondary dropdown-toggle-split\" ngbDropdownToggle>\r\n          <span class=\"sr-only\">Toggle Dropdown</span>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button type=\"button\" class=\"btn btn-outline-success\">Success</button>\r\n        <button type=\"button\" class=\"btn btn-outline-success dropdown-toggle-split\" ngbDropdownToggle>\r\n          <span class=\"sr-only\">Toggle Dropdown</span>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button type=\"button\" class=\"btn btn-outline-danger\">Danger</button>\r\n        <button type=\"button\" class=\"btn btn-outline-danger dropdown-toggle-split\" ngbDropdownToggle>\r\n          <span class=\"sr-only\">Toggle Dropdown</span>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button type=\"button\" class=\"btn btn-outline-warning\">Warning</button>\r\n        <button type=\"button\" class=\"btn btn-outline-warning dropdown-toggle-split\" ngbDropdownToggle>\r\n          <span class=\"sr-only\">Toggle Dropdown</span>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button type=\"button\" class=\"btn btn-outline-info\">Info</button>\r\n        <button type=\"button\" class=\"btn btn-outline-info dropdown-toggle-split\" ngbDropdownToggle>\r\n          <span class=\"sr-only\">Toggle Dropdown</span>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Basic Icon Dropdown\" [options]=\"false\">\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-primary\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-thumbs-up\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-secondary\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-camera\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-success\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-check-circle\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-danger\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-slash\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-warning\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-alert-triangle\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-info\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-info\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Outline Icon Dropdown\" [options]=\"false\">\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-outline-primary\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-thumbs-up\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-outline-secondary\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-camera\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-outline-success\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-check-circle\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-outline-danger\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-slash\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-outline-warning\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-alert-triangle\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-outline-info\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-info\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Basic Rounded Icon Dropdown\" [options]=\"false\">\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-rounded btn-primary\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-thumbs-up\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-rounded btn-secondary\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-camera\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-rounded btn-success\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-check-circle\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-rounded btn-danger\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-slash\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-rounded btn-warning\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-alert-triangle\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-rounded btn-info\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-info\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Outline Rounded Icon Dropdown\" [options]=\"false\">\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-rounded btn-outline-primary\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-thumbs-up\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-rounded btn-outline-secondary\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-camera\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-rounded btn-outline-success\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-check-circle\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-rounded btn-outline-danger\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-slash\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-rounded btn-outline-warning\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-alert-triangle\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"btn-group mb-2 me-2\" ngbDropdown [placement]=\"'bottom-left'\">\r\n        <button class=\"btn drp-icon btn-rounded btn-outline-info\" ngbDropdownToggle type=\"button\">\r\n          <i class=\"feather icon-info\"></i>\r\n        </button>\r\n        <div ngbDropdownMenu>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n          <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Basic Button Group\" [options]=\"false\">\r\n      <div class=\"btn-group mb-2\" role=\"group\" aria-label=\"Basic example\">\r\n        <button type=\"button\" class=\"btn btn-secondary\">Left</button>\r\n        <button type=\"button\" class=\"btn btn-secondary\">Middle</button>\r\n        <button type=\"button\" class=\"btn btn-secondary\">Right</button>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Button Toolbar\" [options]=\"false\">\r\n      <div class=\"btn-toolbar\">\r\n        <div class=\"btn-group me-2 mb-1\">\r\n          <button type=\"button\" class=\"btn btn-secondary\">1</button>\r\n          <button type=\"button\" class=\"btn btn-secondary\">2</button>\r\n          <button type=\"button\" class=\"btn btn-secondary\">3</button>\r\n          <button type=\"button\" class=\"btn btn-secondary\">4</button>\r\n        </div>\r\n        <div class=\"btn-group me-2 mb-1\">\r\n          <button type=\"button\" class=\"btn btn-secondary\">5</button>\r\n          <button type=\"button\" class=\"btn btn-secondary\">6</button>\r\n          <button type=\"button\" class=\"btn btn-secondary\">7</button>\r\n        </div>\r\n        <div class=\"btn-group mb-1\">\r\n          <button type=\"button\" class=\"btn btn-secondary\">8</button>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-12\">\r\n    <app-card cardTitle=\"Button Toolbar Size\" [options]=\"false\">\r\n      <div class=\"row\">\r\n        <div class=\"col-xl-4 col-md-6 mb-2\">\r\n          <p>this is default size</p>\r\n          <div class=\"btn-group\" role=\"group\" aria-label=\"button groups\">\r\n            <button type=\"button\" class=\"btn btn-secondary\">Left</button>\r\n            <button type=\"button\" class=\"btn btn-secondary\">Middle</button>\r\n            <button type=\"button\" class=\"btn btn-secondary\">Right</button>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-xl-4 col-md-12 mb-2\">\r\n          <p>\r\n            use\r\n            <code>.btn-group-lg</code>\r\n            in class\r\n            <code>.btn-group</code>\r\n            class to get large size button group\r\n          </p>\r\n          <div class=\"btn-group btn-group-lg\" role=\"group\" aria-label=\"button groups xl\">\r\n            <button type=\"button\" class=\"btn btn-secondary\">Left</button>\r\n            <button type=\"button\" class=\"btn btn-secondary\">Middle</button>\r\n            <button type=\"button\" class=\"btn btn-secondary\">Right</button>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-xl-4 col-md-6 mb-2\">\r\n          <p>\r\n            use\r\n            <code>.btn-group-sm</code>\r\n            in class\r\n            <code>.btn-group</code>\r\n            class to get small size button group\r\n          </p>\r\n          <div class=\"btn-group btn-group-sm\" role=\"group\" aria-label=\"button groups sm\">\r\n            <button type=\"button\" class=\"btn btn-secondary\">Left</button>\r\n            <button type=\"button\" class=\"btn btn-secondary\">Middle</button>\r\n            <button type=\"button\" class=\"btn btn-secondary\">Right</button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Nesting\" [options]=\"false\">\r\n      <div class=\"btn-group\" role=\"group\">\r\n        <button type=\"button\" class=\"btn btn-secondary\">1</button>\r\n        <button type=\"button\" class=\"btn btn-secondary\">2</button>\r\n        <div class=\"btn-group\" role=\"group\" ngbDropdown>\r\n          <button type=\"button\" class=\"btn btn-secondary\" ngbDropdownToggle>Dropdown</button>\r\n          <div ngbDropdownMenu>\r\n            <a class=\"dropdown-item\" href=\"javascript:\">Dropdown link</a>\r\n            <a class=\"dropdown-item\" href=\"javascript:\">Dropdown link</a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-md-6\">\r\n    <app-card cardTitle=\"Vertical Variation\" [options]=\"false\">\r\n      <div class=\"row\">\r\n        <div class=\"col-4\">\r\n          <div class=\"btn-group-vertical\" role=\"group\" aria-label=\"Button group with nested dropdown\">\r\n            <button type=\"button\" class=\"btn m-0 btn-secondary\">1</button>\r\n            <button type=\"button\" class=\"btn m-0 btn-secondary\">2</button>\r\n            <button type=\"button\" class=\"btn m-0 btn-secondary\">3</button>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-8\">\r\n          <div class=\"btn-group-vertical\" role=\"group\" aria-label=\"Button group with nested dropdown\">\r\n            <button type=\"button\" class=\"btn m-0 btn-secondary\">1</button>\r\n            <button type=\"button\" class=\"btn m-0 btn-secondary\">2</button>\r\n            <div class=\"btn-group\" role=\"group\">\r\n              <div ngbDropdown>\r\n                <button type=\"button\" class=\"btn m-0 btn-secondary dropdown-toggle\" data-bs-toggle=\"dropdown\" ngbDropdownToggle>\r\n                  Dropdown\r\n                </button>\r\n                <div class=\"dropdown-menu\" ngbDropdownMenu>\r\n                  <a class=\"dropdown-item\" href=\"javascript:\">Dropdown link</a>\r\n                  <a class=\"dropdown-item\" href=\"javascript:\">Dropdown link</a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,oCAAoC;;;;AASjE,eAAc,MAAOC,oBAAoB;;;uCAApBA,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPnCE,EAHN,CAAAC,cAAA,aAAiB,aACQ,kBAC2B,QAC3C;UACDD,EAAA,CAAAE,MAAA,kBACA;UAAAF,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACnBH,EAAA,CAAAE,MAAA,iBACA;UAAAF,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjBH,EAAA,CAAAE,MAAA,qCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,iBAA8C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9DH,EAAA,CAAAC,cAAA,iBAAgD;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClEH,EAAA,CAAAC,cAAA,iBAA8C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9DH,EAAA,CAAAC,cAAA,iBAA6C;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC5DH,EAAA,CAAAC,cAAA,iBAA8C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9DH,EAAA,CAAAC,cAAA,iBAA2C;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxDH,EAAA,CAAAC,cAAA,iBAA2C;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxDH,EAAA,CAAAC,cAAA,kBAA2C;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAEnDF,EAFmD,CAAAG,YAAA,EAAS,EAC/C,EACP;UAGFH,EAFJ,CAAAC,cAAA,eAAuB,oBAC2B,SAC3C;UACDD,EAAA,CAAAE,MAAA,mBACA;UAAAF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAE,MAAA,kBACA;UAAAF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjBH,EAAA,CAAAE,MAAA,6CACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,kBAAsD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtEH,EAAA,CAAAC,cAAA,kBAAwD;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1EH,EAAA,CAAAC,cAAA,kBAAsD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtEH,EAAA,CAAAC,cAAA,kBAAqD;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpEH,EAAA,CAAAC,cAAA,kBAAsD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtEH,EAAA,CAAAC,cAAA,kBAAmD;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChEH,EAAA,CAAAC,cAAA,kBAAmD;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAE3DF,EAF2D,CAAAG,YAAA,EAAS,EACvD,EACP;UAGFH,EAFJ,CAAAC,cAAA,eAAuB,oBACiC,SACjD;UACDD,EAAA,CAAAE,MAAA,aACA;UAAAF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxBH,EAAA,CAAAE,MAAA,kBACA;UAAAF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjBH,EAAA,CAAAE,MAAA,oCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,kBAAyD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzEH,EAAA,CAAAC,cAAA,kBAA2D;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC7EH,EAAA,CAAAC,cAAA,kBAAyD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzEH,EAAA,CAAAC,cAAA,kBAAwD;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACvEH,EAAA,CAAAC,cAAA,kBAAyD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzEH,EAAA,CAAAC,cAAA,kBAAsD;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnEH,EAAA,CAAAC,cAAA,kBAAsD;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAE9DF,EAF8D,CAAAG,YAAA,EAAS,EAC1D,EACP;UAGFH,EAFJ,CAAAC,cAAA,eAAuB,oBACmC,SACnD;UACDD,EAAA,CAAAE,MAAA,aACA;UAAAF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtBH,EAAA,CAAAE,MAAA,kBACA;UAAAF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjBH,EAAA,CAAAE,MAAA,sCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,kBAAuD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACvEH,EAAA,CAAAC,cAAA,kBAAyD;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC3EH,EAAA,CAAAC,cAAA,kBAAuD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACvEH,EAAA,CAAAC,cAAA,kBAAsD;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrEH,EAAA,CAAAC,cAAA,kBAAuD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACvEH,EAAA,CAAAC,cAAA,kBAAoD;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACjEH,EAAA,CAAAC,cAAA,kBAAoD;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAE5DF,EAF4D,CAAAG,YAAA,EAAS,EACxD,EACP;UAGFH,EAFJ,CAAAC,cAAA,eAAuB,qBACkC,UAClD;UACDD,EAAA,CAAAE,MAAA,cACA;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzBH,EAAA,CAAAE,MAAA,mBACA;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjBH,EAAA,CAAAE,MAAA,sCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,mBAA0D;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1EH,EAAA,CAAAC,cAAA,mBAA4D;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9EH,EAAA,CAAAC,cAAA,mBAA0D;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1EH,EAAA,CAAAC,cAAA,mBAAyD;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxEH,EAAA,CAAAC,cAAA,mBAA0D;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1EH,EAAA,CAAAC,cAAA,mBAAuD;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpEH,EAAA,CAAAC,cAAA,mBAAuD;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpEH,EAAA,CAAAI,SAAA,WAAM;UACNJ,EAAA,CAAAC,cAAA,UAAG;UACDD,EAAA,CAAAE,MAAA,cACA;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzBH,EAAA,CAAAE,MAAA,mBACA;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAE,MAAA,8CACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,mBAAkE;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClFH,EAAA,CAAAC,cAAA,mBAAoE;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtFH,EAAA,CAAAC,cAAA,mBAAkE;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClFH,EAAA,CAAAC,cAAA,mBAAiE;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChFH,EAAA,CAAAC,cAAA,mBAAkE;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClFH,EAAA,CAAAC,cAAA,mBAA+D;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC5EH,EAAA,CAAAC,cAAA,mBAA+D;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAEvEF,EAFuE,CAAAG,YAAA,EAAS,EACnE,EACP;UAGFH,EAFJ,CAAAC,cAAA,gBAAuB,qBAC+B,UAC/C;UACDD,EAAA,CAAAE,MAAA,cACA;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtBH,EAAA,CAAAE,MAAA,mBACA;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjBH,EAAA,CAAAE,MAAA,mCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,mBAA8H;UAC5HD,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAAsI;UACpID,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAA8H;UAC5HD,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAA0H;UACxHD,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAA8H;UAC5HD,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAAkH;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC/HH,EAAA,CAAAC,cAAA,mBAAkH;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAE1HF,EAF0H,CAAAG,YAAA,EAAS,EACtH,EACP;UAGFH,EAFJ,CAAAC,cAAA,gBAAuB,qBACiC,UACjD;UACDD,EAAA,CAAAE,MAAA,cACA;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,oCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxCH,EAAA,CAAAE,MAAA,mBACA;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjBH,EAAA,CAAAE,MAAA,qCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,mBAAuD;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzEH,EAAA,CAAAC,cAAA,mBAAuD;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzEH,EAAA,CAAAC,cAAA,mBAAsD;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxEH,EAAA,CAAAC,cAAA,mBAAuD;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzEH,EAAA,CAAAC,cAAA,mBAAoD;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAEjEF,EAFiE,CAAAG,YAAA,EAAS,EAC7D,EACP;UAGFH,EAFJ,CAAAC,cAAA,gBAAsB,qBACoC,UACnD;UACDD,EAAA,CAAAE,MAAA,cACA;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpBH,EAAA,CAAAE,MAAA,mBACA;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjBH,EAAA,CAAAE,MAAA,oCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,mBAAqD;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1EH,EAAA,CAAAC,cAAA,mBAAuD;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAEvEF,EAFuE,CAAAG,YAAA,EAAS,EACnE,EACP;UAGFH,EAFJ,CAAAC,cAAA,gBAAsB,qBACoC,UACnD;UACDD,EAAA,CAAAE,MAAA,cACA;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpBH,EAAA,CAAAE,MAAA,mBACA;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjBH,EAAA,CAAAE,MAAA,oCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,mBAAqD;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1EH,EAAA,CAAAC,cAAA,mBAAuD;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAEvEF,EAFuE,CAAAG,YAAA,EAAS,EACnE,EACP;UAIFH,EAFJ,CAAAC,cAAA,gBAAsB,qBAC2C,gBACzB;UAClCD,EAAA,CAAAI,SAAA,kBAA6E;UAC7EJ,EAAA,CAAAC,cAAA,kBAAyD;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3EH,EAAA,CAAAI,SAAA,kBAA6E;UAC7EJ,EAAA,CAAAC,cAAA,kBAAyD;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3EH,EAAA,CAAAI,SAAA,kBAA6E;UAC7EJ,EAAA,CAAAC,cAAA,kBAAyD;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UACrEF,EADqE,CAAAG,YAAA,EAAQ,EACvE;UACNH,EAAA,CAAAI,SAAA,WAAM;UACNJ,EAAA,CAAAC,cAAA,gBAAoC;UAClCD,EAAA,CAAAI,SAAA,kBAAyF;UACzFJ,EAAA,CAAAC,cAAA,kBAA2D;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7EH,EAAA,CAAAI,SAAA,kBAAyF;UACzFJ,EAAA,CAAAC,cAAA,kBAA2D;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7EH,EAAA,CAAAI,SAAA,kBAAyF;UACzFJ,EAAA,CAAAC,cAAA,kBAA2D;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UACvEF,EADuE,CAAAG,YAAA,EAAQ,EACzE;UACNH,EAAA,CAAAI,SAAA,WAAM;UACNJ,EAAA,CAAAC,cAAA,gBAAoC;UAClCD,EAAA,CAAAI,SAAA,kBAA4F;UAC5FJ,EAAA,CAAAC,cAAA,kBAA4D;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9EH,EAAA,CAAAI,SAAA,kBAA4F;UAC5FJ,EAAA,CAAAC,cAAA,kBAA4D;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9EH,EAAA,CAAAI,SAAA,kBAA4F;UAC5FJ,EAAA,CAAAC,cAAA,kBAA2D;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAG3EF,EAH2E,CAAAG,YAAA,EAAQ,EACzE,EACG,EACP;UAKFH,EAFJ,CAAAC,cAAA,gBAAsB,qBACwC,gBACtB;UAClCD,EAAA,CAAAI,SAAA,kBAAyF;UACzFJ,EAAA,CAAAC,cAAA,kBAAuD;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAAI,SAAA,kBAAyF;UACzFJ,EAAA,CAAAC,cAAA,kBAAuD;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAAI,SAAA,kBAA0F;UAC1FJ,EAAA,CAAAC,cAAA,kBAAuD;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAChEF,EADgE,CAAAG,YAAA,EAAQ,EAClE;UACNH,EAAA,CAAAI,SAAA,WAAM;UACNJ,EAAA,CAAAC,cAAA,gBAAoC;UAClCD,EAAA,CAAAI,SAAA,kBAAqG;UACrGJ,EAAA,CAAAC,cAAA,mBAAyD;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxEH,EAAA,CAAAI,SAAA,mBAAqG;UACrGJ,EAAA,CAAAC,cAAA,mBAAyD;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxEH,EAAA,CAAAI,SAAA,mBAAsG;UACtGJ,EAAA,CAAAC,cAAA,mBAAyD;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAClEF,EADkE,CAAAG,YAAA,EAAQ,EACpE;UACNH,EAAA,CAAAI,SAAA,WAAM;UACNJ,EAAA,CAAAC,cAAA,gBAAoC;UAClCD,EAAA,CAAAI,SAAA,mBAAyG;UACzGJ,EAAA,CAAAC,cAAA,mBAA0D;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzEH,EAAA,CAAAI,SAAA,mBAAyG;UACzGJ,EAAA,CAAAC,cAAA,mBAA0D;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzEH,EAAA,CAAAI,SAAA,mBAAyG;UACzGJ,EAAA,CAAAC,cAAA,mBAAyD;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAGtEF,EAHsE,CAAAG,YAAA,EAAQ,EACpE,EACG,EACP;UAIFH,EAFJ,CAAAC,cAAA,gBAAsB,sBACsC,kBACV;UAC5CD,EAAA,CAAAI,SAAA,eAAsC;UACtCJ,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAgD;UAC9CD,EAAA,CAAAI,SAAA,eAAmC;UACnCJ,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA8C;UAC5CD,EAAA,CAAAI,SAAA,eAAyC;UACzCJ,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA6C;UAC3CD,EAAA,CAAAI,SAAA,eAAkC;UAClCJ,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA8C;UAC5CD,EAAA,CAAAI,SAAA,eAA2C;UAC3CJ,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA2C;UACzCD,EAAA,CAAAI,SAAA,eAAiC;UACjCJ,EAAA,CAAAE,MAAA,eACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACA,EACP;UAGFH,EAFJ,CAAAC,cAAA,gBAAsB,sBACyC,mBACL;UACpDD,EAAA,CAAAI,SAAA,eAAsC;UACtCJ,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAAwD;UACtDD,EAAA,CAAAI,SAAA,eAAmC;UACnCJ,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAAsD;UACpDD,EAAA,CAAAI,SAAA,eAAyC;UACzCJ,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAAqD;UACnDD,EAAA,CAAAI,SAAA,eAAkC;UAClCJ,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAAsD;UACpDD,EAAA,CAAAI,SAAA,eAA2C;UAC3CJ,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAAmD;UACjDD,EAAA,CAAAI,SAAA,eAAiC;UACjCJ,EAAA,CAAAE,MAAA,eACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACA,EACP;UAGFH,EAFJ,CAAAC,cAAA,gBAAsB,sBAC8B,oBACO;UACrDD,EAAA,CAAAI,SAAA,eAAsC;UACxCJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAAyD;UACvDD,EAAA,CAAAI,SAAA,eAAmC;UACrCJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAAuD;UACrDD,EAAA,CAAAI,SAAA,eAAyC;UAC3CJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAAsD;UACpDD,EAAA,CAAAI,SAAA,eAAkC;UACpCJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAAuD;UACrDD,EAAA,CAAAI,SAAA,eAA2C;UAC7CJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAAoD;UAClDD,EAAA,CAAAI,SAAA,eAAiC;UAGvCJ,EAFI,CAAAG,YAAA,EAAS,EACA,EACP;UAGFH,EAFJ,CAAAC,cAAA,gBAAsB,sBACiC,oBACY;UAC7DD,EAAA,CAAAI,SAAA,eAAsC;UACxCJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAAiE;UAC/DD,EAAA,CAAAI,SAAA,eAAmC;UACrCJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA+D;UAC7DD,EAAA,CAAAI,SAAA,eAAyC;UAC3CJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA8D;UAC5DD,EAAA,CAAAI,SAAA,eAAkC;UACpCJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA+D;UAC7DD,EAAA,CAAAI,SAAA,eAA2C;UAC7CJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA4D;UAC1DD,EAAA,CAAAI,SAAA,eAAiC;UAGvCJ,EAFI,CAAAG,YAAA,EAAS,EACA,EACP;UAGFH,EAFJ,CAAAC,cAAA,gBAAsB,sBACwC,oBACS;UACjED,EAAA,CAAAI,SAAA,eAAsC;UACxCJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAAqE;UACnED,EAAA,CAAAI,SAAA,eAAmC;UACrCJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAAmE;UACjED,EAAA,CAAAI,SAAA,eAAyC;UAC3CJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAAkE;UAChED,EAAA,CAAAI,SAAA,eAAkC;UACpCJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAAmE;UACjED,EAAA,CAAAI,SAAA,eAA2C;UAC7CJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAAgE;UAC9DD,EAAA,CAAAI,SAAA,eAAiC;UAGvCJ,EAFI,CAAAG,YAAA,EAAS,EACA,EACP;UAGFH,EAFJ,CAAAC,cAAA,gBAAsB,sBACgD,oBACS;UACzED,EAAA,CAAAI,SAAA,eAAsC;UACxCJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA6E;UAC3ED,EAAA,CAAAI,SAAA,eAAmC;UACrCJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA2E;UACzED,EAAA,CAAAI,SAAA,eAAyC;UAC3CJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA0E;UACxED,EAAA,CAAAI,SAAA,eAAkC;UACpCJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA2E;UACzED,EAAA,CAAAI,SAAA,eAA2C;UAC7CJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAAwE;UACtED,EAAA,CAAAI,SAAA,eAAiC;UAGvCJ,EAFI,CAAAG,YAAA,EAAS,EACA,EACP;UAGFH,EAFJ,CAAAC,cAAA,eAAuB,sBACyC,UACzD;UACDD,EAAA,CAAAE,MAAA,cACA;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxBH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9BH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAE,MAAA,wDACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEFH,EADF,CAAAC,cAAA,iBAAyE,oBACP;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE9EH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACL;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAElFH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACP;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE9EH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACR;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE5EH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACP;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE9EH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACV;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAExEH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAIvEF,EAJuE,CAAAG,YAAA,EAAI,EAC/D,EACF,EACG,EACP;UAIAH,EAHN,CAAAC,cAAA,gBAAuB,sBACyC,iBACa,kBACzB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE5DH,EADF,CAAAC,cAAA,oBAAsF,kBAC9D;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAoC;UACpCJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAC1D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,kBACvB;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEhEH,EADF,CAAAC,cAAA,oBAAwF,kBAChE;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAoC;UACpCJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAC1D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,kBACzB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE5DH,EADF,CAAAC,cAAA,oBAAsF,kBAC9D;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAoC;UACpCJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAC1D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,kBAC1B;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE1DH,EADF,CAAAC,cAAA,oBAAqF,kBAC7D;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAoC;UACpCJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAC1D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,kBACzB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE5DH,EADF,CAAAC,cAAA,oBAAsF,kBAC9D;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAoC;UACpCJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAC1D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,kBAC5B;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEtDH,EADF,CAAAC,cAAA,oBAAmF,kBAC3D;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAoC;UACpCJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAIlEF,EAJkE,CAAAG,YAAA,EAAI,EAC1D,EACF,EACG,EACP;UAIAH,EAHN,CAAAC,cAAA,gBAAuB,sBACiD,iBACK,oBACC;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEtFH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACG;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE1FH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACC;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEtFH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACA;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEpFH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACC;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEtFH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACF;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEhFH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAIvEF,EAJuE,CAAAG,YAAA,EAAI,EAC/D,EACF,EACG,EACP;UAIAH,EAHN,CAAAC,cAAA,gBAAuB,sBACiD,iBACK,mBACjB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEpEH,EADF,CAAAC,cAAA,oBAA8F,kBACtE;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAoC;UACpCJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAC1D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,mBACf;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAExEH,EADF,CAAAC,cAAA,oBAAgG,kBACxE;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAoC;UACpCJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAC1D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,mBACjB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEpEH,EADF,CAAAC,cAAA,oBAA8F,kBACtE;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAoC;UACpCJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAC1D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,mBAClB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAElEH,EADF,CAAAC,cAAA,oBAA6F,kBACrE;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAoC;UACpCJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAC1D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,mBACjB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEpEH,EADF,CAAAC,cAAA,oBAA8F,kBACtE;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAoC;UACpCJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAC1D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,mBACpB;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE9DH,EADF,CAAAC,cAAA,oBAA2F,kBACnE;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAoC;UACpCJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAIlEF,EAJkE,CAAAG,YAAA,EAAI,EAC1D,EACF,EACG,EACP;UAIAH,EAHN,CAAAC,cAAA,gBAAsB,sBACwC,iBACe,oBACE;UACvED,EAAA,CAAAI,SAAA,eAAsC;UACxCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACI;UACzED,EAAA,CAAAI,SAAA,eAAmC;UACrCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACE;UACvED,EAAA,CAAAI,SAAA,eAAyC;UAC3CJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACC;UACtED,EAAA,CAAAI,SAAA,eAAkC;UACpCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACE;UACvED,EAAA,CAAAI,SAAA,eAA2C;UAC7CJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACD;UACpED,EAAA,CAAAI,SAAA,eAAiC;UACnCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAIvEF,EAJuE,CAAAG,YAAA,EAAI,EAC/D,EACF,EACG,EACP;UAIAH,EAHN,CAAAC,cAAA,gBAAsB,sBAC0C,iBACa,oBACU;UAC/ED,EAAA,CAAAI,SAAA,eAAsC;UACxCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACY;UACjFD,EAAA,CAAAI,SAAA,eAAmC;UACrCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACU;UAC/ED,EAAA,CAAAI,SAAA,eAAyC;UAC3CJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACS;UAC9ED,EAAA,CAAAI,SAAA,eAAkC;UACpCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACU;UAC/ED,EAAA,CAAAI,SAAA,eAA2C;UAC7CJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACO;UAC5ED,EAAA,CAAAI,SAAA,eAAiC;UACnCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAIvEF,EAJuE,CAAAG,YAAA,EAAI,EAC/D,EACF,EACG,EACP;UAIAH,EAHN,CAAAC,cAAA,gBAAsB,sBACgD,iBACO,oBACc;UACnFD,EAAA,CAAAI,SAAA,eAAsC;UACxCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACgB;UACrFD,EAAA,CAAAI,SAAA,eAAmC;UACrCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACc;UACnFD,EAAA,CAAAI,SAAA,eAAyC;UAC3CJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACa;UAClFD,EAAA,CAAAI,SAAA,eAAkC;UACpCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACc;UACnFD,EAAA,CAAAI,SAAA,eAA2C;UAC7CJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACW;UAChFD,EAAA,CAAAI,SAAA,eAAiC;UACnCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAIvEF,EAJuE,CAAAG,YAAA,EAAI,EAC/D,EACF,EACG,EACP;UAIAH,EAHN,CAAAC,cAAA,gBAAsB,sBACkD,iBACK,oBACsB;UAC3FD,EAAA,CAAAI,SAAA,eAAsC;UACxCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACwB;UAC7FD,EAAA,CAAAI,SAAA,eAAmC;UACrCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACsB;UAC3FD,EAAA,CAAAI,SAAA,eAAyC;UAC3CJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACqB;UAC1FD,EAAA,CAAAI,SAAA,eAAkC;UACpCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACsB;UAC3FD,EAAA,CAAAI,SAAA,eAA2C;UAC7CJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,iBAAyE,oBACmB;UACxFD,EAAA,CAAAI,SAAA,eAAiC;UACnCJ,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAIvEF,EAJuE,CAAAG,YAAA,EAAI,EAC/D,EACF,EACG,EACP;UAIAH,EAHN,CAAAC,cAAA,gBAAsB,sBACuC,iBACW,kBAClB;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC7DH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC/DH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAG3DF,EAH2D,CAAAG,YAAA,EAAS,EAC1D,EACG,EACP;UAKEH,EAJR,CAAAC,cAAA,gBAAsB,sBACmC,iBAC5B,iBACU,kBACiB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1DH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1DH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1DH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,UAAC;UACnDF,EADmD,CAAAG,YAAA,EAAS,EACtD;UAEJH,EADF,CAAAC,cAAA,iBAAiC,kBACiB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1DH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1DH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,UAAC;UACnDF,EADmD,CAAAG,YAAA,EAAS,EACtD;UAEJH,EADF,CAAAC,cAAA,iBAA4B,kBACsB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAIzDF,EAJyD,CAAAG,YAAA,EAAS,EACtD,EACF,EACG,EACP;UAKEH,EAJR,CAAAC,cAAA,gBAAuB,sBACuC,eACzC,iBACqB,UAC/B;UAAAD,EAAA,CAAAE,MAAA,6BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEzBH,EADF,CAAAC,cAAA,iBAA+D,kBACb;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC7DH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,gBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC/DH,EAAA,CAAAC,cAAA,mBAAgD;UAAAD,EAAA,CAAAE,MAAA,eAAK;UAEzDF,EAFyD,CAAAG,YAAA,EAAS,EAC1D,EACF;UAEJH,EADF,CAAAC,cAAA,kBAAqC,WAChC;UACDD,EAAA,CAAAE,MAAA,eACA;UAAAF,EAAA,CAAAC,cAAA,cAAM;UAAAD,EAAA,CAAAE,MAAA,uBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC1BH,EAAA,CAAAE,MAAA,oBACA;UAAAF,EAAA,CAAAC,cAAA,cAAM;UAAAD,EAAA,CAAAE,MAAA,oBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvBH,EAAA,CAAAE,MAAA,gDACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEFH,EADF,CAAAC,cAAA,kBAA+E,mBAC7B;UAAAD,EAAA,CAAAE,MAAA,cAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC7DH,EAAA,CAAAC,cAAA,mBAAgD;UAAAD,EAAA,CAAAE,MAAA,gBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC/DH,EAAA,CAAAC,cAAA,mBAAgD;UAAAD,EAAA,CAAAE,MAAA,eAAK;UAEzDF,EAFyD,CAAAG,YAAA,EAAS,EAC1D,EACF;UAEJH,EADF,CAAAC,cAAA,kBAAoC,WAC/B;UACDD,EAAA,CAAAE,MAAA,eACA;UAAAF,EAAA,CAAAC,cAAA,cAAM;UAAAD,EAAA,CAAAE,MAAA,uBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC1BH,EAAA,CAAAE,MAAA,oBACA;UAAAF,EAAA,CAAAC,cAAA,cAAM;UAAAD,EAAA,CAAAE,MAAA,oBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvBH,EAAA,CAAAE,MAAA,gDACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEFH,EADF,CAAAC,cAAA,kBAA+E,mBAC7B;UAAAD,EAAA,CAAAE,MAAA,cAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC7DH,EAAA,CAAAC,cAAA,mBAAgD;UAAAD,EAAA,CAAAE,MAAA,gBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC/DH,EAAA,CAAAC,cAAA,mBAAgD;UAAAD,EAAA,CAAAE,MAAA,eAAK;UAK/DF,EAL+D,CAAAG,YAAA,EAAS,EAC1D,EACF,EACF,EACG,EACP;UAIAH,EAHN,CAAAC,cAAA,iBAAsB,uBAC4B,iBACV,mBACc;UAAAD,EAAA,CAAAE,MAAA,WAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1DH,EAAA,CAAAC,cAAA,mBAAgD;UAAAD,EAAA,CAAAE,MAAA,WAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAExDH,EADF,CAAAC,cAAA,kBAAgD,qBACoB;UAAAD,EAAA,CAAAE,MAAA,kBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEjFH,EADF,CAAAC,cAAA,kBAAqB,gBACyB;UAAAD,EAAA,CAAAE,MAAA,uBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC7DH,EAAA,CAAAC,cAAA,gBAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAa;UAKnEF,EALmE,CAAAG,YAAA,EAAI,EACzD,EACF,EACF,EACG,EACP;UAMIH,EALV,CAAAC,cAAA,iBAAsB,uBACuC,gBACxC,kBACI,kBAC2E,qBACtC;UAAAD,EAAA,CAAAE,MAAA,WAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9DH,EAAA,CAAAC,cAAA,qBAAoD;UAAAD,EAAA,CAAAE,MAAA,WAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9DH,EAAA,CAAAC,cAAA,qBAAoD;UAAAD,EAAA,CAAAE,MAAA,WAAC;UAEzDF,EAFyD,CAAAG,YAAA,EAAS,EAC1D,EACF;UAGFH,EAFJ,CAAAC,cAAA,kBAAmB,kBAC2E,qBACtC;UAAAD,EAAA,CAAAE,MAAA,WAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9DH,EAAA,CAAAC,cAAA,qBAAoD;UAAAD,EAAA,CAAAE,MAAA,WAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAG1DH,EAFJ,CAAAC,cAAA,iBAAoC,kBACjB,qBACiG;UAC9GD,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,kBAA2C,gBACG;UAAAD,EAAA,CAAAE,MAAA,uBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC7DH,EAAA,CAAAC,cAAA,gBAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAa;UAS3EF,EAT2E,CAAAG,YAAA,EAAI,EACzD,EACF,EACF,EACF,EACF,EACF,EACG,EACP,EACF;;;UAtgC4BH,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAmBjBN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAkBXN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAkBfN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAkBlBN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAiCpBN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UA4BfN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAgBfN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAajBN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAcVN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAgCpBN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UA+BnBN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UA4BdN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UA4B5BN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAsBdN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAsBVN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAsBTN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAsBvBN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAQdN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAQ3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAQ3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAQ3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAQ3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAQ3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAW9BN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UACdN,EAAA,CAAAK,SAAA,EAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAa3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAa3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAa3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAa3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAa3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAgBtBN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UACtBN,EAAA,CAAAK,SAAA,EAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAQ3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAQ3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAQ3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAQ3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAQ3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAWtBN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UACtBN,EAAA,CAAAK,SAAA,EAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAa3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAa3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAa3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAa3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAa3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAgBhCN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UACZN,EAAA,CAAAK,SAAA,EAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAa9BN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UACdN,EAAA,CAAAK,SAAA,EAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAaxBN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UACpBN,EAAA,CAAAK,SAAA,EAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAatBN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UACtBN,EAAA,CAAAK,SAAA,EAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAU3BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAajCN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UASrBN,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAoBZN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UA0C7BN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAeNN,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;;;qBDr+BlDhB,YAAY,EAAAiB,EAAA,CAAAC,aAAA,EAAAC,EAAA,CAAAC,WAAA,EAAAD,EAAA,CAAAE,iBAAA,EAAAF,EAAA,CAAAG,eAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}