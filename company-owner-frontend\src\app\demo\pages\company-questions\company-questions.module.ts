import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { CompanyQuestionsListComponent } from './company-questions-list/company-questions-list.component';
import { CompanyQuestionsComponent } from './company-questions.component';

const routes: Routes = [
  {
    path: '',
    component: CompanyQuestionsComponent,
    children: [
      {
        path: '',
        component: CompanyQuestionsListComponent
      }
    ]
  }
];

@NgModule({
  declarations: [
    CompanyQuestionsComponent,
    CompanyQuestionsListComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes)
  ]
})
export class CompanyQuestionsModule { }