{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n// project import\nimport { CardComponent } from './components/card/card.component';\n// bootstrap import\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { NgbCollapseModule } from '@ng-bootstrap/ng-bootstrap';\n// third party\nimport { NgScrollbarModule } from 'ngx-scrollbar';\nimport * as i0 from \"@angular/core\";\nexport class SharedModule {\n  static {\n    this.ɵfac = function SharedModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, CardComponent, NgbModule, NgScrollbarModule, NgbCollapseModule, CommonModule, FormsModule, ReactiveFormsModule, NgbModule, NgScrollbarModule, NgbCollapseModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, CardComponent, NgbModule, NgScrollbarModule, NgbCollapseModule],\n    exports: [CommonModule, FormsModule, ReactiveFormsModule, CardComponent, NgbModule, NgScrollbarModule, NgbCollapseModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "CardComponent", "NgbModule", "NgbCollapseModule", "NgScrollbarModule", "SharedModule", "imports", "exports"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\shared\\shared.module.ts"], "sourcesContent": ["// angular import\r\nimport { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\n// project import\r\nimport { CardComponent } from './components/card/card.component';\r\n\r\n// bootstrap import\r\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { NgbCollapseModule } from '@ng-bootstrap/ng-bootstrap';\r\n\r\n// third party\r\nimport { NgScrollbarModule } from 'ngx-scrollbar';\r\n\r\n@NgModule({\r\n  declarations: [],\r\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, CardComponent, NgbModule, NgScrollbarModule, NgbCollapseModule],\r\n  exports: [CommonModule, FormsModule, ReactiveFormsModule, CardComponent, NgbModule, NgScrollbarModule, NgbCollapseModule]\r\n})\r\nexport class SharedModule {}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE;AACA,SAASC,aAAa,QAAQ,kCAAkC;AAEhE;AACA,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,iBAAiB,QAAQ,4BAA4B;AAE9D;AACA,SAASC,iBAAiB,QAAQ,eAAe;;AAOjD,OAAM,MAAOC,YAAY;;;uCAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAHbP,YAAY,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,SAAS,EAAEE,iBAAiB,EAAED,iBAAiB,EAC9GL,YAAY,EAAEC,WAAW,EAAEC,mBAAmB,EAAiBE,SAAS,EAAEE,iBAAiB,EAAED,iBAAiB;IAAA;EAAA;;;2EAE7GE,YAAY;IAAAC,OAAA,GAHbR,YAAY,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,SAAS,EAAEE,iBAAiB,EAAED,iBAAiB;IAAAI,OAAA,GAC9GT,YAAY,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,SAAS,EAAEE,iBAAiB,EAAED,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}