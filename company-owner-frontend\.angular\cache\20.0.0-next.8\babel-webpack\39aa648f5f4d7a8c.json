{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class QuestionService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  getQuestions() {\n    return this.http.get(`${this.apiUrl}/product-owner/questions`);\n  }\n  getQuestion(id) {\n    return this.http.get(`${this.apiUrl}/product-owner/questions/${id}`);\n  }\n  createQuestion(question) {\n    return this.http.post(`${this.apiUrl}/product-owner/questions`, question);\n  }\n  updateQuestion(id, question) {\n    return this.http.put(`${this.apiUrl}/product-owner/questions/${id}`, question);\n  }\n  deleteQuestion(id) {\n    return this.http.delete(`${this.apiUrl}/product-owner/questions/${id}`);\n  }\n  // Question options\n  addOption(questionId, option) {\n    return this.http.post(`${this.apiUrl}/product-owner/questions/${questionId}/options`, option);\n  }\n  updateOption(questionId, optionId, option) {\n    return this.http.put(`${this.apiUrl}/product-owner/questions/${questionId}/options/${optionId}`, option);\n  }\n  deleteOption(questionId, optionId) {\n    return this.http.delete(`${this.apiUrl}/product-owner/questions/${questionId}/options/${optionId}`);\n  }\n  static {\n    this.ɵfac = function QuestionService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuestionService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QuestionService,\n      factory: QuestionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "QuestionService", "constructor", "http", "apiUrl", "getQuestions", "get", "getQuestion", "id", "createQuestion", "question", "post", "updateQuestion", "put", "deleteQuestion", "delete", "addOption", "questionId", "option", "updateOption", "optionId", "deleteOption", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\core\\services\\question.service.ts"], "sourcesContent": ["// src/app/core/services/question.service.ts\r\nimport { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from '../../../environments/environment';\r\nimport { Question, QuestionOption } from '../models/question.model';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuestionService {\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getQuestions(): Observable<Question[]> {\r\n    return this.http.get<Question[]>(`${this.apiUrl}/product-owner/questions`);\r\n  }\r\n\r\n  getQuestion(id: string): Observable<Question> {\r\n    return this.http.get<Question>(`${this.apiUrl}/product-owner/questions/${id}`);\r\n  }\r\n\r\n  createQuestion(question: Question): Observable<Question> {\r\n    return this.http.post<Question>(`${this.apiUrl}/product-owner/questions`, question);\r\n  }\r\n\r\n  updateQuestion(id: string, question: Partial<Question>): Observable<Question> {\r\n    return this.http.put<Question>(`${this.apiUrl}/product-owner/questions/${id}`, question);\r\n  }\r\n\r\n  deleteQuestion(id: string): Observable<any> {\r\n    return this.http.delete(`${this.apiUrl}/product-owner/questions/${id}`);\r\n  }\r\n\r\n  // Question options\r\n  addOption(questionId: string, option: QuestionOption): Observable<QuestionOption> {\r\n    return this.http.post<QuestionOption>(`${this.apiUrl}/product-owner/questions/${questionId}/options`, option);\r\n  }\r\n\r\n  updateOption(questionId: string, optionId: string, option: QuestionOption): Observable<QuestionOption> {\r\n    return this.http.put<QuestionOption>(`${this.apiUrl}/product-owner/questions/${questionId}/options/${optionId}`, option);\r\n  }\r\n\r\n  deleteOption(questionId: string, optionId: string): Observable<any> {\r\n    return this.http.delete(`${this.apiUrl}/product-owner/questions/${questionId}/options/${optionId}`);\r\n  }\r\n}"], "mappings": "AAIA,SAASA,WAAW,QAAQ,mCAAmC;;;AAM/D,OAAM,MAAOC,eAAe;EAG1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;EAEI;EAEvCC,YAAYA,CAAA;IACV,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAa,GAAG,IAAI,CAACF,MAAM,0BAA0B,CAAC;EAC5E;EAEAG,WAAWA,CAACC,EAAU;IACpB,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAW,GAAG,IAAI,CAACF,MAAM,4BAA4BI,EAAE,EAAE,CAAC;EAChF;EAEAC,cAAcA,CAACC,QAAkB;IAC/B,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAW,GAAG,IAAI,CAACP,MAAM,0BAA0B,EAAEM,QAAQ,CAAC;EACrF;EAEAE,cAAcA,CAACJ,EAAU,EAAEE,QAA2B;IACpD,OAAO,IAAI,CAACP,IAAI,CAACU,GAAG,CAAW,GAAG,IAAI,CAACT,MAAM,4BAA4BI,EAAE,EAAE,EAAEE,QAAQ,CAAC;EAC1F;EAEAI,cAAcA,CAACN,EAAU;IACvB,OAAO,IAAI,CAACL,IAAI,CAACY,MAAM,CAAC,GAAG,IAAI,CAACX,MAAM,4BAA4BI,EAAE,EAAE,CAAC;EACzE;EAEA;EACAQ,SAASA,CAACC,UAAkB,EAAEC,MAAsB;IAClD,OAAO,IAAI,CAACf,IAAI,CAACQ,IAAI,CAAiB,GAAG,IAAI,CAACP,MAAM,4BAA4Ba,UAAU,UAAU,EAAEC,MAAM,CAAC;EAC/G;EAEAC,YAAYA,CAACF,UAAkB,EAAEG,QAAgB,EAAEF,MAAsB;IACvE,OAAO,IAAI,CAACf,IAAI,CAACU,GAAG,CAAiB,GAAG,IAAI,CAACT,MAAM,4BAA4Ba,UAAU,YAAYG,QAAQ,EAAE,EAAEF,MAAM,CAAC;EAC1H;EAEAG,YAAYA,CAACJ,UAAkB,EAAEG,QAAgB;IAC/C,OAAO,IAAI,CAACjB,IAAI,CAACY,MAAM,CAAC,GAAG,IAAI,CAACX,MAAM,4BAA4Ba,UAAU,YAAYG,QAAQ,EAAE,CAAC;EACrG;;;uCApCWnB,eAAe,EAAAqB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAfxB,eAAe;MAAAyB,OAAA,EAAfzB,eAAe,CAAA0B,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}