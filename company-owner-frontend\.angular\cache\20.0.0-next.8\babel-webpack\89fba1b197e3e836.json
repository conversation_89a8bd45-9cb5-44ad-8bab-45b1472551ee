{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Ng<PERSON>one, Injectable, ElementRef, Directive } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { Subject, Observable, takeWhile, switchMap, merge, fromEvent, take, takeUntil, finalize } from 'rxjs';\n\n/**\n * https://github.com/gre/bezier-easing\n * BezierEasing - use bezier curve for transition easing function\n * by <PERSON><PERSON><PERSON><PERSON> 2014 - 2015 – MIT License\n */\n// These values are established by empiricism with tests (tradeoff: performance VS precision)\nconst NEWTON_ITERATIONS = 4;\nconst NEWTON_MIN_SLOPE = 0.001;\nconst SUBDIVISION_PRECISION = 0.0000001;\nconst SUBDIVISION_MAX_ITERATIONS = 10;\nconst kSplineTableSize = 11;\nconst kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\nconst float32ArraySupported = typeof Float32Array === 'function';\nfunction A(aA1, aA2) {\n  return 1.0 - 3.0 * aA2 + 3.0 * aA1;\n}\nfunction B(aA1, aA2) {\n  return 3.0 * aA2 - 6.0 * aA1;\n}\nfunction C(aA1) {\n  return 3.0 * aA1;\n}\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nfunction calcBezier(aT, aA1, aA2) {\n  return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT;\n}\n// Returns dx/dt given t, x1, and x2, or dy/dt given t, y1, and y2.\nfunction getSlope(aT, aA1, aA2) {\n  return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1);\n}\nfunction binarySubdivide(aX, aA, aB, mX1, mX2) {\n  let currentX,\n    currentT,\n    i = 0;\n  do {\n    currentT = aA + (aB - aA) / 2.0;\n    currentX = calcBezier(currentT, mX1, mX2) - aX;\n    if (currentX > 0.0) {\n      aB = currentT;\n    } else {\n      aA = currentT;\n    }\n  } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);\n  return currentT;\n}\nfunction newtonRaphsonIterate(aX, aGuessT, mX1, mX2) {\n  for (let i = 0; i < NEWTON_ITERATIONS; ++i) {\n    const currentSlope = getSlope(aGuessT, mX1, mX2);\n    if (currentSlope === 0.0) {\n      return aGuessT;\n    }\n    const currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n    aGuessT -= currentX / currentSlope;\n  }\n  return aGuessT;\n}\nfunction LinearEasing(x) {\n  return x;\n}\nfunction bezier(mX1, mY1, mX2, mY2) {\n  if (!(0 <= mX1 && mX1 <= 1 && 0 <= mX2 && mX2 <= 1)) {\n    throw new Error('bezier x values must be in [0, 1] range');\n  }\n  if (mX1 === mY1 && mX2 === mY2) {\n    return LinearEasing;\n  }\n  // Precompute samples table\n  const sampleValues = float32ArraySupported ? new Float32Array(kSplineTableSize) : new Array(kSplineTableSize);\n  for (let i = 0; i < kSplineTableSize; ++i) {\n    sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n  }\n  function getTForX(aX) {\n    let intervalStart = 0.0;\n    let currentSample = 1;\n    const lastSample = kSplineTableSize - 1;\n    for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {\n      intervalStart += kSampleStepSize;\n    }\n    --currentSample;\n    // Interpolate to provide an initial guess for t\n    const dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n    const guessForT = intervalStart + dist * kSampleStepSize;\n    const initialSlope = getSlope(guessForT, mX1, mX2);\n    if (initialSlope >= NEWTON_MIN_SLOPE) {\n      return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n    } else if (initialSlope === 0.0) {\n      return guessForT;\n    } else {\n      return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);\n    }\n  }\n  return function BezierEasing(x) {\n    // Because JavaScript number are imprecise, we should guarantee the extremes are right.\n    if (x === 0) {\n      return 0;\n    }\n    if (x === 1) {\n      return 1;\n    }\n    return calcBezier(getTForX(x), mY1, mY2);\n  };\n}\nconst defaultSmoothScrollOptions = {\n  duration: 468,\n  easing: {\n    x1: 0.42,\n    y1: 0,\n    x2: 0.58,\n    y2: 1\n  }\n};\nconst SMOOTH_SCROLL_OPTIONS = new InjectionToken('SMOOTH_SCROLL_OPTIONS', {\n  providedIn: 'root',\n  factory: () => defaultSmoothScrollOptions\n});\nfunction provideSmoothScrollOptions(options) {\n  return [{\n    provide: SMOOTH_SCROLL_OPTIONS,\n    useValue: {\n      ...defaultSmoothScrollOptions,\n      ...options\n    }\n  }];\n}\nclass SmoothScrollManager {\n  constructor() {\n    this.document = inject(DOCUMENT);\n    this.zone = inject(NgZone);\n    // Default options\n    this._defaultOptions = inject(SMOOTH_SCROLL_OPTIONS);\n    // Keeps track of the ongoing SmoothScroll functions, so they can be handled in case of duplication.\n    // Each scrolled element gets a destroyer stream which gets deleted immediately after it completes.\n    // Purpose: If user called a scroll function again on the same element before the scrolls completes,\n    // it cancels the ongoing scroll and starts a new one\n    this.onGoingScrolls = new Map();\n  }\n  /**\n   * Timing method\n   */\n  get now() {\n    return this.document.defaultView.performance?.now?.bind(this.document.defaultView.performance) || Date.now;\n  }\n  /**\n   * changes scroll position inside an element\n   */\n  scrollElement(el, x, y) {\n    el.scrollLeft = x;\n    el.scrollTop = y;\n  }\n  /**\n   * Handles a given parameter of type HTMLElement, ElementRef or selector\n   */\n  getElement(el, parent) {\n    if (typeof el === 'string') {\n      return (parent || this.document).querySelector(el);\n    }\n    return coerceElement(el);\n  }\n  /**\n   * Initializes a destroyer stream, re-initializes it if the element is already being scrolled\n   */\n  getScrollDestroyerRef(el) {\n    if (this.onGoingScrolls.has(el)) {\n      this.onGoingScrolls.get(el).next();\n    }\n    return this.onGoingScrolls.set(el, new Subject()).get(el);\n  }\n  /**\n   * A function called recursively that, given a context, steps through scrolling\n   */\n  step(context) {\n    return new Observable(subscriber => {\n      let elapsed = (this.now() - context.startTime) / context.duration;\n      // avoid elapsed times higher than one\n      elapsed = elapsed > 1 ? 1 : elapsed;\n      // apply easing to elapsed time\n      const value = context.easing(elapsed);\n      context.currentX = context.startX + (context.x - context.startX) * value;\n      context.currentY = context.startY + (context.y - context.startY) * value;\n      this.scrollElement(context.scrollable, context.currentX, context.currentY);\n      // Proceed to the step\n      requestAnimationFrame(() => {\n        subscriber.next();\n        subscriber.complete();\n      });\n    });\n  }\n  /**\n   * Checks if smooth scroll has reached, cleans up the smooth scroll stream\n   */\n  isReached(context, destroyed) {\n    if (context.currentX === context.x && context.currentY === context.y) {\n      // IMPORTANT: Destroy the stream when scroll is reached ASAP!\n      destroyed.next();\n      return true;\n    }\n    return false;\n  }\n  /**\n   * Scroll recursively until coordinates are reached\n   * @param context\n   * @param destroyed\n   */\n  scrolling(context, destroyed) {\n    return this.step(context).pipe(\n    // Continue while target coordinates hasn't reached yet\n    takeWhile(() => !this.isReached(context, destroyed)), switchMap(() => this.scrolling(context, destroyed)));\n  }\n  /**\n   * Deletes the destroyer function, runs if the smooth scroll has finished or interrupted\n   */\n  onScrollReached(el, resolve, destroyed) {\n    destroyed.complete();\n    this.onGoingScrolls.delete(el);\n    this.zone.run(() => resolve());\n  }\n  /**\n   * Terminates an ongoing smooth scroll\n   */\n  interrupted(el, destroyed) {\n    return merge(fromEvent(el, 'wheel', {\n      passive: true,\n      capture: true\n    }), fromEvent(el, 'touchmove', {\n      passive: true,\n      capture: true\n    }), destroyed).pipe(take(1));\n  }\n  applyScrollToOptions(el, options) {\n    if (!options.duration) {\n      this.scrollElement(el, options.left, options.top);\n      return Promise.resolve();\n    }\n    return new Promise(resolve => {\n      this.zone.runOutsideAngular(() => {\n        // Initialize a destroyer stream, reinitialize it if the element is already being scrolled\n        const destroyed = this.getScrollDestroyerRef(el);\n        const context = {\n          scrollable: el,\n          startTime: this.now(),\n          startX: el.scrollLeft,\n          startY: el.scrollTop,\n          x: options.left == null ? el.scrollLeft : ~~options.left,\n          y: options.top == null ? el.scrollTop : ~~options.top,\n          duration: options.duration,\n          easing: bezier(options.easing.x1, options.easing.y1, options.easing.x2, options.easing.y2)\n        };\n        this.scrolling(context, destroyed).pipe(\n        // Continue until interrupted by another scroll (new smooth scroll / wheel / touchmove)\n        takeUntil(this.interrupted(el, destroyed)),\n        // Once finished, clean up the destroyer stream and resolve the promise\n        finalize(() => this.onScrollReached(el, resolve, destroyed))).subscribe();\n      });\n    });\n  }\n  /**\n   * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n   * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param scrollable element\n   * @param customOptions specified the offsets to scroll to.\n   */\n  scrollTo(scrollable, customOptions) {\n    const el = this.getElement(scrollable);\n    const isRtl = getComputedStyle(el).direction === 'rtl';\n    const options = {\n      ...this._defaultOptions,\n      ...customOptions,\n      ...{\n        // Rewrite start & end offsets as right or left offsets.\n        left: customOptions.left == null ? isRtl ? customOptions.end : customOptions.start : customOptions.left,\n        right: customOptions.right == null ? isRtl ? customOptions.start : customOptions.end : customOptions.right\n      }\n    };\n    // Rewrite the bottom offset as a top offset.\n    if (options.bottom != null) {\n      options.top = el.scrollHeight - el.clientHeight - options.bottom;\n    }\n    // Rewrite the right offset as a left offset.\n    if (isRtl) {\n      if (options.left != null) {\n        options.right = el.scrollWidth - el.clientWidth - options.left;\n      }\n      options.left = options.right ? -options.right : options.right;\n    } else {\n      if (options.right != null) {\n        options.left = el.scrollWidth - el.clientWidth - options.right;\n      }\n    }\n    return this.applyScrollToOptions(el, options);\n  }\n  /**\n   * Scroll to element by reference or selector\n   */\n  scrollToElement(scrollable, target, customOptions = {}) {\n    const scrollableEl = this.getElement(scrollable);\n    const targetEl = this.getElement(target, scrollableEl);\n    const isRtl = getComputedStyle(scrollableEl).direction === 'rtl';\n    if (!targetEl || !scrollableEl) {\n      return Promise.resolve();\n    }\n    const scrollableRect = scrollableEl.getBoundingClientRect();\n    const targetRect = targetEl.getBoundingClientRect();\n    const options = {\n      ...this._defaultOptions,\n      ...customOptions,\n      ...{\n        top: targetRect.top + scrollableEl.scrollTop - scrollableRect.top + (customOptions.top || 0),\n        // Rewrite start & end offsets as right or left offsets.\n        left: customOptions.left == null ? isRtl ? customOptions.end : customOptions.start : customOptions.left,\n        right: customOptions.right == null ? isRtl ? customOptions.start : customOptions.end : customOptions.right\n      }\n    };\n    if (customOptions.center) {\n      // Calculate the center of the container\n      const containerCenterX = scrollableRect.left + scrollableRect.width / 2;\n      const containerCenterY = scrollableRect.top + scrollableRect.height / 2;\n      // Calculate the target's position relative to the container\n      const targetCenterX = targetRect.left + targetRect.width / 2;\n      const targetCenterY = targetRect.top + targetRect.height / 2;\n      // Calculate the scroll position to center the target element in the container\n      options.left = targetCenterX - containerCenterX + scrollableEl.scrollLeft;\n      options.top = targetCenterY - containerCenterY + scrollableEl.scrollTop;\n      return this.applyScrollToOptions(scrollableEl, options);\n    }\n    if (options.bottom != null) {\n      const bottomEdge = scrollableRect.height - targetRect.height;\n      options.top = targetRect.top + scrollableEl.scrollTop - scrollableRect.top - bottomEdge + (customOptions.bottom || 0);\n    }\n    // Rewrite the right offset as a left offset.\n    if (isRtl) {\n      options.left = targetRect.left - scrollableRect.left + scrollableEl.scrollLeft + (options.left || 0);\n      if (options.right != null) {\n        options.left = targetRect.right - scrollableRect.left + scrollableEl.scrollLeft - scrollableRect.width + (options.right || 0);\n      }\n    } else {\n      options.left = targetRect.left - scrollableRect.left + scrollableEl.scrollLeft + (options.left || 0);\n      if (options.right != null) {\n        options.left = targetRect.right - scrollableRect.left + scrollableEl.scrollLeft - scrollableRect.width + (options.right || 0);\n      }\n    }\n    const computedOptions = {\n      top: options.top,\n      left: options.left,\n      easing: options.easing,\n      duration: options.duration\n    };\n    return this.applyScrollToOptions(scrollableEl, computedOptions);\n  }\n  static {\n    this.ɵfac = function SmoothScrollManager_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SmoothScrollManager)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SmoothScrollManager,\n      factory: SmoothScrollManager.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SmoothScrollManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass SmoothScroll {\n  constructor() {\n    this.smoothScroll = inject(SmoothScrollManager);\n    this.element = inject(ElementRef);\n  }\n  scrollTo(options) {\n    return this.smoothScroll.scrollTo(this.element, options);\n  }\n  scrollToElement(target, options) {\n    return this.smoothScroll.scrollToElement(this.element, target, options);\n  }\n  static {\n    this.ɵfac = function SmoothScroll_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SmoothScroll)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SmoothScroll,\n      selectors: [[\"\", \"smoothScroll\", \"\"]],\n      exportAs: [\"smoothScroll\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SmoothScroll, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[smoothScroll]',\n      exportAs: 'smoothScroll'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { SMOOTH_SCROLL_OPTIONS, SmoothScroll, SmoothScrollManager, provideSmoothScrollOptions };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "NgZone", "Injectable", "ElementRef", "Directive", "DOCUMENT", "coerceElement", "Subject", "Observable", "<PERSON><PERSON><PERSON><PERSON>", "switchMap", "merge", "fromEvent", "take", "takeUntil", "finalize", "NEWTON_ITERATIONS", "NEWTON_MIN_SLOPE", "SUBDIVISION_PRECISION", "SUBDIVISION_MAX_ITERATIONS", "kSplineTableSize", "kSampleStepSize", "float32ArraySupported", "Float32Array", "A", "aA1", "aA2", "B", "C", "calcBezier", "aT", "getSlope", "binarySubdivide", "aX", "aA", "aB", "mX1", "mX2", "currentX", "currentT", "i", "Math", "abs", "newtonRaphsonIterate", "aGuessT", "currentSlope", "LinearEasing", "x", "bezier", "mY1", "mY2", "Error", "sampleValues", "Array", "getTForX", "intervalStart", "currentSample", "lastSample", "dist", "guessForT", "initialSlope", "BezierEasing", "defaultSmoothScrollOptions", "duration", "easing", "x1", "y1", "x2", "y2", "SMOOTH_SCROLL_OPTIONS", "providedIn", "factory", "provideSmoothScrollOptions", "options", "provide", "useValue", "SmoothScrollManager", "constructor", "document", "zone", "_defaultOptions", "onGoingScrolls", "Map", "now", "defaultView", "performance", "bind", "Date", "scrollElement", "el", "y", "scrollLeft", "scrollTop", "getElement", "parent", "querySelector", "getScrollDestroyerRef", "has", "get", "next", "set", "step", "context", "subscriber", "elapsed", "startTime", "value", "startX", "currentY", "startY", "scrollable", "requestAnimationFrame", "complete", "isReached", "destroyed", "scrolling", "pipe", "onScrollReached", "resolve", "delete", "run", "interrupted", "passive", "capture", "applyScrollToOptions", "left", "top", "Promise", "runOutsideAngular", "subscribe", "scrollTo", "customOptions", "isRtl", "getComputedStyle", "direction", "end", "start", "right", "bottom", "scrollHeight", "clientHeight", "scrollWidth", "clientWidth", "scrollToElement", "target", "scrollableEl", "targetEl", "scrollableRect", "getBoundingClientRect", "targetRect", "center", "containerCenterX", "width", "containerCenterY", "height", "targetCenterX", "targetCenterY", "bottomEdge", "computedOptions", "ɵfac", "SmoothScrollManager_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "ngDevMode", "ɵsetClassMetadata", "type", "args", "SmoothScroll", "smoothScroll", "element", "SmoothScroll_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "exportAs", "standalone", "selector"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/ngx-scrollbar/fesm2022/ngx-scrollbar-smooth-scroll.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Ng<PERSON>one, Injectable, ElementRef, Directive } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { Subject, Observable, takeWhile, switchMap, merge, fromEvent, take, takeUntil, finalize } from 'rxjs';\n\n/**\n * https://github.com/gre/bezier-easing\n * BezierEasing - use bezier curve for transition easing function\n * by <PERSON><PERSON><PERSON><PERSON> 2014 - 2015 – MIT License\n */\n// These values are established by empiricism with tests (tradeoff: performance VS precision)\nconst NEWTON_ITERATIONS = 4;\nconst NEWTON_MIN_SLOPE = 0.001;\nconst SUBDIVISION_PRECISION = 0.0000001;\nconst SUBDIVISION_MAX_ITERATIONS = 10;\nconst kSplineTableSize = 11;\nconst kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\nconst float32ArraySupported = typeof Float32Array === 'function';\nfunction A(aA1, aA2) {\n    return 1.0 - 3.0 * aA2 + 3.0 * aA1;\n}\nfunction B(aA1, aA2) {\n    return 3.0 * aA2 - 6.0 * aA1;\n}\nfunction C(aA1) {\n    return 3.0 * aA1;\n}\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nfunction calcBezier(aT, aA1, aA2) {\n    return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT;\n}\n// Returns dx/dt given t, x1, and x2, or dy/dt given t, y1, and y2.\nfunction getSlope(aT, aA1, aA2) {\n    return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1);\n}\nfunction binarySubdivide(aX, aA, aB, mX1, mX2) {\n    let currentX, currentT, i = 0;\n    do {\n        currentT = aA + (aB - aA) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - aX;\n        if (currentX > 0.0) {\n            aB = currentT;\n        }\n        else {\n            aA = currentT;\n        }\n    } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);\n    return currentT;\n}\nfunction newtonRaphsonIterate(aX, aGuessT, mX1, mX2) {\n    for (let i = 0; i < NEWTON_ITERATIONS; ++i) {\n        const currentSlope = getSlope(aGuessT, mX1, mX2);\n        if (currentSlope === 0.0) {\n            return aGuessT;\n        }\n        const currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n        aGuessT -= currentX / currentSlope;\n    }\n    return aGuessT;\n}\nfunction LinearEasing(x) {\n    return x;\n}\nfunction bezier(mX1, mY1, mX2, mY2) {\n    if (!(0 <= mX1 && mX1 <= 1 && 0 <= mX2 && mX2 <= 1)) {\n        throw new Error('bezier x values must be in [0, 1] range');\n    }\n    if (mX1 === mY1 && mX2 === mY2) {\n        return LinearEasing;\n    }\n    // Precompute samples table\n    const sampleValues = float32ArraySupported ? new Float32Array(kSplineTableSize) : new Array(kSplineTableSize);\n    for (let i = 0; i < kSplineTableSize; ++i) {\n        sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n    }\n    function getTForX(aX) {\n        let intervalStart = 0.0;\n        let currentSample = 1;\n        const lastSample = kSplineTableSize - 1;\n        for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {\n            intervalStart += kSampleStepSize;\n        }\n        --currentSample;\n        // Interpolate to provide an initial guess for t\n        const dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n        const guessForT = intervalStart + dist * kSampleStepSize;\n        const initialSlope = getSlope(guessForT, mX1, mX2);\n        if (initialSlope >= NEWTON_MIN_SLOPE) {\n            return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n        }\n        else if (initialSlope === 0.0) {\n            return guessForT;\n        }\n        else {\n            return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);\n        }\n    }\n    return function BezierEasing(x) {\n        // Because JavaScript number are imprecise, we should guarantee the extremes are right.\n        if (x === 0) {\n            return 0;\n        }\n        if (x === 1) {\n            return 1;\n        }\n        return calcBezier(getTForX(x), mY1, mY2);\n    };\n}\n\nconst defaultSmoothScrollOptions = {\n    duration: 468,\n    easing: {\n        x1: 0.42,\n        y1: 0,\n        x2: 0.58,\n        y2: 1\n    }\n};\n\nconst SMOOTH_SCROLL_OPTIONS = new InjectionToken('SMOOTH_SCROLL_OPTIONS', {\n    providedIn: 'root',\n    factory: () => defaultSmoothScrollOptions\n});\nfunction provideSmoothScrollOptions(options) {\n    return [\n        {\n            provide: SMOOTH_SCROLL_OPTIONS,\n            useValue: { ...defaultSmoothScrollOptions, ...options }\n        }\n    ];\n}\n\nclass SmoothScrollManager {\n    constructor() {\n        this.document = inject(DOCUMENT);\n        this.zone = inject(NgZone);\n        // Default options\n        this._defaultOptions = inject(SMOOTH_SCROLL_OPTIONS);\n        // Keeps track of the ongoing SmoothScroll functions, so they can be handled in case of duplication.\n        // Each scrolled element gets a destroyer stream which gets deleted immediately after it completes.\n        // Purpose: If user called a scroll function again on the same element before the scrolls completes,\n        // it cancels the ongoing scroll and starts a new one\n        this.onGoingScrolls = new Map();\n    }\n    /**\n     * Timing method\n     */\n    get now() {\n        return this.document.defaultView.performance?.now?.bind(this.document.defaultView.performance) || Date.now;\n    }\n    /**\n     * changes scroll position inside an element\n     */\n    scrollElement(el, x, y) {\n        el.scrollLeft = x;\n        el.scrollTop = y;\n    }\n    /**\n     * Handles a given parameter of type HTMLElement, ElementRef or selector\n     */\n    getElement(el, parent) {\n        if (typeof el === 'string') {\n            return (parent || this.document).querySelector(el);\n        }\n        return coerceElement(el);\n    }\n    /**\n     * Initializes a destroyer stream, re-initializes it if the element is already being scrolled\n     */\n    getScrollDestroyerRef(el) {\n        if (this.onGoingScrolls.has(el)) {\n            this.onGoingScrolls.get(el).next();\n        }\n        return this.onGoingScrolls.set(el, new Subject()).get(el);\n    }\n    /**\n     * A function called recursively that, given a context, steps through scrolling\n     */\n    step(context) {\n        return new Observable((subscriber) => {\n            let elapsed = (this.now() - context.startTime) / context.duration;\n            // avoid elapsed times higher than one\n            elapsed = elapsed > 1 ? 1 : elapsed;\n            // apply easing to elapsed time\n            const value = context.easing(elapsed);\n            context.currentX = context.startX + (context.x - context.startX) * value;\n            context.currentY = context.startY + (context.y - context.startY) * value;\n            this.scrollElement(context.scrollable, context.currentX, context.currentY);\n            // Proceed to the step\n            requestAnimationFrame(() => {\n                subscriber.next();\n                subscriber.complete();\n            });\n        });\n    }\n    /**\n     * Checks if smooth scroll has reached, cleans up the smooth scroll stream\n     */\n    isReached(context, destroyed) {\n        if (context.currentX === context.x && context.currentY === context.y) {\n            // IMPORTANT: Destroy the stream when scroll is reached ASAP!\n            destroyed.next();\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Scroll recursively until coordinates are reached\n     * @param context\n     * @param destroyed\n     */\n    scrolling(context, destroyed) {\n        return this.step(context).pipe(\n        // Continue while target coordinates hasn't reached yet\n        takeWhile(() => !this.isReached(context, destroyed)), switchMap(() => this.scrolling(context, destroyed)));\n    }\n    /**\n     * Deletes the destroyer function, runs if the smooth scroll has finished or interrupted\n     */\n    onScrollReached(el, resolve, destroyed) {\n        destroyed.complete();\n        this.onGoingScrolls.delete(el);\n        this.zone.run(() => resolve());\n    }\n    /**\n     * Terminates an ongoing smooth scroll\n     */\n    interrupted(el, destroyed) {\n        return merge(fromEvent(el, 'wheel', { passive: true, capture: true }), fromEvent(el, 'touchmove', { passive: true, capture: true }), destroyed).pipe(take(1));\n    }\n    applyScrollToOptions(el, options) {\n        if (!options.duration) {\n            this.scrollElement(el, options.left, options.top);\n            return Promise.resolve();\n        }\n        return new Promise((resolve) => {\n            this.zone.runOutsideAngular(() => {\n                // Initialize a destroyer stream, reinitialize it if the element is already being scrolled\n                const destroyed = this.getScrollDestroyerRef(el);\n                const context = {\n                    scrollable: el,\n                    startTime: this.now(),\n                    startX: el.scrollLeft,\n                    startY: el.scrollTop,\n                    x: options.left == null ? el.scrollLeft : ~~options.left,\n                    y: options.top == null ? el.scrollTop : ~~options.top,\n                    duration: options.duration,\n                    easing: bezier(options.easing.x1, options.easing.y1, options.easing.x2, options.easing.y2)\n                };\n                this.scrolling(context, destroyed).pipe(\n                // Continue until interrupted by another scroll (new smooth scroll / wheel / touchmove)\n                takeUntil(this.interrupted(el, destroyed)), \n                // Once finished, clean up the destroyer stream and resolve the promise\n                finalize(() => this.onScrollReached(el, resolve, destroyed))).subscribe();\n            });\n        });\n    }\n    /**\n     * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n     * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param scrollable element\n     * @param customOptions specified the offsets to scroll to.\n     */\n    scrollTo(scrollable, customOptions) {\n        const el = this.getElement(scrollable);\n        const isRtl = getComputedStyle(el).direction === 'rtl';\n        const options = {\n            ...this._defaultOptions,\n            ...customOptions,\n            ...{\n                // Rewrite start & end offsets as right or left offsets.\n                left: customOptions.left == null ? (isRtl ? customOptions.end : customOptions.start) : customOptions.left,\n                right: customOptions.right == null ? (isRtl ? customOptions.start : customOptions.end) : customOptions.right\n            }\n        };\n        // Rewrite the bottom offset as a top offset.\n        if (options.bottom != null) {\n            options.top = el.scrollHeight - el.clientHeight - options.bottom;\n        }\n        // Rewrite the right offset as a left offset.\n        if (isRtl) {\n            if (options.left != null) {\n                options.right = el.scrollWidth - el.clientWidth - options.left;\n            }\n            options.left = options.right ? -options.right : options.right;\n        }\n        else {\n            if (options.right != null) {\n                options.left = el.scrollWidth - el.clientWidth - options.right;\n            }\n        }\n        return this.applyScrollToOptions(el, options);\n    }\n    /**\n     * Scroll to element by reference or selector\n     */\n    scrollToElement(scrollable, target, customOptions = {}) {\n        const scrollableEl = this.getElement(scrollable);\n        const targetEl = this.getElement(target, scrollableEl);\n        const isRtl = getComputedStyle(scrollableEl).direction === 'rtl';\n        if (!targetEl || !scrollableEl) {\n            return Promise.resolve();\n        }\n        const scrollableRect = scrollableEl.getBoundingClientRect();\n        const targetRect = targetEl.getBoundingClientRect();\n        const options = {\n            ...this._defaultOptions,\n            ...customOptions,\n            ...{\n                top: targetRect.top + scrollableEl.scrollTop - scrollableRect.top + (customOptions.top || 0),\n                // Rewrite start & end offsets as right or left offsets.\n                left: customOptions.left == null ? (isRtl ? customOptions.end : customOptions.start) : customOptions.left,\n                right: customOptions.right == null ? (isRtl ? customOptions.start : customOptions.end) : customOptions.right\n            }\n        };\n        if (customOptions.center) {\n            // Calculate the center of the container\n            const containerCenterX = scrollableRect.left + scrollableRect.width / 2;\n            const containerCenterY = scrollableRect.top + scrollableRect.height / 2;\n            // Calculate the target's position relative to the container\n            const targetCenterX = targetRect.left + targetRect.width / 2;\n            const targetCenterY = targetRect.top + targetRect.height / 2;\n            // Calculate the scroll position to center the target element in the container\n            options.left = targetCenterX - containerCenterX + scrollableEl.scrollLeft;\n            options.top = targetCenterY - containerCenterY + scrollableEl.scrollTop;\n            return this.applyScrollToOptions(scrollableEl, options);\n        }\n        if (options.bottom != null) {\n            const bottomEdge = scrollableRect.height - targetRect.height;\n            options.top = targetRect.top + scrollableEl.scrollTop - scrollableRect.top - bottomEdge + (customOptions.bottom || 0);\n        }\n        // Rewrite the right offset as a left offset.\n        if (isRtl) {\n            options.left = targetRect.left - scrollableRect.left + scrollableEl.scrollLeft + (options.left || 0);\n            if (options.right != null) {\n                options.left = targetRect.right - scrollableRect.left + scrollableEl.scrollLeft - scrollableRect.width + (options.right || 0);\n            }\n        }\n        else {\n            options.left = targetRect.left - scrollableRect.left + scrollableEl.scrollLeft + (options.left || 0);\n            if (options.right != null) {\n                options.left = targetRect.right - scrollableRect.left + scrollableEl.scrollLeft - scrollableRect.width + (options.right || 0);\n            }\n        }\n        const computedOptions = {\n            top: options.top,\n            left: options.left,\n            easing: options.easing,\n            duration: options.duration\n        };\n        return this.applyScrollToOptions(scrollableEl, computedOptions);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: SmoothScrollManager, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: SmoothScrollManager, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: SmoothScrollManager, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }] });\n\nclass SmoothScroll {\n    constructor() {\n        this.smoothScroll = inject(SmoothScrollManager);\n        this.element = inject((ElementRef));\n    }\n    scrollTo(options) {\n        return this.smoothScroll.scrollTo(this.element, options);\n    }\n    scrollToElement(target, options) {\n        return this.smoothScroll.scrollToElement(this.element, target, options);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: SmoothScroll, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.1.1\", type: SmoothScroll, isStandalone: true, selector: \"[smoothScroll]\", exportAs: [\"smoothScroll\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.1\", ngImport: i0, type: SmoothScroll, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[smoothScroll]',\n                    exportAs: 'smoothScroll'\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { SMOOTH_SCROLL_OPTIONS, SmoothScroll, SmoothScrollManager, provideSmoothScrollOptions };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,eAAe;AACjG,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,MAAM;;AAE7G;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,CAAC;AAC3B,MAAMC,gBAAgB,GAAG,KAAK;AAC9B,MAAMC,qBAAqB,GAAG,SAAS;AACvC,MAAMC,0BAA0B,GAAG,EAAE;AACrC,MAAMC,gBAAgB,GAAG,EAAE;AAC3B,MAAMC,eAAe,GAAG,GAAG,IAAID,gBAAgB,GAAG,GAAG,CAAC;AACtD,MAAME,qBAAqB,GAAG,OAAOC,YAAY,KAAK,UAAU;AAChE,SAASC,CAACA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACjB,OAAO,GAAG,GAAG,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAGD,GAAG;AACtC;AACA,SAASE,CAACA,CAACF,GAAG,EAAEC,GAAG,EAAE;EACjB,OAAO,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAGD,GAAG;AAChC;AACA,SAASG,CAACA,CAACH,GAAG,EAAE;EACZ,OAAO,GAAG,GAAGA,GAAG;AACpB;AACA;AACA,SAASI,UAAUA,CAACC,EAAE,EAAEL,GAAG,EAAEC,GAAG,EAAE;EAC9B,OAAO,CAAC,CAACF,CAAC,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGI,EAAE,GAAGH,CAAC,CAACF,GAAG,EAAEC,GAAG,CAAC,IAAII,EAAE,GAAGF,CAAC,CAACH,GAAG,CAAC,IAAIK,EAAE;AAChE;AACA;AACA,SAASC,QAAQA,CAACD,EAAE,EAAEL,GAAG,EAAEC,GAAG,EAAE;EAC5B,OAAO,GAAG,GAAGF,CAAC,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGI,EAAE,GAAGA,EAAE,GAAG,GAAG,GAAGH,CAAC,CAACF,GAAG,EAAEC,GAAG,CAAC,GAAGI,EAAE,GAAGF,CAAC,CAACH,GAAG,CAAC;AACxE;AACA,SAASO,eAAeA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC3C,IAAIC,QAAQ;IAAEC,QAAQ;IAAEC,CAAC,GAAG,CAAC;EAC7B,GAAG;IACCD,QAAQ,GAAGL,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,IAAI,GAAG;IAC/BI,QAAQ,GAAGT,UAAU,CAACU,QAAQ,EAAEH,GAAG,EAAEC,GAAG,CAAC,GAAGJ,EAAE;IAC9C,IAAIK,QAAQ,GAAG,GAAG,EAAE;MAChBH,EAAE,GAAGI,QAAQ;IACjB,CAAC,MACI;MACDL,EAAE,GAAGK,QAAQ;IACjB;EACJ,CAAC,QAAQE,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAC,GAAGpB,qBAAqB,IAAI,EAAEsB,CAAC,GAAGrB,0BAA0B;EACvF,OAAOoB,QAAQ;AACnB;AACA,SAASI,oBAAoBA,CAACV,EAAE,EAAEW,OAAO,EAAER,GAAG,EAAEC,GAAG,EAAE;EACjD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,iBAAiB,EAAE,EAAEwB,CAAC,EAAE;IACxC,MAAMK,YAAY,GAAGd,QAAQ,CAACa,OAAO,EAAER,GAAG,EAAEC,GAAG,CAAC;IAChD,IAAIQ,YAAY,KAAK,GAAG,EAAE;MACtB,OAAOD,OAAO;IAClB;IACA,MAAMN,QAAQ,GAAGT,UAAU,CAACe,OAAO,EAAER,GAAG,EAAEC,GAAG,CAAC,GAAGJ,EAAE;IACnDW,OAAO,IAAIN,QAAQ,GAAGO,YAAY;EACtC;EACA,OAAOD,OAAO;AAClB;AACA,SAASE,YAAYA,CAACC,CAAC,EAAE;EACrB,OAAOA,CAAC;AACZ;AACA,SAASC,MAAMA,CAACZ,GAAG,EAAEa,GAAG,EAAEZ,GAAG,EAAEa,GAAG,EAAE;EAChC,IAAI,EAAE,CAAC,IAAId,GAAG,IAAIA,GAAG,IAAI,CAAC,IAAI,CAAC,IAAIC,GAAG,IAAIA,GAAG,IAAI,CAAC,CAAC,EAAE;IACjD,MAAM,IAAIc,KAAK,CAAC,yCAAyC,CAAC;EAC9D;EACA,IAAIf,GAAG,KAAKa,GAAG,IAAIZ,GAAG,KAAKa,GAAG,EAAE;IAC5B,OAAOJ,YAAY;EACvB;EACA;EACA,MAAMM,YAAY,GAAG9B,qBAAqB,GAAG,IAAIC,YAAY,CAACH,gBAAgB,CAAC,GAAG,IAAIiC,KAAK,CAACjC,gBAAgB,CAAC;EAC7G,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,gBAAgB,EAAE,EAAEoB,CAAC,EAAE;IACvCY,YAAY,CAACZ,CAAC,CAAC,GAAGX,UAAU,CAACW,CAAC,GAAGnB,eAAe,EAAEe,GAAG,EAAEC,GAAG,CAAC;EAC/D;EACA,SAASiB,QAAQA,CAACrB,EAAE,EAAE;IAClB,IAAIsB,aAAa,GAAG,GAAG;IACvB,IAAIC,aAAa,GAAG,CAAC;IACrB,MAAMC,UAAU,GAAGrC,gBAAgB,GAAG,CAAC;IACvC,OAAOoC,aAAa,KAAKC,UAAU,IAAIL,YAAY,CAACI,aAAa,CAAC,IAAIvB,EAAE,EAAE,EAAEuB,aAAa,EAAE;MACvFD,aAAa,IAAIlC,eAAe;IACpC;IACA,EAAEmC,aAAa;IACf;IACA,MAAME,IAAI,GAAG,CAACzB,EAAE,GAAGmB,YAAY,CAACI,aAAa,CAAC,KAAKJ,YAAY,CAACI,aAAa,GAAG,CAAC,CAAC,GAAGJ,YAAY,CAACI,aAAa,CAAC,CAAC;IACjH,MAAMG,SAAS,GAAGJ,aAAa,GAAGG,IAAI,GAAGrC,eAAe;IACxD,MAAMuC,YAAY,GAAG7B,QAAQ,CAAC4B,SAAS,EAAEvB,GAAG,EAAEC,GAAG,CAAC;IAClD,IAAIuB,YAAY,IAAI3C,gBAAgB,EAAE;MAClC,OAAO0B,oBAAoB,CAACV,EAAE,EAAE0B,SAAS,EAAEvB,GAAG,EAAEC,GAAG,CAAC;IACxD,CAAC,MACI,IAAIuB,YAAY,KAAK,GAAG,EAAE;MAC3B,OAAOD,SAAS;IACpB,CAAC,MACI;MACD,OAAO3B,eAAe,CAACC,EAAE,EAAEsB,aAAa,EAAEA,aAAa,GAAGlC,eAAe,EAAEe,GAAG,EAAEC,GAAG,CAAC;IACxF;EACJ;EACA,OAAO,SAASwB,YAAYA,CAACd,CAAC,EAAE;IAC5B;IACA,IAAIA,CAAC,KAAK,CAAC,EAAE;MACT,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,KAAK,CAAC,EAAE;MACT,OAAO,CAAC;IACZ;IACA,OAAOlB,UAAU,CAACyB,QAAQ,CAACP,CAAC,CAAC,EAAEE,GAAG,EAAEC,GAAG,CAAC;EAC5C,CAAC;AACL;AAEA,MAAMY,0BAA0B,GAAG;EAC/BC,QAAQ,EAAE,GAAG;EACbC,MAAM,EAAE;IACJC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE;EACR;AACJ,CAAC;AAED,MAAMC,qBAAqB,GAAG,IAAItE,cAAc,CAAC,uBAAuB,EAAE;EACtEuE,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAMT;AACnB,CAAC,CAAC;AACF,SAASU,0BAA0BA,CAACC,OAAO,EAAE;EACzC,OAAO,CACH;IACIC,OAAO,EAAEL,qBAAqB;IAC9BM,QAAQ,EAAE;MAAE,GAAGb,0BAA0B;MAAE,GAAGW;IAAQ;EAC1D,CAAC,CACJ;AACL;AAEA,MAAMG,mBAAmB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG9E,MAAM,CAACK,QAAQ,CAAC;IAChC,IAAI,CAAC0E,IAAI,GAAG/E,MAAM,CAACC,MAAM,CAAC;IAC1B;IACA,IAAI,CAAC+E,eAAe,GAAGhF,MAAM,CAACqE,qBAAqB,CAAC;IACpD;IACA;IACA;IACA;IACA,IAAI,CAACY,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EACnC;EACA;AACJ;AACA;EACI,IAAIC,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACL,QAAQ,CAACM,WAAW,CAACC,WAAW,EAAEF,GAAG,EAAEG,IAAI,CAAC,IAAI,CAACR,QAAQ,CAACM,WAAW,CAACC,WAAW,CAAC,IAAIE,IAAI,CAACJ,GAAG;EAC9G;EACA;AACJ;AACA;EACIK,aAAaA,CAACC,EAAE,EAAE1C,CAAC,EAAE2C,CAAC,EAAE;IACpBD,EAAE,CAACE,UAAU,GAAG5C,CAAC;IACjB0C,EAAE,CAACG,SAAS,GAAGF,CAAC;EACpB;EACA;AACJ;AACA;EACIG,UAAUA,CAACJ,EAAE,EAAEK,MAAM,EAAE;IACnB,IAAI,OAAOL,EAAE,KAAK,QAAQ,EAAE;MACxB,OAAO,CAACK,MAAM,IAAI,IAAI,CAAChB,QAAQ,EAAEiB,aAAa,CAACN,EAAE,CAAC;IACtD;IACA,OAAOnF,aAAa,CAACmF,EAAE,CAAC;EAC5B;EACA;AACJ;AACA;EACIO,qBAAqBA,CAACP,EAAE,EAAE;IACtB,IAAI,IAAI,CAACR,cAAc,CAACgB,GAAG,CAACR,EAAE,CAAC,EAAE;MAC7B,IAAI,CAACR,cAAc,CAACiB,GAAG,CAACT,EAAE,CAAC,CAACU,IAAI,CAAC,CAAC;IACtC;IACA,OAAO,IAAI,CAAClB,cAAc,CAACmB,GAAG,CAACX,EAAE,EAAE,IAAIlF,OAAO,CAAC,CAAC,CAAC,CAAC2F,GAAG,CAACT,EAAE,CAAC;EAC7D;EACA;AACJ;AACA;EACIY,IAAIA,CAACC,OAAO,EAAE;IACV,OAAO,IAAI9F,UAAU,CAAE+F,UAAU,IAAK;MAClC,IAAIC,OAAO,GAAG,CAAC,IAAI,CAACrB,GAAG,CAAC,CAAC,GAAGmB,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACvC,QAAQ;MACjE;MACAyC,OAAO,GAAGA,OAAO,GAAG,CAAC,GAAG,CAAC,GAAGA,OAAO;MACnC;MACA,MAAME,KAAK,GAAGJ,OAAO,CAACtC,MAAM,CAACwC,OAAO,CAAC;MACrCF,OAAO,CAAChE,QAAQ,GAAGgE,OAAO,CAACK,MAAM,GAAG,CAACL,OAAO,CAACvD,CAAC,GAAGuD,OAAO,CAACK,MAAM,IAAID,KAAK;MACxEJ,OAAO,CAACM,QAAQ,GAAGN,OAAO,CAACO,MAAM,GAAG,CAACP,OAAO,CAACZ,CAAC,GAAGY,OAAO,CAACO,MAAM,IAAIH,KAAK;MACxE,IAAI,CAAClB,aAAa,CAACc,OAAO,CAACQ,UAAU,EAAER,OAAO,CAAChE,QAAQ,EAAEgE,OAAO,CAACM,QAAQ,CAAC;MAC1E;MACAG,qBAAqB,CAAC,MAAM;QACxBR,UAAU,CAACJ,IAAI,CAAC,CAAC;QACjBI,UAAU,CAACS,QAAQ,CAAC,CAAC;MACzB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIC,SAASA,CAACX,OAAO,EAAEY,SAAS,EAAE;IAC1B,IAAIZ,OAAO,CAAChE,QAAQ,KAAKgE,OAAO,CAACvD,CAAC,IAAIuD,OAAO,CAACM,QAAQ,KAAKN,OAAO,CAACZ,CAAC,EAAE;MAClE;MACAwB,SAAS,CAACf,IAAI,CAAC,CAAC;MAChB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;EACIgB,SAASA,CAACb,OAAO,EAAEY,SAAS,EAAE;IAC1B,OAAO,IAAI,CAACb,IAAI,CAACC,OAAO,CAAC,CAACc,IAAI;IAC9B;IACA3G,SAAS,CAAC,MAAM,CAAC,IAAI,CAACwG,SAAS,CAACX,OAAO,EAAEY,SAAS,CAAC,CAAC,EAAExG,SAAS,CAAC,MAAM,IAAI,CAACyG,SAAS,CAACb,OAAO,EAAEY,SAAS,CAAC,CAAC,CAAC;EAC9G;EACA;AACJ;AACA;EACIG,eAAeA,CAAC5B,EAAE,EAAE6B,OAAO,EAAEJ,SAAS,EAAE;IACpCA,SAAS,CAACF,QAAQ,CAAC,CAAC;IACpB,IAAI,CAAC/B,cAAc,CAACsC,MAAM,CAAC9B,EAAE,CAAC;IAC9B,IAAI,CAACV,IAAI,CAACyC,GAAG,CAAC,MAAMF,OAAO,CAAC,CAAC,CAAC;EAClC;EACA;AACJ;AACA;EACIG,WAAWA,CAAChC,EAAE,EAAEyB,SAAS,EAAE;IACvB,OAAOvG,KAAK,CAACC,SAAS,CAAC6E,EAAE,EAAE,OAAO,EAAE;MAAEiC,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,EAAE/G,SAAS,CAAC6E,EAAE,EAAE,WAAW,EAAE;MAAEiC,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,EAAET,SAAS,CAAC,CAACE,IAAI,CAACvG,IAAI,CAAC,CAAC,CAAC,CAAC;EACjK;EACA+G,oBAAoBA,CAACnC,EAAE,EAAEhB,OAAO,EAAE;IAC9B,IAAI,CAACA,OAAO,CAACV,QAAQ,EAAE;MACnB,IAAI,CAACyB,aAAa,CAACC,EAAE,EAAEhB,OAAO,CAACoD,IAAI,EAAEpD,OAAO,CAACqD,GAAG,CAAC;MACjD,OAAOC,OAAO,CAACT,OAAO,CAAC,CAAC;IAC5B;IACA,OAAO,IAAIS,OAAO,CAAET,OAAO,IAAK;MAC5B,IAAI,CAACvC,IAAI,CAACiD,iBAAiB,CAAC,MAAM;QAC9B;QACA,MAAMd,SAAS,GAAG,IAAI,CAAClB,qBAAqB,CAACP,EAAE,CAAC;QAChD,MAAMa,OAAO,GAAG;UACZQ,UAAU,EAAErB,EAAE;UACdgB,SAAS,EAAE,IAAI,CAACtB,GAAG,CAAC,CAAC;UACrBwB,MAAM,EAAElB,EAAE,CAACE,UAAU;UACrBkB,MAAM,EAAEpB,EAAE,CAACG,SAAS;UACpB7C,CAAC,EAAE0B,OAAO,CAACoD,IAAI,IAAI,IAAI,GAAGpC,EAAE,CAACE,UAAU,GAAG,CAAC,CAAClB,OAAO,CAACoD,IAAI;UACxDnC,CAAC,EAAEjB,OAAO,CAACqD,GAAG,IAAI,IAAI,GAAGrC,EAAE,CAACG,SAAS,GAAG,CAAC,CAACnB,OAAO,CAACqD,GAAG;UACrD/D,QAAQ,EAAEU,OAAO,CAACV,QAAQ;UAC1BC,MAAM,EAAEhB,MAAM,CAACyB,OAAO,CAACT,MAAM,CAACC,EAAE,EAAEQ,OAAO,CAACT,MAAM,CAACE,EAAE,EAAEO,OAAO,CAACT,MAAM,CAACG,EAAE,EAAEM,OAAO,CAACT,MAAM,CAACI,EAAE;QAC7F,CAAC;QACD,IAAI,CAAC+C,SAAS,CAACb,OAAO,EAAEY,SAAS,CAAC,CAACE,IAAI;QACvC;QACAtG,SAAS,CAAC,IAAI,CAAC2G,WAAW,CAAChC,EAAE,EAAEyB,SAAS,CAAC,CAAC;QAC1C;QACAnG,QAAQ,CAAC,MAAM,IAAI,CAACsG,eAAe,CAAC5B,EAAE,EAAE6B,OAAO,EAAEJ,SAAS,CAAC,CAAC,CAAC,CAACe,SAAS,CAAC,CAAC;MAC7E,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,QAAQA,CAACpB,UAAU,EAAEqB,aAAa,EAAE;IAChC,MAAM1C,EAAE,GAAG,IAAI,CAACI,UAAU,CAACiB,UAAU,CAAC;IACtC,MAAMsB,KAAK,GAAGC,gBAAgB,CAAC5C,EAAE,CAAC,CAAC6C,SAAS,KAAK,KAAK;IACtD,MAAM7D,OAAO,GAAG;MACZ,GAAG,IAAI,CAACO,eAAe;MACvB,GAAGmD,aAAa;MAChB,GAAG;QACC;QACAN,IAAI,EAAEM,aAAa,CAACN,IAAI,IAAI,IAAI,GAAIO,KAAK,GAAGD,aAAa,CAACI,GAAG,GAAGJ,aAAa,CAACK,KAAK,GAAIL,aAAa,CAACN,IAAI;QACzGY,KAAK,EAAEN,aAAa,CAACM,KAAK,IAAI,IAAI,GAAIL,KAAK,GAAGD,aAAa,CAACK,KAAK,GAAGL,aAAa,CAACI,GAAG,GAAIJ,aAAa,CAACM;MAC3G;IACJ,CAAC;IACD;IACA,IAAIhE,OAAO,CAACiE,MAAM,IAAI,IAAI,EAAE;MACxBjE,OAAO,CAACqD,GAAG,GAAGrC,EAAE,CAACkD,YAAY,GAAGlD,EAAE,CAACmD,YAAY,GAAGnE,OAAO,CAACiE,MAAM;IACpE;IACA;IACA,IAAIN,KAAK,EAAE;MACP,IAAI3D,OAAO,CAACoD,IAAI,IAAI,IAAI,EAAE;QACtBpD,OAAO,CAACgE,KAAK,GAAGhD,EAAE,CAACoD,WAAW,GAAGpD,EAAE,CAACqD,WAAW,GAAGrE,OAAO,CAACoD,IAAI;MAClE;MACApD,OAAO,CAACoD,IAAI,GAAGpD,OAAO,CAACgE,KAAK,GAAG,CAAChE,OAAO,CAACgE,KAAK,GAAGhE,OAAO,CAACgE,KAAK;IACjE,CAAC,MACI;MACD,IAAIhE,OAAO,CAACgE,KAAK,IAAI,IAAI,EAAE;QACvBhE,OAAO,CAACoD,IAAI,GAAGpC,EAAE,CAACoD,WAAW,GAAGpD,EAAE,CAACqD,WAAW,GAAGrE,OAAO,CAACgE,KAAK;MAClE;IACJ;IACA,OAAO,IAAI,CAACb,oBAAoB,CAACnC,EAAE,EAAEhB,OAAO,CAAC;EACjD;EACA;AACJ;AACA;EACIsE,eAAeA,CAACjC,UAAU,EAAEkC,MAAM,EAAEb,aAAa,GAAG,CAAC,CAAC,EAAE;IACpD,MAAMc,YAAY,GAAG,IAAI,CAACpD,UAAU,CAACiB,UAAU,CAAC;IAChD,MAAMoC,QAAQ,GAAG,IAAI,CAACrD,UAAU,CAACmD,MAAM,EAAEC,YAAY,CAAC;IACtD,MAAMb,KAAK,GAAGC,gBAAgB,CAACY,YAAY,CAAC,CAACX,SAAS,KAAK,KAAK;IAChE,IAAI,CAACY,QAAQ,IAAI,CAACD,YAAY,EAAE;MAC5B,OAAOlB,OAAO,CAACT,OAAO,CAAC,CAAC;IAC5B;IACA,MAAM6B,cAAc,GAAGF,YAAY,CAACG,qBAAqB,CAAC,CAAC;IAC3D,MAAMC,UAAU,GAAGH,QAAQ,CAACE,qBAAqB,CAAC,CAAC;IACnD,MAAM3E,OAAO,GAAG;MACZ,GAAG,IAAI,CAACO,eAAe;MACvB,GAAGmD,aAAa;MAChB,GAAG;QACCL,GAAG,EAAEuB,UAAU,CAACvB,GAAG,GAAGmB,YAAY,CAACrD,SAAS,GAAGuD,cAAc,CAACrB,GAAG,IAAIK,aAAa,CAACL,GAAG,IAAI,CAAC,CAAC;QAC5F;QACAD,IAAI,EAAEM,aAAa,CAACN,IAAI,IAAI,IAAI,GAAIO,KAAK,GAAGD,aAAa,CAACI,GAAG,GAAGJ,aAAa,CAACK,KAAK,GAAIL,aAAa,CAACN,IAAI;QACzGY,KAAK,EAAEN,aAAa,CAACM,KAAK,IAAI,IAAI,GAAIL,KAAK,GAAGD,aAAa,CAACK,KAAK,GAAGL,aAAa,CAACI,GAAG,GAAIJ,aAAa,CAACM;MAC3G;IACJ,CAAC;IACD,IAAIN,aAAa,CAACmB,MAAM,EAAE;MACtB;MACA,MAAMC,gBAAgB,GAAGJ,cAAc,CAACtB,IAAI,GAAGsB,cAAc,CAACK,KAAK,GAAG,CAAC;MACvE,MAAMC,gBAAgB,GAAGN,cAAc,CAACrB,GAAG,GAAGqB,cAAc,CAACO,MAAM,GAAG,CAAC;MACvE;MACA,MAAMC,aAAa,GAAGN,UAAU,CAACxB,IAAI,GAAGwB,UAAU,CAACG,KAAK,GAAG,CAAC;MAC5D,MAAMI,aAAa,GAAGP,UAAU,CAACvB,GAAG,GAAGuB,UAAU,CAACK,MAAM,GAAG,CAAC;MAC5D;MACAjF,OAAO,CAACoD,IAAI,GAAG8B,aAAa,GAAGJ,gBAAgB,GAAGN,YAAY,CAACtD,UAAU;MACzElB,OAAO,CAACqD,GAAG,GAAG8B,aAAa,GAAGH,gBAAgB,GAAGR,YAAY,CAACrD,SAAS;MACvE,OAAO,IAAI,CAACgC,oBAAoB,CAACqB,YAAY,EAAExE,OAAO,CAAC;IAC3D;IACA,IAAIA,OAAO,CAACiE,MAAM,IAAI,IAAI,EAAE;MACxB,MAAMmB,UAAU,GAAGV,cAAc,CAACO,MAAM,GAAGL,UAAU,CAACK,MAAM;MAC5DjF,OAAO,CAACqD,GAAG,GAAGuB,UAAU,CAACvB,GAAG,GAAGmB,YAAY,CAACrD,SAAS,GAAGuD,cAAc,CAACrB,GAAG,GAAG+B,UAAU,IAAI1B,aAAa,CAACO,MAAM,IAAI,CAAC,CAAC;IACzH;IACA;IACA,IAAIN,KAAK,EAAE;MACP3D,OAAO,CAACoD,IAAI,GAAGwB,UAAU,CAACxB,IAAI,GAAGsB,cAAc,CAACtB,IAAI,GAAGoB,YAAY,CAACtD,UAAU,IAAIlB,OAAO,CAACoD,IAAI,IAAI,CAAC,CAAC;MACpG,IAAIpD,OAAO,CAACgE,KAAK,IAAI,IAAI,EAAE;QACvBhE,OAAO,CAACoD,IAAI,GAAGwB,UAAU,CAACZ,KAAK,GAAGU,cAAc,CAACtB,IAAI,GAAGoB,YAAY,CAACtD,UAAU,GAAGwD,cAAc,CAACK,KAAK,IAAI/E,OAAO,CAACgE,KAAK,IAAI,CAAC,CAAC;MACjI;IACJ,CAAC,MACI;MACDhE,OAAO,CAACoD,IAAI,GAAGwB,UAAU,CAACxB,IAAI,GAAGsB,cAAc,CAACtB,IAAI,GAAGoB,YAAY,CAACtD,UAAU,IAAIlB,OAAO,CAACoD,IAAI,IAAI,CAAC,CAAC;MACpG,IAAIpD,OAAO,CAACgE,KAAK,IAAI,IAAI,EAAE;QACvBhE,OAAO,CAACoD,IAAI,GAAGwB,UAAU,CAACZ,KAAK,GAAGU,cAAc,CAACtB,IAAI,GAAGoB,YAAY,CAACtD,UAAU,GAAGwD,cAAc,CAACK,KAAK,IAAI/E,OAAO,CAACgE,KAAK,IAAI,CAAC,CAAC;MACjI;IACJ;IACA,MAAMqB,eAAe,GAAG;MACpBhC,GAAG,EAAErD,OAAO,CAACqD,GAAG;MAChBD,IAAI,EAAEpD,OAAO,CAACoD,IAAI;MAClB7D,MAAM,EAAES,OAAO,CAACT,MAAM;MACtBD,QAAQ,EAAEU,OAAO,CAACV;IACtB,CAAC;IACD,OAAO,IAAI,CAAC6D,oBAAoB,CAACqB,YAAY,EAAEa,eAAe,CAAC;EACnE;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,4BAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFrF,mBAAmB;IAAA,CAAoD;EAAE;EACnL;IAAS,IAAI,CAACsF,KAAK,kBAD6EpK,EAAE,CAAAqK,kBAAA;MAAAC,KAAA,EACYxF,mBAAmB;MAAAL,OAAA,EAAnBK,mBAAmB,CAAAmF,IAAA;MAAAzF,UAAA,EAAc;IAAM,EAAG;EAAE;AAC9J;AACA;EAAA,QAAA+F,SAAA,oBAAAA,SAAA,KAHoGvK,EAAE,CAAAwK,iBAAA,CAGX1F,mBAAmB,EAAc,CAAC;IACjH2F,IAAI,EAAErK,UAAU;IAChBsK,IAAI,EAAE,CAAC;MACClG,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMmG,YAAY,CAAC;EACf5F,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6F,YAAY,GAAG1K,MAAM,CAAC4E,mBAAmB,CAAC;IAC/C,IAAI,CAAC+F,OAAO,GAAG3K,MAAM,CAAEG,UAAW,CAAC;EACvC;EACA+H,QAAQA,CAACzD,OAAO,EAAE;IACd,OAAO,IAAI,CAACiG,YAAY,CAACxC,QAAQ,CAAC,IAAI,CAACyC,OAAO,EAAElG,OAAO,CAAC;EAC5D;EACAsE,eAAeA,CAACC,MAAM,EAAEvE,OAAO,EAAE;IAC7B,OAAO,IAAI,CAACiG,YAAY,CAAC3B,eAAe,CAAC,IAAI,CAAC4B,OAAO,EAAE3B,MAAM,EAAEvE,OAAO,CAAC;EAC3E;EACA;IAAS,IAAI,CAACsF,IAAI,YAAAa,qBAAAX,iBAAA;MAAA,YAAAA,iBAAA,IAAwFQ,YAAY;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACI,IAAI,kBAtB8E/K,EAAE,CAAAgL,iBAAA;MAAAP,IAAA,EAsBJE,YAAY;MAAAM,SAAA;MAAAC,QAAA;IAAA,EAA6F;EAAE;AAC7M;AACA;EAAA,QAAAX,SAAA,oBAAAA,SAAA,KAxBoGvK,EAAE,CAAAwK,iBAAA,CAwBXG,YAAY,EAAc,CAAC;IAC1GF,IAAI,EAAEnK,SAAS;IACfoK,IAAI,EAAE,CAAC;MACCS,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,gBAAgB;MAC1BF,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS3G,qBAAqB,EAAEoG,YAAY,EAAE7F,mBAAmB,EAAEJ,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}