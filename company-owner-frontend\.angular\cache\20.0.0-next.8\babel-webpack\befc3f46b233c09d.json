{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\nexport function takeUntil(notifier) {\n  return operate((source, subscriber) => {\n    innerFrom(notifier).subscribe(createOperatorSubscriber(subscriber, () => subscriber.complete(), noop));\n    !subscriber.closed && source.subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "noop", "takeUntil", "notifier", "source", "subscriber", "subscribe", "complete", "closed"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/rxjs/dist/esm/internal/operators/takeUntil.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\nexport function takeUntil(notifier) {\n    return operate((source, subscriber) => {\n        innerFrom(notifier).subscribe(createOperatorSubscriber(subscriber, () => subscriber.complete(), noop));\n        !subscriber.closed && source.subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAO,SAASC,SAASA,CAACC,QAAQ,EAAE;EAChC,OAAOL,OAAO,CAAC,CAACM,MAAM,EAAEC,UAAU,KAAK;IACnCL,SAAS,CAACG,QAAQ,CAAC,CAACG,SAAS,CAACP,wBAAwB,CAACM,UAAU,EAAE,MAAMA,UAAU,CAACE,QAAQ,CAAC,CAAC,EAAEN,IAAI,CAAC,CAAC;IACtG,CAACI,UAAU,CAACG,MAAM,IAAIJ,MAAM,CAACE,SAAS,CAACD,UAAU,CAAC;EACtD,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}