{"ast": null, "code": "import { VERSION } from '@angular/core';\n\n// TODO(crisbeto): remove this function when making breaking changes for v20.\n/**\n * Binds an event listener with specific options in a backwards-compatible way.\n * This function is necessary, because `Renderer2.listen` only supports listener options\n * after 19.1 and during the v19 period we support any 19.x version.\n * @docs-private\n */\nfunction _bindEventWithOptions(renderer, target, eventName, callback, options) {\n  const major = parseInt(VERSION.major);\n  const minor = parseInt(VERSION.minor);\n  // Event options in `listen` are only supported in 19.1 and beyond.\n  // We also allow 0.0.x, because that indicates a build at HEAD.\n  if (major > 19 || major === 19 && minor > 0 || major === 0 && minor === 0) {\n    return renderer.listen(target, eventName, callback, options);\n  }\n  target.addEventListener(eventName, callback, options);\n  return () => {\n    target.removeEventListener(eventName, callback, options);\n  };\n}\nexport { _bindEventWithOptions as _ };", "map": {"version": 3, "names": ["VERSION", "_bindEventWithOptions", "renderer", "target", "eventName", "callback", "options", "major", "parseInt", "minor", "listen", "addEventListener", "removeEventListener", "_"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/@angular/cdk/fesm2022/backwards-compatibility-DHR38MsD.mjs"], "sourcesContent": ["import { VERSION } from '@angular/core';\n\n// TODO(crisbeto): remove this function when making breaking changes for v20.\n/**\n * Binds an event listener with specific options in a backwards-compatible way.\n * This function is necessary, because `Renderer2.listen` only supports listener options\n * after 19.1 and during the v19 period we support any 19.x version.\n * @docs-private\n */\nfunction _bindEventWithOptions(renderer, target, eventName, callback, options) {\n    const major = parseInt(VERSION.major);\n    const minor = parseInt(VERSION.minor);\n    // Event options in `listen` are only supported in 19.1 and beyond.\n    // We also allow 0.0.x, because that indicates a build at HEAD.\n    if (major > 19 || (major === 19 && minor > 0) || (major === 0 && minor === 0)) {\n        return renderer.listen(target, eventName, callback, options);\n    }\n    target.addEventListener(eventName, callback, options);\n    return () => {\n        target.removeEventListener(eventName, callback, options);\n    };\n}\n\nexport { _bindEventWithOptions as _ };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC3E,MAAMC,KAAK,GAAGC,QAAQ,CAACR,OAAO,CAACO,KAAK,CAAC;EACrC,MAAME,KAAK,GAAGD,QAAQ,CAACR,OAAO,CAACS,KAAK,CAAC;EACrC;EACA;EACA,IAAIF,KAAK,GAAG,EAAE,IAAKA,KAAK,KAAK,EAAE,IAAIE,KAAK,GAAG,CAAE,IAAKF,KAAK,KAAK,CAAC,IAAIE,KAAK,KAAK,CAAE,EAAE;IAC3E,OAAOP,QAAQ,CAACQ,MAAM,CAACP,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EAChE;EACAH,MAAM,CAACQ,gBAAgB,CAACP,SAAS,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACrD,OAAO,MAAM;IACTH,MAAM,CAACS,mBAAmB,CAACR,SAAS,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EAC5D,CAAC;AACL;AAEA,SAASL,qBAAqB,IAAIY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}