{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class GuestComponent {\n  static {\n    this.ɵfac = function GuestComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GuestComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GuestComponent,\n      selectors: [[\"app-guest\"]],\n      decls: 1,\n      vars: 0,\n      template: function GuestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [RouterModule, i1.RouterOutlet],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterModule", "GuestComponent", "selectors", "decls", "vars", "template", "GuestComponent_Template", "rf", "ctx", "i0", "ɵɵelement", "i1", "RouterOutlet", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\guest\\guest.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\guest\\guest.component.html"], "sourcesContent": ["// angular import\r\nimport { Component } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-guest',\r\n  imports: [RouterModule],\r\n  templateUrl: './guest.component.html',\r\n  styleUrls: ['./guest.component.scss']\r\n})\r\nexport class GuestComponent {}\r\n", "<router-outlet></router-outlet>\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;;;AAQ9C,OAAM,MAAOC,cAAc;;;uCAAdA,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV3BE,EAAA,CAAAC,SAAA,oBAA+B;;;qBDMnBV,YAAY,EAAAW,EAAA,CAAAC,YAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}