{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/auth.service\";\nexport class AuthGuard {\n  constructor(router, authService) {\n    this.router = router;\n    this.authService = authService;\n  }\n  canActivate(route, state) {\n    if (this.authService.isLoggedIn()) {\n      // Check if route has data.roles and user has one of the required roles\n      if (route.data['roles'] && !this.checkRoles(route.data['roles'])) {\n        // Role not authorized, redirect to home page\n        this.router.navigate(['/']);\n        return false;\n      }\n      // Authorized, return true\n      return true;\n    }\n    // Not logged in, redirect to login page with return url\n    this.router.navigate(['/auth/signin'], {\n      queryParams: {\n        returnUrl: state.url\n      }\n    });\n    return false;\n  }\n  checkRoles(roles) {\n    const userRole = this.authService.currentUserValue?.role;\n    return roles.includes(userRole);\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthGuard)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "router", "authService", "canActivate", "route", "state", "isLoggedIn", "data", "checkRoles", "navigate", "queryParams", "returnUrl", "url", "roles", "userRole", "currentUserValue", "role", "includes", "i0", "ɵɵinject", "i1", "Router", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\core\\guards\\auth.guard.ts"], "sourcesContent": ["// src/app/core/guards/auth.guard.ts\r\nimport { Injectable } from '@angular/core';\r\nimport { Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';\r\nimport { AuthService } from '../services/auth.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthGuard {\r\n  constructor(\r\n    private router: Router,\r\n    private authService: AuthService\r\n  ) {}\r\n\r\n  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {\r\n    if (this.authService.isLoggedIn()) {\r\n      // Check if route has data.roles and user has one of the required roles\r\n      if (route.data['roles'] && !this.checkRoles(route.data['roles'])) {\r\n        // Role not authorized, redirect to home page\r\n        this.router.navigate(['/']);\r\n        return false;\r\n      }\r\n      \r\n      // Authorized, return true\r\n      return true;\r\n    }\r\n\r\n    // Not logged in, redirect to login page with return url\r\n    this.router.navigate(['/auth/signin'], { queryParams: { returnUrl: state.url } });\r\n    return false;\r\n  }\r\n\r\n  private checkRoles(roles: string[]): boolean {\r\n    const userRole = this.authService.currentUserValue?.role;\r\n    return roles.includes(userRole);\r\n  }\r\n}"], "mappings": ";;;AAQA,OAAM,MAAOA,SAAS;EACpBC,YACUC,MAAc,EACdC,WAAwB;IADxB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;EAClB;EAEHC,WAAWA,CAACC,KAA6B,EAAEC,KAA0B;IACnE,IAAI,IAAI,CAACH,WAAW,CAACI,UAAU,EAAE,EAAE;MACjC;MACA,IAAIF,KAAK,CAACG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAACC,UAAU,CAACJ,KAAK,CAACG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;QAChE;QACA,IAAI,CAACN,MAAM,CAACQ,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,OAAO,KAAK;MACd;MAEA;MACA,OAAO,IAAI;IACb;IAEA;IACA,IAAI,CAACR,MAAM,CAACQ,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;MAAEC,WAAW,EAAE;QAAEC,SAAS,EAAEN,KAAK,CAACO;MAAG;IAAE,CAAE,CAAC;IACjF,OAAO,KAAK;EACd;EAEQJ,UAAUA,CAACK,KAAe;IAChC,MAAMC,QAAQ,GAAG,IAAI,CAACZ,WAAW,CAACa,gBAAgB,EAAEC,IAAI;IACxD,OAAOH,KAAK,CAACI,QAAQ,CAACH,QAAQ,CAAC;EACjC;;;uCA3BWf,SAAS,EAAAmB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAATxB,SAAS;MAAAyB,OAAA,EAATzB,SAAS,CAAA0B,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}