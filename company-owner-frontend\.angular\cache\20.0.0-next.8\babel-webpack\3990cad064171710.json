{"ast": null, "code": "import { SharedModule } from 'src/app/theme/shared/shared.module';\nimport { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../../theme/shared/components/card/card.component\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nexport default class BasicElementsComponent {\n  static {\n    this.ɵfac = function BasicElementsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BasicElementsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BasicElementsComponent,\n      selectors: [[\"app-basic-elements\"]],\n      decls: 740,\n      vars: 4,\n      consts: [[1, \"row\"], [1, \"col-sm-12\"], [\"cardTitle\", \"Basic Component\", 3, \"options\"], [1, \"col-md-6\"], [\"action\", \"javascript:\"], [1, \"form-group\"], [\"for\", \"exampleInputEmail1\"], [\"type\", \"email\", \"id\", \"exampleInputEmail1\", \"aria-describedby\", \"emailHelp\", \"placeholder\", \"Enter email\", 1, \"form-control\"], [\"id\", \"emailHelp\", 1, \"form-text\", \"text-muted\"], [\"for\", \"exampleInputPassword1\"], [\"type\", \"password\", \"id\", \"exampleInputPassword1\", \"placeholder\", \"Password\", 1, \"form-control\"], [1, \"form-group\", \"form-check\"], [\"type\", \"checkbox\", \"id\", \"exampleCheck1\", 1, \"form-check-input\"], [\"for\", \"exampleCheck1\", 1, \"form-check-label\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [\"for\", \"text\"], [\"type\", \"text\", \"placeholder\", \"Text\", 1, \"form-control\"], [\"for\", \"exampleFormControlSelect1\"], [\"id\", \"exampleFormControlSelect1\", 1, \"form-control\"], [\"for\", \"exampleFormControlTextarea1\"], [\"id\", \"exampleFormControlTextarea1\", \"rows\", \"3\", 1, \"form-control\"], [1, \"mt-5\"], [\"type\", \"text\", \"placeholder\", \".form-control-lg\", 1, \"mb-3\", \"form-control\", \"form-control-lg\"], [\"type\", \"text\", \"placeholder\", \"Default input\", 1, \"mb-3\", \"form-control\"], [\"type\", \"text\", \"placeholder\", \".form-control-sm\", 1, \"mb-3\", \"form-control\", \"form-control-sm\"], [1, \"mb-3\", \"form-control\", \"form-control-lg\"], [\"title\", \"select\", 1, \"mb-3\", \"form-control\"], [\"action\", \"\"], [\"for\", \"exampleDataList\", 1, \"form-label\"], [\"list\", \"datalistOptions\", \"id\", \"exampleDataList\", \"placeholder\", \"Type to search...\", 1, \"form-control\"], [\"type\", \"text\", \"placeholder\", \"Readonly input here\\u2026\", \"readonly\", \"\", 1, \"form-control\"], [1, \"form-group\", \"row\"], [\"for\", \"staticEmail\", 1, \"col-sm-3\", \"col-form-label\"], [1, \"col-sm-9\"], [\"type\", \"text\", \"readonly\", \"\", \"id\", \"staticEmail\", \"value\", \"<EMAIL>\", 1, \"form-control-plaintext\"], [\"for\", \"inputPassword\", 1, \"col-sm-3\", \"col-form-label\"], [\"type\", \"password\", \"id\", \"inputPassword\", \"placeholder\", \"Password\", 1, \"form-control\"], [\"action\", \"javascript:\", 1, \"row\", \"row-cols-md-auto\", \"g-3\", \"align-items-center\"], [1, \"col-12\"], [\"for\", \"inlineFormInputName\", 1, \"sr-only\"], [\"type\", \"text\", \"readonly\", \"\", \"id\", \"inlineFormInputName\", 1, \"form-control\"], [\"for\", \"inlineFormInputGroupUsername\", 1, \"sr-only\"], [1, \"input-group\"], [1, \"input-group-text\"], [\"type\", \"text\", \"readonly\", \"\", \"id\", \"inlineFormInputGroupUsername\", \"placeholder\", \"Username\", 1, \"form-control\"], [\"for\", \"inlineFormSelectPref\", 1, \"sr-only\"], [\"id\", \"inlineFormSelectPref\", 1, \"form-select\"], [\"selected\", \"\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"type\", \"checkbox\", \"id\", \"inlineFormCheck\", 1, \"form-check-input\"], [\"for\", \"inlineFormCheck\", 1, \"form-check-label\", \"px-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\"], [1, \"form-group\", \"col-md-6\"], [\"for\", \"inputEmail4\"], [\"type\", \"email\", \"id\", \"inputEmail4\", \"placeholder\", \"Email\", 1, \"form-control\"], [\"for\", \"inputPassword4\"], [\"type\", \"password\", \"id\", \"inputPassword4\", \"placeholder\", \"Password\", 1, \"form-control\"], [\"for\", \"inputAddress\"], [\"type\", \"text\", \"id\", \"inputAddress\", \"placeholder\", \"1234 Main St\", 1, \"form-control\"], [\"for\", \"inputAddress2\"], [\"type\", \"text\", \"id\", \"inputAddress2\", \"placeholder\", \"Apartment, studio, or floor\", 1, \"form-control\"], [\"for\", \"inputCity\"], [\"type\", \"text\", \"id\", \"inputCity\", 1, \"form-control\"], [1, \"form-group\", \"col-md-4\"], [\"for\", \"inputState\"], [\"id\", \"inputState\", 1, \"form-control\"], [1, \"form-group\", \"col-md-2\"], [\"for\", \"inputZip\"], [\"type\", \"text\", \"id\", \"inputZip\", 1, \"form-control\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"gridCheck\", 1, \"form-check-input\"], [\"for\", \"gridCheck\", 1, \"form-check-label\"], [\"for\", \"inputEmail3\", 1, \"col-sm-3\", \"col-form-label\"], [\"type\", \"email\", \"id\", \"inputEmail3\", \"placeholder\", \"Email\", 1, \"form-control\"], [\"for\", \"inputPassword3\", 1, \"col-sm-3\", \"col-form-label\"], [\"type\", \"password\", \"id\", \"inputPassword3\", \"placeholder\", \"Password\", 1, \"form-control\"], [\"type\", \"radio\", \"name\", \"gridRadios\", \"id\", \"gridRadios1\", \"value\", \"option1\", \"checked\", \"\", 1, \"form-check-input\"], [\"for\", \"gridRadios1\", 1, \"form-check-label\"], [\"type\", \"radio\", \"name\", \"gridRadios\", \"id\", \"gridRadios2\", \"value\", \"option2\", 1, \"form-check-input\"], [\"for\", \"gridRadios2\", 1, \"form-check-label\"], [1, \"form-check\", \"disabled\"], [\"type\", \"radio\", \"name\", \"gridRadios\", \"id\", \"gridRadios3\", \"value\", \"option3\", \"disabled\", \"\", 1, \"form-check-input\"], [\"for\", \"gridRadios3\", 1, \"form-check-label\"], [1, \"col-sm-3\"], [\"type\", \"checkbox\", \"id\", \"gridCheck1\", 1, \"form-check-input\"], [\"for\", \"gridCheck1\", 1, \"form-check-label\"], [1, \"col-sm-10\"], [\"for\", \"colFormLabelSm\", 1, \"col-sm-3\", \"col-form-label\", \"col-form-label-sm\"], [\"type\", \"email\", \"id\", \"colFormLabelSm\", \"placeholder\", \"col-form-label-sm\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"colFormLabel\", 1, \"col-sm-3\", \"col-form-label\"], [\"type\", \"email\", \"id\", \"colFormLabel\", \"placeholder\", \"col-form-label\", 1, \"form-control\"], [\"for\", \"colFormLabelLg\", 1, \"col-sm-3\", \"col-form-label\", \"col-form-label-lg\"], [\"type\", \"email\", \"id\", \"colFormLabelLg\", \"placeholder\", \"col-form-label-lg\", 1, \"form-control\", \"form-control-lg\"], [\"for\", \"inputPassword5\"], [\"type\", \"password\", \"id\", \"inputPassword5\", \"aria-describedby\", \"passwordHelpBlock\", 1, \"form-control\"], [\"id\", \"passwordHelpBlock\", 1, \"form-text\", \"text-muted\"], [\"action\", \"javascript:\", 1, \"form-inline\"], [1, \"form-group\", \"mt-3\"], [\"for\", \"inputPassword6\"], [\"type\", \"password\", \"id\", \"inputPassword6\", \"aria-describedby\", \"passwordHelpInline\", 1, \"form-control\"], [\"id\", \"passwordHelpInline\", 1, \"text-muted\"], [\"action\", \"javascript:\", \"novalidate\", \"\", 1, \"needs-validation\"], [1, \"col-md-4\", \"mb-3\"], [\"for\", \"validationCustom01\"], [\"type\", \"text\", \"id\", \"validationCustom01\", \"placeholder\", \"First name\", \"value\", \"Mark\", \"required\", \"\", 1, \"form-control\"], [1, \"valid-feedback\"], [\"for\", \"validationCustom02\"], [\"type\", \"text\", \"id\", \"validationCustom02\", \"placeholder\", \"Last name\", \"value\", \"Otto\", \"required\", \"\", 1, \"form-control\"], [\"for\", \"validationTooltipUsername\"], [\"id\", \"validationTooltipUsernamePrepend\", 1, \"input-group-text\"], [\"type\", \"text\", \"id\", \"validationTooltipUsername\", \"placeholder\", \"Username\", \"aria-describedby\", \"validationTooltipUsernamePrepend\", \"required\", \"\", 1, \"form-control\"], [1, \"invalid-feedback\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"validationCustom03\"], [\"type\", \"text\", \"id\", \"validationCustom03\", \"placeholder\", \"City\", \"required\", \"\", 1, \"form-control\"], [1, \"col-md-3\", \"mb-3\"], [\"for\", \"validationCustom04\"], [\"type\", \"text\", \"id\", \"validationCustom04\", \"placeholder\", \"State\", \"required\", \"\", 1, \"form-control\"], [\"for\", \"validationCustom05\"], [\"type\", \"text\", \"id\", \"validationCustom05\", \"placeholder\", \"Zip\", \"required\", \"\", 1, \"form-control\"], [\"type\", \"checkbox\", \"value\", \"\", \"id\", \"invalidCheck\", \"required\", \"\", 1, \"form-check-input\"], [\"for\", \"invalidCheck\", 1, \"form-check-label\"], [1, \"was-validated\"], [1, \"mb-3\"], [\"for\", \"validationTextarea\", 1, \"form-label\"], [\"id\", \"validationTextarea\", \"placeholder\", \"Required example textarea\", \"required\", \"\", 1, \"form-control\"], [1, \"form-check\", \"mb-3\"], [\"type\", \"checkbox\", \"id\", \"validationFormCheck1\", \"required\", \"\", 1, \"form-check-input\"], [\"for\", \"validationFormCheck1\", 1, \"form-check-label\"], [\"type\", \"radio\", \"id\", \"validationFormCheck2\", \"name\", \"radio-stacked\", \"required\", \"\", 1, \"form-check-input\"], [\"for\", \"validationFormCheck2\", 1, \"form-check-label\"], [\"type\", \"radio\", \"id\", \"validationFormCheck3\", \"name\", \"radio-stacked\", \"required\", \"\", 1, \"form-check-input\"], [\"for\", \"validationFormCheck3\", 1, \"form-check-label\"], [\"required\", \"\", \"aria-label\", \"select example\", 1, \"form-select\"], [\"value\", \"\"], [\"type\", \"file\", \"aria-label\", \"file example\", \"required\", \"\", 1, \"form-control\"], [\"type\", \"submit\", \"disabled\", \"\", 1, \"btn\", \"btn-primary\"], [\"type\", \"button\", \"type\", \"submit\", \"disabled\", \"\", 1, \"btn\", \"btn-primary\"], [\"for\", \"validationTooltip01\"], [\"type\", \"text\", \"id\", \"validationTooltip01\", \"placeholder\", \"First name\", \"value\", \"Mark\", \"required\", \"\", 1, \"form-control\"], [1, \"valid-tooltip\"], [\"for\", \"validationTooltip02\"], [\"type\", \"text\", \"id\", \"validationTooltip02\", \"placeholder\", \"Last name\", \"value\", \"Otto\", \"required\", \"\", 1, \"form-control\"], [1, \"invalid-tooltip\"], [\"for\", \"validationTooltip03\"], [\"type\", \"text\", \"id\", \"validationTooltip03\", \"placeholder\", \"City\", \"required\", \"\", 1, \"form-control\"], [\"for\", \"validationTooltip04\"], [\"type\", \"text\", \"id\", \"validationTooltip04\", \"placeholder\", \"State\", \"required\", \"\", 1, \"form-control\"], [\"for\", \"validationTooltip05\"], [\"type\", \"text\", \"id\", \"validationTooltip05\", \"placeholder\", \"Zip\", \"required\", \"\", 1, \"form-control\"], [1, \"mt-3\"], [1, \"custom-control\", \"custom-checkbox\"], [\"type\", \"checkbox\", \"id\", \"customCheck1\", 1, \"custom-control-input\"], [\"for\", \"customCheck1\", 1, \"custom-control-label\", \"ps-2\"], [1, \"custom-control\", \"custom-radio\", \"mb-1\"], [\"type\", \"radio\", \"id\", \"customRadio1\", \"name\", \"customRadio\", 1, \"custom-control-input\"], [\"for\", \"customRadio1\", 1, \"custom-control-label\", \"ps-2\"], [1, \"custom-control\", \"custom-radio\"], [\"type\", \"radio\", \"id\", \"customRadio2\", \"name\", \"customRadio\", 1, \"custom-control-input\"], [\"for\", \"customRadio2\", 1, \"custom-control-label\", \"ps-2\"], [1, \"custom-control\", \"custom-radio\", \"custom-control-inline\", \"mb-1\"], [\"type\", \"radio\", \"id\", \"customRadioInline1\", \"name\", \"customRadioInline1\", 1, \"custom-control-input\"], [\"for\", \"customRadioInline1\", 1, \"custom-control-label\", \"ps-2\"], [1, \"custom-control\", \"custom-radio\", \"custom-control-inline\"], [\"type\", \"radio\", \"id\", \"customRadioInline2\", \"name\", \"customRadioInline1\", 1, \"custom-control-input\"], [\"for\", \"customRadioInline2\", 1, \"custom-control-label\", \"ps-2\"], [\"for\", \"customRange1\"], [\"type\", \"range\", \"id\", \"customRange1\", 1, \"form-range\", \"d-block\"], [\"for\", \"customRange2\"], [\"type\", \"range\", \"min\", \"0\", \"max\", \"5\", \"id\", \"customRange2\", 1, \"form-range\", \"d-block\"], [\"for\", \"customRange3\"], [\"type\", \"range\", \"min\", \"0\", \"max\", \"5\", \"step\", \"0.5\", \"id\", \"customRange3\", 1, \"form-range\", \"d-block\"], [\"cardTitle\", \"Input Group\", 3, \"options\"], [1, \"input-group\", \"mb-3\"], [\"id\", \"basic-addon1\", 1, \"input-group-text\"], [\"type\", \"text\", \"placeholder\", \"Username\", \"aria-label\", \"Username\", \"aria-describedby\", \"basic-addon1\", 1, \"form-control\"], [\"type\", \"text\", \"placeholder\", \"Recipient's username\", \"aria-label\", \"Recipient's username\", \"aria-describedby\", \"basic-addon2\", 1, \"form-control\"], [\"id\", \"basic-addon2\", 1, \"input-group-text\"], [\"for\", \"basic-url\", 1, \"form-label\"], [\"id\", \"basic-addon3\", 1, \"input-group-text\"], [\"type\", \"text\", \"id\", \"basic-url\", \"aria-describedby\", \"basic-addon3\", 1, \"form-control\"], [\"type\", \"text\", \"aria-label\", \"Amount (to the nearest dollar)\", 1, \"form-control\"], [\"aria-label\", \"With textarea\", 1, \"form-control\"], [1, \"input-group\", \"input-group-sm\", \"mb-3\"], [\"id\", \"inputGroup-sizing-sm\", 1, \"input-group-text\"], [\"type\", \"text\", \"aria-label\", \"Sizing example input\", \"aria-describedby\", \"inputGroup-sizing-sm\", 1, \"form-control\"], [\"id\", \"inputGroup-sizing-default\", 1, \"input-group-text\"], [\"type\", \"text\", \"aria-label\", \"Sizing example input\", \"aria-describedby\", \"inputGroup-sizing-default\", 1, \"form-control\"], [1, \"input-group\", \"input-group-lg\"], [\"id\", \"inputGroup-sizing-lg\", 1, \"input-group-text\"], [\"type\", \"text\", \"aria-label\", \"Sizing example input\", \"aria-describedby\", \"inputGroup-sizing-lg\", 1, \"form-control\"], [\"type\", \"checkbox\", \"value\", \"\", \"aria-label\", \"Checkbox for following text input\", 1, \"form-check-input\", \"mt-0\"], [\"type\", \"text\", \"aria-label\", \"Text input with checkbox\", 1, \"form-control\"], [\"type\", \"radio\", \"value\", \"\", \"aria-label\", \"Radio button for following text input\", 1, \"form-check-input\", \"mt-0\"], [\"type\", \"text\", \"aria-label\", \"Text input with radio button\", 1, \"form-control\"], [\"type\", \"text\", \"aria-label\", \"First name\", 1, \"form-control\"], [\"type\", \"text\", \"aria-label\", \"Last name\", 1, \"form-control\"], [\"type\", \"text\", \"aria-label\", \"Dollar amount (with dot and two decimal places)\", 1, \"form-control\"], [\"type\", \"button\", \"id\", \"button-addon1\", 1, \"btn\", \"btn-outline-secondary\"], [\"type\", \"text\", \"placeholder\", \"\", \"aria-label\", \"Example text with button addon\", \"aria-describedby\", \"button-addon1\", 1, \"form-control\"], [\"type\", \"text\", \"placeholder\", \"Recipient's username\", \"aria-label\", \"Recipient's username\", \"aria-describedby\", \"button-addon2\", 1, \"form-control\"], [\"type\", \"button\", \"id\", \"button-addon2\", 1, \"btn\", \"btn-outline-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\"], [\"type\", \"text\", \"placeholder\", \"\", \"aria-label\", \"Example text with two button addons\", 1, \"form-control\"], [\"type\", \"text\", \"placeholder\", \"Recipient's username\", \"aria-label\", \"Recipient's username with two button addons\", 1, \"form-control\"], [\"ngbDropdown\", \"\", \"placement\", \"bottom-start\", 1, \"input-group-prepend\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"one-dropdown\"], [\"ngbDropdownMenu\", \"\"], [\"href\", \"javascript:\", 1, \"dropdown-item\"], [\"role\", \"separator\", 1, \"dropdown-divider\"], [\"type\", \"text\", \"aria-label\", \"Text input with dropdown button\", 1, \"form-control\"], [\"ngbDropdown\", \"\", 1, \"input-group-append\", 3, \"placement\"], [\"ngbDropdown\", \"\", \"placement\", \"bottom-start\", 1, \"input-group\"], [\"type\", \"button\", \"aria-expanded\", \"false\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-outline-secondary\"], [1, \"dropdown-divider\"], [\"type\", \"text\", \"aria-label\", \"Text input with 2 dropdown buttons\", 1, \"form-control\"], [\"type\", \"button\", \"aria-expanded\", \"false\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\"], [\"ngbDropdownMenu1\", \"\", 1, \"dropdown-menu\", \"dropdown-menu-end\"], [\"ngbDropdown\", \"\", 1, \"input-group\", \"mb-3\", 3, \"placement\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle-split\"], [1, \"sr-only\"], [\"type\", \"text\", \"aria-label\", \"Text input with segmented dropdown button\", 1, \"form-control\"], [\"ngbDropdown\", \"\", 1, \"input-group\"], [1, \"input-group-prepend\"], [\"for\", \"inputGroupSelect01\", 1, \"input-group-text\"], [\"id\", \"inputGroupSelect01\", 1, \"form-select\"], [\"id\", \"inputGroupSelect02\", 1, \"form-select\"], [1, \"input-group-append\"], [\"for\", \"inputGroupSelect02\", 1, \"input-group-text\"], [\"id\", \"inputGroupSelect03\", 1, \"form-select\"], [\"id\", \"inputGroupSelect04\", 1, \"form-select\"], [\"for\", \"inputGroupFile01\", 1, \"input-group-text\"], [\"type\", \"file\", \"id\", \"inputGroupFile01\", 1, \"form-control\"], [\"id\", \"inputGroupFileAddon03\", \"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\"], [\"type\", \"file\", \"id\", \"inputGroupFile03\", \"aria-describedby\", \"inputGroupFileAddon03\", \"aria-label\", \"Upload\", 1, \"form-control\"]],\n      template: function BasicElementsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"app-card\", 2)(3, \"h5\");\n          i0.ɵɵtext(4, \"Form controls\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"hr\");\n          i0.ɵɵelementStart(6, \"div\", 0)(7, \"div\", 3)(8, \"form\", 4)(9, \"div\", 5)(10, \"label\", 6);\n          i0.ɵɵtext(11, \"Email address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"input\", 7);\n          i0.ɵɵelementStart(13, \"small\", 8);\n          i0.ɵɵtext(14, \"We'll never share your email with anyone else.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 5)(16, \"label\", 9);\n          i0.ɵɵtext(17, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"input\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 11);\n          i0.ɵɵelement(20, \"input\", 12);\n          i0.ɵɵelementStart(21, \"label\", 13);\n          i0.ɵɵtext(22, \"Check me out\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"button\", 14);\n          i0.ɵɵtext(24, \"Submit\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 3)(26, \"form\", 4)(27, \"div\", 5)(28, \"label\", 15);\n          i0.ɵɵtext(29, \"Text\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(30, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 5)(32, \"label\", 17);\n          i0.ɵɵtext(33, \"Example select\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"select\", 18)(35, \"option\");\n          i0.ɵɵtext(36, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"option\");\n          i0.ɵɵtext(38, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"option\");\n          i0.ɵɵtext(40, \"3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"option\");\n          i0.ɵɵtext(42, \"4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"option\");\n          i0.ɵɵtext(44, \"5\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 5)(46, \"label\", 19);\n          i0.ɵɵtext(47, \"Example textarea\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(48, \"textarea\", 20);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(49, \"h5\", 21);\n          i0.ɵɵtext(50, \"Sizing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(51, \"hr\");\n          i0.ɵɵelementStart(52, \"div\", 0)(53, \"div\", 3);\n          i0.ɵɵelement(54, \"input\", 22)(55, \"input\", 23)(56, \"input\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 3)(58, \"select\", 25)(59, \"option\");\n          i0.ɵɵtext(60, \"Large select\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"select\", 26)(62, \"option\");\n          i0.ɵɵtext(63, \"Default select\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(64, \"div\", 1)(65, \"h5\", 21);\n          i0.ɵɵtext(66, \"Datalist\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(67, \"hr\");\n          i0.ɵɵelementStart(68, \"form\", 27)(69, \"div\", 5)(70, \"label\", 28);\n          i0.ɵɵtext(71, \"Datalist example\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(72, \"input\", 29);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(73, \"div\", 0)(74, \"div\", 3)(75, \"h5\", 21);\n          i0.ɵɵtext(76, \"Readonly\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(77, \"hr\")(78, \"input\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 3)(80, \"h5\", 21);\n          i0.ɵɵtext(81, \"Readonly plain Text\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(82, \"hr\");\n          i0.ɵɵelementStart(83, \"form\", 4)(84, \"div\", 31)(85, \"label\", 32);\n          i0.ɵɵtext(86, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"div\", 33);\n          i0.ɵɵelement(88, \"input\", 34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(89, \"div\", 31)(90, \"label\", 35);\n          i0.ɵɵtext(91, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"div\", 33);\n          i0.ɵɵelement(93, \"input\", 36);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(94, \"h5\", 21);\n          i0.ɵɵtext(95, \"Inline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(96, \"hr\");\n          i0.ɵɵelementStart(97, \"form\", 37)(98, \"div\", 38)(99, \"label\", 39);\n          i0.ɵɵtext(100, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(101, \"input\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"div\", 38)(103, \"label\", 41);\n          i0.ɵɵtext(104, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"div\", 42)(106, \"div\", 43);\n          i0.ɵɵtext(107, \"@\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(108, \"input\", 44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(109, \"div\", 38)(110, \"label\", 45);\n          i0.ɵɵtext(111, \"Preference\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"select\", 46)(113, \"option\", 47);\n          i0.ɵɵtext(114, \"Choose...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"option\", 48);\n          i0.ɵɵtext(116, \"One\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"option\", 49);\n          i0.ɵɵtext(118, \"Two\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(119, \"option\", 50);\n          i0.ɵɵtext(120, \"Three\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(121, \"div\", 38);\n          i0.ɵɵelement(122, \"input\", 51);\n          i0.ɵɵelementStart(123, \"label\", 52);\n          i0.ɵɵtext(124, \"Remember me\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(125, \"div\", 38)(126, \"button\", 53);\n          i0.ɵɵtext(127, \"Submit\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(128, \"h5\", 21);\n          i0.ɵɵtext(129, \"Form Grid\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(130, \"hr\");\n          i0.ɵɵelementStart(131, \"form\", 4)(132, \"div\", 0)(133, \"div\", 54)(134, \"label\", 55);\n          i0.ɵɵtext(135, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(136, \"input\", 56);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(137, \"div\", 54)(138, \"label\", 57);\n          i0.ɵɵtext(139, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(140, \"input\", 58);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(141, \"div\", 5)(142, \"label\", 59);\n          i0.ɵɵtext(143, \"Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(144, \"input\", 60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(145, \"div\", 5)(146, \"label\", 61);\n          i0.ɵɵtext(147, \"Address 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(148, \"input\", 62);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(149, \"div\", 0)(150, \"div\", 54)(151, \"label\", 63);\n          i0.ɵɵtext(152, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(153, \"input\", 64);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(154, \"div\", 65)(155, \"label\", 66);\n          i0.ɵɵtext(156, \"State\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(157, \"select\", 67)(158, \"option\", 47);\n          i0.ɵɵtext(159, \"select\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(160, \"option\");\n          i0.ɵɵtext(161, \"Large select\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(162, \"div\", 68)(163, \"label\", 69);\n          i0.ɵɵtext(164, \"Zip\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(165, \"input\", 70);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(166, \"div\", 5)(167, \"div\", 71);\n          i0.ɵɵelement(168, \"input\", 72);\n          i0.ɵɵelementStart(169, \"label\", 73);\n          i0.ɵɵtext(170, \"Check me out\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(171, \"button\", 14);\n          i0.ɵɵtext(172, \"Sign in\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(173, \"div\", 0)(174, \"div\", 3)(175, \"h5\", 21);\n          i0.ɵɵtext(176, \"Horizontal Form\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(177, \"hr\");\n          i0.ɵɵelementStart(178, \"form\", 4)(179, \"div\", 31)(180, \"label\", 74);\n          i0.ɵɵtext(181, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(182, \"div\", 33);\n          i0.ɵɵelement(183, \"input\", 75);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(184, \"div\", 31)(185, \"label\", 76);\n          i0.ɵɵtext(186, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(187, \"div\", 33);\n          i0.ɵɵelement(188, \"input\", 77);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(189, \"fieldset\", 5)(190, \"div\", 0)(191, \"label\", 76);\n          i0.ɵɵtext(192, \"Radios\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(193, \"div\", 33)(194, \"div\", 71);\n          i0.ɵɵelement(195, \"input\", 78);\n          i0.ɵɵelementStart(196, \"label\", 79);\n          i0.ɵɵtext(197, \"First radio\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(198, \"div\", 71);\n          i0.ɵɵelement(199, \"input\", 80);\n          i0.ɵɵelementStart(200, \"label\", 81);\n          i0.ɵɵtext(201, \"Second radio\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(202, \"div\", 82);\n          i0.ɵɵelement(203, \"input\", 83);\n          i0.ɵɵelementStart(204, \"label\", 84);\n          i0.ɵɵtext(205, \"Third disabled radio\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(206, \"div\", 31)(207, \"div\", 85);\n          i0.ɵɵtext(208, \"Checkbox\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(209, \"div\", 33)(210, \"div\", 71);\n          i0.ɵɵelement(211, \"input\", 86);\n          i0.ɵɵelementStart(212, \"label\", 87);\n          i0.ɵɵtext(213, \"Example checkbox\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(214, \"div\", 31)(215, \"div\", 88)(216, \"button\", 14);\n          i0.ɵɵtext(217, \"Sign in\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(218, \"div\", 3)(219, \"h5\", 21);\n          i0.ɵɵtext(220, \"Horizontal Form Label Sizing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(221, \"hr\");\n          i0.ɵɵelementStart(222, \"form\", 4)(223, \"div\", 31)(224, \"label\", 89);\n          i0.ɵɵtext(225, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(226, \"div\", 33);\n          i0.ɵɵelement(227, \"input\", 90);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(228, \"div\", 31)(229, \"label\", 91);\n          i0.ɵɵtext(230, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(231, \"div\", 33);\n          i0.ɵɵelement(232, \"input\", 92);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(233, \"div\", 31)(234, \"label\", 93);\n          i0.ɵɵtext(235, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(236, \"div\", 33);\n          i0.ɵɵelement(237, \"input\", 94);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(238, \"h5\", 21);\n          i0.ɵɵtext(239, \"Help Text\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(240, \"hr\");\n          i0.ɵɵelementStart(241, \"label\", 95);\n          i0.ɵɵtext(242, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(243, \"input\", 96);\n          i0.ɵɵelementStart(244, \"small\", 97);\n          i0.ɵɵtext(245, \" Your password must be 8-20 characters long, contain letters and numbers, and must not contain spaces, special characters, or emoji. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(246, \"form\", 98)(247, \"div\", 99)(248, \"label\", 100);\n          i0.ɵɵtext(249, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(250, \"input\", 101);\n          i0.ɵɵelementStart(251, \"small\", 102);\n          i0.ɵɵtext(252, \"Must be 8-20 characters long.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(253, \"h5\", 21);\n          i0.ɵɵtext(254, \"Validation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(255, \"hr\");\n          i0.ɵɵelementStart(256, \"form\", 103)(257, \"div\", 0)(258, \"div\", 104)(259, \"label\", 105);\n          i0.ɵɵtext(260, \"First name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(261, \"input\", 106);\n          i0.ɵɵelementStart(262, \"div\", 107);\n          i0.ɵɵtext(263, \"Looks good!\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(264, \"div\", 104)(265, \"label\", 108);\n          i0.ɵɵtext(266, \"Last name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(267, \"input\", 109);\n          i0.ɵɵelementStart(268, \"div\", 107);\n          i0.ɵɵtext(269, \"Looks good!\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(270, \"div\", 104)(271, \"label\", 110);\n          i0.ɵɵtext(272, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(273, \"div\", 42)(274, \"span\", 111);\n          i0.ɵɵtext(275, \"@\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(276, \"input\", 112);\n          i0.ɵɵelementStart(277, \"div\", 113);\n          i0.ɵɵtext(278, \"Please choose a username.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(279, \"div\", 0)(280, \"div\", 114)(281, \"label\", 115);\n          i0.ɵɵtext(282, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(283, \"input\", 116);\n          i0.ɵɵelementStart(284, \"div\", 113);\n          i0.ɵɵtext(285, \"Please provide a valid city.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(286, \"div\", 117)(287, \"label\", 118);\n          i0.ɵɵtext(288, \"State\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(289, \"input\", 119);\n          i0.ɵɵelementStart(290, \"div\", 113);\n          i0.ɵɵtext(291, \"Please provide a valid state.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(292, \"div\", 117)(293, \"label\", 120);\n          i0.ɵɵtext(294, \"Zip\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(295, \"input\", 121);\n          i0.ɵɵelementStart(296, \"div\", 113);\n          i0.ɵɵtext(297, \"Please provide a valid zip.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(298, \"div\", 5)(299, \"div\", 71);\n          i0.ɵɵelement(300, \"input\", 122);\n          i0.ɵɵelementStart(301, \"label\", 123);\n          i0.ɵɵtext(302, \"Agree to terms and conditions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(303, \"div\", 113);\n          i0.ɵɵtext(304, \"You must agree before submitting.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(305, \"button\", 14);\n          i0.ɵɵtext(306, \"Submit form\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(307, \"h5\", 21);\n          i0.ɵɵtext(308, \"Supported Elements\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(309, \"hr\");\n          i0.ɵɵelementStart(310, \"form\", 124)(311, \"div\", 125)(312, \"label\", 126);\n          i0.ɵɵtext(313, \"Textarea\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(314, \"textarea\", 127);\n          i0.ɵɵelementStart(315, \"div\", 113);\n          i0.ɵɵtext(316, \"Please enter a message in the textarea.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(317, \"div\", 128);\n          i0.ɵɵelement(318, \"input\", 129);\n          i0.ɵɵelementStart(319, \"label\", 130);\n          i0.ɵɵtext(320, \"Check this checkbox\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(321, \"div\", 113);\n          i0.ɵɵtext(322, \"Example invalid feedback text\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(323, \"div\", 71);\n          i0.ɵɵelement(324, \"input\", 131);\n          i0.ɵɵelementStart(325, \"label\", 132);\n          i0.ɵɵtext(326, \"Toggle this radio\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(327, \"div\", 128);\n          i0.ɵɵelement(328, \"input\", 133);\n          i0.ɵɵelementStart(329, \"label\", 134);\n          i0.ɵɵtext(330, \"Or toggle this other radio\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(331, \"div\", 113);\n          i0.ɵɵtext(332, \"More example invalid feedback text\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(333, \"div\", 125)(334, \"select\", 135)(335, \"option\", 136);\n          i0.ɵɵtext(336, \"Open this select menu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(337, \"option\", 48);\n          i0.ɵɵtext(338, \"One\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(339, \"option\", 49);\n          i0.ɵɵtext(340, \"Two\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(341, \"option\", 50);\n          i0.ɵɵtext(342, \"Three\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(343, \"div\", 113);\n          i0.ɵɵtext(344, \"Example invalid select feedback\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(345, \"div\", 125);\n          i0.ɵɵelement(346, \"input\", 137);\n          i0.ɵɵelementStart(347, \"div\", 113);\n          i0.ɵɵtext(348, \"Example invalid form file feedback\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(349, \"div\", 125)(350, \"button\", 138);\n          i0.ɵɵtext(351, \"Submit form\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(352, \"div\", 125)(353, \"button\", 139);\n          i0.ɵɵtext(354, \"Submitform\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(355, \"h5\", 21);\n          i0.ɵɵtext(356, \"Tooltips\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(357, \"hr\");\n          i0.ɵɵelementStart(358, \"form\", 103)(359, \"div\", 0)(360, \"div\", 104)(361, \"label\", 140);\n          i0.ɵɵtext(362, \"First name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(363, \"input\", 141);\n          i0.ɵɵelementStart(364, \"div\", 142);\n          i0.ɵɵtext(365, \"Looks good!\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(366, \"div\", 104)(367, \"label\", 143);\n          i0.ɵɵtext(368, \"Last name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(369, \"input\", 144);\n          i0.ɵɵelementStart(370, \"div\", 142);\n          i0.ɵɵtext(371, \"Looks good!\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(372, \"div\", 104)(373, \"label\", 110);\n          i0.ɵɵtext(374, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(375, \"div\", 42)(376, \"div\", 42)(377, \"span\", 111);\n          i0.ɵɵtext(378, \"@\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(379, \"input\", 112);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(380, \"div\", 145);\n          i0.ɵɵtext(381, \"Please choose a unique and valid username.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(382, \"div\", 0)(383, \"div\", 114)(384, \"label\", 146);\n          i0.ɵɵtext(385, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(386, \"input\", 147);\n          i0.ɵɵelementStart(387, \"div\", 145);\n          i0.ɵɵtext(388, \"Please provide a valid city.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(389, \"div\", 117)(390, \"label\", 148);\n          i0.ɵɵtext(391, \"State\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(392, \"input\", 149);\n          i0.ɵɵelementStart(393, \"div\", 145);\n          i0.ɵɵtext(394, \"Please provide a valid state.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(395, \"div\", 117)(396, \"label\", 150);\n          i0.ɵɵtext(397, \"Zip\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(398, \"input\", 151);\n          i0.ɵɵelementStart(399, \"div\", 145);\n          i0.ɵɵtext(400, \"Please provide a valid zip.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(401, \"button\", 14);\n          i0.ɵɵtext(402, \"Submit form\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(403, \"h3\", 21);\n          i0.ɵɵtext(404, \"Checkboxes and Radios\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(405, \"h5\", 152);\n          i0.ɵɵtext(406, \"Checkboxes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(407, \"hr\");\n          i0.ɵɵelementStart(408, \"div\", 153);\n          i0.ɵɵelement(409, \"input\", 154);\n          i0.ɵɵelementStart(410, \"label\", 155);\n          i0.ɵɵtext(411, \"Check this custom checkbox\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(412, \"div\", 0)(413, \"div\", 3)(414, \"h5\", 21);\n          i0.ɵɵtext(415, \"Radios\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(416, \"hr\");\n          i0.ɵɵelementStart(417, \"div\", 156);\n          i0.ɵɵelement(418, \"input\", 157);\n          i0.ɵɵelementStart(419, \"label\", 158);\n          i0.ɵɵtext(420, \"Toggle this custom radio\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(421, \"div\", 159);\n          i0.ɵɵelement(422, \"input\", 160);\n          i0.ɵɵelementStart(423, \"label\", 161);\n          i0.ɵɵtext(424, \"Or toggle this other custom radio\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(425, \"h5\", 21);\n          i0.ɵɵtext(426, \"Inline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(427, \"hr\");\n          i0.ɵɵelementStart(428, \"div\", 162);\n          i0.ɵɵelement(429, \"input\", 163);\n          i0.ɵɵelementStart(430, \"label\", 164);\n          i0.ɵɵtext(431, \"Toggle this custom radio\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(432, \"div\", 165);\n          i0.ɵɵelement(433, \"input\", 166);\n          i0.ɵɵelementStart(434, \"label\", 167);\n          i0.ɵɵtext(435, \"Or toggle this other custom radio\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(436, \"div\", 3)(437, \"h5\", 21);\n          i0.ɵɵtext(438, \"Range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(439, \"hr\");\n          i0.ɵɵelementStart(440, \"label\", 168);\n          i0.ɵɵtext(441, \"Example range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(442, \"input\", 169);\n          i0.ɵɵelementStart(443, \"label\", 170);\n          i0.ɵɵtext(444, \"Example range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(445, \"input\", 171);\n          i0.ɵɵelementStart(446, \"label\", 172);\n          i0.ɵɵtext(447, \"Example range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(448, \"input\", 173);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(449, \"div\", 1)(450, \"app-card\", 174)(451, \"div\", 175)(452, \"span\", 176);\n          i0.ɵɵtext(453, \"@\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(454, \"input\", 177);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(455, \"div\", 175);\n          i0.ɵɵelement(456, \"input\", 178);\n          i0.ɵɵelementStart(457, \"span\", 179);\n          i0.ɵɵtext(458, \"@example.com\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(459, \"label\", 180);\n          i0.ɵɵtext(460, \"Your vanity URL\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(461, \"div\", 175)(462, \"span\", 181);\n          i0.ɵɵtext(463, \"https://example.com/users/\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(464, \"input\", 182);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(465, \"div\", 175)(466, \"span\", 43);\n          i0.ɵɵtext(467, \"$\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(468, \"input\", 183);\n          i0.ɵɵelementStart(469, \"span\", 43);\n          i0.ɵɵtext(470, \".00\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(471, \"div\", 42)(472, \"span\", 43);\n          i0.ɵɵtext(473, \"With textarea\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(474, \"textarea\", 184);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(475, \"div\", 0)(476, \"div\", 3)(477, \"h5\", 21);\n          i0.ɵɵtext(478, \"Sizing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(479, \"hr\");\n          i0.ɵɵelementStart(480, \"div\", 185)(481, \"span\", 186);\n          i0.ɵɵtext(482, \"Small\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(483, \"input\", 187);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(484, \"div\", 175)(485, \"span\", 188);\n          i0.ɵɵtext(486, \"Default\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(487, \"input\", 189);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(488, \"div\", 190)(489, \"span\", 191);\n          i0.ɵɵtext(490, \"Large\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(491, \"input\", 192);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(492, \"div\", 3)(493, \"h5\", 21);\n          i0.ɵɵtext(494, \"Checkboxes and radios\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(495, \"hr\");\n          i0.ɵɵelementStart(496, \"div\", 175)(497, \"div\", 43);\n          i0.ɵɵelement(498, \"input\", 193);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(499, \"input\", 194);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(500, \"div\", 42)(501, \"div\", 43);\n          i0.ɵɵelement(502, \"input\", 195);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(503, \"input\", 196);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(504, \"div\", 3)(505, \"h5\", 21);\n          i0.ɵɵtext(506, \"Multiple inputs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(507, \"hr\");\n          i0.ɵɵelementStart(508, \"div\", 42)(509, \"span\", 43);\n          i0.ɵɵtext(510, \"First and last name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(511, \"input\", 197)(512, \"input\", 198);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(513, \"div\", 3)(514, \"h5\", 21);\n          i0.ɵɵtext(515, \"Multiple addons\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(516, \"hr\");\n          i0.ɵɵelementStart(517, \"div\", 175)(518, \"span\", 43);\n          i0.ɵɵtext(519, \"$\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(520, \"span\", 43);\n          i0.ɵɵtext(521, \"0.00\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(522, \"input\", 199);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(523, \"div\", 42);\n          i0.ɵɵelement(524, \"input\", 199);\n          i0.ɵɵelementStart(525, \"span\", 43);\n          i0.ɵɵtext(526, \"$\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(527, \"span\", 43);\n          i0.ɵɵtext(528, \"0.00\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(529, \"h5\", 21);\n          i0.ɵɵtext(530, \"Button Addons\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(531, \"hr\");\n          i0.ɵɵelementStart(532, \"div\", 0)(533, \"div\", 3)(534, \"div\", 175)(535, \"button\", 200);\n          i0.ɵɵtext(536, \"Button\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(537, \"input\", 201);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(538, \"div\", 175);\n          i0.ɵɵelement(539, \"input\", 202);\n          i0.ɵɵelementStart(540, \"button\", 203);\n          i0.ɵɵtext(541, \"Button\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(542, \"div\", 3)(543, \"div\", 175)(544, \"button\", 204);\n          i0.ɵɵtext(545, \"Button\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(546, \"button\", 204);\n          i0.ɵɵtext(547, \"Button\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(548, \"input\", 205);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(549, \"div\", 42);\n          i0.ɵɵelement(550, \"input\", 206);\n          i0.ɵɵelementStart(551, \"button\", 204);\n          i0.ɵɵtext(552, \"Button\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(553, \"button\", 204);\n          i0.ɵɵtext(554, \"Button\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(555, \"div\", 3)(556, \"h5\", 21);\n          i0.ɵɵtext(557, \"Buttons With Dropdowns\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(558, \"hr\");\n          i0.ɵɵelementStart(559, \"div\", 175)(560, \"div\", 207)(561, \"button\", 208);\n          i0.ɵɵtext(562, \"Dropdown\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(563, \"div\", 209)(564, \"a\", 210);\n          i0.ɵɵtext(565, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(566, \"a\", 210);\n          i0.ɵɵtext(567, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(568, \"a\", 210);\n          i0.ɵɵtext(569, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(570, \"div\", 211);\n          i0.ɵɵelementStart(571, \"a\", 210);\n          i0.ɵɵtext(572, \"Separated link\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(573, \"input\", 212);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(574, \"div\", 175);\n          i0.ɵɵelement(575, \"input\", 212);\n          i0.ɵɵelementStart(576, \"div\", 213)(577, \"button\", 208);\n          i0.ɵɵtext(578, \"Dropdown\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(579, \"div\", 209)(580, \"a\", 210);\n          i0.ɵɵtext(581, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(582, \"a\", 210);\n          i0.ɵɵtext(583, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(584, \"a\", 210);\n          i0.ɵɵtext(585, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(586, \"div\", 211);\n          i0.ɵɵelementStart(587, \"a\", 210);\n          i0.ɵɵtext(588, \"Separated link\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(589, \"div\", 214)(590, \"button\", 215);\n          i0.ɵɵtext(591, \"Dropdown\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(592, \"ul\", 209)(593, \"li\")(594, \"a\", 210);\n          i0.ɵɵtext(595, \"Action before\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(596, \"li\")(597, \"a\", 210);\n          i0.ɵɵtext(598, \"Another action before\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(599, \"li\")(600, \"a\", 210);\n          i0.ɵɵtext(601, \"Something else here\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(602, \"li\");\n          i0.ɵɵelement(603, \"hr\", 216);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(604, \"li\")(605, \"a\", 210);\n          i0.ɵɵtext(606, \"Separated link\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(607, \"input\", 217);\n          i0.ɵɵelementStart(608, \"button\", 218);\n          i0.ɵɵtext(609, \" Dropdown \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(610, \"ul\", 219)(611, \"li\")(612, \"a\", 210);\n          i0.ɵɵtext(613, \"Action\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(614, \"li\")(615, \"a\", 210);\n          i0.ɵɵtext(616, \"Another action\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(617, \"li\")(618, \"a\", 210);\n          i0.ɵɵtext(619, \"Something else here\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(620, \"li\");\n          i0.ɵɵelement(621, \"hr\", 216);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(622, \"li\")(623, \"a\", 210);\n          i0.ɵɵtext(624, \"Separated link\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(625, \"div\", 3)(626, \"h5\", 21);\n          i0.ɵɵtext(627, \"Segmented Buttons\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(628, \"hr\");\n          i0.ɵɵelementStart(629, \"div\", 220)(630, \"button\", 204);\n          i0.ɵɵtext(631, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(632, \"button\", 221)(633, \"span\", 222);\n          i0.ɵɵtext(634, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(635, \"div\", 209)(636, \"a\", 210);\n          i0.ɵɵtext(637, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(638, \"a\", 210);\n          i0.ɵɵtext(639, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(640, \"a\", 210);\n          i0.ɵɵtext(641, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(642, \"div\", 211);\n          i0.ɵɵelementStart(643, \"a\", 210);\n          i0.ɵɵtext(644, \"Separated link\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(645, \"input\", 223);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(646, \"div\", 224);\n          i0.ɵɵelement(647, \"input\", 223);\n          i0.ɵɵelementStart(648, \"button\", 204);\n          i0.ɵɵtext(649, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(650, \"button\", 221)(651, \"span\", 222);\n          i0.ɵɵtext(652, \"Toggle Dropdown\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(653, \"div\", 209)(654, \"a\", 210);\n          i0.ɵɵtext(655, \"Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(656, \"a\", 210);\n          i0.ɵɵtext(657, \"Another action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(658, \"a\", 210);\n          i0.ɵɵtext(659, \"Something else here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(660, \"div\", 211);\n          i0.ɵɵelementStart(661, \"a\", 210);\n          i0.ɵɵtext(662, \"Separated link\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(663, \"h3\", 21);\n          i0.ɵɵtext(664, \"Custom Forms\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(665, \"div\", 0)(666, \"div\", 3)(667, \"h5\", 152);\n          i0.ɵɵtext(668, \"Custom Select\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(669, \"hr\");\n          i0.ɵɵelementStart(670, \"div\", 175)(671, \"div\", 225)(672, \"label\", 226);\n          i0.ɵɵtext(673, \"Options\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(674, \"select\", 227)(675, \"option\", 47);\n          i0.ɵɵtext(676, \"Choose...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(677, \"option\", 48);\n          i0.ɵɵtext(678, \"One\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(679, \"option\", 49);\n          i0.ɵɵtext(680, \"Two\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(681, \"option\", 50);\n          i0.ɵɵtext(682, \"Three\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(683, \"div\", 175)(684, \"select\", 228)(685, \"option\", 47);\n          i0.ɵɵtext(686, \"Choose...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(687, \"option\", 48);\n          i0.ɵɵtext(688, \"One\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(689, \"option\", 49);\n          i0.ɵɵtext(690, \"Two\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(691, \"option\", 50);\n          i0.ɵɵtext(692, \"Three\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(693, \"div\", 229)(694, \"label\", 230);\n          i0.ɵɵtext(695, \"Options\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(696, \"div\", 175)(697, \"button\", 204);\n          i0.ɵɵtext(698, \"Button\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(699, \"select\", 231)(700, \"option\", 47);\n          i0.ɵɵtext(701, \"Choose...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(702, \"option\", 48);\n          i0.ɵɵtext(703, \"One\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(704, \"option\", 49);\n          i0.ɵɵtext(705, \"Two\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(706, \"option\", 50);\n          i0.ɵɵtext(707, \"Three\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(708, \"div\", 42)(709, \"select\", 232)(710, \"option\", 47);\n          i0.ɵɵtext(711, \"Choose...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(712, \"option\", 48);\n          i0.ɵɵtext(713, \"One\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(714, \"option\", 49);\n          i0.ɵɵtext(715, \"Two\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(716, \"option\", 50);\n          i0.ɵɵtext(717, \"Three\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(718, \"button\", 204);\n          i0.ɵɵtext(719, \"Button\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(720, \"div\", 3)(721, \"h5\", 152);\n          i0.ɵɵtext(722, \"Custom File Input\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(723, \"hr\");\n          i0.ɵɵelementStart(724, \"div\", 175)(725, \"label\", 233);\n          i0.ɵɵtext(726, \"Upload\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(727, \"input\", 234);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(728, \"div\", 175);\n          i0.ɵɵelement(729, \"input\", 234);\n          i0.ɵɵelementStart(730, \"label\", 233);\n          i0.ɵɵtext(731, \"Upload\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(732, \"div\", 175)(733, \"button\", 235);\n          i0.ɵɵtext(734, \"Button\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(735, \"input\", 236);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(736, \"div\", 42);\n          i0.ɵɵelement(737, \"input\", 236);\n          i0.ɵɵelementStart(738, \"button\", 235);\n          i0.ɵɵtext(739, \"Button\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(448);\n          i0.ɵɵproperty(\"options\", false);\n          i0.ɵɵadvance(126);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n          i0.ɵɵadvance(53);\n          i0.ɵɵproperty(\"placement\", \"bottom-left\");\n        }\n      },\n      dependencies: [SharedModule, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.NgControlStatusGroup, i1.NgForm, i2.CardComponent, i3.NgbDropdown, i3.NgbDropdownToggle, i3.NgbDropdownMenu, NgbDropdownModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "NgbDropdownModule", "BasicElementsComponent", "selectors", "decls", "vars", "consts", "template", "BasicElementsComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "i1", "ɵNgNoValidate", "NgSelectOption", "ɵNgSelectMultipleOption", "NgControlStatusGroup", "NgForm", "i2", "CardComponent", "i3", "NgbDropdown", "NgbDropdownToggle", "NgbDropdownMenu", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\form-elements\\basic-elements\\basic-elements.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\form-elements\\basic-elements\\basic-elements.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\r\nimport { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';\r\n\r\n@Component({\r\n  selector: 'app-basic-elements',\r\n  standalone: true,\r\n  imports: [SharedModule, NgbDropdownModule],\r\n  templateUrl: './basic-elements.component.html',\r\n  styleUrls: ['./basic-elements.component.scss']\r\n})\r\nexport default class BasicElementsComponent {}\r\n", "<div class=\"row\">\r\n  <div class=\"col-sm-12\">\r\n    <app-card cardTitle=\"Basic Component\" [options]=\"false\">\r\n      <h5>Form controls</h5>\r\n      <hr />\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\">\r\n          <form action=\"javascript:\">\r\n            <div class=\"form-group\">\r\n              <label for=\"exampleInputEmail1\">Email address</label>\r\n              <input type=\"email\" class=\"form-control\" id=\"exampleInputEmail1\" aria-describedby=\"emailHelp\" placeholder=\"Enter email\" />\r\n              <small id=\"emailHelp\" class=\"form-text text-muted\">We'll never share your email with anyone else.</small>\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label for=\"exampleInputPassword1\">Password</label>\r\n              <input type=\"password\" class=\"form-control\" id=\"exampleInputPassword1\" placeholder=\"Password\" />\r\n            </div>\r\n            <div class=\"form-group form-check\">\r\n              <input type=\"checkbox\" class=\"form-check-input\" id=\"exampleCheck1\" />\r\n              <label class=\"form-check-label\" for=\"exampleCheck1\">Check me out</label>\r\n            </div>\r\n            <button type=\"submit\" class=\"btn btn-primary\">Submit</button>\r\n          </form>\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <form action=\"javascript:\">\r\n            <div class=\"form-group\">\r\n              <label for=\"text\">Text</label>\r\n              <input type=\"text\" class=\"form-control\" placeholder=\"Text\" />\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label for=\"exampleFormControlSelect1\">Example select</label>\r\n              <select class=\"form-control\" id=\"exampleFormControlSelect1\">\r\n                <option>1</option>\r\n                <option>2</option>\r\n                <option>3</option>\r\n                <option>4</option>\r\n                <option>5</option>\r\n              </select>\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label for=\"exampleFormControlTextarea1\">Example textarea</label>\r\n              <textarea class=\"form-control\" id=\"exampleFormControlTextarea1\" rows=\"3\"></textarea>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n      <h5 class=\"mt-5\">Sizing</h5>\r\n      <hr />\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\">\r\n          <input class=\"mb-3 form-control form-control-lg\" type=\"text\" placeholder=\".form-control-lg\" />\r\n          <input class=\"mb-3 form-control\" type=\"text\" placeholder=\"Default input\" />\r\n          <input class=\"mb-3 form-control form-control-sm\" type=\"text\" placeholder=\".form-control-sm\" />\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <select class=\"mb-3 form-control form-control-lg\">\r\n            <option>Large select</option>\r\n          </select>\r\n          <select class=\"mb-3 form-control\" title=\"select\">\r\n            <option>Default select</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-sm-12\">\r\n        <h5 class=\"mt-5\">Datalist</h5>\r\n        <hr />\r\n        <form action=\"\">\r\n          <div class=\"form-group\">\r\n            <label for=\"exampleDataList\" class=\"form-label\">Datalist example</label>\r\n            <input class=\"form-control\" list=\"datalistOptions\" id=\"exampleDataList\" placeholder=\"Type to search...\" />\r\n          </div>\r\n        </form>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-5\">Readonly</h5>\r\n          <hr />\r\n          <input class=\"form-control\" type=\"text\" placeholder=\"Readonly input here…\" readonly />\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-5\">Readonly plain Text</h5>\r\n          <hr />\r\n          <form action=\"javascript:\">\r\n            <div class=\"form-group row\">\r\n              <label for=\"staticEmail\" class=\"col-sm-3 col-form-label\">Email</label>\r\n              <div class=\"col-sm-9\">\r\n                <input type=\"text\" readonly class=\"form-control-plaintext\" id=\"staticEmail\" value=\"<EMAIL>\" />\r\n              </div>\r\n            </div>\r\n            <div class=\"form-group row\">\r\n              <label for=\"inputPassword\" class=\"col-sm-3 col-form-label\">Password</label>\r\n              <div class=\"col-sm-9\">\r\n                <input type=\"password\" class=\"form-control\" id=\"inputPassword\" placeholder=\"Password\" />\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n      <h5 class=\"mt-5\">Inline</h5>\r\n      <hr />\r\n      <form action=\"javascript:\" class=\"row row-cols-md-auto g-3 align-items-center\">\r\n        <div class=\"col-12\">\r\n          <label for=\"inlineFormInputName\" class=\"sr-only\">Name</label>\r\n          <input type=\"text\" readonly class=\"form-control\" id=\"inlineFormInputName\" />\r\n        </div>\r\n        <div class=\"col-12\">\r\n          <label for=\"inlineFormInputGroupUsername\" class=\"sr-only\">Username</label>\r\n          <div class=\"input-group\">\r\n            <div class=\"input-group-text\">&#64;</div>\r\n            <input type=\"text\" readonly class=\"form-control\" id=\"inlineFormInputGroupUsername\" placeholder=\"Username\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"col-12\">\r\n          <label for=\"inlineFormSelectPref\" class=\"sr-only\">Preference</label>\r\n          <select class=\"form-select\" id=\"inlineFormSelectPref\">\r\n            <option selected>Choose...</option>\r\n            <option value=\"1\">One</option>\r\n            <option value=\"2\">Two</option>\r\n            <option value=\"3\">Three</option>\r\n          </select>\r\n        </div>\r\n        <div class=\"col-12\">\r\n          <input type=\"checkbox\" class=\"form-check-input\" id=\"inlineFormCheck\" />\r\n          <label for=\"inlineFormCheck\" class=\"form-check-label px-2\">Remember me</label>\r\n        </div>\r\n        <div class=\"col-12\">\r\n          <button type=\"button\" class=\"btn btn-primary\">Submit</button>\r\n        </div>\r\n      </form>\r\n      <h5 class=\"mt-5\">Form Grid</h5>\r\n      <hr />\r\n      <form action=\"javascript:\">\r\n        <div class=\"row\">\r\n          <div class=\"form-group col-md-6\">\r\n            <label for=\"inputEmail4\">Email</label>\r\n            <input type=\"email\" class=\"form-control\" id=\"inputEmail4\" placeholder=\"Email\" />\r\n          </div>\r\n          <div class=\"form-group col-md-6\">\r\n            <label for=\"inputPassword4\">Password</label>\r\n            <input type=\"password\" class=\"form-control\" id=\"inputPassword4\" placeholder=\"Password\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"form-group\">\r\n          <label for=\"inputAddress\">Address</label>\r\n          <input type=\"text\" class=\"form-control\" id=\"inputAddress\" placeholder=\"1234 Main St\" />\r\n        </div>\r\n        <div class=\"form-group\">\r\n          <label for=\"inputAddress2\">Address 2</label>\r\n          <input type=\"text\" class=\"form-control\" id=\"inputAddress2\" placeholder=\"Apartment, studio, or floor\" />\r\n        </div>\r\n        <div class=\"row\">\r\n          <div class=\"form-group col-md-6\">\r\n            <label for=\"inputCity\">City</label>\r\n            <input type=\"text\" class=\"form-control\" id=\"inputCity\" />\r\n          </div>\r\n          <div class=\"form-group col-md-4\">\r\n            <label for=\"inputState\">State</label>\r\n            <select id=\"inputState\" class=\"form-control\">\r\n              <option selected>select</option>\r\n              <option>Large select</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"form-group col-md-2\">\r\n            <label for=\"inputZip\">Zip</label>\r\n            <input type=\"text\" class=\"form-control\" id=\"inputZip\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"form-group\">\r\n          <div class=\"form-check\">\r\n            <input class=\"form-check-input\" type=\"checkbox\" id=\"gridCheck\" />\r\n            <label class=\"form-check-label\" for=\"gridCheck\">Check me out</label>\r\n          </div>\r\n        </div>\r\n        <button type=\"submit\" class=\"btn btn-primary\">Sign in</button>\r\n      </form>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-5\">Horizontal Form</h5>\r\n          <hr />\r\n          <form action=\"javascript:\">\r\n            <div class=\"form-group row\">\r\n              <label for=\"inputEmail3\" class=\"col-sm-3 col-form-label\">Email</label>\r\n              <div class=\"col-sm-9\">\r\n                <input type=\"email\" class=\"form-control\" id=\"inputEmail3\" placeholder=\"Email\" />\r\n              </div>\r\n            </div>\r\n            <div class=\"form-group row\">\r\n              <label for=\"inputPassword3\" class=\"col-sm-3 col-form-label\">Password</label>\r\n              <div class=\"col-sm-9\">\r\n                <input type=\"password\" class=\"form-control\" id=\"inputPassword3\" placeholder=\"Password\" />\r\n              </div>\r\n            </div>\r\n            <fieldset class=\"form-group\">\r\n              <div class=\"row\">\r\n                <label for=\"inputPassword3\" class=\"col-sm-3 col-form-label\">Radios</label>\r\n                <div class=\"col-sm-9\">\r\n                  <div class=\"form-check\">\r\n                    <input class=\"form-check-input\" type=\"radio\" name=\"gridRadios\" id=\"gridRadios1\" value=\"option1\" checked />\r\n                    <label class=\"form-check-label\" for=\"gridRadios1\">First radio</label>\r\n                  </div>\r\n                  <div class=\"form-check\">\r\n                    <input class=\"form-check-input\" type=\"radio\" name=\"gridRadios\" id=\"gridRadios2\" value=\"option2\" />\r\n                    <label class=\"form-check-label\" for=\"gridRadios2\">Second radio</label>\r\n                  </div>\r\n                  <div class=\"form-check disabled\">\r\n                    <input class=\"form-check-input\" type=\"radio\" name=\"gridRadios\" id=\"gridRadios3\" value=\"option3\" disabled />\r\n                    <label class=\"form-check-label\" for=\"gridRadios3\">Third disabled radio</label>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </fieldset>\r\n            <div class=\"form-group row\">\r\n              <div class=\"col-sm-3\">Checkbox</div>\r\n              <div class=\"col-sm-9\">\r\n                <div class=\"form-check\">\r\n                  <input class=\"form-check-input\" type=\"checkbox\" id=\"gridCheck1\" />\r\n                  <label class=\"form-check-label\" for=\"gridCheck1\">Example checkbox</label>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"form-group row\">\r\n              <div class=\"col-sm-10\">\r\n                <button type=\"submit\" class=\"btn btn-primary\">Sign in</button>\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-5\">Horizontal Form Label Sizing</h5>\r\n          <hr />\r\n          <form action=\"javascript:\">\r\n            <div class=\"form-group row\">\r\n              <label for=\"colFormLabelSm\" class=\"col-sm-3 col-form-label col-form-label-sm\">Email</label>\r\n              <div class=\"col-sm-9\">\r\n                <input type=\"email\" class=\"form-control form-control-sm\" id=\"colFormLabelSm\" placeholder=\"col-form-label-sm\" />\r\n              </div>\r\n            </div>\r\n            <div class=\"form-group row\">\r\n              <label for=\"colFormLabel\" class=\"col-sm-3 col-form-label\">Email</label>\r\n              <div class=\"col-sm-9\">\r\n                <input type=\"email\" class=\"form-control\" id=\"colFormLabel\" placeholder=\"col-form-label\" />\r\n              </div>\r\n            </div>\r\n            <div class=\"form-group row\">\r\n              <label for=\"colFormLabelLg\" class=\"col-sm-3 col-form-label col-form-label-lg\">Email</label>\r\n              <div class=\"col-sm-9\">\r\n                <input type=\"email\" class=\"form-control form-control-lg\" id=\"colFormLabelLg\" placeholder=\"col-form-label-lg\" />\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n      <h5 class=\"mt-5\">Help Text</h5>\r\n      <hr />\r\n      <label for=\"inputPassword5\">Password</label>\r\n      <input type=\"password\" id=\"inputPassword5\" class=\"form-control\" aria-describedby=\"passwordHelpBlock\" />\r\n      <small id=\"passwordHelpBlock\" class=\"form-text text-muted\">\r\n        Your password must be 8-20 characters long, contain letters and numbers, and must not contain spaces, special characters, or emoji.\r\n      </small>\r\n      <form action=\"javascript:\" class=\"form-inline\">\r\n        <div class=\"form-group mt-3\">\r\n          <label for=\"inputPassword6\">Password</label>\r\n          <input type=\"password\" id=\"inputPassword6\" class=\"form-control\" aria-describedby=\"passwordHelpInline\" />\r\n          <small id=\"passwordHelpInline\" class=\"text-muted\">Must be 8-20 characters long.</small>\r\n        </div>\r\n      </form>\r\n      <h5 class=\"mt-5\">Validation</h5>\r\n      <hr />\r\n      <form action=\"javascript:\" class=\"needs-validation\" novalidate>\r\n        <div class=\"row\">\r\n          <div class=\"col-md-4 mb-3\">\r\n            <label for=\"validationCustom01\">First name</label>\r\n            <input type=\"text\" class=\"form-control\" id=\"validationCustom01\" placeholder=\"First name\" value=\"Mark\" required />\r\n            <div class=\"valid-feedback\">Looks good!</div>\r\n          </div>\r\n          <div class=\"col-md-4 mb-3\">\r\n            <label for=\"validationCustom02\">Last name</label>\r\n            <input type=\"text\" class=\"form-control\" id=\"validationCustom02\" placeholder=\"Last name\" value=\"Otto\" required />\r\n            <div class=\"valid-feedback\">Looks good!</div>\r\n          </div>\r\n          <div class=\"col-md-4 mb-3\">\r\n            <label for=\"validationTooltipUsername\">Username</label>\r\n            <div class=\"input-group\">\r\n              <span class=\"input-group-text\" id=\"validationTooltipUsernamePrepend\">&#64;</span>\r\n              <input\r\n                type=\"text\"\r\n                class=\"form-control\"\r\n                id=\"validationTooltipUsername\"\r\n                placeholder=\"Username\"\r\n                aria-describedby=\"validationTooltipUsernamePrepend\"\r\n                required\r\n              />\r\n              <div class=\"invalid-feedback\">Please choose a username.</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"row\">\r\n          <div class=\"col-md-6 mb-3\">\r\n            <label for=\"validationCustom03\">City</label>\r\n            <input type=\"text\" class=\"form-control\" id=\"validationCustom03\" placeholder=\"City\" required />\r\n            <div class=\"invalid-feedback\">Please provide a valid city.</div>\r\n          </div>\r\n          <div class=\"col-md-3 mb-3\">\r\n            <label for=\"validationCustom04\">State</label>\r\n            <input type=\"text\" class=\"form-control\" id=\"validationCustom04\" placeholder=\"State\" required />\r\n            <div class=\"invalid-feedback\">Please provide a valid state.</div>\r\n          </div>\r\n          <div class=\"col-md-3 mb-3\">\r\n            <label for=\"validationCustom05\">Zip</label>\r\n            <input type=\"text\" class=\"form-control\" id=\"validationCustom05\" placeholder=\"Zip\" required />\r\n            <div class=\"invalid-feedback\">Please provide a valid zip.</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"form-group\">\r\n          <div class=\"form-check\">\r\n            <input class=\"form-check-input\" type=\"checkbox\" value=\"\" id=\"invalidCheck\" required />\r\n            <label class=\"form-check-label\" for=\"invalidCheck\">Agree to terms and conditions</label>\r\n            <div class=\"invalid-feedback\">You must agree before submitting.</div>\r\n          </div>\r\n        </div>\r\n        <button class=\"btn btn-primary\" type=\"submit\">Submit form</button>\r\n      </form>\r\n      <h5 class=\"mt-5\">Supported Elements</h5>\r\n      <hr />\r\n      <form class=\"was-validated\">\r\n        <div class=\"mb-3\">\r\n          <label for=\"validationTextarea\" class=\"form-label\">Textarea</label>\r\n          <textarea class=\"form-control\" id=\"validationTextarea\" placeholder=\"Required example textarea\" required></textarea>\r\n          <div class=\"invalid-feedback\">Please enter a message in the textarea.</div>\r\n        </div>\r\n\r\n        <div class=\"form-check mb-3\">\r\n          <input type=\"checkbox\" class=\"form-check-input\" id=\"validationFormCheck1\" required />\r\n          <label class=\"form-check-label\" for=\"validationFormCheck1\">Check this checkbox</label>\r\n          <div class=\"invalid-feedback\">Example invalid feedback text</div>\r\n        </div>\r\n\r\n        <div class=\"form-check\">\r\n          <input type=\"radio\" class=\"form-check-input\" id=\"validationFormCheck2\" name=\"radio-stacked\" required />\r\n          <label class=\"form-check-label\" for=\"validationFormCheck2\">Toggle this radio</label>\r\n        </div>\r\n        <div class=\"form-check mb-3\">\r\n          <input type=\"radio\" class=\"form-check-input\" id=\"validationFormCheck3\" name=\"radio-stacked\" required />\r\n          <label class=\"form-check-label\" for=\"validationFormCheck3\">Or toggle this other radio</label>\r\n          <div class=\"invalid-feedback\">More example invalid feedback text</div>\r\n        </div>\r\n\r\n        <div class=\"mb-3\">\r\n          <select class=\"form-select\" required aria-label=\"select example\">\r\n            <option value=\"\">Open this select menu</option>\r\n            <option value=\"1\">One</option>\r\n            <option value=\"2\">Two</option>\r\n            <option value=\"3\">Three</option>\r\n          </select>\r\n          <div class=\"invalid-feedback\">Example invalid select feedback</div>\r\n        </div>\r\n\r\n        <div class=\"mb-3\">\r\n          <input type=\"file\" class=\"form-control\" aria-label=\"file example\" required />\r\n          <div class=\"invalid-feedback\">Example invalid form file feedback</div>\r\n        </div>\r\n\r\n        <div class=\"mb-3\">\r\n          <button class=\"btn btn-primary\" type=\"submit\" disabled>Submit form</button>\r\n        </div>\r\n      </form>\r\n      <div class=\"mb-3\">\r\n        <button type=\"button\" class=\"btn btn-primary\" type=\"submit\" disabled>Submitform</button>\r\n      </div>\r\n      <h5 class=\"mt-5\">Tooltips</h5>\r\n      <hr />\r\n      <form action=\"javascript:\" class=\"needs-validation\" novalidate>\r\n        <div class=\"row\">\r\n          <div class=\"col-md-4 mb-3\">\r\n            <label for=\"validationTooltip01\">First name</label>\r\n            <input type=\"text\" class=\"form-control\" id=\"validationTooltip01\" placeholder=\"First name\" value=\"Mark\" required />\r\n            <div class=\"valid-tooltip\">Looks good!</div>\r\n          </div>\r\n          <div class=\"col-md-4 mb-3\">\r\n            <label for=\"validationTooltip02\">Last name</label>\r\n            <input type=\"text\" class=\"form-control\" id=\"validationTooltip02\" placeholder=\"Last name\" value=\"Otto\" required />\r\n            <div class=\"valid-tooltip\">Looks good!</div>\r\n          </div>\r\n          <div class=\"col-md-4 mb-3\">\r\n            <label for=\"validationTooltipUsername\">Username</label>\r\n            <div class=\"input-group\">\r\n              <div class=\"input-group\">\r\n                <span class=\"input-group-text\" id=\"validationTooltipUsernamePrepend\">&#64;</span>\r\n                <input\r\n                  type=\"text\"\r\n                  class=\"form-control\"\r\n                  id=\"validationTooltipUsername\"\r\n                  placeholder=\"Username\"\r\n                  aria-describedby=\"validationTooltipUsernamePrepend\"\r\n                  required\r\n                />\r\n              </div>\r\n              <div class=\"invalid-tooltip\">Please choose a unique and valid username.</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"row\">\r\n          <div class=\"col-md-6 mb-3\">\r\n            <label for=\"validationTooltip03\">City</label>\r\n            <input type=\"text\" class=\"form-control\" id=\"validationTooltip03\" placeholder=\"City\" required />\r\n            <div class=\"invalid-tooltip\">Please provide a valid city.</div>\r\n          </div>\r\n          <div class=\"col-md-3 mb-3\">\r\n            <label for=\"validationTooltip04\">State</label>\r\n            <input type=\"text\" class=\"form-control\" id=\"validationTooltip04\" placeholder=\"State\" required />\r\n            <div class=\"invalid-tooltip\">Please provide a valid state.</div>\r\n          </div>\r\n          <div class=\"col-md-3 mb-3\">\r\n            <label for=\"validationTooltip05\">Zip</label>\r\n            <input type=\"text\" class=\"form-control\" id=\"validationTooltip05\" placeholder=\"Zip\" required />\r\n            <div class=\"invalid-tooltip\">Please provide a valid zip.</div>\r\n          </div>\r\n        </div>\r\n        <button class=\"btn btn-primary\" type=\"submit\">Submit form</button>\r\n      </form>\r\n      <h3 class=\"mt-5\">Checkboxes and Radios</h3>\r\n      <h5 class=\"mt-3\">Checkboxes</h5>\r\n      <hr />\r\n      <div class=\"custom-control custom-checkbox\">\r\n        <input type=\"checkbox\" class=\"custom-control-input\" id=\"customCheck1\" />\r\n        <label class=\"custom-control-label ps-2\" for=\"customCheck1\">Check this custom checkbox</label>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-5\">Radios</h5>\r\n          <hr />\r\n          <div class=\"custom-control custom-radio mb-1\">\r\n            <input type=\"radio\" id=\"customRadio1\" name=\"customRadio\" class=\"custom-control-input\" />\r\n            <label class=\"custom-control-label ps-2\" for=\"customRadio1\">Toggle this custom radio</label>\r\n          </div>\r\n          <div class=\"custom-control custom-radio\">\r\n            <input type=\"radio\" id=\"customRadio2\" name=\"customRadio\" class=\"custom-control-input\" />\r\n            <label class=\"custom-control-label ps-2\" for=\"customRadio2\">Or toggle this other custom radio</label>\r\n          </div>\r\n          <h5 class=\"mt-5\">Inline</h5>\r\n          <hr />\r\n          <div class=\"custom-control custom-radio custom-control-inline mb-1\">\r\n            <input type=\"radio\" id=\"customRadioInline1\" name=\"customRadioInline1\" class=\"custom-control-input\" />\r\n            <label class=\"custom-control-label ps-2\" for=\"customRadioInline1\">Toggle this custom radio</label>\r\n          </div>\r\n          <div class=\"custom-control custom-radio custom-control-inline\">\r\n            <input type=\"radio\" id=\"customRadioInline2\" name=\"customRadioInline1\" class=\"custom-control-input\" />\r\n            <label class=\"custom-control-label ps-2\" for=\"customRadioInline2\">Or toggle this other custom radio</label>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-5\">Range</h5>\r\n          <hr />\r\n          <label for=\"customRange1\">Example range</label>\r\n          <input type=\"range\" class=\"form-range d-block\" id=\"customRange1\" />\r\n          <label for=\"customRange2\">Example range</label>\r\n          <input type=\"range\" class=\"form-range d-block\" min=\"0\" max=\"5\" id=\"customRange2\" />\r\n          <label for=\"customRange3\">Example range</label>\r\n          <input type=\"range\" class=\"form-range d-block\" min=\"0\" max=\"5\" step=\"0.5\" id=\"customRange3\" />\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n  <div class=\"col-sm-12\">\r\n    <app-card cardTitle=\"Input Group\" [options]=\"false\">\r\n      <div class=\"input-group mb-3\">\r\n        <span class=\"input-group-text\" id=\"basic-addon1\">&#64;</span>\r\n        <input type=\"text\" class=\"form-control\" placeholder=\"Username\" aria-label=\"Username\" aria-describedby=\"basic-addon1\" />\r\n      </div>\r\n      <div class=\"input-group mb-3\">\r\n        <input\r\n          type=\"text\"\r\n          class=\"form-control\"\r\n          placeholder=\"Recipient's username\"\r\n          aria-label=\"Recipient's username\"\r\n          aria-describedby=\"basic-addon2\"\r\n        />\r\n        <span class=\"input-group-text\" id=\"basic-addon2\">&#64;example.com</span>\r\n      </div>\r\n      <label for=\"basic-url\" class=\"form-label\">Your vanity URL</label>\r\n      <div class=\"input-group mb-3\">\r\n        <span class=\"input-group-text\" id=\"basic-addon3\">https://example.com/users/</span>\r\n        <input type=\"text\" class=\"form-control\" id=\"basic-url\" aria-describedby=\"basic-addon3\" />\r\n      </div>\r\n      <div class=\"input-group mb-3\">\r\n        <span class=\"input-group-text\">$</span>\r\n        <input type=\"text\" class=\"form-control\" aria-label=\"Amount (to the nearest dollar)\" />\r\n        <span class=\"input-group-text\">.00</span>\r\n      </div>\r\n      <div class=\"input-group\">\r\n        <span class=\"input-group-text\">With textarea</span>\r\n        <textarea class=\"form-control\" aria-label=\"With textarea\"></textarea>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-5\">Sizing</h5>\r\n          <hr />\r\n          <div class=\"input-group input-group-sm mb-3\">\r\n            <span class=\"input-group-text\" id=\"inputGroup-sizing-sm\">Small</span>\r\n            <input type=\"text\" class=\"form-control\" aria-label=\"Sizing example input\" aria-describedby=\"inputGroup-sizing-sm\" />\r\n          </div>\r\n          <div class=\"input-group mb-3\">\r\n            <span class=\"input-group-text\" id=\"inputGroup-sizing-default\">Default</span>\r\n            <input type=\"text\" class=\"form-control\" aria-label=\"Sizing example input\" aria-describedby=\"inputGroup-sizing-default\" />\r\n          </div>\r\n          <div class=\"input-group input-group-lg\">\r\n            <span class=\"input-group-text\" id=\"inputGroup-sizing-lg\">Large</span>\r\n            <input type=\"text\" class=\"form-control\" aria-label=\"Sizing example input\" aria-describedby=\"inputGroup-sizing-lg\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-5\">Checkboxes and radios</h5>\r\n          <hr />\r\n          <div class=\"input-group mb-3\">\r\n            <div class=\"input-group-text\">\r\n              <input class=\"form-check-input mt-0\" type=\"checkbox\" value=\"\" aria-label=\"Checkbox for following text input\" />\r\n            </div>\r\n            <input type=\"text\" class=\"form-control\" aria-label=\"Text input with checkbox\" />\r\n          </div>\r\n          <div class=\"input-group\">\r\n            <div class=\"input-group-text\">\r\n              <input class=\"form-check-input mt-0\" type=\"radio\" value=\"\" aria-label=\"Radio button for following text input\" />\r\n            </div>\r\n            <input type=\"text\" class=\"form-control\" aria-label=\"Text input with radio button\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-5\">Multiple inputs</h5>\r\n          <hr />\r\n          <div class=\"input-group\">\r\n            <span class=\"input-group-text\">First and last name</span>\r\n            <input type=\"text\" aria-label=\"First name\" class=\"form-control\" />\r\n            <input type=\"text\" aria-label=\"Last name\" class=\"form-control\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-5\">Multiple addons</h5>\r\n          <hr />\r\n          <div class=\"input-group mb-3\">\r\n            <span class=\"input-group-text\">$</span>\r\n            <span class=\"input-group-text\">0.00</span>\r\n            <input type=\"text\" class=\"form-control\" aria-label=\"Dollar amount (with dot and two decimal places)\" />\r\n          </div>\r\n\r\n          <div class=\"input-group\">\r\n            <input type=\"text\" class=\"form-control\" aria-label=\"Dollar amount (with dot and two decimal places)\" />\r\n            <span class=\"input-group-text\">$</span>\r\n            <span class=\"input-group-text\">0.00</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <h5 class=\"mt-5\">Button Addons</h5>\r\n      <hr />\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\">\r\n          <div class=\"input-group mb-3\">\r\n            <button class=\"btn btn-outline-secondary\" type=\"button\" id=\"button-addon1\">Button</button>\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control\"\r\n              placeholder=\"\"\r\n              aria-label=\"Example text with button addon\"\r\n              aria-describedby=\"button-addon1\"\r\n            />\r\n          </div>\r\n\r\n          <div class=\"input-group mb-3\">\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control\"\r\n              placeholder=\"Recipient's username\"\r\n              aria-label=\"Recipient's username\"\r\n              aria-describedby=\"button-addon2\"\r\n            />\r\n            <button class=\"btn btn-outline-secondary\" type=\"button\" id=\"button-addon2\">Button</button>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <div class=\"input-group mb-3\">\r\n            <button class=\"btn btn-outline-secondary\" type=\"button\">Button</button>\r\n            <button class=\"btn btn-outline-secondary\" type=\"button\">Button</button>\r\n            <input type=\"text\" class=\"form-control\" placeholder=\"\" aria-label=\"Example text with two button addons\" />\r\n          </div>\r\n          <div class=\"input-group\">\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control\"\r\n              placeholder=\"Recipient's username\"\r\n              aria-label=\"Recipient's username with two button addons\"\r\n            />\r\n            <button class=\"btn btn-outline-secondary\" type=\"button\">Button</button>\r\n            <button class=\"btn btn-outline-secondary\" type=\"button\">Button</button>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-5\">Buttons With Dropdowns</h5>\r\n          <hr />\r\n          <div class=\"input-group mb-3\">\r\n            <div class=\"input-group-prepend\" ngbDropdown placement=\"bottom-start\">\r\n              <button class=\"btn btn-outline-secondary one-dropdown\" ngbDropdownToggle type=\"button\">Dropdown</button>\r\n              <div ngbDropdownMenu>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n                <div role=\"separator\" class=\"dropdown-divider\"></div>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n              </div>\r\n            </div>\r\n            <input type=\"text\" class=\"form-control\" aria-label=\"Text input with dropdown button\" />\r\n          </div>\r\n          <div class=\"input-group mb-3\">\r\n            <input type=\"text\" class=\"form-control\" aria-label=\"Text input with dropdown button\" />\r\n            <div class=\"input-group-append\" ngbDropdown [placement]=\"'bottom-left'\">\r\n              <button class=\"btn btn-outline-secondary one-dropdown\" ngbDropdownToggle type=\"button\">Dropdown</button>\r\n              <div ngbDropdownMenu>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n                <div role=\"separator\" class=\"dropdown-divider\"></div>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"input-group\" ngbDropdown placement=\"bottom-start\">\r\n            <button class=\"btn btn-outline-secondary\" type=\"button\" aria-expanded=\"false\" ngbDropdownToggle>Dropdown</button>\r\n            <ul ngbDropdownMenu>\r\n              <li>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Action before</a>\r\n              </li>\r\n              <li>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Another action before</a>\r\n              </li>\r\n              <li>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n              </li>\r\n              <li><hr class=\"dropdown-divider\" /></li>\r\n              <li>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n              </li>\r\n            </ul>\r\n            <input type=\"text\" class=\"form-control\" aria-label=\"Text input with 2 dropdown buttons\" />\r\n            <button class=\"btn btn-outline-secondary dropdown-toggle\" type=\"button\" aria-expanded=\"false\" ngbDropdownToggle>\r\n              Dropdown\r\n            </button>\r\n            <ul class=\"dropdown-menu dropdown-menu-end\" ngbDropdownMenu1>\r\n              <li><a class=\"dropdown-item\" href=\"javascript:\">Action</a></li>\r\n              <li>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n              </li>\r\n              <li>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n              </li>\r\n              <li><hr class=\"dropdown-divider\" /></li>\r\n              <li>\r\n                <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-5\">Segmented Buttons</h5>\r\n          <hr />\r\n          <div class=\"input-group mb-3\" ngbDropdown [placement]=\"'bottom-left'\">\r\n            <button type=\"button\" class=\"btn btn-outline-secondary\">Action</button>\r\n            <button type=\"button\" class=\"btn btn-outline-secondary dropdown-toggle-split\" ngbDropdownToggle>\r\n              <span class=\"sr-only\">Toggle Dropdown</span>\r\n            </button>\r\n            <div ngbDropdownMenu>\r\n              <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n              <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n              <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n              <div role=\"separator\" class=\"dropdown-divider\"></div>\r\n              <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n            </div>\r\n            <input type=\"text\" class=\"form-control\" aria-label=\"Text input with segmented dropdown button\" />\r\n          </div>\r\n          <div class=\"input-group\" ngbDropdown>\r\n            <input type=\"text\" class=\"form-control\" aria-label=\"Text input with segmented dropdown button\" />\r\n            <button type=\"button\" class=\"btn btn-outline-secondary\">Action</button>\r\n            <button type=\"button\" class=\"btn btn-outline-secondary dropdown-toggle-split\" ngbDropdownToggle>\r\n              <span class=\"sr-only\">Toggle Dropdown</span>\r\n            </button>\r\n            <div ngbDropdownMenu>\r\n              <a class=\"dropdown-item\" href=\"javascript:\">Action</a>\r\n              <a class=\"dropdown-item\" href=\"javascript:\">Another action</a>\r\n              <a class=\"dropdown-item\" href=\"javascript:\">Something else here</a>\r\n              <div role=\"separator\" class=\"dropdown-divider\"></div>\r\n              <a class=\"dropdown-item\" href=\"javascript:\">Separated link</a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <h3 class=\"mt-5\">Custom Forms</h3>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-3\">Custom Select</h5>\r\n          <hr />\r\n          <div class=\"input-group mb-3\">\r\n            <div class=\"input-group-prepend\">\r\n              <label class=\"input-group-text\" for=\"inputGroupSelect01\">Options</label>\r\n            </div>\r\n            <select class=\"form-select\" id=\"inputGroupSelect01\">\r\n              <option selected>Choose...</option>\r\n              <option value=\"1\">One</option>\r\n              <option value=\"2\">Two</option>\r\n              <option value=\"3\">Three</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"input-group mb-3\">\r\n            <select class=\"form-select\" id=\"inputGroupSelect02\">\r\n              <option selected>Choose...</option>\r\n              <option value=\"1\">One</option>\r\n              <option value=\"2\">Two</option>\r\n              <option value=\"3\">Three</option>\r\n            </select>\r\n            <div class=\"input-group-append\">\r\n              <label class=\"input-group-text\" for=\"inputGroupSelect02\">Options</label>\r\n            </div>\r\n          </div>\r\n          <div class=\"input-group mb-3\">\r\n            <button class=\"btn btn-outline-secondary\" type=\"button\">Button</button>\r\n            <select class=\"form-select\" id=\"inputGroupSelect03\">\r\n              <option selected>Choose...</option>\r\n              <option value=\"1\">One</option>\r\n              <option value=\"2\">Two</option>\r\n              <option value=\"3\">Three</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"input-group\">\r\n            <select class=\"form-select\" id=\"inputGroupSelect04\">\r\n              <option selected>Choose...</option>\r\n              <option value=\"1\">One</option>\r\n              <option value=\"2\">Two</option>\r\n              <option value=\"3\">Three</option>\r\n            </select>\r\n            <button class=\"btn btn-outline-secondary\" type=\"button\">Button</button>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <h5 class=\"mt-3\">Custom File Input</h5>\r\n          <hr />\r\n          <div class=\"input-group mb-3\">\r\n            <label for=\"inputGroupFile01\" class=\"input-group-text\">Upload</label>\r\n            <input type=\"file\" class=\"form-control\" id=\"inputGroupFile01\" />\r\n          </div>\r\n          <div class=\"input-group mb-3\">\r\n            <input type=\"file\" class=\"form-control\" id=\"inputGroupFile01\" />\r\n            <label for=\"inputGroupFile01\" class=\"input-group-text\">Upload</label>\r\n          </div>\r\n          <div class=\"input-group mb-3\">\r\n            <button class=\"btn btn-outline-secondary\" id=\"inputGroupFileAddon03\" type=\"button\">Button</button>\r\n            <input type=\"file\" class=\"form-control\" id=\"inputGroupFile03\" aria-describedby=\"inputGroupFileAddon03\" aria-label=\"Upload\" />\r\n          </div>\r\n          <div class=\"input-group\">\r\n            <input type=\"file\" class=\"form-control\" id=\"inputGroupFile03\" aria-describedby=\"inputGroupFileAddon03\" aria-label=\"Upload\" />\r\n            <button class=\"btn btn-outline-secondary\" id=\"inputGroupFileAddon03\" type=\"button\">Button</button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </app-card>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,oCAAoC;AACjE,SAASC,iBAAiB,QAAQ,4BAA4B;;;;;AAS9D,eAAc,MAAOC,sBAAsB;;;uCAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRrCE,EAHN,CAAAC,cAAA,aAAiB,aACQ,kBACmC,SAClD;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAI,SAAA,SAAM;UAKEJ,EAJR,CAAAC,cAAA,aAAiB,aACO,cACO,aACD,gBACU;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAI,SAAA,gBAA0H;UAC1HJ,EAAA,CAAAC,cAAA,gBAAmD;UAAAD,EAAA,CAAAE,MAAA,sDAA8C;UACnGF,EADmG,CAAAG,YAAA,EAAQ,EACrG;UAEJH,EADF,CAAAC,cAAA,cAAwB,gBACa;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAI,SAAA,iBAAgG;UAClGJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAmC;UACjCD,EAAA,CAAAI,SAAA,iBAAqE;UACrEJ,EAAA,CAAAC,cAAA,iBAAoD;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAClEF,EADkE,CAAAG,YAAA,EAAQ,EACpE;UACNH,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAExDF,EAFwD,CAAAG,YAAA,EAAS,EACxD,EACH;UAIAH,EAHN,CAAAC,cAAA,cAAsB,eACO,cACD,iBACJ;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9BH,EAAA,CAAAI,SAAA,iBAA6D;UAC/DJ,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,cAAwB,iBACiB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE3DH,EADF,CAAAC,cAAA,kBAA4D,cAClD;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClBH,EAAA,CAAAC,cAAA,cAAQ;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClBH,EAAA,CAAAC,cAAA,cAAQ;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClBH,EAAA,CAAAC,cAAA,cAAQ;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClBH,EAAA,CAAAC,cAAA,cAAQ;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAEbF,EAFa,CAAAG,YAAA,EAAS,EACX,EACL;UAEJH,EADF,CAAAC,cAAA,cAAwB,iBACmB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjEH,EAAA,CAAAI,SAAA,oBAAoF;UAI5FJ,EAHM,CAAAG,YAAA,EAAM,EACD,EACH,EACF;UACNH,EAAA,CAAAC,cAAA,cAAiB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAI,SAAA,UAAM;UAEJJ,EADF,CAAAC,cAAA,cAAiB,cACO;UAGpBD,EAFA,CAAAI,SAAA,iBAA8F,iBACnB,iBACmB;UAChGJ,EAAA,CAAAG,YAAA,EAAM;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,kBAC8B,cACxC;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UACtBF,EADsB,CAAAG,YAAA,EAAS,EACtB;UAEPH,EADF,CAAAC,cAAA,kBAAiD,cACvC;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAG5BF,EAH4B,CAAAG,YAAA,EAAS,EACxB,EACL,EACF;UAEJH,EADF,CAAAC,cAAA,cAAuB,cACJ;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9BH,EAAA,CAAAI,SAAA,UAAM;UAGFJ,EAFJ,CAAAC,cAAA,gBAAgB,cACU,iBAC0B;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxEH,EAAA,CAAAI,SAAA,iBAA0G;UAGhHJ,EAFI,CAAAG,YAAA,EAAM,EACD,EACH;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,cACO,cACH;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE9BH,EADA,CAAAI,SAAA,UAAM,iBACgF;UACxFJ,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,cAAsB,cACH;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAI,SAAA,UAAM;UAGFJ,EAFJ,CAAAC,cAAA,eAA2B,eACG,iBAC+B;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAI,SAAA,iBAAwG;UAE5GJ,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,eAA4B,iBACiC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3EH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAI,SAAA,iBAAwF;UAKlGJ,EAJQ,CAAAG,YAAA,EAAM,EACF,EACD,EACH,EACF;UACNH,EAAA,CAAAC,cAAA,cAAiB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAI,SAAA,UAAM;UAGFJ,EAFJ,CAAAC,cAAA,gBAA+E,eACzD,iBAC+B;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7DH,EAAA,CAAAI,SAAA,kBAA4E;UAC9EJ,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,gBAAoB,kBACwC;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAExEH,EADF,CAAAC,cAAA,gBAAyB,gBACO;UAAAD,EAAA,CAAAE,MAAA,UAAK;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzCH,EAAA,CAAAI,SAAA,kBAA4G;UAEhHJ,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,gBAAoB,kBACgC;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAElEH,EADF,CAAAC,cAAA,mBAAsD,mBACnC;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnCH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAE3BF,EAF2B,CAAAG,YAAA,EAAS,EACzB,EACL;UACNH,EAAA,CAAAC,cAAA,gBAAoB;UAClBD,EAAA,CAAAI,SAAA,kBAAuE;UACvEJ,EAAA,CAAAC,cAAA,kBAA2D;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACxEF,EADwE,CAAAG,YAAA,EAAQ,EAC1E;UAEJH,EADF,CAAAC,cAAA,gBAAoB,mBAC4B;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAExDF,EAFwD,CAAAG,YAAA,EAAS,EACzD,EACD;UACPH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAI,SAAA,WAAM;UAIAJ,EAHN,CAAAC,cAAA,gBAA2B,eACR,gBACkB,kBACN;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAI,SAAA,kBAAgF;UAClFJ,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,gBAAiC,kBACH;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAI,SAAA,kBAAyF;UAE7FJ,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,eAAwB,kBACI;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzCH,EAAA,CAAAI,SAAA,kBAAuF;UACzFJ,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAwB,kBACK;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAI,SAAA,kBAAuG;UACzGJ,EAAA,CAAAG,YAAA,EAAM;UAGFH,EAFJ,CAAAC,cAAA,eAAiB,gBACkB,kBACR;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnCH,EAAA,CAAAI,SAAA,kBAAyD;UAC3DJ,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,gBAAiC,kBACP;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEnCH,EADF,CAAAC,cAAA,mBAA6C,mBAC1B;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChCH,EAAA,CAAAC,cAAA,eAAQ;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAExBF,EAFwB,CAAAG,YAAA,EAAS,EACtB,EACL;UAEJH,EADF,CAAAC,cAAA,gBAAiC,kBACT;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjCH,EAAA,CAAAI,SAAA,kBAAwD;UAE5DJ,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,eAAwB,gBACE;UACtBD,EAAA,CAAAI,SAAA,kBAAiE;UACjEJ,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAEhEF,EAFgE,CAAAG,YAAA,EAAQ,EAChE,EACF;UACNH,EAAA,CAAAC,cAAA,mBAA8C;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UACvDF,EADuD,CAAAG,YAAA,EAAS,EACzD;UAGHH,EAFJ,CAAAC,cAAA,eAAiB,eACO,eACH;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAI,SAAA,WAAM;UAGFJ,EAFJ,CAAAC,cAAA,gBAA2B,gBACG,kBAC+B;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAAC,cAAA,gBAAsB;UACpBD,EAAA,CAAAI,SAAA,kBAAgF;UAEpFJ,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,gBAA4B,kBACkC;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5EH,EAAA,CAAAC,cAAA,gBAAsB;UACpBD,EAAA,CAAAI,SAAA,kBAAyF;UAE7FJ,EADE,CAAAG,YAAA,EAAM,EACF;UAGFH,EAFJ,CAAAC,cAAA,oBAA6B,eACV,kBAC6C;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAExEH,EADF,CAAAC,cAAA,gBAAsB,gBACI;UACtBD,EAAA,CAAAI,SAAA,kBAA0G;UAC1GJ,EAAA,CAAAC,cAAA,kBAAkD;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAC/DF,EAD+D,CAAAG,YAAA,EAAQ,EACjE;UACNH,EAAA,CAAAC,cAAA,gBAAwB;UACtBD,EAAA,CAAAI,SAAA,kBAAkG;UAClGJ,EAAA,CAAAC,cAAA,kBAAkD;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAChEF,EADgE,CAAAG,YAAA,EAAQ,EAClE;UACNH,EAAA,CAAAC,cAAA,gBAAiC;UAC/BD,EAAA,CAAAI,SAAA,kBAA2G;UAC3GJ,EAAA,CAAAC,cAAA,kBAAkD;UAAAD,EAAA,CAAAE,MAAA,6BAAoB;UAI9EF,EAJ8E,CAAAG,YAAA,EAAQ,EAC1E,EACF,EACF,EACG;UAETH,EADF,CAAAC,cAAA,gBAA4B,gBACJ;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAElCH,EADF,CAAAC,cAAA,gBAAsB,gBACI;UACtBD,EAAA,CAAAI,SAAA,kBAAkE;UAClEJ,EAAA,CAAAC,cAAA,kBAAiD;UAAAD,EAAA,CAAAE,MAAA,yBAAgB;UAGvEF,EAHuE,CAAAG,YAAA,EAAQ,EACrE,EACF,EACF;UAGFH,EAFJ,CAAAC,cAAA,gBAA4B,gBACH,mBACyB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAI7DF,EAJ6D,CAAAG,YAAA,EAAS,EAC1D,EACF,EACD,EACH;UAEJH,EADF,CAAAC,cAAA,eAAsB,eACH;UAAAD,EAAA,CAAAE,MAAA,qCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAI,SAAA,WAAM;UAGFJ,EAFJ,CAAAC,cAAA,gBAA2B,gBACG,kBACoD;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3FH,EAAA,CAAAC,cAAA,gBAAsB;UACpBD,EAAA,CAAAI,SAAA,kBAA+G;UAEnHJ,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,gBAA4B,kBACgC;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvEH,EAAA,CAAAC,cAAA,gBAAsB;UACpBD,EAAA,CAAAI,SAAA,kBAA0F;UAE9FJ,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,gBAA4B,kBACoD;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3FH,EAAA,CAAAC,cAAA,gBAAsB;UACpBD,EAAA,CAAAI,SAAA,kBAA+G;UAKzHJ,EAJQ,CAAAG,YAAA,EAAM,EACF,EACD,EACH,EACF;UACNH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAI,SAAA,WAAM;UACNJ,EAAA,CAAAC,cAAA,kBAA4B;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAI,SAAA,kBAAuG;UACvGJ,EAAA,CAAAC,cAAA,kBAA2D;UACzDD,EAAA,CAAAE,MAAA,8IACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGJH,EAFJ,CAAAC,cAAA,iBAA+C,gBAChB,mBACC;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAI,SAAA,mBAAwG;UACxGJ,EAAA,CAAAC,cAAA,mBAAkD;UAAAD,EAAA,CAAAE,MAAA,sCAA6B;UAEnFF,EAFmF,CAAAG,YAAA,EAAQ,EACnF,EACD;UACPH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAI,SAAA,WAAM;UAIAJ,EAHN,CAAAC,cAAA,kBAA+D,eAC5C,iBACY,mBACO;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClDH,EAAA,CAAAI,SAAA,mBAAiH;UACjHJ,EAAA,CAAAC,cAAA,iBAA4B;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACzCF,EADyC,CAAAG,YAAA,EAAM,EACzC;UAEJH,EADF,CAAAC,cAAA,iBAA2B,mBACO;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAI,SAAA,mBAAgH;UAChHJ,EAAA,CAAAC,cAAA,iBAA4B;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACzCF,EADyC,CAAAG,YAAA,EAAM,EACzC;UAEJH,EADF,CAAAC,cAAA,iBAA2B,mBACc;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAErDH,EADF,CAAAC,cAAA,gBAAyB,kBAC8C;UAAAD,EAAA,CAAAE,MAAA,UAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjFH,EAAA,CAAAI,SAAA,mBAOE;UACFJ,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,kCAAyB;UAG7DF,EAH6D,CAAAG,YAAA,EAAM,EACzD,EACF,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAiB,iBACY,mBACO;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAI,SAAA,mBAA8F;UAC9FJ,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,qCAA4B;UAC5DF,EAD4D,CAAAG,YAAA,EAAM,EAC5D;UAEJH,EADF,CAAAC,cAAA,iBAA2B,mBACO;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7CH,EAAA,CAAAI,SAAA,mBAA+F;UAC/FJ,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,sCAA6B;UAC7DF,EAD6D,CAAAG,YAAA,EAAM,EAC7D;UAEJH,EADF,CAAAC,cAAA,iBAA2B,mBACO;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAI,SAAA,mBAA6F;UAC7FJ,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,oCAA2B;UAE7DF,EAF6D,CAAAG,YAAA,EAAM,EAC3D,EACF;UAEJH,EADF,CAAAC,cAAA,eAAwB,gBACE;UACtBD,EAAA,CAAAI,SAAA,mBAAsF;UACtFJ,EAAA,CAAAC,cAAA,mBAAmD;UAAAD,EAAA,CAAAE,MAAA,sCAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxFH,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,0CAAiC;UAEnEF,EAFmE,CAAAG,YAAA,EAAM,EACjE,EACF;UACNH,EAAA,CAAAC,cAAA,mBAA8C;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAC3DF,EAD2D,CAAAG,YAAA,EAAS,EAC7D;UACPH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,2BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAAI,SAAA,WAAM;UAGFJ,EAFJ,CAAAC,cAAA,kBAA4B,iBACR,mBACmC;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnEH,EAAA,CAAAI,SAAA,sBAAmH;UACnHJ,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,gDAAuC;UACvEF,EADuE,CAAAG,YAAA,EAAM,EACvE;UAENH,EAAA,CAAAC,cAAA,iBAA6B;UAC3BD,EAAA,CAAAI,SAAA,mBAAqF;UACrFJ,EAAA,CAAAC,cAAA,mBAA2D;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtFH,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,sCAA6B;UAC7DF,EAD6D,CAAAG,YAAA,EAAM,EAC7D;UAENH,EAAA,CAAAC,cAAA,gBAAwB;UACtBD,EAAA,CAAAI,SAAA,mBAAuG;UACvGJ,EAAA,CAAAC,cAAA,mBAA2D;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UAC9EF,EAD8E,CAAAG,YAAA,EAAQ,EAChF;UACNH,EAAA,CAAAC,cAAA,iBAA6B;UAC3BD,EAAA,CAAAI,SAAA,mBAAuG;UACvGJ,EAAA,CAAAC,cAAA,mBAA2D;UAAAD,EAAA,CAAAE,MAAA,mCAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7FH,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,2CAAkC;UAClEF,EADkE,CAAAG,YAAA,EAAM,EAClE;UAIFH,EAFJ,CAAAC,cAAA,iBAAkB,oBACiD,oBAC9C;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC/CH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,cAAK;UACzBF,EADyB,CAAAG,YAAA,EAAS,EACzB;UACTH,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,wCAA+B;UAC/DF,EAD+D,CAAAG,YAAA,EAAM,EAC/D;UAENH,EAAA,CAAAC,cAAA,iBAAkB;UAChBD,EAAA,CAAAI,SAAA,mBAA6E;UAC7EJ,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,2CAAkC;UAClEF,EADkE,CAAAG,YAAA,EAAM,EAClE;UAGJH,EADF,CAAAC,cAAA,iBAAkB,oBACuC;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAEtEF,EAFsE,CAAAG,YAAA,EAAS,EACvE,EACD;UAELH,EADF,CAAAC,cAAA,iBAAkB,oBACqD;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UACjFF,EADiF,CAAAG,YAAA,EAAS,EACpF;UACNH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9BH,EAAA,CAAAI,SAAA,WAAM;UAIAJ,EAHN,CAAAC,cAAA,kBAA+D,eAC5C,iBACY,mBACQ;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAI,SAAA,mBAAkH;UAClHJ,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACxCF,EADwC,CAAAG,YAAA,EAAM,EACxC;UAEJH,EADF,CAAAC,cAAA,iBAA2B,mBACQ;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClDH,EAAA,CAAAI,SAAA,mBAAiH;UACjHJ,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACxCF,EADwC,CAAAG,YAAA,EAAM,EACxC;UAEJH,EADF,CAAAC,cAAA,iBAA2B,mBACc;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGnDH,EAFJ,CAAAC,cAAA,gBAAyB,gBACE,kBAC8C;UAAAD,EAAA,CAAAE,MAAA,UAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjFH,EAAA,CAAAI,SAAA,mBAOE;UACJJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,iBAA6B;UAAAD,EAAA,CAAAE,MAAA,mDAA0C;UAG7EF,EAH6E,CAAAG,YAAA,EAAM,EACzE,EACF,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAiB,iBACY,mBACQ;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7CH,EAAA,CAAAI,SAAA,mBAA+F;UAC/FJ,EAAA,CAAAC,cAAA,iBAA6B;UAAAD,EAAA,CAAAE,MAAA,qCAA4B;UAC3DF,EAD2D,CAAAG,YAAA,EAAM,EAC3D;UAEJH,EADF,CAAAC,cAAA,iBAA2B,mBACQ;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9CH,EAAA,CAAAI,SAAA,mBAAgG;UAChGJ,EAAA,CAAAC,cAAA,iBAA6B;UAAAD,EAAA,CAAAE,MAAA,sCAA6B;UAC5DF,EAD4D,CAAAG,YAAA,EAAM,EAC5D;UAEJH,EADF,CAAAC,cAAA,iBAA2B,mBACQ;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAI,SAAA,mBAA8F;UAC9FJ,EAAA,CAAAC,cAAA,iBAA6B;UAAAD,EAAA,CAAAE,MAAA,oCAA2B;UAE5DF,EAF4D,CAAAG,YAAA,EAAM,EAC1D,EACF;UACNH,EAAA,CAAAC,cAAA,mBAA8C;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAC3DF,EAD2D,CAAAG,YAAA,EAAS,EAC7D;UACPH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3CH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAI,SAAA,WAAM;UACNJ,EAAA,CAAAC,cAAA,iBAA4C;UAC1CD,EAAA,CAAAI,SAAA,mBAAwE;UACxEJ,EAAA,CAAAC,cAAA,mBAA4D;UAAAD,EAAA,CAAAE,MAAA,mCAA0B;UACxFF,EADwF,CAAAG,YAAA,EAAQ,EAC1F;UAGFH,EAFJ,CAAAC,cAAA,eAAiB,eACO,eACH;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAI,SAAA,WAAM;UACNJ,EAAA,CAAAC,cAAA,iBAA8C;UAC5CD,EAAA,CAAAI,SAAA,mBAAwF;UACxFJ,EAAA,CAAAC,cAAA,mBAA4D;UAAAD,EAAA,CAAAE,MAAA,iCAAwB;UACtFF,EADsF,CAAAG,YAAA,EAAQ,EACxF;UACNH,EAAA,CAAAC,cAAA,iBAAyC;UACvCD,EAAA,CAAAI,SAAA,mBAAwF;UACxFJ,EAAA,CAAAC,cAAA,mBAA4D;UAAAD,EAAA,CAAAE,MAAA,0CAAiC;UAC/FF,EAD+F,CAAAG,YAAA,EAAQ,EACjG;UACNH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAI,SAAA,WAAM;UACNJ,EAAA,CAAAC,cAAA,iBAAoE;UAClED,EAAA,CAAAI,SAAA,mBAAqG;UACrGJ,EAAA,CAAAC,cAAA,mBAAkE;UAAAD,EAAA,CAAAE,MAAA,iCAAwB;UAC5FF,EAD4F,CAAAG,YAAA,EAAQ,EAC9F;UACNH,EAAA,CAAAC,cAAA,iBAA+D;UAC7DD,EAAA,CAAAI,SAAA,mBAAqG;UACrGJ,EAAA,CAAAC,cAAA,mBAAkE;UAAAD,EAAA,CAAAE,MAAA,0CAAiC;UAEvGF,EAFuG,CAAAG,YAAA,EAAQ,EACvG,EACF;UAEJH,EADF,CAAAC,cAAA,eAAsB,eACH;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3BH,EAAA,CAAAI,SAAA,WAAM;UACNJ,EAAA,CAAAC,cAAA,mBAA0B;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/CH,EAAA,CAAAI,SAAA,mBAAmE;UACnEJ,EAAA,CAAAC,cAAA,mBAA0B;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/CH,EAAA,CAAAI,SAAA,mBAAmF;UACnFJ,EAAA,CAAAC,cAAA,mBAA0B;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/CH,EAAA,CAAAI,SAAA,mBAA8F;UAItGJ,EAHM,CAAAG,YAAA,EAAM,EACF,EACG,EACP;UAIAH,EAHN,CAAAC,cAAA,eAAuB,sBAC+B,iBACpB,kBACqB;UAAAD,EAAA,CAAAE,MAAA,UAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7DH,EAAA,CAAAI,SAAA,mBAAuH;UACzHJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,iBAA8B;UAC5BD,EAAA,CAAAI,SAAA,mBAME;UACFJ,EAAA,CAAAC,cAAA,kBAAiD;UAAAD,EAAA,CAAAE,MAAA,qBAAgB;UACnEF,EADmE,CAAAG,YAAA,EAAO,EACpE;UACNH,EAAA,CAAAC,cAAA,mBAA0C;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE/DH,EADF,CAAAC,cAAA,iBAA8B,kBACqB;UAAAD,EAAA,CAAAE,MAAA,mCAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClFH,EAAA,CAAAI,SAAA,mBAAyF;UAC3FJ,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,iBAA8B,iBACG;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvCH,EAAA,CAAAI,SAAA,mBAAsF;UACtFJ,EAAA,CAAAC,cAAA,iBAA+B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UACpCF,EADoC,CAAAG,YAAA,EAAO,EACrC;UAEJH,EADF,CAAAC,cAAA,gBAAyB,iBACQ;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACnDH,EAAA,CAAAI,SAAA,sBAAqE;UACvEJ,EAAA,CAAAG,YAAA,EAAM;UAGFH,EAFJ,CAAAC,cAAA,eAAiB,eACO,eACH;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAI,SAAA,WAAM;UAEJJ,EADF,CAAAC,cAAA,iBAA6C,kBACc;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrEH,EAAA,CAAAI,SAAA,mBAAoH;UACtHJ,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,iBAA8B,kBACkC;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5EH,EAAA,CAAAI,SAAA,mBAAyH;UAC3HJ,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,iBAAwC,kBACmB;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrEH,EAAA,CAAAI,SAAA,mBAAoH;UAExHJ,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,eAAsB,eACH;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3CH,EAAA,CAAAI,SAAA,WAAM;UAEJJ,EADF,CAAAC,cAAA,iBAA8B,gBACE;UAC5BD,EAAA,CAAAI,SAAA,mBAA+G;UACjHJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAI,SAAA,mBAAgF;UAClFJ,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,gBAAyB,gBACO;UAC5BD,EAAA,CAAAI,SAAA,mBAAgH;UAClHJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAI,SAAA,mBAAoF;UAExFJ,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,eAAsB,eACH;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAI,SAAA,WAAM;UAEJJ,EADF,CAAAC,cAAA,gBAAyB,iBACQ;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzDH,EADA,CAAAI,SAAA,mBAAkE,mBACD;UAErEJ,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,eAAsB,eACH;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAI,SAAA,WAAM;UAEJJ,EADF,CAAAC,cAAA,iBAA8B,iBACG;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvCH,EAAA,CAAAC,cAAA,iBAA+B;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC1CH,EAAA,CAAAI,SAAA,mBAAuG;UACzGJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,gBAAyB;UACvBD,EAAA,CAAAI,SAAA,mBAAuG;UACvGJ,EAAA,CAAAC,cAAA,iBAA+B;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvCH,EAAA,CAAAC,cAAA,iBAA+B;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAGzCF,EAHyC,CAAAG,YAAA,EAAO,EACtC,EACF,EACF;UACNH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnCH,EAAA,CAAAI,SAAA,WAAM;UAIAJ,EAHN,CAAAC,cAAA,eAAiB,eACO,iBACU,oBAC+C;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1FH,EAAA,CAAAI,SAAA,mBAME;UACJJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,iBAA8B;UAC5BD,EAAA,CAAAI,SAAA,mBAME;UACFJ,EAAA,CAAAC,cAAA,oBAA2E;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAErFF,EAFqF,CAAAG,YAAA,EAAS,EACtF,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAsB,iBACU,oBAC4B;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACvEH,EAAA,CAAAC,cAAA,oBAAwD;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACvEH,EAAA,CAAAI,SAAA,mBAA0G;UAC5GJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UACvBD,EAAA,CAAAI,SAAA,mBAKE;UACFJ,EAAA,CAAAC,cAAA,oBAAwD;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACvEH,EAAA,CAAAC,cAAA,oBAAwD;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAElEF,EAFkE,CAAAG,YAAA,EAAS,EACnE,EACF;UAEJH,EADF,CAAAC,cAAA,eAAsB,eACH;UAAAD,EAAA,CAAAE,MAAA,+BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAI,SAAA,WAAM;UAGFJ,EAFJ,CAAAC,cAAA,iBAA8B,iBAC0C,oBACmB;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEtGH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAqD;UACrDJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAC1D,EACF;UACNH,EAAA,CAAAI,SAAA,mBAAuF;UACzFJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,iBAA8B;UAC5BD,EAAA,CAAAI,SAAA,mBAAuF;UAErFJ,EADF,CAAAC,cAAA,iBAAwE,oBACiB;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEtGH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAqD;UACrDJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAGhEF,EAHgE,CAAAG,YAAA,EAAI,EAC1D,EACF,EACF;UAEJH,EADF,CAAAC,cAAA,iBAA8D,oBACoC;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAG7GH,EAFJ,CAAAC,cAAA,gBAAoB,WACd,eAC0C;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAC3DF,EAD2D,CAAAG,YAAA,EAAI,EAC1D;UAEHH,EADF,CAAAC,cAAA,WAAI,eAC0C;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UACnEF,EADmE,CAAAG,YAAA,EAAI,EAClE;UAEHH,EADF,CAAAC,cAAA,WAAI,eAC0C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UACjEF,EADiE,CAAAG,YAAA,EAAI,EAChE;UACLH,EAAA,CAAAC,cAAA,WAAI;UAAAD,EAAA,CAAAI,SAAA,gBAA+B;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAEtCH,EADF,CAAAC,cAAA,WAAI,eAC0C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAC3D,EACF;UACLH,EAAA,CAAAI,SAAA,mBAA0F;UAC1FJ,EAAA,CAAAC,cAAA,oBAAgH;UAC9GD,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEHH,EADN,CAAAC,cAAA,gBAA6D,WACvD,eAA4C;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UAE7DH,EADF,CAAAC,cAAA,WAAI,eAC0C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAC5DF,EAD4D,CAAAG,YAAA,EAAI,EAC3D;UAEHH,EADF,CAAAC,cAAA,WAAI,eAC0C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UACjEF,EADiE,CAAAG,YAAA,EAAI,EAChE;UACLH,EAAA,CAAAC,cAAA,WAAI;UAAAD,EAAA,CAAAI,SAAA,gBAA+B;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAEtCH,EADF,CAAAC,cAAA,WAAI,eAC0C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAIlEF,EAJkE,CAAAG,YAAA,EAAI,EAC3D,EACF,EACD,EACF;UAEJH,EADF,CAAAC,cAAA,eAAsB,eACH;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAI,SAAA,WAAM;UAEJJ,EADF,CAAAC,cAAA,iBAAsE,oBACZ;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAErEH,EADF,CAAAC,cAAA,oBAAgG,kBACxE;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAqD;UACrDJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAC5DF,EAD4D,CAAAG,YAAA,EAAI,EAC1D;UACNH,EAAA,CAAAI,SAAA,mBAAiG;UACnGJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,iBAAqC;UACnCD,EAAA,CAAAI,SAAA,mBAAiG;UACjGJ,EAAA,CAAAC,cAAA,oBAAwD;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAErEH,EADF,CAAAC,cAAA,oBAAgG,kBACxE;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACvCF,EADuC,CAAAG,YAAA,EAAO,EACrC;UAEPH,EADF,CAAAC,cAAA,iBAAqB,eACyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAI,SAAA,iBAAqD;UACrDJ,EAAA,CAAAC,cAAA,eAA4C;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAIlEF,EAJkE,CAAAG,YAAA,EAAI,EAC1D,EACF,EACF,EACF;UACNH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG9BH,EAFJ,CAAAC,cAAA,eAAiB,eACO,gBACH;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnCH,EAAA,CAAAI,SAAA,WAAM;UAGFJ,EAFJ,CAAAC,cAAA,iBAA8B,iBACK,mBAC0B;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAClEF,EADkE,CAAAG,YAAA,EAAQ,EACpE;UAEJH,EADF,CAAAC,cAAA,oBAAoD,mBACjC;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnCH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAE3BF,EAF2B,CAAAG,YAAA,EAAS,EACzB,EACL;UAGFH,EAFJ,CAAAC,cAAA,iBAA8B,oBACwB,mBACjC;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnCH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,cAAK;UACzBF,EADyB,CAAAG,YAAA,EAAS,EACzB;UAEPH,EADF,CAAAC,cAAA,iBAAgC,mBAC2B;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAEpEF,EAFoE,CAAAG,YAAA,EAAQ,EACpE,EACF;UAEJH,EADF,CAAAC,cAAA,iBAA8B,oBAC4B;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAErEH,EADF,CAAAC,cAAA,oBAAoD,mBACjC;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnCH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAE3BF,EAF2B,CAAAG,YAAA,EAAS,EACzB,EACL;UAGFH,EAFJ,CAAAC,cAAA,gBAAyB,oBAC6B,mBACjC;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnCH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,cAAK;UACzBF,EADyB,CAAAG,YAAA,EAAS,EACzB;UACTH,EAAA,CAAAC,cAAA,oBAAwD;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAElEF,EAFkE,CAAAG,YAAA,EAAS,EACnE,EACF;UAEJH,EADF,CAAAC,cAAA,eAAsB,gBACH;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAI,SAAA,WAAM;UAEJJ,EADF,CAAAC,cAAA,iBAA8B,mBAC2B;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrEH,EAAA,CAAAI,SAAA,mBAAgE;UAClEJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,iBAA8B;UAC5BD,EAAA,CAAAI,SAAA,mBAAgE;UAChEJ,EAAA,CAAAC,cAAA,mBAAuD;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAC/DF,EAD+D,CAAAG,YAAA,EAAQ,EACjE;UAEJH,EADF,CAAAC,cAAA,iBAA8B,oBACuD;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClGH,EAAA,CAAAI,SAAA,mBAA6H;UAC/HJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UACvBD,EAAA,CAAAI,SAAA,mBAA6H;UAC7HJ,EAAA,CAAAC,cAAA,oBAAmF;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAMrGF,EANqG,CAAAG,YAAA,EAAS,EAC9F,EACF,EACF,EACG,EACP,EACF;;;UAxvBoCH,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UA+crBN,EAAA,CAAAK,SAAA,KAAiB;UAAjBL,EAAA,CAAAM,UAAA,kBAAiB;UAoJCN,EAAA,CAAAK,SAAA,KAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;UAkD/BN,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,4BAA2B;;;qBDhpBnEjB,YAAY,EAAAkB,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,uBAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,MAAA,EAAAC,EAAA,CAAAC,aAAA,EAAAC,EAAA,CAAAC,WAAA,EAAAD,EAAA,CAAAE,iBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAE5B,iBAAiB;MAAA6B,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}