{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { TablesRoutingModule } from './tables-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class TablesModule {\n  static {\n    this.ɵfac = function TablesModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TablesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TablesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TablesRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TablesModule, {\n    imports: [CommonModule, TablesRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "TablesRoutingModule", "TablesModule", "imports"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\tables\\tables.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { TablesRoutingModule } from './tables-routing.module';\r\n\r\n@NgModule({\r\n  declarations: [],\r\n  imports: [CommonModule, TablesRoutingModule]\r\n})\r\nexport class TablesModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,yBAAyB;;AAM7D,OAAM,MAAOC,YAAY;;;uCAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAFbF,YAAY,EAAEC,mBAAmB;IAAA;EAAA;;;2EAEhCC,YAAY;IAAAC,OAAA,GAFbH,YAAY,EAAEC,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}