{"ast": null, "code": "// Angular Import\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NavigationEnd, Router, RouterModule } from '@angular/router';\nimport { Title } from '@angular/platform-browser';\n// project import\nimport { NavigationItems } from 'src/app/theme/layout/admin/navigation/navigation';\nimport { SharedModule } from '../../shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = () => [\"/dashboard/default/\"];\nfunction BreadcrumbsComponent_For_1_Conditional_0_For_6_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h5\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const breadcrumb_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", breadcrumb_r1.title, \" \");\n  }\n}\nfunction BreadcrumbsComponent_For_1_Conditional_0_For_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, BreadcrumbsComponent_For_1_Conditional_0_For_6_Conditional_0_Template, 2, 1, \"h5\", 8);\n  }\n  if (rf & 2) {\n    const ɵ$index_13_r2 = ctx.$index;\n    const ɵ$count_13_r3 = ctx.$count;\n    i0.ɵɵconditional(ɵ$index_13_r2 === ɵ$count_13_r3 - 1 ? 0 : -1);\n  }\n}\nfunction BreadcrumbsComponent_For_1_Conditional_0_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 7);\n    i0.ɵɵelement(1, \"i\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction BreadcrumbsComponent_For_1_Conditional_0_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(2, _c0));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(\"Home\");\n  }\n}\nfunction BreadcrumbsComponent_For_1_Conditional_0_For_12_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 6)(1, \"a\", 7);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const breadcrumb_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", breadcrumb_r4.url);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(breadcrumb_r4.title);\n  }\n}\nfunction BreadcrumbsComponent_For_1_Conditional_0_For_12_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 6)(1, \"a\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const breadcrumb_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(breadcrumb_r4.title);\n  }\n}\nfunction BreadcrumbsComponent_For_1_Conditional_0_For_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, BreadcrumbsComponent_For_1_Conditional_0_For_12_Conditional_0_Template, 3, 2, \"li\", 6);\n    i0.ɵɵconditionalCreate(1, BreadcrumbsComponent_For_1_Conditional_0_For_12_Conditional_1_Template, 3, 1, \"li\", 6);\n  }\n  if (rf & 2) {\n    const breadcrumb_r4 = ctx.$implicit;\n    i0.ɵɵconditional(breadcrumb_r4.url !== false ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(breadcrumb_r4.url === false && breadcrumb_r4.type !== \"group\" ? 1 : -1);\n  }\n}\nfunction BreadcrumbsComponent_For_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n    i0.ɵɵrepeaterCreate(5, BreadcrumbsComponent_For_1_Conditional_0_For_6_Template, 1, 1, null, null, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ul\", 5)(8, \"li\", 6);\n    i0.ɵɵconditionalCreate(9, BreadcrumbsComponent_For_1_Conditional_0_Conditional_9_Template, 2, 2, \"a\", 7);\n    i0.ɵɵconditionalCreate(10, BreadcrumbsComponent_For_1_Conditional_0_Conditional_10_Template, 2, 3, \"a\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵrepeaterCreate(11, BreadcrumbsComponent_For_1_Conditional_0_For_12_Template, 2, 2, null, null, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵrepeater(ctx_r4.navigationList);\n    i0.ɵɵadvance(4);\n    i0.ɵɵconditional(ctx_r4.type === \"theme2\" ? 9 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r4.type === \"theme1\" ? 10 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r4.navigationList);\n  }\n}\nfunction BreadcrumbsComponent_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, BreadcrumbsComponent_For_1_Conditional_0_Template, 13, 2, \"div\", 0);\n  }\n  if (rf & 2) {\n    const breadcrumb_r6 = ctx.$implicit;\n    const ɵ$index_1_r7 = ctx.$index;\n    const ɵ$count_1_r8 = ctx.$count;\n    i0.ɵɵconditional(ɵ$index_1_r7 === ɵ$count_1_r8 - 1 && breadcrumb_r6.breadcrumbs !== false ? 0 : -1);\n  }\n}\nexport class BreadcrumbsComponent {\n  // constructor\n  constructor() {\n    this.route = inject(Router);\n    this.titleService = inject(Title);\n    this.breadcrumbList = [];\n    this.navigations = NavigationItems;\n    this.type = 'theme1';\n    this.setBreadcrumb();\n  }\n  // public method\n  setBreadcrumb() {\n    this.route.events.subscribe(router => {\n      if (router instanceof NavigationEnd) {\n        const activeLink = router.url;\n        const breadcrumbList = this.filterNavigation(this.navigations, activeLink);\n        this.navigationList = breadcrumbList;\n        const title = breadcrumbList[breadcrumbList.length - 1]?.title || 'Welcome';\n        this.titleService.setTitle(title + ' | Berry Angular Admin Template');\n      }\n    });\n  }\n  filterNavigation(navItems, activeLink) {\n    for (const navItem of navItems) {\n      if (navItem.type === 'item' && 'url' in navItem && navItem.url === activeLink) {\n        return [{\n          url: 'url' in navItem ? navItem.url : false,\n          title: navItem.title,\n          breadcrumbs: 'breadcrumbs' in navItem ? navItem.breadcrumbs : true,\n          type: navItem.type\n        }];\n      }\n      if ((navItem.type === 'group' || navItem.type === 'collapse') && 'children' in navItem) {\n        const breadcrumbList = this.filterNavigation(navItem.children, activeLink);\n        if (breadcrumbList.length > 0) {\n          breadcrumbList.unshift({\n            url: 'url' in navItem ? navItem.url : false,\n            title: navItem.title,\n            breadcrumbs: 'breadcrumbs' in navItem ? navItem.breadcrumbs : true,\n            type: navItem.type\n          });\n          return breadcrumbList;\n        }\n      }\n    }\n    return [];\n  }\n  static {\n    this.ɵfac = function BreadcrumbsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BreadcrumbsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BreadcrumbsComponent,\n      selectors: [[\"app-breadcrumb\"]],\n      inputs: {\n        type: \"type\"\n      },\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"page-header\"], [1, \"page-block\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-12\"], [1, \"page-header-title\"], [1, \"breadcrumb\"], [1, \"breadcrumb-item\"], [3, \"routerLink\"], [1, \"m-b-10\"], [1, \"feather\", \"icon-home\"], [\"href\", \"javascript:\"]],\n      template: function BreadcrumbsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵrepeaterCreate(0, BreadcrumbsComponent_For_1_Template, 1, 1, null, null, i0.ɵɵrepeaterTrackByIdentity);\n        }\n        if (rf & 2) {\n          i0.ɵɵrepeater(ctx.navigationList);\n        }\n      },\n      dependencies: [CommonModule, RouterModule, i1.RouterLink, SharedModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["inject", "CommonModule", "NavigationEnd", "Router", "RouterModule", "Title", "NavigationItems", "SharedModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "breadcrumb_r1", "title", "ɵɵconditionalCreate", "BreadcrumbsComponent_For_1_Conditional_0_For_6_Conditional_0_Template", "ɵɵconditional", "ɵ$index_13_r2", "ɵ$count_13_r3", "ɵɵelement", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate", "breadcrumb_r4", "url", "BreadcrumbsComponent_For_1_Conditional_0_For_12_Conditional_0_Template", "BreadcrumbsComponent_For_1_Conditional_0_For_12_Conditional_1_Template", "type", "ɵɵrepeaterCreate", "BreadcrumbsComponent_For_1_Conditional_0_For_6_Template", "ɵɵrepeaterTrackByIdentity", "BreadcrumbsComponent_For_1_Conditional_0_Conditional_9_Template", "BreadcrumbsComponent_For_1_Conditional_0_Conditional_10_Template", "BreadcrumbsComponent_For_1_Conditional_0_For_12_Template", "ɵɵrepeater", "ctx_r4", "navigationList", "BreadcrumbsComponent_For_1_Conditional_0_Template", "ɵ$index_1_r7", "ɵ$count_1_r8", "breadcrumb_r6", "breadcrumbs", "BreadcrumbsComponent", "constructor", "route", "titleService", "breadcrumbList", "navigations", "setBreadcrumb", "events", "subscribe", "router", "activeLink", "filterNavigation", "length", "setTitle", "navItems", "navItem", "children", "unshift", "selectors", "inputs", "decls", "vars", "consts", "template", "BreadcrumbsComponent_Template", "rf", "ctx", "BreadcrumbsComponent_For_1_Template", "i1", "RouterLink", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\shared\\components\\breadcrumbs\\breadcrumbs.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\shared\\components\\breadcrumbs\\breadcrumbs.component.html"], "sourcesContent": ["// Angular Import\r\nimport { Component, Input, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NavigationEnd, Router, RouterModule, Event } from '@angular/router';\r\nimport { Title } from '@angular/platform-browser';\r\n\r\n// project import\r\nimport { NavigationItem, NavigationItems } from 'src/app/theme/layout/admin/navigation/navigation';\r\nimport { SharedModule } from '../../shared.module';\r\n\r\ninterface titleType {\r\n  // eslint-disable-next-line\r\n  url: string | boolean | any | undefined;\r\n  title: string;\r\n  breadcrumbs: unknown;\r\n  type: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-breadcrumb',\r\n  imports: [CommonModule, RouterModule, SharedModule],\r\n  templateUrl: './breadcrumbs.component.html',\r\n  styleUrls: ['./breadcrumbs.component.scss']\r\n})\r\nexport class BreadcrumbsComponent {\r\n  private route = inject(Router);\r\n  private titleService = inject(Title);\r\n\r\n  // public props\r\n  @Input() type: string;\r\n\r\n  navigations: NavigationItem[];\r\n  breadcrumbList: string[] = [];\r\n  navigationList!: titleType[];\r\n\r\n  // constructor\r\n  constructor() {\r\n    this.navigations = NavigationItems;\r\n    this.type = 'theme1';\r\n    this.setBreadcrumb();\r\n  }\r\n\r\n  // public method\r\n  setBreadcrumb() {\r\n    this.route.events.subscribe((router: Event) => {\r\n      if (router instanceof NavigationEnd) {\r\n        const activeLink = router.url;\r\n        const breadcrumbList = this.filterNavigation(this.navigations, activeLink);\r\n        this.navigationList = breadcrumbList;\r\n        const title = breadcrumbList[breadcrumbList.length - 1]?.title || 'Welcome';\r\n        this.titleService.setTitle(title + ' | Berry Angular Admin Template');\r\n      }\r\n    });\r\n  }\r\n\r\n  filterNavigation(navItems: NavigationItem[], activeLink: string): titleType[] {\r\n    for (const navItem of navItems) {\r\n      if (navItem.type === 'item' && 'url' in navItem && navItem.url === activeLink) {\r\n        return [\r\n          {\r\n            url: 'url' in navItem ? navItem.url : false,\r\n            title: navItem.title,\r\n            breadcrumbs: 'breadcrumbs' in navItem ? navItem.breadcrumbs : true,\r\n            type: navItem.type\r\n          }\r\n        ];\r\n      }\r\n      if ((navItem.type === 'group' || navItem.type === 'collapse') && 'children' in navItem) {\r\n        const breadcrumbList = this.filterNavigation(navItem.children!, activeLink);\r\n        if (breadcrumbList.length > 0) {\r\n          breadcrumbList.unshift({\r\n            url: 'url' in navItem ? navItem.url : false,\r\n            title: navItem.title,\r\n            breadcrumbs: 'breadcrumbs' in navItem ? navItem.breadcrumbs : true,\r\n            type: navItem.type\r\n          });\r\n          return breadcrumbList;\r\n        }\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n}\r\n", "@for (breadcrumb of navigationList; track breadcrumb; let last = $last) {\r\n  @if (last && breadcrumb.breadcrumbs !== false) {\r\n    <div class=\"page-header\">\r\n      <div class=\"page-block\">\r\n        <div class=\"row align-items-center\">\r\n          <div class=\"col-md-12\">\r\n            <div class=\"page-header-title\">\r\n              @for (breadcrumb of navigationList; track breadcrumb; let last = $last) {\r\n                @if (last) {\r\n                  <h5 class=\"m-b-10\">\r\n                    {{ breadcrumb.title }}\r\n                  </h5>\r\n                }\r\n              }\r\n            </div>\r\n            <ul class=\"breadcrumb\">\r\n              <li class=\"breadcrumb-item\">\r\n                @if (type === 'theme2') {\r\n                  <a [routerLink]=\"['/dashboard/default/']\"><i class=\"feather icon-home\"></i></a>\r\n                }\r\n                @if (type === 'theme1') {\r\n                  <a [routerLink]=\"['/dashboard/default/']\">{{ 'Home' }}</a>\r\n                }\r\n              </li>\r\n              @for (breadcrumb of navigationList; track breadcrumb) {\r\n                @if (breadcrumb.url !== false) {\r\n                  <li class=\"breadcrumb-item\">\r\n                    <a [routerLink]=\"breadcrumb.url\">{{ breadcrumb.title }}</a>\r\n                  </li>\r\n                }\r\n                @if (breadcrumb.url === false && breadcrumb.type !== 'group') {\r\n                  <li class=\"breadcrumb-item\">\r\n                    <a href=\"javascript:\">{{ breadcrumb.title }}</a>\r\n                  </li>\r\n                }\r\n              }\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  }\r\n}\r\n"], "mappings": "AAAA;AACA,SAA2BA,MAAM,QAAQ,eAAe;AACxD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,EAAEC,MAAM,EAAEC,YAAY,QAAe,iBAAiB;AAC5E,SAASC,KAAK,QAAQ,2BAA2B;AAEjD;AACA,SAAyBC,eAAe,QAAQ,kDAAkD;AAClG,SAASC,YAAY,QAAQ,qBAAqB;;;;;;ICChCC,EAAA,CAAAC,cAAA,YAAmB;IACjBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IADHH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,aAAA,CAAAC,KAAA,MACF;;;;;IAHFP,EAAA,CAAAQ,mBAAA,IAAAC,qEAAA,gBAAY;;;;;IAAZT,EAAA,CAAAU,aAAA,CAAAC,aAAA,KAAAC,aAAA,cAIC;;;;;IAMCZ,EAAA,CAAAC,cAAA,WAA0C;IAAAD,EAAA,CAAAa,SAAA,WAAiC;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;IAA5EH,EAAA,CAAAc,UAAA,eAAAd,EAAA,CAAAe,eAAA,IAAAC,GAAA,EAAsC;;;;;IAGzChB,EAAA,CAAAC,cAAA,WAA0C;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;IAAvDH,EAAA,CAAAc,UAAA,eAAAd,EAAA,CAAAe,eAAA,IAAAC,GAAA,EAAsC;IAAChB,EAAA,CAAAI,SAAA,EAAY;IAAZJ,EAAA,CAAAiB,iBAAA,QAAY;;;;;IAMpDjB,EADF,CAAAC,cAAA,YAA4B,WACO;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IACzDF,EADyD,CAAAG,YAAA,EAAI,EACxD;;;;IADAH,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAc,UAAA,eAAAI,aAAA,CAAAC,GAAA,CAA6B;IAACnB,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAiB,iBAAA,CAAAC,aAAA,CAAAX,KAAA,CAAsB;;;;;IAKvDP,EADF,CAAAC,cAAA,YAA4B,YACJ;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAC9CF,EAD8C,CAAAG,YAAA,EAAI,EAC7C;;;;IADmBH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAiB,iBAAA,CAAAC,aAAA,CAAAX,KAAA,CAAsB;;;;;IAPhDP,EAAA,CAAAQ,mBAAA,IAAAY,sEAAA,gBAAgC;IAKhCpB,EAAA,CAAAQ,mBAAA,IAAAa,sEAAA,gBAA+D;;;;IAL/DrB,EAAA,CAAAU,aAAA,CAAAQ,aAAA,CAAAC,GAAA,oBAIC;IACDnB,EAAA,CAAAI,SAAA,EAIC;IAJDJ,EAAA,CAAAU,aAAA,CAAAQ,aAAA,CAAAC,GAAA,cAAAD,aAAA,CAAAI,IAAA,sBAIC;;;;;IA5BLtB,EAJR,CAAAC,cAAA,aAAyB,aACC,aACc,aACX,aACU;IAC7BD,EAAA,CAAAuB,gBAAA,IAAAC,uDAAA,oBAAAxB,EAAA,CAAAyB,yBAAA,CAMC;IACHzB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,YAAuB,YACO;IAC1BD,EAAA,CAAAQ,mBAAA,IAAAkB,+DAAA,eAAyB;IAGzB1B,EAAA,CAAAQ,mBAAA,KAAAmB,gEAAA,eAAyB;IAG3B3B,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAuB,gBAAA,KAAAK,wDAAA,oBAAA5B,EAAA,CAAAyB,yBAAA,CAWC;IAKXzB,EAJQ,CAAAG,YAAA,EAAK,EACD,EACF,EACF,EACF;;;;IAjCIH,EAAA,CAAAI,SAAA,GAMC;IANDJ,EAAA,CAAA6B,UAAA,CAAAC,MAAA,CAAAC,cAAA,CAMC;IAIC/B,EAAA,CAAAI,SAAA,GAEC;IAFDJ,EAAA,CAAAU,aAAA,CAAAoB,MAAA,CAAAR,IAAA,uBAEC;IACDtB,EAAA,CAAAI,SAAA,EAEC;IAFDJ,EAAA,CAAAU,aAAA,CAAAoB,MAAA,CAAAR,IAAA,wBAEC;IAEHtB,EAAA,CAAAI,SAAA,EAWC;IAXDJ,EAAA,CAAA6B,UAAA,CAAAC,MAAA,CAAAC,cAAA,CAWC;;;;;IAlCb/B,EAAA,CAAAQ,mBAAA,IAAAwB,iDAAA,kBAAgD;;;;;;IAAhDhC,EAAA,CAAAU,aAAA,CAAAuB,YAAA,KAAAC,YAAA,QAAAC,aAAA,CAAAC,WAAA,oBAwCC;;;ADjBH,OAAM,MAAOC,oBAAoB;EAW/B;EACAC,YAAA;IAXQ,KAAAC,KAAK,GAAG/C,MAAM,CAACG,MAAM,CAAC;IACtB,KAAA6C,YAAY,GAAGhD,MAAM,CAACK,KAAK,CAAC;IAMpC,KAAA4C,cAAc,GAAa,EAAE;IAK3B,IAAI,CAACC,WAAW,GAAG5C,eAAe;IAClC,IAAI,CAACwB,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACqB,aAAa,EAAE;EACtB;EAEA;EACAA,aAAaA,CAAA;IACX,IAAI,CAACJ,KAAK,CAACK,MAAM,CAACC,SAAS,CAAEC,MAAa,IAAI;MAC5C,IAAIA,MAAM,YAAYpD,aAAa,EAAE;QACnC,MAAMqD,UAAU,GAAGD,MAAM,CAAC3B,GAAG;QAC7B,MAAMsB,cAAc,GAAG,IAAI,CAACO,gBAAgB,CAAC,IAAI,CAACN,WAAW,EAAEK,UAAU,CAAC;QAC1E,IAAI,CAAChB,cAAc,GAAGU,cAAc;QACpC,MAAMlC,KAAK,GAAGkC,cAAc,CAACA,cAAc,CAACQ,MAAM,GAAG,CAAC,CAAC,EAAE1C,KAAK,IAAI,SAAS;QAC3E,IAAI,CAACiC,YAAY,CAACU,QAAQ,CAAC3C,KAAK,GAAG,iCAAiC,CAAC;MACvE;IACF,CAAC,CAAC;EACJ;EAEAyC,gBAAgBA,CAACG,QAA0B,EAAEJ,UAAkB;IAC7D,KAAK,MAAMK,OAAO,IAAID,QAAQ,EAAE;MAC9B,IAAIC,OAAO,CAAC9B,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI8B,OAAO,IAAIA,OAAO,CAACjC,GAAG,KAAK4B,UAAU,EAAE;QAC7E,OAAO,CACL;UACE5B,GAAG,EAAE,KAAK,IAAIiC,OAAO,GAAGA,OAAO,CAACjC,GAAG,GAAG,KAAK;UAC3CZ,KAAK,EAAE6C,OAAO,CAAC7C,KAAK;UACpB6B,WAAW,EAAE,aAAa,IAAIgB,OAAO,GAAGA,OAAO,CAAChB,WAAW,GAAG,IAAI;UAClEd,IAAI,EAAE8B,OAAO,CAAC9B;SACf,CACF;MACH;MACA,IAAI,CAAC8B,OAAO,CAAC9B,IAAI,KAAK,OAAO,IAAI8B,OAAO,CAAC9B,IAAI,KAAK,UAAU,KAAK,UAAU,IAAI8B,OAAO,EAAE;QACtF,MAAMX,cAAc,GAAG,IAAI,CAACO,gBAAgB,CAACI,OAAO,CAACC,QAAS,EAAEN,UAAU,CAAC;QAC3E,IAAIN,cAAc,CAACQ,MAAM,GAAG,CAAC,EAAE;UAC7BR,cAAc,CAACa,OAAO,CAAC;YACrBnC,GAAG,EAAE,KAAK,IAAIiC,OAAO,GAAGA,OAAO,CAACjC,GAAG,GAAG,KAAK;YAC3CZ,KAAK,EAAE6C,OAAO,CAAC7C,KAAK;YACpB6B,WAAW,EAAE,aAAa,IAAIgB,OAAO,GAAGA,OAAO,CAAChB,WAAW,GAAG,IAAI;YAClEd,IAAI,EAAE8B,OAAO,CAAC9B;WACf,CAAC;UACF,OAAOmB,cAAc;QACvB;MACF;IACF;IACA,OAAO,EAAE;EACX;;;uCAzDWJ,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAkB,SAAA;MAAAC,MAAA;QAAAlC,IAAA;MAAA;MAAAmC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxBjC9D,EAAA,CAAAuB,gBAAA,IAAAyC,mCAAA,oBAAAhE,EAAA,CAAAyB,yBAAA,CA0CC;;;UA1CDzB,EAAA,CAAA6B,UAAA,CAAAkC,GAAA,CAAAhC,cAAA,CA0CC;;;qBDtBWtC,YAAY,EAAEG,YAAY,EAAAqE,EAAA,CAAAC,UAAA,EAAEnE,YAAY;MAAAoE,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}