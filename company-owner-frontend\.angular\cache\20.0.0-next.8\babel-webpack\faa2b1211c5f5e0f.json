{"ast": null, "code": "// project import\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport class NavSearchComponent {\n  // constructor\n  constructor() {\n    this.searchWidth = 0;\n  }\n  // public method\n  searchOn() {\n    document.querySelector('#main-search').classList.add('open');\n    this.searchInterval = setInterval(() => {\n      if (this.searchWidth >= 170) {\n        clearInterval(this.searchInterval);\n        // return false;\n      }\n      this.searchWidth = this.searchWidth + 30;\n      this.searchWidthString = this.searchWidth + 'px';\n    }, 35);\n  }\n  searchOff() {\n    this.searchInterval = setInterval(() => {\n      if (this.searchWidth <= 0) {\n        document.querySelector('#main-search').classList.remove('open');\n        clearInterval(this.searchInterval);\n        // return false;\n      }\n      this.searchWidth = this.searchWidth - 30;\n      this.searchWidthString = this.searchWidth + 'px';\n    }, 35);\n  }\n  static {\n    this.ɵfac = function NavSearchComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NavSearchComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavSearchComponent,\n      selectors: [[\"app-nav-search\"]],\n      decls: 7,\n      vars: 2,\n      consts: [[\"id\", \"main-search\", 1, \"main-search\"], [1, \"input-group\"], [\"type\", \"text\", \"id\", \"m-search\", \"placeholder\", \"Search . . .\", 1, \"form-control\"], [\"href\", \"javascript:\", 1, \"input-group-append\", \"search-close\", 3, \"click\"], [1, \"feather\", \"icon-x\", \"input-group-text\"], [\"role\", \"none\", 1, \"input-group-append\", \"search-btn\", \"btn\", \"btn-primary\", \"d-flex\", 3, \"click\", \"keypress\"], [1, \"feather\", \"icon-search\", \"input-group-text\"]],\n      template: function NavSearchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"input\", 2);\n          i0.ɵɵelementStart(3, \"a\", 3);\n          i0.ɵɵlistener(\"click\", function NavSearchComponent_Template_a_click_3_listener() {\n            return ctx.searchOff();\n          });\n          i0.ɵɵelement(4, \"i\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"span\", 5);\n          i0.ɵɵlistener(\"click\", function NavSearchComponent_Template_span_click_5_listener() {\n            return ctx.searchOn();\n          })(\"keypress\", function NavSearchComponent_Template_span_keypress_5_listener() {\n            return ctx.searchOn();\n          });\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"width\", ctx.searchWidthString);\n        }\n      },\n      dependencies: [SharedModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "NavSearchComponent", "constructor", "searchWidth", "searchOn", "document", "querySelector", "classList", "add", "searchInterval", "setInterval", "clearInterval", "searchWidthString", "searchOff", "remove", "selectors", "decls", "vars", "consts", "template", "NavSearchComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵlistener", "NavSearchComponent_Template_a_click_3_listener", "ɵɵelementEnd", "NavSearchComponent_Template_span_click_5_listener", "NavSearchComponent_Template_span_keypress_5_listener", "ɵɵadvance", "ɵɵstyleProp", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\nav-bar\\nav-left\\nav-search\\nav-search.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\nav-bar\\nav-left\\nav-search\\nav-search.component.html"], "sourcesContent": ["// angular import\r\nimport { Component } from '@angular/core';\r\n\r\n// project import\r\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\r\n\r\n@Component({\r\n  selector: 'app-nav-search',\r\n  imports: [SharedModule],\r\n  templateUrl: './nav-search.component.html',\r\n  styleUrls: ['./nav-search.component.scss']\r\n})\r\nexport class NavSearchComponent {\r\n  // public props\r\n  searchInterval;\r\n  searchWidth: number;\r\n  searchWidthString: string;\r\n\r\n  // constructor\r\n  constructor() {\r\n    this.searchWidth = 0;\r\n  }\r\n\r\n  // public method\r\n  searchOn() {\r\n    document.querySelector('#main-search').classList.add('open');\r\n    this.searchInterval = setInterval(() => {\r\n      if (this.searchWidth >= 170) {\r\n        clearInterval(this.searchInterval);\r\n        // return false;\r\n      }\r\n      this.searchWidth = this.searchWidth + 30;\r\n      this.searchWidthString = this.searchWidth + 'px';\r\n    }, 35);\r\n  }\r\n\r\n  searchOff() {\r\n    this.searchInterval = setInterval(() => {\r\n      if (this.searchWidth <= 0) {\r\n        document.querySelector('#main-search').classList.remove('open');\r\n        clearInterval(this.searchInterval);\r\n        // return false;\r\n      }\r\n      this.searchWidth = this.searchWidth - 30;\r\n      this.searchWidthString = this.searchWidth + 'px';\r\n    }, 35);\r\n  }\r\n}\r\n", "<div id=\"main-search\" class=\"main-search\">\r\n  <div class=\"input-group\">\r\n    <input type=\"text\" id=\"m-search\" class=\"form-control\" placeholder=\"Search . . .\" [style.width]=\"searchWidthString\" />\r\n    <a href=\"javascript:\" class=\"input-group-append search-close\" (click)=\"searchOff()\">\r\n      <i class=\"feather icon-x input-group-text\"></i>\r\n    </a>\r\n    <span class=\"input-group-append search-btn btn btn-primary d-flex\" (click)=\"searchOn()\" role=\"none\" (keypress)=\"searchOn()\">\r\n      <i class=\"feather icon-search input-group-text\"></i>\r\n    </span>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAGA;AACA,SAASA,YAAY,QAAQ,oCAAoC;;AAQjE,OAAM,MAAOC,kBAAkB;EAM7B;EACAC,YAAA;IACE,IAAI,CAACC,WAAW,GAAG,CAAC;EACtB;EAEA;EACAC,QAAQA,CAAA;IACNC,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC,CAACC,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;IAC5D,IAAI,CAACC,cAAc,GAAGC,WAAW,CAAC,MAAK;MACrC,IAAI,IAAI,CAACP,WAAW,IAAI,GAAG,EAAE;QAC3BQ,aAAa,CAAC,IAAI,CAACF,cAAc,CAAC;QAClC;MACF;MACA,IAAI,CAACN,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,EAAE;MACxC,IAAI,CAACS,iBAAiB,GAAG,IAAI,CAACT,WAAW,GAAG,IAAI;IAClD,CAAC,EAAE,EAAE,CAAC;EACR;EAEAU,SAASA,CAAA;IACP,IAAI,CAACJ,cAAc,GAAGC,WAAW,CAAC,MAAK;MACrC,IAAI,IAAI,CAACP,WAAW,IAAI,CAAC,EAAE;QACzBE,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC,CAACC,SAAS,CAACO,MAAM,CAAC,MAAM,CAAC;QAC/DH,aAAa,CAAC,IAAI,CAACF,cAAc,CAAC;QAClC;MACF;MACA,IAAI,CAACN,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,EAAE;MACxC,IAAI,CAACS,iBAAiB,GAAG,IAAI,CAACT,WAAW,GAAG,IAAI;IAClD,CAAC,EAAE,EAAE,CAAC;EACR;;;uCAlCWF,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAc,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX7BE,EADF,CAAAC,cAAA,aAA0C,aACf;UACvBD,EAAA,CAAAE,SAAA,eAAqH;UACrHF,EAAA,CAAAC,cAAA,WAAoF;UAAtBD,EAAA,CAAAG,UAAA,mBAAAC,+CAAA;YAAA,OAASL,GAAA,CAAAT,SAAA,EAAW;UAAA,EAAC;UACjFU,EAAA,CAAAE,SAAA,WAA+C;UACjDF,EAAA,CAAAK,YAAA,EAAI;UACJL,EAAA,CAAAC,cAAA,cAA4H;UAAxBD,EAAjC,CAAAG,UAAA,mBAAAG,kDAAA;YAAA,OAASP,GAAA,CAAAlB,QAAA,EAAU;UAAA,EAAC,sBAAA0B,qDAAA;YAAA,OAAyBR,GAAA,CAAAlB,QAAA,EAAU;UAAA,EAAC;UACzHmB,EAAA,CAAAE,SAAA,WAAoD;UAG1DF,EAFI,CAAAK,YAAA,EAAO,EACH,EACF;;;UAR+EL,EAAA,CAAAQ,SAAA,GAAiC;UAAjCR,EAAA,CAAAS,WAAA,UAAAV,GAAA,CAAAV,iBAAA,CAAiC;;;qBDM1GZ,YAAY;MAAAiC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}