{"ast": null, "code": "/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nexport { ActivatedRoute, ActivatedRouteSnapshot, ActivationEnd, ActivationStart, BaseRouteReuseStrategy, ChildActivationEnd, ChildActivationStart, ChildrenOutletContexts, DefaultTitleStrategy, DefaultUrlSerializer, EventType, GuardsCheckEnd, GuardsCheckStart, NavigationCancel, NavigationCancellationCode, NavigationEnd, NavigationError, NavigationSkipped, NavigationSkippedCode, NavigationStart, OutletContext, PRIMARY_OUTLET, ROUTER_CONFIGURATION, ROUTER_OUTLET_DATA, ROUTES, RedirectCommand, ResolveEnd, ResolveStart, RouteConfigLoadEnd, RouteConfigLoadStart, RouteReuseStrategy, Router, RouterEvent, RouterOutlet, RouterState, RouterStateSnapshot, RoutesRecognized, Scroll, TitleStrategy, UrlHandlingStrategy, UrlSegment, UrlSegmentGroup, UrlSerializer, UrlTree, convertToParamMap, createUrlTreeFromSnapshot, defaultUrlMatcher, ɵEmptyOutletComponent, afterNextNavigation as ɵafterNextNavigation, loadChildren as ɵloadChildren } from './router-BxrGTdzL.mjs';\nexport { NoPreloading, PreloadAllModules, PreloadingStrategy, ROUTER_INITIALIZER, RouterLink, RouterLinkActive, RouterLink as RouterLinkWithHref, RouterModule, RouterPreloader, provideRouter, provideRoutes, withComponentInputBinding, withDebugTracing, withDisabledInitialNavigation, withEnabledBlockingInitialNavigation, withHashLocation, withInMemoryScrolling, withNavigationErrorHandler, withPreloading, withRouterConfig, withViewTransitions, ROUTER_PROVIDERS as ɵROUTER_PROVIDERS } from './router_module-avVs2gWt.mjs';\nimport { inject, Version } from '@angular/core';\nimport '@angular/common';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport '@angular/platform-browser';\n\n/**\n * Maps an array of injectable classes with canMatch functions to an array of equivalent\n * `CanMatchFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToCanMatch(providers) {\n  return providers.map(provider => (...params) => inject(provider).canMatch(...params));\n}\n/**\n * Maps an array of injectable classes with canActivate functions to an array of equivalent\n * `CanActivateFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToCanActivate(providers) {\n  return providers.map(provider => (...params) => inject(provider).canActivate(...params));\n}\n/**\n * Maps an array of injectable classes with canActivateChild functions to an array of equivalent\n * `CanActivateChildFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToCanActivateChild(providers) {\n  return providers.map(provider => (...params) => inject(provider).canActivateChild(...params));\n}\n/**\n * Maps an array of injectable classes with canDeactivate functions to an array of equivalent\n * `CanDeactivateFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToCanDeactivate(providers) {\n  return providers.map(provider => (...params) => inject(provider).canDeactivate(...params));\n}\n/**\n * Maps an injectable class with a resolve function to an equivalent `ResolveFn`\n * for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='Resolve'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToResolve(provider) {\n  return (...params) => inject(provider).resolve(...params);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('20.0.0-next.8');\nexport { VERSION, mapToCanActivate, mapToCanActivateChild, mapToCanDeactivate, mapToCanMatch, mapToResolve };", "map": {"version": 3, "names": ["ActivatedRoute", "ActivatedRouteSnapshot", "ActivationEnd", "ActivationStart", "BaseRouteReuseStrategy", "ChildActivationEnd", "ChildActivationStart", "ChildrenOutletContexts", "DefaultTitleStrategy", "DefaultUrlSerializer", "EventType", "GuardsCheckEnd", "GuardsCheckStart", "NavigationCancel", "NavigationCancellationCode", "NavigationEnd", "NavigationError", "NavigationSkipped", "NavigationSkippedCode", "NavigationStart", "OutletContext", "PRIMARY_OUTLET", "ROUTER_CONFIGURATION", "ROUTER_OUTLET_DATA", "ROUTES", "RedirectCommand", "ResolveEnd", "ResolveStart", "RouteConfigLoadEnd", "RouteConfigLoadStart", "RouteReuseStrategy", "Router", "RouterEvent", "RouterOutlet", "RouterState", "RouterStateSnapshot", "RoutesRecognized", "<PERSON><PERSON>", "TitleStrategy", "UrlHandlingStrategy", "UrlSegment", "UrlSegmentGroup", "UrlSerializer", "UrlTree", "convertToParamMap", "createUrlTreeFromSnapshot", "defaultUrlMatcher", "ɵEmptyOutletComponent", "afterNextNavigation", "ɵafterNextNavigation", "loadChildren", "ɵloadChildren", "NoPreloading", "PreloadAllModules", "PreloadingStrategy", "ROUTER_INITIALIZER", "RouterLink", "RouterLinkActive", "RouterLinkWithHref", "RouterModule", "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "provideRouter", "provideRoutes", "withComponentInputBinding", "withDebugTracing", "withDisabledInitialNavigation", "withEnabledBlockingInitialNavigation", "withHashLocation", "withInMemoryScrolling", "withNavigationErrorHandler", "withPreloading", "withRouterConfig", "withViewTransitions", "ROUTER_PROVIDERS", "ɵROUTER_PROVIDERS", "inject", "Version", "mapToCanMatch", "providers", "map", "provider", "params", "canMatch", "mapToCanActivate", "canActivate", "mapToCanActivateChild", "canActivateChild", "mapToCanDeactivate", "canDeactivate", "mapToResolve", "resolve", "VERSION"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/@angular/router/fesm2022/router.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nexport { ActivatedRoute, ActivatedRouteSnapshot, ActivationEnd, ActivationStart, BaseRouteReuseStrategy, ChildActivationEnd, ChildActivationStart, ChildrenOutletContexts, DefaultTitleStrategy, DefaultUrlSerializer, EventType, GuardsCheckEnd, GuardsCheckStart, NavigationCancel, NavigationCancellationCode, NavigationEnd, NavigationError, NavigationSkipped, NavigationSkippedCode, NavigationStart, OutletContext, PRIMARY_OUTLET, ROUTER_CONFIGURATION, ROUTER_OUTLET_DATA, ROUTES, RedirectCommand, ResolveEnd, ResolveStart, RouteConfigLoadEnd, RouteConfigLoadStart, RouteReuseStrategy, Router, RouterEvent, RouterOutlet, RouterState, RouterStateSnapshot, RoutesRecognized, Scroll, TitleStrategy, UrlHandlingStrategy, UrlSegment, UrlSegmentGroup, UrlSerializer, UrlTree, convertToParamMap, createUrlTreeFromSnapshot, defaultUrlMatcher, ɵEmptyOutletComponent, afterNextNavigation as ɵafterNextNavigation, loadChildren as ɵloadChildren } from './router-BxrGTdzL.mjs';\nexport { NoPreloading, PreloadAllModules, PreloadingStrategy, ROUTER_INITIALIZER, RouterLink, RouterLinkActive, RouterLink as RouterLinkWithHref, RouterModule, RouterPreloader, provideRouter, provideRoutes, withComponentInputBinding, withDebugTracing, withDisabledInitialNavigation, withEnabledBlockingInitialNavigation, withHashLocation, withInMemoryScrolling, withNavigationErrorHandler, withPreloading, withRouterConfig, withViewTransitions, ROUTER_PROVIDERS as ɵROUTER_PROVIDERS } from './router_module-avVs2gWt.mjs';\nimport { inject, Version } from '@angular/core';\nimport '@angular/common';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport '@angular/platform-browser';\n\n/**\n * Maps an array of injectable classes with canMatch functions to an array of equivalent\n * `CanMatchFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToCanMatch(providers) {\n    return providers.map((provider) => (...params) => inject(provider).canMatch(...params));\n}\n/**\n * Maps an array of injectable classes with canActivate functions to an array of equivalent\n * `CanActivateFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToCanActivate(providers) {\n    return providers.map((provider) => (...params) => inject(provider).canActivate(...params));\n}\n/**\n * Maps an array of injectable classes with canActivateChild functions to an array of equivalent\n * `CanActivateChildFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToCanActivateChild(providers) {\n    return providers.map((provider) => (...params) => inject(provider).canActivateChild(...params));\n}\n/**\n * Maps an array of injectable classes with canDeactivate functions to an array of equivalent\n * `CanDeactivateFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToCanDeactivate(providers) {\n    return providers.map((provider) => (...params) => inject(provider).canDeactivate(...params));\n}\n/**\n * Maps an injectable class with a resolve function to an equivalent `ResolveFn`\n * for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='Resolve'}\n *\n * @publicApi\n * @see {@link Route}\n */\nfunction mapToResolve(provider) {\n    return (...params) => inject(provider).resolve(...params);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('20.0.0-next.8');\n\nexport { VERSION, mapToCanActivate, mapToCanActivateChild, mapToCanDeactivate, mapToCanMatch, mapToResolve };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,cAAc,EAAEC,sBAAsB,EAAEC,aAAa,EAAEC,eAAe,EAAEC,sBAAsB,EAAEC,kBAAkB,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,0BAA0B,EAAEC,aAAa,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,cAAc,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,MAAM,EAAEC,eAAe,EAAEC,UAAU,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,UAAU,EAAEC,eAAe,EAAEC,aAAa,EAAEC,OAAO,EAAEC,iBAAiB,EAAEC,yBAAyB,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,mBAAmB,IAAIC,oBAAoB,EAAEC,YAAY,IAAIC,aAAa,QAAQ,uBAAuB;AACh8B,SAASC,YAAY,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,gBAAgB,EAAED,UAAU,IAAIE,kBAAkB,EAAEC,YAAY,EAAEC,eAAe,EAAEC,aAAa,EAAEC,aAAa,EAAEC,yBAAyB,EAAEC,gBAAgB,EAAEC,6BAA6B,EAAEC,oCAAoC,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,0BAA0B,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,gBAAgB,IAAIC,iBAAiB,QAAQ,8BAA8B;AACxgB,SAASC,MAAM,EAAEC,OAAO,QAAQ,eAAe;AAC/C,OAAO,iBAAiB;AACxB,OAAO,MAAM;AACb,OAAO,gBAAgB;AACvB,OAAO,2BAA2B;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,SAAS,EAAE;EAC9B,OAAOA,SAAS,CAACC,GAAG,CAAEC,QAAQ,IAAK,CAAC,GAAGC,MAAM,KAAKN,MAAM,CAACK,QAAQ,CAAC,CAACE,QAAQ,CAAC,GAAGD,MAAM,CAAC,CAAC;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gBAAgBA,CAACL,SAAS,EAAE;EACjC,OAAOA,SAAS,CAACC,GAAG,CAAEC,QAAQ,IAAK,CAAC,GAAGC,MAAM,KAAKN,MAAM,CAACK,QAAQ,CAAC,CAACI,WAAW,CAAC,GAAGH,MAAM,CAAC,CAAC;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,qBAAqBA,CAACP,SAAS,EAAE;EACtC,OAAOA,SAAS,CAACC,GAAG,CAAEC,QAAQ,IAAK,CAAC,GAAGC,MAAM,KAAKN,MAAM,CAACK,QAAQ,CAAC,CAACM,gBAAgB,CAAC,GAAGL,MAAM,CAAC,CAAC;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,kBAAkBA,CAACT,SAAS,EAAE;EACnC,OAAOA,SAAS,CAACC,GAAG,CAAEC,QAAQ,IAAK,CAAC,GAAGC,MAAM,KAAKN,MAAM,CAACK,QAAQ,CAAC,CAACQ,aAAa,CAAC,GAAGP,MAAM,CAAC,CAAC;AAChG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,YAAYA,CAACT,QAAQ,EAAE;EAC5B,OAAO,CAAC,GAAGC,MAAM,KAAKN,MAAM,CAACK,QAAQ,CAAC,CAACU,OAAO,CAAC,GAAGT,MAAM,CAAC;AAC7D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,OAAO,GAAG,IAAIf,OAAO,CAAC,eAAe,CAAC;AAE5C,SAASe,OAAO,EAAER,gBAAgB,EAAEE,qBAAqB,EAAEE,kBAAkB,EAAEV,aAAa,EAAEY,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}