{"ast": null, "code": "import { enableProdMode, importProvidersFrom } from '@angular/core';\nimport { environment } from './environments/environment';\nimport { BrowserModule, bootstrapApplication } from '@angular/platform-browser';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { AppRoutingModule } from './app/app-routing.module';\nimport { AppComponent } from './app/app.component';\nif (environment.production) {\n  enableProdMode();\n}\nbootstrapApplication(AppComponent, {\n  providers: [importProvidersFrom(BrowserModule, AppRoutingModule), provideAnimations()]\n}).catch(err => console.error(err));", "map": {"version": 3, "names": ["enableProdMode", "importProvidersFrom", "environment", "BrowserModule", "bootstrapApplication", "provideAnimations", "AppRoutingModule", "AppComponent", "production", "providers", "catch", "err", "console", "error"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\main.ts"], "sourcesContent": ["import { enableProdMode, importProvidersFrom } from '@angular/core';\r\n\r\nimport { environment } from './environments/environment';\r\nimport { BrowserModule, bootstrapApplication } from '@angular/platform-browser';\r\nimport { provideAnimations } from '@angular/platform-browser/animations';\r\n\r\nimport { AppRoutingModule } from './app/app-routing.module';\r\nimport { AppComponent } from './app/app.component';\r\n\r\nif (environment.production) {\r\n  enableProdMode();\r\n}\r\n\r\nbootstrapApplication(AppComponent, {\r\n  providers: [importProvidersFrom(BrowserModule, AppRoutingModule), provideAnimations()]\r\n}).catch((err) => console.error(err));\r\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,mBAAmB,QAAQ,eAAe;AAEnE,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,SAASC,iBAAiB,QAAQ,sCAAsC;AAExE,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,YAAY,QAAQ,qBAAqB;AAElD,IAAIL,WAAW,CAACM,UAAU,EAAE;EAC1BR,cAAc,EAAE;AAClB;AAEAI,oBAAoB,CAACG,YAAY,EAAE;EACjCE,SAAS,EAAE,CAACR,mBAAmB,CAACE,aAAa,EAAEG,gBAAgB,CAAC,EAAED,iBAAiB,EAAE;CACtF,CAAC,CAACK,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}