{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  children: [{\n    path: 'bootstrap',\n    loadComponent: () => import('./tbl-bootstrap/tbl-bootstrap.component')\n  }]\n}];\nexport class TablesRoutingModule {\n  static {\n    this.ɵfac = function TablesRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TablesRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TablesRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TablesRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "routes", "path", "children", "loadComponent", "TablesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\tables\\tables-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    children: [\r\n      {\r\n        path: 'bootstrap',\r\n        loadComponent: () => import('./tbl-bootstrap/tbl-bootstrap.component')\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class TablesRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;;;AAEtD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,WAAW;IACjBE,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC;GACtE;CAEJ,CACF;AAMD,OAAM,MAAOC,mBAAmB;;;uCAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAHpBL,YAAY,CAACM,QAAQ,CAACL,MAAM,CAAC,EAC7BD,YAAY;IAAA;EAAA;;;2EAEXK,mBAAmB;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAFpBT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}