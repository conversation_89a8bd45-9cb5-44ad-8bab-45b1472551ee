{"ast": null, "code": "// angular import\nimport { inject, output } from '@angular/core';\nimport { Location } from '@angular/common';\n// project import\nimport { environment } from 'src/environments/environment';\nimport { NavigationItems } from '../navigation';\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\nimport { NavGroupComponent } from './nav-group/nav-group.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-scrollbar\";\nfunction NavContentComponent_For_4_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-nav-group\", 5);\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"item\", item_r1);\n  }\n}\nfunction NavContentComponent_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, NavContentComponent_For_4_Conditional_0_Template, 1, 1, \"app-nav-group\", 5);\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵconditional(item_r1.type === \"group\" ? 0 : -1);\n  }\n}\nexport class NavContentComponent {\n  // constructor\n  constructor() {\n    this.location = inject(Location);\n    // public method\n    // version\n    this.title = 'Demo application for version numbering';\n    this.currentApplicationVersion = environment.appVersion;\n    this.windowWidth = window.innerWidth;\n    this.NavCollapsedMob = output();\n    this.navigations = NavigationItems;\n  }\n  fireOutClick() {\n    let current_url = this.location.path();\n    if (this.location['_baseHref']) {\n      current_url = this.location['_baseHref'] + this.location.path();\n    }\n    const link = \"a.nav-link[ href='\" + current_url + \"' ]\";\n    const ele = document.querySelector(link);\n    if (ele !== null && ele !== undefined) {\n      const parent = ele.parentElement;\n      const up_parent = parent.parentElement.parentElement;\n      const last_parent = up_parent.parentElement;\n      if (parent.classList.contains('pcoded-hasmenu')) {\n        parent.classList.add('pcoded-trigger');\n        parent.classList.add('active');\n      } else if (up_parent.classList.contains('pcoded-hasmenu')) {\n        up_parent.classList.add('pcoded-trigger');\n        up_parent.classList.add('active');\n      } else if (last_parent.classList.contains('pcoded-hasmenu')) {\n        last_parent.classList.add('pcoded-trigger');\n        last_parent.classList.add('active');\n      }\n    }\n  }\n  static {\n    this.ɵfac = function NavContentComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NavContentComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavContentComponent,\n      selectors: [[\"app-nav-content\"]],\n      outputs: {\n        NavCollapsedMob: \"NavCollapsedMob\"\n      },\n      decls: 8,\n      vars: 1,\n      consts: [[\"exclude\", \"'#mobile-collapse1'\", \"visibility\", \"hover\", 2, \"height\", \"calc(100vh - 70px)\"], [1, \"navbar-content\"], [1, \"nav\", \"pcoded-inner-navbar\", 3, \"clickOutside\"], [1, \"version\"], [\"for\", \"version\", \"disabled\", \"\", 1, \"pe-auto\"], [3, \"item\"]],\n      template: function NavContentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ng-scrollbar\", 0)(1, \"div\", 1)(2, \"ul\", 2);\n          i0.ɵɵlistener(\"clickOutside\", function NavContentComponent_Template_ul_clickOutside_2_listener() {\n            return ctx.fireOutClick();\n          });\n          i0.ɵɵrepeaterCreate(3, NavContentComponent_For_4_Template, 1, 1, null, null, i0.ɵɵrepeaterTrackByIdentity);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"label\", 4);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵrepeater(ctx.navigations);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"v\", ctx.currentApplicationVersion);\n        }\n      },\n      dependencies: [SharedModule, i1.NgScrollbar, NavGroupComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["inject", "output", "Location", "environment", "NavigationItems", "SharedModule", "NavGroupComponent", "i0", "ɵɵelement", "ɵɵproperty", "item_r1", "ɵɵconditionalCreate", "NavContentComponent_For_4_Conditional_0_Template", "ɵɵconditional", "type", "NavContentComponent", "constructor", "location", "title", "currentApplicationVersion", "appVersion", "windowWidth", "window", "innerWidth", "NavCollapsedMob", "navigations", "fireOutClick", "current_url", "path", "link", "ele", "document", "querySelector", "undefined", "parent", "parentElement", "up_parent", "last_parent", "classList", "contains", "add", "selectors", "outputs", "decls", "vars", "consts", "template", "NavContentComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "NavContentComponent_Template_ul_clickOutside_2_listener", "ɵɵrepeaterCreate", "NavContentComponent_For_4_Template", "ɵɵrepeaterTrackByIdentity", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵrepeater", "ɵɵtextInterpolate1", "i1", "NgScrollbar", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\navigation\\nav-content\\nav-content.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\navigation\\nav-content\\nav-content.component.html"], "sourcesContent": ["// angular import\r\nimport { Component, inject, output } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\n\r\n// project import\r\nimport { environment } from 'src/environments/environment';\r\nimport { NavigationItem, NavigationItems } from '../navigation';\r\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\r\nimport { NavGroupComponent } from './nav-group/nav-group.component';\r\n\r\n@Component({\r\n  selector: 'app-nav-content',\r\n  imports: [SharedModule, NavGroupComponent],\r\n  templateUrl: './nav-content.component.html',\r\n  styleUrls: ['./nav-content.component.scss']\r\n})\r\nexport class NavContentComponent {\r\n  private location = inject(Location);\r\n\r\n  // public method\r\n  // version\r\n  title = 'Demo application for version numbering';\r\n  currentApplicationVersion = environment.appVersion;\r\n\r\n  navigations!: NavigationItem[];\r\n  wrapperWidth: number;\r\n  windowWidth = window.innerWidth;\r\n\r\n  NavCollapsedMob = output();\r\n\r\n  // constructor\r\n  constructor() {\r\n    this.navigations = NavigationItems;\r\n  }\r\n\r\n  fireOutClick() {\r\n    let current_url = this.location.path();\r\n    if (this.location['_baseHref']) {\r\n      current_url = this.location['_baseHref'] + this.location.path();\r\n    }\r\n    const link = \"a.nav-link[ href='\" + current_url + \"' ]\";\r\n    const ele = document.querySelector(link);\r\n    if (ele !== null && ele !== undefined) {\r\n      const parent = ele.parentElement;\r\n      const up_parent = parent.parentElement.parentElement;\r\n      const last_parent = up_parent.parentElement;\r\n      if (parent.classList.contains('pcoded-hasmenu')) {\r\n        parent.classList.add('pcoded-trigger');\r\n        parent.classList.add('active');\r\n      } else if (up_parent.classList.contains('pcoded-hasmenu')) {\r\n        up_parent.classList.add('pcoded-trigger');\r\n        up_parent.classList.add('active');\r\n      } else if (last_parent.classList.contains('pcoded-hasmenu')) {\r\n        last_parent.classList.add('pcoded-trigger');\r\n        last_parent.classList.add('active');\r\n      }\r\n    }\r\n  }\r\n}\r\n", "<!--  vertical layouts -->\r\n<ng-scrollbar style=\"height: calc(100vh - 70px)\" exclude=\"'#mobile-collapse1'\" visibility=\"hover\">\r\n  <div class=\"navbar-content\">\r\n    <ul class=\"nav pcoded-inner-navbar\" (clickOutside)=\"fireOutClick()\">\r\n      @for (item of navigations; track item) {\r\n        @if (item.type === 'group') {\r\n          <app-nav-group [item]=\"item\" />\r\n        }\r\n      }\r\n    </ul>\r\n    <div class=\"version\">\r\n      <label for=\"version\" disabled class=\"pe-auto\">v{{ currentApplicationVersion }}</label>\r\n    </div>\r\n  </div>\r\n</ng-scrollbar>\r\n"], "mappings": "AAAA;AACA,SAAoBA,MAAM,EAAEC,MAAM,QAAQ,eAAe;AACzD,SAASC,QAAQ,QAAQ,iBAAiB;AAE1C;AACA,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAAyBC,eAAe,QAAQ,eAAe;AAC/D,SAASC,YAAY,QAAQ,oCAAoC;AACjE,SAASC,iBAAiB,QAAQ,iCAAiC;;;;;ICFzDC,EAAA,CAAAC,SAAA,uBAA+B;;;;IAAhBD,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAa;;;;;IAD9BH,EAAA,CAAAI,mBAAA,IAAAC,gDAAA,2BAA6B;;;;IAA7BL,EAAA,CAAAM,aAAA,CAAAH,OAAA,CAAAI,IAAA,sBAEC;;;ADST,OAAM,MAAOC,mBAAmB;EAc9B;EACAC,YAAA;IAdQ,KAAAC,QAAQ,GAAGjB,MAAM,CAACE,QAAQ,CAAC;IAEnC;IACA;IACA,KAAAgB,KAAK,GAAG,wCAAwC;IAChD,KAAAC,yBAAyB,GAAGhB,WAAW,CAACiB,UAAU;IAIlD,KAAAC,WAAW,GAAGC,MAAM,CAACC,UAAU;IAE/B,KAAAC,eAAe,GAAGvB,MAAM,EAAE;IAIxB,IAAI,CAACwB,WAAW,GAAGrB,eAAe;EACpC;EAEAsB,YAAYA,CAAA;IACV,IAAIC,WAAW,GAAG,IAAI,CAACV,QAAQ,CAACW,IAAI,EAAE;IACtC,IAAI,IAAI,CAACX,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC9BU,WAAW,GAAG,IAAI,CAACV,QAAQ,CAAC,WAAW,CAAC,GAAG,IAAI,CAACA,QAAQ,CAACW,IAAI,EAAE;IACjE;IACA,MAAMC,IAAI,GAAG,oBAAoB,GAAGF,WAAW,GAAG,KAAK;IACvD,MAAMG,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAACH,IAAI,CAAC;IACxC,IAAIC,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKG,SAAS,EAAE;MACrC,MAAMC,MAAM,GAAGJ,GAAG,CAACK,aAAa;MAChC,MAAMC,SAAS,GAAGF,MAAM,CAACC,aAAa,CAACA,aAAa;MACpD,MAAME,WAAW,GAAGD,SAAS,CAACD,aAAa;MAC3C,IAAID,MAAM,CAACI,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QAC/CL,MAAM,CAACI,SAAS,CAACE,GAAG,CAAC,gBAAgB,CAAC;QACtCN,MAAM,CAACI,SAAS,CAACE,GAAG,CAAC,QAAQ,CAAC;MAChC,CAAC,MAAM,IAAIJ,SAAS,CAACE,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QACzDH,SAAS,CAACE,SAAS,CAACE,GAAG,CAAC,gBAAgB,CAAC;QACzCJ,SAAS,CAACE,SAAS,CAACE,GAAG,CAAC,QAAQ,CAAC;MACnC,CAAC,MAAM,IAAIH,WAAW,CAACC,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QAC3DF,WAAW,CAACC,SAAS,CAACE,GAAG,CAAC,gBAAgB,CAAC;QAC3CH,WAAW,CAACC,SAAS,CAACE,GAAG,CAAC,QAAQ,CAAC;MACrC;IACF;EACF;;;uCAzCWzB,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAA0B,SAAA;MAAAC,OAAA;QAAAlB,eAAA;MAAA;MAAAmB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCb5BzC,EAFJ,CAAA2C,cAAA,sBAAkG,aACpE,YAC0C;UAAhC3C,EAAA,CAAA4C,UAAA,0BAAAC,wDAAA;YAAA,OAAgBH,GAAA,CAAAvB,YAAA,EAAc;UAAA,EAAC;UACjEnB,EAAA,CAAA8C,gBAAA,IAAAC,kCAAA,oBAAA/C,EAAA,CAAAgD,yBAAA,CAIC;UACHhD,EAAA,CAAAiD,YAAA,EAAK;UAEHjD,EADF,CAAA2C,cAAA,aAAqB,eAC2B;UAAA3C,EAAA,CAAAkD,MAAA,GAAgC;UAGpFlD,EAHoF,CAAAiD,YAAA,EAAQ,EAClF,EACF,EACO;;;UAVTjD,EAAA,CAAAmD,SAAA,GAIC;UAJDnD,EAAA,CAAAoD,UAAA,CAAAV,GAAA,CAAAxB,WAAA,CAIC;UAG6ClB,EAAA,CAAAmD,SAAA,GAAgC;UAAhCnD,EAAA,CAAAqD,kBAAA,MAAAX,GAAA,CAAA9B,yBAAA,CAAgC;;;qBDCxEd,YAAY,EAAAwD,EAAA,CAAAC,WAAA,EAAExD,iBAAiB;MAAAyD,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}