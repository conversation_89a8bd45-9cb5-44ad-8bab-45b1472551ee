{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AuthenticationRoutingModule } from './authentication-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class AuthenticationModule {\n  static {\n    this.ɵfac = function AuthenticationModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthenticationModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AuthenticationModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, AuthenticationRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthenticationModule, {\n    imports: [CommonModule, AuthenticationRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "AuthenticationRoutingModule", "AuthenticationModule", "imports"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\authentication\\authentication.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { AuthenticationRoutingModule } from './authentication-routing.module';\r\n\r\n@NgModule({\r\n  declarations: [],\r\n  imports: [CommonModule, AuthenticationRoutingModule]\r\n})\r\nexport class AuthenticationModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,2BAA2B,QAAQ,iCAAiC;;AAM7E,OAAM,MAAOC,oBAAoB;;;uCAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAFrBF,YAAY,EAAEC,2BAA2B;IAAA;EAAA;;;2EAExCC,oBAAoB;IAAAC,OAAA,GAFrBH,YAAY,EAAEC,2BAA2B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}