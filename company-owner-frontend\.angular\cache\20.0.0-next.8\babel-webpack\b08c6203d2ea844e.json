{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ConfigurationComponent {\n  static {\n    this.ɵfac = function ConfigurationComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ConfigurationComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ConfigurationComponent,\n      selectors: [[\"app-configuration\"]],\n      decls: 4,\n      vars: 0,\n      consts: [[1, \"fixed-button\", \"active\"], [\"href\", \"https://codedthemes.com/item/datta-able-angular/?utm_source=free_demo&utm_medium=codedthemes&utm_campaign=button_download_premium\", \"target\", \"_blank\", 1, \"btn\", \"btn-md\", \"btn-theme\", \"has-ripple\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-shopping-cart\"]],\n      template: function ConfigurationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"a\", 1);\n          i0.ɵɵelement(2, \"i\", 2);\n          i0.ɵɵtext(3, \" Upgrade To Pro \");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ConfigurationComponent", "selectors", "decls", "vars", "consts", "template", "ConfigurationComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\configuration\\configuration.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\configuration\\configuration.component.html"], "sourcesContent": ["// angular import\r\nimport { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-configuration',\r\n  templateUrl: './configuration.component.html',\r\n  styleUrls: ['./configuration.component.scss']\r\n})\r\nexport class ConfigurationComponent {}\r\n", "<div class=\"fixed-button active\">\r\n  <a\r\n    href=\"https://codedthemes.com/item/datta-able-angular/?utm_source=free_demo&utm_medium=codedthemes&utm_campaign=button_download_premium\"\r\n    target=\"_blank\"\r\n    class=\"btn btn-md btn-theme has-ripple\"\r\n  >\r\n    <i class=\"fa fa-shopping-cart\" aria-hidden=\"true\"></i>\r\n    Upgrade To Pro\r\n  </a>\r\n</div>\r\n"], "mappings": ";AAQA,OAAM,MAAOA,sBAAsB;;;uCAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPjCE,EADF,CAAAC,cAAA,aAAiC,WAK9B;UACCD,EAAA,CAAAE,SAAA,WAAsD;UACtDF,EAAA,CAAAG,MAAA,uBACF;UACFH,EADE,CAAAI,YAAA,EAAI,EACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}