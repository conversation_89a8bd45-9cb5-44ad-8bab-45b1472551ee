{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  children: [{\n    path: 'basic',\n    loadComponent: () => import('./basic-elements/basic-elements.component')\n  }]\n}];\nexport class FormElementsRoutingModule {\n  static {\n    this.ɵfac = function FormElementsRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormElementsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: FormElementsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(FormElementsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "routes", "path", "children", "loadComponent", "FormElementsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\form-elements\\form-elements-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    children: [\r\n      {\r\n        path: 'basic',\r\n        loadComponent: () => import('./basic-elements/basic-elements.component')\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class FormElementsRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;;;AAEtD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,OAAO;IACbE,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,2CAA2C;GACxE;CAEJ,CACF;AAMD,OAAM,MAAOC,yBAAyB;;;uCAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA;IAAyB;EAAA;;;gBAH1BL,YAAY,CAACM,QAAQ,CAACL,MAAM,CAAC,EAC7BD,YAAY;IAAA;EAAA;;;2EAEXK,yBAAyB;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAF1BT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}