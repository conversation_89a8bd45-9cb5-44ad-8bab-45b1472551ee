{"ast": null, "code": "// angular import\nimport { output } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n// project import\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nconst _c0 = () => [\"/dashboard/default/\"];\nconst _c1 = a0 => ({\n  on: a0\n});\nexport class NavLogoComponent {\n  constructor() {\n    this.NavCollapse = output();\n    this.windowWidth = window.innerWidth;\n  }\n  // public method\n  navCollapse() {\n    if (this.windowWidth >= 992) {\n      this.navCollapsed = !this.navCollapsed;\n      this.NavCollapse.emit();\n    }\n  }\n  static {\n    this.ɵfac = function NavLogoComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NavLogoComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavLogoComponent,\n      selectors: [[\"app-nav-logo\"]],\n      inputs: {\n        navCollapsed: \"navCollapsed\"\n      },\n      outputs: {\n        NavCollapse: \"NavCollapse\"\n      },\n      decls: 8,\n      vars: 5,\n      consts: [[1, \"\"], [1, \"b-brand\", 3, \"routerLink\"], [1, \"b-bg\"], [1, \"feather\", \"icon-trending-up\"], [1, \"b-title\"], [\"href\", \"javascript:\", \"id\", \"mobile-collapse\", 1, \"mobile-menu\", 3, \"click\", \"ngClass\"]],\n      template: function NavLogoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"a\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"span\", 4);\n          i0.ɵɵtext(5, \"Datta Able\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"a\", 5);\n          i0.ɵɵlistener(\"click\", function NavLogoComponent_Template_a_click_6_listener() {\n            return ctx.navCollapse();\n          });\n          i0.ɵɵelement(7, \"span\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(2, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c1, ctx.navCollapsed));\n        }\n      },\n      dependencies: [SharedModule, i1.NgClass, RouterModule, i2.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["output", "RouterModule", "SharedModule", "NavLogoComponent", "constructor", "NavCollapse", "windowWidth", "window", "innerWidth", "navCollapse", "navCollapsed", "emit", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "NavLogoComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "NavLogoComponent_Template_a_click_6_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵpureFunction1", "_c1", "i1", "Ng<PERSON><PERSON>", "i2", "RouterLink", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\navigation\\nav-logo\\nav-logo.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\navigation\\nav-logo\\nav-logo.component.html"], "sourcesContent": ["// angular import\r\nimport { Component, Input, output } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n// project import\r\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\r\n\r\n@Component({\r\n  selector: 'app-nav-logo',\r\n  imports: [SharedModule, RouterModule],\r\n  templateUrl: './nav-logo.component.html',\r\n  styleUrls: ['./nav-logo.component.scss']\r\n})\r\nexport class NavLogoComponent {\r\n  // public props\r\n  @Input() navCollapsed: boolean;\r\n  NavCollapse = output();\r\n  windowWidth = window.innerWidth;\r\n\r\n  // public method\r\n  navCollapse() {\r\n    if (this.windowWidth >= 992) {\r\n      this.navCollapsed = !this.navCollapsed;\r\n      this.NavCollapse.emit();\r\n    }\r\n  }\r\n}\r\n", "<div class=\"\">\r\n  <a [routerLink]=\"['/dashboard/default/']\" class=\"b-brand\">\r\n    <div class=\"b-bg\">\r\n      <i class=\"feather icon-trending-up\"></i>\r\n    </div>\r\n    <span class=\"b-title\">Datta Able</span>\r\n  </a>\r\n  <a href=\"javascript:\" class=\"mobile-menu\" [ngClass]=\"{ on: navCollapsed }\" id=\"mobile-collapse\" (click)=\"navCollapse()\"><span></span></a>\r\n</div>\r\n"], "mappings": "AAAA;AACA,SAA2BA,MAAM,QAAQ,eAAe;AACxD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,YAAY,QAAQ,oCAAoC;;;;;;;;AAQjE,OAAM,MAAOC,gBAAgB;EAN7BC,YAAA;IASE,KAAAC,WAAW,GAAGL,MAAM,EAAE;IACtB,KAAAM,WAAW,GAAGC,MAAM,CAACC,UAAU;;EAE/B;EACAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACH,WAAW,IAAI,GAAG,EAAE;MAC3B,IAAI,CAACI,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;MACtC,IAAI,CAACL,WAAW,CAACM,IAAI,EAAE;IACzB;EACF;;;uCAZWR,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAS,SAAA;MAAAC,MAAA;QAAAH,YAAA;MAAA;MAAAI,OAAA;QAAAT,WAAA;MAAA;MAAAU,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXzBE,EAFJ,CAAAC,cAAA,aAAc,WAC8C,aACtC;UAChBD,EAAA,CAAAE,SAAA,WAAwC;UAC1CF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAI,MAAA,iBAAU;UAClCJ,EADkC,CAAAG,YAAA,EAAO,EACrC;UACJH,EAAA,CAAAC,cAAA,WAAwH;UAAxBD,EAAA,CAAAK,UAAA,mBAAAC,6CAAA;YAAA,OAASP,GAAA,CAAAZ,WAAA,EAAa;UAAA,EAAC;UAACa,EAAA,CAAAE,SAAA,WAAa;UACvIF,EADuI,CAAAG,YAAA,EAAI,EACrI;;;UAPDH,EAAA,CAAAO,SAAA,EAAsC;UAAtCP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAAS,eAAA,IAAAC,GAAA,EAAsC;UAMCV,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAW,eAAA,IAAAC,GAAA,EAAAb,GAAA,CAAAX,YAAA,EAAgC;;;qBDEhER,YAAY,EAAAiC,EAAA,CAAAC,OAAA,EAAEnC,YAAY,EAAAoC,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}