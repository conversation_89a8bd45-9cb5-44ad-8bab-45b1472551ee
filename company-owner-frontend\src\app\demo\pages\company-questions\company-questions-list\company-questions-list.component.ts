import { Component, OnInit } from '@angular/core';
import { QuestionService } from '../../../../core/services/question.service';
import { Question } from '../../../../core/models/question.model';

@Component({
  selector: 'app-company-questions-list',
  templateUrl: './company-questions-list.component.html',
  styleUrls: ['./company-questions-list.component.scss']
})
export class CompanyQuestionsListComponent implements OnInit {
  questions: Question[] = [];
  loading = false;
  error = '';

  constructor(private questionService: QuestionService) {}

  ngOnInit(): void {
    this.loadQuestions();
  }

  loadQuestions(): void {
    this.loading = true;
    this.questionService.getQuestions()
      .subscribe({
        next: (data) => {
          this.questions = data;
          this.loading = false;
        },
        error: (error) => {
          this.error = 'Failed to load questions';
          this.loading = false;
          console.error('Error loading questions:', error);
        }
      });
  }
}