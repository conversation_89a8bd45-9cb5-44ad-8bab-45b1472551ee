{"ast": null, "code": "// project import\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\nimport { NavSearchComponent } from './nav-search/nav-search.component';\n//\nimport screenfull from 'screenfull';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nexport class NavLeftComponent {\n  constructor() {\n    this.screenFull = true;\n  }\n  // life cycle hook\n  ngOnInit() {\n    if (screenfull.isEnabled) {\n      this.screenFull = screenfull.isFullscreen; // Initialize based on current fullscreen state\n      screenfull.on('change', () => {\n        this.screenFull = screenfull.isFullscreen;\n      });\n    }\n  }\n  ngOnDestroy() {\n    if (screenfull.isEnabled) {\n      screenfull.off('change', () => {\n        this.screenFull = screenfull.isFullscreen;\n      });\n    }\n  }\n  toggleFullscreen() {\n    if (screenfull.isEnabled) {\n      screenfull.toggle().then(() => {\n        this.screenFull = screenfull.isFullscreen;\n      });\n    }\n  }\n  static {\n    this.ɵfac = function NavLeftComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NavLeftComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavLeftComponent,\n      selectors: [[\"app-nav-left\"]],\n      decls: 19,\n      vars: 1,\n      consts: [[1, \"navbar-nav\"], [\"href\", \"javascript:\", 1, \"full-screen\", 3, \"click\"], [1, \"feather\", 3, \"ngClass\"], [\"ngbDropdown\", \"\", \"placement\", \"auto\", 1, \"nav-item\", \"show-items\"], [\"href\", \"javascript:\", \"ngbDropdownToggle\", \"\"], [\"ngbDropdownMenu\", \"\"], [\"href\", \"javascript:\", \"ngbDropdownItem\", \"\"], [1, \"nav-item\"]],\n      template: function NavLeftComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ul\", 0)(1, \"li\")(2, \"a\", 1);\n          i0.ɵɵlistener(\"click\", function NavLeftComponent_Template_a_click_2_listener() {\n            return ctx.toggleFullscreen();\n          });\n          i0.ɵɵelement(3, \"i\", 2);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"li\", 3)(5, \"a\", 4);\n          i0.ɵɵtext(6, \"Dropdown\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"ul\", 5)(8, \"li\")(9, \"a\", 6);\n          i0.ɵɵtext(10, \"Action\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"li\")(12, \"a\", 6);\n          i0.ɵɵtext(13, \"Another action\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"li\")(15, \"a\", 6);\n          i0.ɵɵtext(16, \"Something else here\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(17, \"li\", 7);\n          i0.ɵɵelement(18, \"app-nav-search\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", ctx.screenFull ? \"icon-minimize\" : \"icon-maximize\");\n        }\n      },\n      dependencies: [SharedModule, i1.NgClass, i2.NgbDropdown, i2.NgbDropdownToggle, i2.NgbDropdownMenu, i2.NgbDropdownItem, NavSearchComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "NavSearchComponent", "screenfull", "NavLeftComponent", "constructor", "screenFull", "ngOnInit", "isEnabled", "isFullscreen", "on", "ngOnDestroy", "off", "toggleFullscreen", "toggle", "then", "selectors", "decls", "vars", "consts", "template", "NavLeftComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵlistener", "NavLeftComponent_Template_a_click_2_listener", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "i1", "Ng<PERSON><PERSON>", "i2", "NgbDropdown", "NgbDropdownToggle", "NgbDropdownMenu", "NgbDropdownItem", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\nav-bar\\nav-left\\nav-left.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\nav-bar\\nav-left\\nav-left.component.html"], "sourcesContent": ["// angular import\r\nimport { Component, OnD<PERSON>roy, OnInit } from '@angular/core';\r\n\r\n// project import\r\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\r\nimport { NavSearchComponent } from './nav-search/nav-search.component';\r\n\r\n//\r\nimport screenfull from 'screenfull';\r\n\r\n@Component({\r\n  selector: 'app-nav-left',\r\n  imports: [SharedModule, NavSearchComponent],\r\n  templateUrl: './nav-left.component.html',\r\n  styleUrls: ['./nav-left.component.scss']\r\n})\r\nexport class NavLeftComponent implements OnInit, OnDestroy {\r\n  screenFull = true;\r\n\r\n  // life cycle hook\r\n  ngOnInit() {\r\n    if (screenfull.isEnabled) {\r\n      this.screenFull = screenfull.isFullscreen; // Initialize based on current fullscreen state\r\n      screenfull.on('change', () => {\r\n        this.screenFull = screenfull.isFullscreen;\r\n      });\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (screenfull.isEnabled) {\r\n      screenfull.off('change', () => {\r\n        this.screenFull = screenfull.isFullscreen;\r\n      });\r\n    }\r\n  }\r\n\r\n  toggleFullscreen() {\r\n    if (screenfull.isEnabled) {\r\n      screenfull.toggle().then(() => {\r\n        this.screenFull = screenfull.isFullscreen;\r\n      });\r\n    }\r\n  }\r\n}\r\n", "<ul class=\"navbar-nav\">\r\n  <li>\r\n    <a href=\"javascript:\" class=\"full-screen\" (click)=\"toggleFullscreen()\">\r\n      <i class=\"feather\" [ngClass]=\"screenFull ? 'icon-minimize' : 'icon-maximize'\"></i>\r\n    </a>\r\n  </li>\r\n  <li class=\"nav-item show-items\" ngbDropdown placement=\"auto\">\r\n    <a href=\"javascript:\" ngbDropdownToggle>Dropdown</a>\r\n    <ul ngbDropdownMenu>\r\n      <li><a href=\"javascript:\" ngbDropdownItem>Action</a></li>\r\n      <li><a href=\"javascript:\" ngbDropdownItem>Another action</a></li>\r\n      <li>\r\n        <a href=\"javascript:\" ngbDropdownItem>Something else here</a>\r\n      </li>\r\n    </ul>\r\n  </li>\r\n  <li class=\"nav-item\">\r\n    <app-nav-search />\r\n  </li>\r\n</ul>\r\n"], "mappings": "AAGA;AACA,SAASA,YAAY,QAAQ,oCAAoC;AACjE,SAASC,kBAAkB,QAAQ,mCAAmC;AAEtE;AACA,OAAOC,UAAU,MAAM,YAAY;;;;AAQnC,OAAM,MAAOC,gBAAgB;EAN7BC,YAAA;IAOE,KAAAC,UAAU,GAAG,IAAI;;EAEjB;EACAC,QAAQA,CAAA;IACN,IAAIJ,UAAU,CAACK,SAAS,EAAE;MACxB,IAAI,CAACF,UAAU,GAAGH,UAAU,CAACM,YAAY,CAAC,CAAC;MAC3CN,UAAU,CAACO,EAAE,CAAC,QAAQ,EAAE,MAAK;QAC3B,IAAI,CAACJ,UAAU,GAAGH,UAAU,CAACM,YAAY;MAC3C,CAAC,CAAC;IACJ;EACF;EAEAE,WAAWA,CAAA;IACT,IAAIR,UAAU,CAACK,SAAS,EAAE;MACxBL,UAAU,CAACS,GAAG,CAAC,QAAQ,EAAE,MAAK;QAC5B,IAAI,CAACN,UAAU,GAAGH,UAAU,CAACM,YAAY;MAC3C,CAAC,CAAC;IACJ;EACF;EAEAI,gBAAgBA,CAAA;IACd,IAAIV,UAAU,CAACK,SAAS,EAAE;MACxBL,UAAU,CAACW,MAAM,EAAE,CAACC,IAAI,CAAC,MAAK;QAC5B,IAAI,CAACT,UAAU,GAAGH,UAAU,CAACM,YAAY;MAC3C,CAAC,CAAC;IACJ;EACF;;;uCA3BWL,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAY,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdzBE,EAFJ,CAAAC,cAAA,YAAuB,SACjB,WACqE;UAA7BD,EAAA,CAAAE,UAAA,mBAAAC,6CAAA;YAAA,OAASJ,GAAA,CAAAV,gBAAA,EAAkB;UAAA,EAAC;UACpEW,EAAA,CAAAI,SAAA,WAAkF;UAEtFJ,EADE,CAAAK,YAAA,EAAI,EACD;UAEHL,EADF,CAAAC,cAAA,YAA6D,WACnB;UAAAD,EAAA,CAAAM,MAAA,eAAQ;UAAAN,EAAA,CAAAK,YAAA,EAAI;UAE9CL,EADN,CAAAC,cAAA,YAAoB,SACd,WAAsC;UAAAD,EAAA,CAAAM,MAAA,cAAM;UAAIN,EAAJ,CAAAK,YAAA,EAAI,EAAK;UACrDL,EAAJ,CAAAC,cAAA,UAAI,YAAsC;UAAAD,EAAA,CAAAM,MAAA,sBAAc;UAAIN,EAAJ,CAAAK,YAAA,EAAI,EAAK;UAE/DL,EADF,CAAAC,cAAA,UAAI,YACoC;UAAAD,EAAA,CAAAM,MAAA,2BAAmB;UAG/DN,EAH+D,CAAAK,YAAA,EAAI,EAC1D,EACF,EACF;UACLL,EAAA,CAAAC,cAAA,aAAqB;UACnBD,EAAA,CAAAI,SAAA,sBAAkB;UAEtBJ,EADE,CAAAK,YAAA,EAAK,EACF;;;UAhBoBL,EAAA,CAAAO,SAAA,GAA0D;UAA1DP,EAAA,CAAAQ,UAAA,YAAAT,GAAA,CAAAjB,UAAA,qCAA0D;;;qBDSvEL,YAAY,EAAAgC,EAAA,CAAAC,OAAA,EAAAC,EAAA,CAAAC,WAAA,EAAAD,EAAA,CAAAE,iBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,eAAA,EAAErC,kBAAkB;MAAAsC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}