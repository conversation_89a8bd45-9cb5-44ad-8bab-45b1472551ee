{"ast": null, "code": "import _asyncToGenerator from \"D:/employee-survey-app/company-owner-frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { inject, RuntimeError, formatRuntimeError, ErrorHandler, DestroyRef, assertNotInReactiveContext, assertInInjectionContext, Injector, ViewContext, ChangeDetectionScheduler, EffectScheduler, setInjectorProfilerContext, emitEffectCreatedEvent, EFFECTS, noop, FLAGS, markAncestorsForTraversal, setIsRefreshingViews, NodeInjectorDestroyRef, InjectionToken, signalAsReadonlyFn, PendingTasks, signal } from './root_effect_scheduler-BZMWiScf.mjs';\nimport { setActiveConsumer, createComputed, SIGNAL, REACTIVE_NODE, consumerDestroy, isInNotificationPhase, consumerPollProducersForChange, consumerBeforeComputation, consumerAfterComputation } from './signal-B6pMq7KS.mjs';\nimport { untracked as untracked$1, createLinkedSignal, linkedSignalSetFn, linkedSignalUpdateFn } from './untracked-Bz5WMeU1.mjs';\n\n/**\n * An `OutputEmitterRef` is created by the `output()` function and can be\n * used to emit values to consumers of your directive or component.\n *\n * Consumers of your directive/component can bind to the output and\n * subscribe to changes via the bound event syntax. For example:\n *\n * ```html\n * <my-comp (valueChange)=\"processNewValue($event)\" />\n * ```\n *\n * @publicAPI\n */\nclass OutputEmitterRef {\n  destroyed = false;\n  listeners = null;\n  errorHandler = inject(ErrorHandler, {\n    optional: true\n  });\n  /** @internal */\n  destroyRef = inject(DestroyRef);\n  constructor() {\n    // Clean-up all listeners and mark as destroyed upon destroy.\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n      this.listeners = null;\n    });\n  }\n  subscribe(callback) {\n    if (this.destroyed) {\n      throw new RuntimeError(953 /* RuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected subscription to destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.');\n    }\n    (this.listeners ??= []).push(callback);\n    return {\n      unsubscribe: () => {\n        const idx = this.listeners?.indexOf(callback);\n        if (idx !== undefined && idx !== -1) {\n          this.listeners?.splice(idx, 1);\n        }\n      }\n    };\n  }\n  /** Emits a new value to the output. */\n  emit(value) {\n    if (this.destroyed) {\n      console.warn(formatRuntimeError(953 /* RuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected emit for destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.'));\n      return;\n    }\n    if (this.listeners === null) {\n      return;\n    }\n    const previousConsumer = setActiveConsumer(null);\n    try {\n      for (const listenerFn of this.listeners) {\n        try {\n          listenerFn(value);\n        } catch (err) {\n          this.errorHandler?.handleError(err);\n        }\n      }\n    } finally {\n      setActiveConsumer(previousConsumer);\n    }\n  }\n}\n/** Gets the owning `DestroyRef` for the given output. */\nfunction getOutputDestroyRef(ref) {\n  return ref.destroyRef;\n}\n\n/**\n * Execute an arbitrary function in a non-reactive (non-tracking) context. The executed function\n * can, optionally, return a value.\n */\nfunction untracked(nonReactiveReadsFn) {\n  return untracked$1(nonReactiveReadsFn);\n}\n\n/**\n * Create a computed `Signal` which derives a reactive value from an expression.\n */\nfunction computed(computation, options) {\n  const getter = createComputed(computation, options?.equal);\n  if (ngDevMode) {\n    getter.toString = () => `[Computed: ${getter()}]`;\n    getter[SIGNAL].debugName = options?.debugName;\n  }\n  return getter;\n}\nclass EffectRefImpl {\n  [SIGNAL];\n  constructor(node) {\n    this[SIGNAL] = node;\n  }\n  destroy() {\n    this[SIGNAL].destroy();\n  }\n}\n/**\n * Registers an \"effect\" that will be scheduled & executed whenever the signals that it reads\n * changes.\n *\n * Angular has two different kinds of effect: component effects and root effects. Component effects\n * are created when `effect()` is called from a component, directive, or within a service of a\n * component/directive. Root effects are created when `effect()` is called from outside the\n * component tree, such as in a root service.\n *\n * The two effect types differ in their timing. Component effects run as a component lifecycle\n * event during Angular's synchronization (change detection) process, and can safely read input\n * signals or create/destroy views that depend on component state. Root effects run as microtasks\n * and have no connection to the component tree or change detection.\n *\n * `effect()` must be run in injection context, unless the `injector` option is manually specified.\n */\nfunction effect(effectFn, options) {\n  ngDevMode && assertNotInReactiveContext(effect, 'Call `effect` outside of a reactive context. For example, schedule the ' + 'effect inside the component constructor.');\n  !options?.injector && assertInInjectionContext(effect);\n  if (ngDevMode && options?.allowSignalWrites !== undefined) {\n    console.warn(`The 'allowSignalWrites' flag is deprecated and no longer impacts effect() (writes are always allowed)`);\n  }\n  const injector = options?.injector ?? inject(Injector);\n  let destroyRef = options?.manualCleanup !== true ? injector.get(DestroyRef) : null;\n  let node;\n  const viewContext = injector.get(ViewContext, null, {\n    optional: true\n  });\n  const notifier = injector.get(ChangeDetectionScheduler);\n  if (viewContext !== null) {\n    // This effect was created in the context of a view, and will be associated with the view.\n    node = createViewEffect(viewContext.view, notifier, effectFn);\n    if (destroyRef instanceof NodeInjectorDestroyRef && destroyRef._lView === viewContext.view) {\n      // The effect is being created in the same view as the `DestroyRef` references, so it will be\n      // automatically destroyed without the need for an explicit `DestroyRef` registration.\n      destroyRef = null;\n    }\n  } else {\n    // This effect was created outside the context of a view, and will be scheduled independently.\n    node = createRootEffect(effectFn, injector.get(EffectScheduler), notifier);\n  }\n  node.injector = injector;\n  if (destroyRef !== null) {\n    // If we need to register for cleanup, do that here.\n    node.onDestroyFn = destroyRef.onDestroy(() => node.destroy());\n  }\n  const effectRef = new EffectRefImpl(node);\n  if (ngDevMode) {\n    node.debugName = options?.debugName ?? '';\n    const prevInjectorProfilerContext = setInjectorProfilerContext({\n      injector,\n      token: null\n    });\n    try {\n      emitEffectCreatedEvent(effectRef);\n    } finally {\n      setInjectorProfilerContext(prevInjectorProfilerContext);\n    }\n  }\n  return effectRef;\n}\n/**\n * Not public API, which guarantees `EffectScheduler` only ever comes from the application root\n * injector.\n */\n/* @__PURE__ */\nnew InjectionToken('', {\n  providedIn: 'root',\n  factory: () => inject(EffectScheduler)\n});\nconst BASE_EFFECT_NODE = /* @__PURE__ */(() => ({\n  ...REACTIVE_NODE,\n  consumerIsAlwaysLive: true,\n  consumerAllowSignalWrites: true,\n  dirty: true,\n  hasRun: false,\n  cleanupFns: undefined,\n  zone: null,\n  kind: 'effect',\n  onDestroyFn: noop,\n  run() {\n    this.dirty = false;\n    if (ngDevMode && isInNotificationPhase()) {\n      throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n    }\n    if (this.hasRun && !consumerPollProducersForChange(this)) {\n      return;\n    }\n    this.hasRun = true;\n    const registerCleanupFn = cleanupFn => (this.cleanupFns ??= []).push(cleanupFn);\n    const prevNode = consumerBeforeComputation(this);\n    // We clear `setIsRefreshingViews` so that `markForCheck()` within the body of an effect will\n    // cause CD to reach the component in question.\n    const prevRefreshingViews = setIsRefreshingViews(false);\n    try {\n      this.maybeCleanup();\n      this.fn(registerCleanupFn);\n    } finally {\n      setIsRefreshingViews(prevRefreshingViews);\n      consumerAfterComputation(this, prevNode);\n    }\n  },\n  maybeCleanup() {\n    if (!this.cleanupFns?.length) {\n      return;\n    }\n    const prevConsumer = setActiveConsumer(null);\n    try {\n      // Attempt to run the cleanup functions. Regardless of failure or success, we consider\n      // cleanup \"completed\" and clear the list for the next run of the effect. Note that an error\n      // from the cleanup function will still crash the current run of the effect.\n      while (this.cleanupFns.length) {\n        this.cleanupFns.pop()();\n      }\n    } finally {\n      this.cleanupFns = [];\n      setActiveConsumer(prevConsumer);\n    }\n  }\n}))();\nconst ROOT_EFFECT_NODE = /* @__PURE__ */(() => ({\n  ...BASE_EFFECT_NODE,\n  consumerMarkedDirty() {\n    this.scheduler.schedule(this);\n    this.notifier.notify(12 /* NotificationSource.RootEffect */);\n  },\n  destroy() {\n    consumerDestroy(this);\n    this.onDestroyFn();\n    this.maybeCleanup();\n    this.scheduler.remove(this);\n  }\n}))();\nconst VIEW_EFFECT_NODE = /* @__PURE__ */(() => ({\n  ...BASE_EFFECT_NODE,\n  consumerMarkedDirty() {\n    this.view[FLAGS] |= 8192 /* LViewFlags.HasChildViewsToRefresh */;\n    markAncestorsForTraversal(this.view);\n    this.notifier.notify(13 /* NotificationSource.ViewEffect */);\n  },\n  destroy() {\n    consumerDestroy(this);\n    this.onDestroyFn();\n    this.maybeCleanup();\n    this.view[EFFECTS]?.delete(this);\n  }\n}))();\nfunction createViewEffect(view, notifier, fn) {\n  const node = Object.create(VIEW_EFFECT_NODE);\n  node.view = view;\n  node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n  node.notifier = notifier;\n  node.fn = fn;\n  view[EFFECTS] ??= new Set();\n  view[EFFECTS].add(node);\n  node.consumerMarkedDirty(node);\n  return node;\n}\nfunction createRootEffect(fn, scheduler, notifier) {\n  const node = Object.create(ROOT_EFFECT_NODE);\n  node.fn = fn;\n  node.scheduler = scheduler;\n  node.notifier = notifier;\n  node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n  node.scheduler.add(node);\n  node.notifier.notify(12 /* NotificationSource.RootEffect */);\n  return node;\n}\n\n/**\n * Status of a `Resource`.\n *\n * @experimental\n */\nvar ResourceStatus;\n(function (ResourceStatus) {\n  /**\n   * The resource has no valid request and will not perform any loading.\n   *\n   * `value()` will be `undefined`.\n   */\n  ResourceStatus[ResourceStatus[\"Idle\"] = 0] = \"Idle\";\n  /**\n   * Loading failed with an error.\n   *\n   * `value()` will be `undefined`.\n   */\n  ResourceStatus[ResourceStatus[\"Error\"] = 1] = \"Error\";\n  /**\n   * The resource is currently loading a new value as a result of a change in its `request`.\n   *\n   * `value()` will be `undefined`.\n   */\n  ResourceStatus[ResourceStatus[\"Loading\"] = 2] = \"Loading\";\n  /**\n   * The resource is currently reloading a fresh value for the same request.\n   *\n   * `value()` will continue to return the previously fetched value during the reloading operation.\n   */\n  ResourceStatus[ResourceStatus[\"Reloading\"] = 3] = \"Reloading\";\n  /**\n   * Loading has completed and the resource has the value returned from the loader.\n   */\n  ResourceStatus[ResourceStatus[\"Resolved\"] = 4] = \"Resolved\";\n  /**\n   * The resource's value was set locally via `.set()` or `.update()`.\n   */\n  ResourceStatus[ResourceStatus[\"Local\"] = 5] = \"Local\";\n})(ResourceStatus || (ResourceStatus = {}));\nconst identityFn = v => v;\nfunction linkedSignal(optionsOrComputation, options) {\n  if (typeof optionsOrComputation === 'function') {\n    const getter = createLinkedSignal(optionsOrComputation, identityFn, options?.equal);\n    return upgradeLinkedSignalGetter(getter);\n  } else {\n    const getter = createLinkedSignal(optionsOrComputation.source, optionsOrComputation.computation, optionsOrComputation.equal);\n    return upgradeLinkedSignalGetter(getter);\n  }\n}\nfunction upgradeLinkedSignalGetter(getter) {\n  if (ngDevMode) {\n    getter.toString = () => `[LinkedSignal: ${getter()}]`;\n  }\n  const node = getter[SIGNAL];\n  const upgradedGetter = getter;\n  upgradedGetter.set = newValue => linkedSignalSetFn(node, newValue);\n  upgradedGetter.update = updateFn => linkedSignalUpdateFn(node, updateFn);\n  upgradedGetter.asReadonly = signalAsReadonlyFn.bind(getter);\n  return upgradedGetter;\n}\nfunction resource(options) {\n  options?.injector || assertInInjectionContext(resource);\n  const request = options.request ?? (() => null);\n  return new ResourceImpl(request, getLoader(options), options.defaultValue, options.equal ? wrapEqualityFn(options.equal) : undefined, options.injector ?? inject(Injector));\n}\n/**\n * Base class which implements `.value` as a `WritableSignal` by delegating `.set` and `.update`.\n */\nclass BaseWritableResource {\n  value;\n  constructor(value) {\n    this.value = value;\n    this.value.set = this.set.bind(this);\n    this.value.update = this.update.bind(this);\n    this.value.asReadonly = signalAsReadonlyFn;\n  }\n  update(updateFn) {\n    this.set(updateFn(untracked(this.value)));\n  }\n  isLoading = computed(() => this.status() === ResourceStatus.Loading || this.status() === ResourceStatus.Reloading);\n  hasValue() {\n    return this.value() !== undefined;\n  }\n  asReadonly() {\n    return this;\n  }\n}\n/**\n * Implementation for `resource()` which uses a `linkedSignal` to manage the resource's state.\n */\nclass ResourceImpl extends BaseWritableResource {\n  loaderFn;\n  defaultValue;\n  equal;\n  pendingTasks;\n  /**\n   * The current state of the resource. Status, value, and error are derived from this.\n   */\n  state;\n  /**\n   * Combines the current request with a reload counter which allows the resource to be reloaded on\n   * imperative command.\n   */\n  extRequest;\n  effectRef;\n  pendingController;\n  resolvePendingTask = undefined;\n  destroyed = false;\n  constructor(request, loaderFn, defaultValue, equal, injector) {\n    super(\n    // Feed a computed signal for the value to `BaseWritableResource`, which will upgrade it to a\n    // `WritableSignal` that delegates to `ResourceImpl.set`.\n    computed(() => {\n      const streamValue = this.state().stream?.();\n      return streamValue && isResolved(streamValue) ? streamValue.value : this.defaultValue;\n    }, {\n      equal\n    }));\n    this.loaderFn = loaderFn;\n    this.defaultValue = defaultValue;\n    this.equal = equal;\n    // Extend `request()` to include a writable reload signal.\n    this.extRequest = linkedSignal({\n      source: request,\n      computation: request => ({\n        request,\n        reload: 0\n      })\n    });\n    // The main resource state is managed in a `linkedSignal`, which allows the resource to change\n    // state instantaneously when the request signal changes.\n    this.state = linkedSignal({\n      // Whenever the request changes,\n      source: this.extRequest,\n      // Compute the state of the resource given a change in status.\n      computation: (extRequest, previous) => {\n        const status = extRequest.request === undefined ? ResourceStatus.Idle : ResourceStatus.Loading;\n        if (!previous) {\n          return {\n            extRequest,\n            status,\n            previousStatus: ResourceStatus.Idle,\n            stream: undefined\n          };\n        } else {\n          return {\n            extRequest,\n            status,\n            previousStatus: projectStatusOfState(previous.value),\n            // If the request hasn't changed, keep the previous stream.\n            stream: previous.value.extRequest.request === extRequest.request ? previous.value.stream : undefined\n          };\n        }\n      }\n    });\n    this.effectRef = effect(this.loadEffect.bind(this), {\n      injector,\n      manualCleanup: true\n    });\n    this.pendingTasks = injector.get(PendingTasks);\n    // Cancel any pending request when the resource itself is destroyed.\n    injector.get(DestroyRef).onDestroy(() => this.destroy());\n  }\n  status = computed(() => projectStatusOfState(this.state()));\n  error = computed(() => {\n    const stream = this.state().stream?.();\n    return stream && !isResolved(stream) ? stream.error : undefined;\n  });\n  /**\n   * Called either directly via `WritableResource.set` or via `.value.set()`.\n   */\n  set(value) {\n    if (this.destroyed) {\n      return;\n    }\n    const current = untracked(this.value);\n    const state = untracked(this.state);\n    if (state.status === ResourceStatus.Local && (this.equal ? this.equal(current, value) : current === value)) {\n      return;\n    }\n    // Enter Local state with the user-defined value.\n    this.state.set({\n      extRequest: state.extRequest,\n      status: ResourceStatus.Local,\n      previousStatus: ResourceStatus.Local,\n      stream: signal({\n        value\n      })\n    });\n    // We're departing from whatever state the resource was in previously, so cancel any in-progress\n    // loading operations.\n    this.abortInProgressLoad();\n  }\n  reload() {\n    // We don't want to restart in-progress loads.\n    const {\n      status\n    } = untracked(this.state);\n    if (status === ResourceStatus.Idle || status === ResourceStatus.Loading) {\n      return false;\n    }\n    // Increment the request reload to trigger the `state` linked signal to switch us to `Reload`\n    this.extRequest.update(({\n      request,\n      reload\n    }) => ({\n      request,\n      reload: reload + 1\n    }));\n    return true;\n  }\n  destroy() {\n    this.destroyed = true;\n    this.effectRef.destroy();\n    this.abortInProgressLoad();\n    // Destroyed resources enter Idle state.\n    this.state.set({\n      extRequest: {\n        request: undefined,\n        reload: 0\n      },\n      status: ResourceStatus.Idle,\n      previousStatus: ResourceStatus.Idle,\n      stream: undefined\n    });\n  }\n  loadEffect() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const extRequest = _this.extRequest();\n      // Capture the previous status before any state transitions. Note that this is `untracked` since\n      // we do not want the effect to depend on the state of the resource, only on the request.\n      const {\n        status: currentStatus,\n        previousStatus\n      } = untracked(_this.state);\n      if (extRequest.request === undefined) {\n        // Nothing to load (and we should already be in a non-loading state).\n        return;\n      } else if (currentStatus !== ResourceStatus.Loading) {\n        // We're not in a loading or reloading state, so this loading request is stale.\n        return;\n      }\n      // Cancel any previous loading attempts.\n      _this.abortInProgressLoad();\n      // Capturing _this_ load's pending task in a local variable is important here. We may attempt to\n      // resolve it twice:\n      //\n      //  1. when the loading function promise resolves/rejects\n      //  2. when cancelling the loading operation\n      //\n      // After the loading operation is cancelled, `this.resolvePendingTask` no longer represents this\n      // particular task, but this `await` may eventually resolve/reject. Thus, when we cancel in\n      // response to (1) below, we need to cancel the locally saved task.\n      let resolvePendingTask = _this.resolvePendingTask = _this.pendingTasks.add();\n      const {\n        signal: abortSignal\n      } = _this.pendingController = new AbortController();\n      try {\n        // The actual loading is run through `untracked` - only the request side of `resource` is\n        // reactive. This avoids any confusion with signals tracking or not tracking depending on\n        // which side of the `await` they are.\n        const stream = yield untracked(() => {\n          return _this.loaderFn({\n            request: extRequest.request,\n            abortSignal,\n            previous: {\n              status: previousStatus\n            }\n          });\n        });\n        // If this request has been aborted, or the current request no longer\n        // matches this load, then we should ignore this resolution.\n        if (abortSignal.aborted || untracked(_this.extRequest) !== extRequest) {\n          return;\n        }\n        _this.state.set({\n          extRequest,\n          status: ResourceStatus.Resolved,\n          previousStatus: ResourceStatus.Resolved,\n          stream\n        });\n      } catch (err) {\n        if (abortSignal.aborted || untracked(_this.extRequest) !== extRequest) {\n          return;\n        }\n        _this.state.set({\n          extRequest,\n          status: ResourceStatus.Resolved,\n          previousStatus: ResourceStatus.Error,\n          stream: signal({\n            error: err\n          })\n        });\n      } finally {\n        // Resolve the pending task now that the resource has a value.\n        resolvePendingTask?.();\n        resolvePendingTask = undefined;\n      }\n    })();\n  }\n  abortInProgressLoad() {\n    untracked(() => this.pendingController?.abort());\n    this.pendingController = undefined;\n    // Once the load is aborted, we no longer want to block stability on its resolution.\n    this.resolvePendingTask?.();\n    this.resolvePendingTask = undefined;\n  }\n}\n/**\n * Wraps an equality function to handle either value being `undefined`.\n */\nfunction wrapEqualityFn(equal) {\n  return (a, b) => a === undefined || b === undefined ? a === b : equal(a, b);\n}\nfunction getLoader(options) {\n  if (isStreamingResourceOptions(options)) {\n    return options.stream;\n  }\n  return /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(function* (params) {\n      try {\n        return signal({\n          value: yield options.loader(params)\n        });\n      } catch (err) {\n        return signal({\n          error: err\n        });\n      }\n    });\n    return function (_x) {\n      return _ref.apply(this, arguments);\n    };\n  }();\n}\nfunction isStreamingResourceOptions(options) {\n  return !!options.stream;\n}\n/**\n * Project from a state with `ResourceInternalStatus` to the user-facing `ResourceStatus`\n */\nfunction projectStatusOfState(state) {\n  switch (state.status) {\n    case ResourceStatus.Loading:\n      return state.extRequest.reload === 0 ? ResourceStatus.Loading : ResourceStatus.Reloading;\n    case ResourceStatus.Resolved:\n      return isResolved(untracked(state.stream)) ? ResourceStatus.Resolved : ResourceStatus.Error;\n    default:\n      return state.status;\n  }\n}\nfunction isResolved(state) {\n  return state.error === undefined;\n}\nexport { OutputEmitterRef, ResourceImpl, ResourceStatus, computed, effect, getOutputDestroyRef, linkedSignal, resource, untracked };", "map": {"version": 3, "names": ["inject", "RuntimeError", "formatRuntimeError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DestroyRef", "assertNotInReactiveContext", "assertInInjectionContext", "Injector", "ViewContext", "ChangeDetectionScheduler", "EffectScheduler", "setInjectorProfilerContext", "emitEffectCreatedEvent", "EFFECTS", "noop", "FLAGS", "markAncestorsForTraversal", "setIsRefreshingViews", "NodeInjectorDestroyRef", "InjectionToken", "signalAsReadonlyFn", "PendingTasks", "signal", "setActiveConsumer", "createComputed", "SIGNAL", "REACTIVE_NODE", "consumerDestroy", "isInNotificationPhase", "consumerPollProducersForChange", "consumerBeforeComputation", "consumerAfterComputation", "untracked", "untracked$1", "createLinkedSignal", "linkedSignalSetFn", "linkedSignalUpdateFn", "OutputEmitterRef", "destroyed", "listeners", "<PERSON><PERSON><PERSON><PERSON>", "optional", "destroyRef", "constructor", "onDestroy", "subscribe", "callback", "ngDevMode", "push", "unsubscribe", "idx", "indexOf", "undefined", "splice", "emit", "value", "console", "warn", "previousConsumer", "listenerFn", "err", "handleError", "getOutputDestroyRef", "ref", "nonReactiveReadsFn", "computed", "computation", "options", "getter", "equal", "toString", "debugName", "EffectRefImpl", "node", "destroy", "effect", "effectFn", "injector", "allowSignalWrites", "manualCleanup", "get", "viewContext", "notifier", "createViewEffect", "view", "_l<PERSON>iew", "createRootEffect", "onDestroyFn", "effectRef", "prevInjectorProfilerContext", "token", "providedIn", "factory", "BASE_EFFECT_NODE", "consumerIsAlwaysLive", "consumerAllowSignalWrites", "dirty", "<PERSON><PERSON>un", "cleanupFns", "zone", "kind", "run", "Error", "registerCleanupFn", "cleanupFn", "prevNode", "prevRefreshingViews", "maybeCleanup", "fn", "length", "prevConsumer", "pop", "ROOT_EFFECT_NODE", "consumerMarkedDirty", "scheduler", "schedule", "notify", "remove", "VIEW_EFFECT_NODE", "delete", "Object", "create", "Zone", "current", "Set", "add", "ResourceStatus", "identityFn", "v", "linkedSignal", "optionsOrComputation", "upgradeLinkedSignalGetter", "source", "upgradedGetter", "set", "newValue", "update", "updateFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bind", "resource", "request", "ResourceImpl", "<PERSON><PERSON><PERSON><PERSON>", "defaultValue", "wrapEqualityFn", "BaseWritableResource", "isLoading", "status", "Loading", "Reloading", "hasValue", "loaderFn", "pendingTasks", "state", "extRequest", "pendingController", "resolvePendingTask", "streamValue", "stream", "isResolved", "reload", "previous", "Idle", "previousStatus", "projectStatusOfState", "loadEffect", "error", "Local", "abortInProgressLoad", "_this", "_asyncToGenerator", "currentStatus", "abortSignal", "AbortController", "aborted", "Resolved", "abort", "a", "b", "isStreamingResourceOptions", "_ref", "params", "loader", "_x", "apply", "arguments"], "sources": ["D:/employee-survey-app/company-owner-frontend/node_modules/@angular/core/fesm2022/resource-DhKtse7l.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0-next.8\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { inject, RuntimeError, formatRuntimeError, Error<PERSON>andler, DestroyRef, assertNotInReactiveContext, assertInInjectionContext, Injector, ViewContext, ChangeDetectionScheduler, EffectScheduler, setInjectorProfilerContext, emitEffectCreatedEvent, EFFECTS, noop, FLAGS, markAncestorsForTraversal, setIsRefreshingViews, NodeInjectorDestroyRef, InjectionToken, signalAsReadonlyFn, PendingTasks, signal } from './root_effect_scheduler-BZMWiScf.mjs';\nimport { setActiveConsumer, createComputed, SIGNAL, REACTIVE_NODE, consumerDestroy, isInNotificationPhase, consumerPollProducersForChange, consumerBeforeComputation, consumerAfterComputation } from './signal-B6pMq7KS.mjs';\nimport { untracked as untracked$1, createLinkedSignal, linkedSignalSetFn, linkedSignalUpdateFn } from './untracked-Bz5WMeU1.mjs';\n\n/**\n * An `OutputEmitterRef` is created by the `output()` function and can be\n * used to emit values to consumers of your directive or component.\n *\n * Consumers of your directive/component can bind to the output and\n * subscribe to changes via the bound event syntax. For example:\n *\n * ```html\n * <my-comp (valueChange)=\"processNewValue($event)\" />\n * ```\n *\n * @publicAPI\n */\nclass OutputEmitterRef {\n    destroyed = false;\n    listeners = null;\n    errorHandler = inject(ErrorHandler, { optional: true });\n    /** @internal */\n    destroyRef = inject(DestroyRef);\n    constructor() {\n        // Clean-up all listeners and mark as destroyed upon destroy.\n        this.destroyRef.onDestroy(() => {\n            this.destroyed = true;\n            this.listeners = null;\n        });\n    }\n    subscribe(callback) {\n        if (this.destroyed) {\n            throw new RuntimeError(953 /* RuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected subscription to destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.');\n        }\n        (this.listeners ??= []).push(callback);\n        return {\n            unsubscribe: () => {\n                const idx = this.listeners?.indexOf(callback);\n                if (idx !== undefined && idx !== -1) {\n                    this.listeners?.splice(idx, 1);\n                }\n            },\n        };\n    }\n    /** Emits a new value to the output. */\n    emit(value) {\n        if (this.destroyed) {\n            console.warn(formatRuntimeError(953 /* RuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected emit for destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.'));\n            return;\n        }\n        if (this.listeners === null) {\n            return;\n        }\n        const previousConsumer = setActiveConsumer(null);\n        try {\n            for (const listenerFn of this.listeners) {\n                try {\n                    listenerFn(value);\n                }\n                catch (err) {\n                    this.errorHandler?.handleError(err);\n                }\n            }\n        }\n        finally {\n            setActiveConsumer(previousConsumer);\n        }\n    }\n}\n/** Gets the owning `DestroyRef` for the given output. */\nfunction getOutputDestroyRef(ref) {\n    return ref.destroyRef;\n}\n\n/**\n * Execute an arbitrary function in a non-reactive (non-tracking) context. The executed function\n * can, optionally, return a value.\n */\nfunction untracked(nonReactiveReadsFn) {\n    return untracked$1(nonReactiveReadsFn);\n}\n\n/**\n * Create a computed `Signal` which derives a reactive value from an expression.\n */\nfunction computed(computation, options) {\n    const getter = createComputed(computation, options?.equal);\n    if (ngDevMode) {\n        getter.toString = () => `[Computed: ${getter()}]`;\n        getter[SIGNAL].debugName = options?.debugName;\n    }\n    return getter;\n}\n\nclass EffectRefImpl {\n    [SIGNAL];\n    constructor(node) {\n        this[SIGNAL] = node;\n    }\n    destroy() {\n        this[SIGNAL].destroy();\n    }\n}\n/**\n * Registers an \"effect\" that will be scheduled & executed whenever the signals that it reads\n * changes.\n *\n * Angular has two different kinds of effect: component effects and root effects. Component effects\n * are created when `effect()` is called from a component, directive, or within a service of a\n * component/directive. Root effects are created when `effect()` is called from outside the\n * component tree, such as in a root service.\n *\n * The two effect types differ in their timing. Component effects run as a component lifecycle\n * event during Angular's synchronization (change detection) process, and can safely read input\n * signals or create/destroy views that depend on component state. Root effects run as microtasks\n * and have no connection to the component tree or change detection.\n *\n * `effect()` must be run in injection context, unless the `injector` option is manually specified.\n */\nfunction effect(effectFn, options) {\n    ngDevMode &&\n        assertNotInReactiveContext(effect, 'Call `effect` outside of a reactive context. For example, schedule the ' +\n            'effect inside the component constructor.');\n    !options?.injector && assertInInjectionContext(effect);\n    if (ngDevMode && options?.allowSignalWrites !== undefined) {\n        console.warn(`The 'allowSignalWrites' flag is deprecated and no longer impacts effect() (writes are always allowed)`);\n    }\n    const injector = options?.injector ?? inject(Injector);\n    let destroyRef = options?.manualCleanup !== true ? injector.get(DestroyRef) : null;\n    let node;\n    const viewContext = injector.get(ViewContext, null, { optional: true });\n    const notifier = injector.get(ChangeDetectionScheduler);\n    if (viewContext !== null) {\n        // This effect was created in the context of a view, and will be associated with the view.\n        node = createViewEffect(viewContext.view, notifier, effectFn);\n        if (destroyRef instanceof NodeInjectorDestroyRef && destroyRef._lView === viewContext.view) {\n            // The effect is being created in the same view as the `DestroyRef` references, so it will be\n            // automatically destroyed without the need for an explicit `DestroyRef` registration.\n            destroyRef = null;\n        }\n    }\n    else {\n        // This effect was created outside the context of a view, and will be scheduled independently.\n        node = createRootEffect(effectFn, injector.get(EffectScheduler), notifier);\n    }\n    node.injector = injector;\n    if (destroyRef !== null) {\n        // If we need to register for cleanup, do that here.\n        node.onDestroyFn = destroyRef.onDestroy(() => node.destroy());\n    }\n    const effectRef = new EffectRefImpl(node);\n    if (ngDevMode) {\n        node.debugName = options?.debugName ?? '';\n        const prevInjectorProfilerContext = setInjectorProfilerContext({ injector, token: null });\n        try {\n            emitEffectCreatedEvent(effectRef);\n        }\n        finally {\n            setInjectorProfilerContext(prevInjectorProfilerContext);\n        }\n    }\n    return effectRef;\n}\n/**\n * Not public API, which guarantees `EffectScheduler` only ever comes from the application root\n * injector.\n */\n/* @__PURE__ */ new InjectionToken('', {\n    providedIn: 'root',\n    factory: () => inject(EffectScheduler),\n});\nconst BASE_EFFECT_NODE = \n/* @__PURE__ */ (() => ({\n    ...REACTIVE_NODE,\n    consumerIsAlwaysLive: true,\n    consumerAllowSignalWrites: true,\n    dirty: true,\n    hasRun: false,\n    cleanupFns: undefined,\n    zone: null,\n    kind: 'effect',\n    onDestroyFn: noop,\n    run() {\n        this.dirty = false;\n        if (ngDevMode && isInNotificationPhase()) {\n            throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n        }\n        if (this.hasRun && !consumerPollProducersForChange(this)) {\n            return;\n        }\n        this.hasRun = true;\n        const registerCleanupFn = (cleanupFn) => (this.cleanupFns ??= []).push(cleanupFn);\n        const prevNode = consumerBeforeComputation(this);\n        // We clear `setIsRefreshingViews` so that `markForCheck()` within the body of an effect will\n        // cause CD to reach the component in question.\n        const prevRefreshingViews = setIsRefreshingViews(false);\n        try {\n            this.maybeCleanup();\n            this.fn(registerCleanupFn);\n        }\n        finally {\n            setIsRefreshingViews(prevRefreshingViews);\n            consumerAfterComputation(this, prevNode);\n        }\n    },\n    maybeCleanup() {\n        if (!this.cleanupFns?.length) {\n            return;\n        }\n        const prevConsumer = setActiveConsumer(null);\n        try {\n            // Attempt to run the cleanup functions. Regardless of failure or success, we consider\n            // cleanup \"completed\" and clear the list for the next run of the effect. Note that an error\n            // from the cleanup function will still crash the current run of the effect.\n            while (this.cleanupFns.length) {\n                this.cleanupFns.pop()();\n            }\n        }\n        finally {\n            this.cleanupFns = [];\n            setActiveConsumer(prevConsumer);\n        }\n    },\n}))();\nconst ROOT_EFFECT_NODE = \n/* @__PURE__ */ (() => ({\n    ...BASE_EFFECT_NODE,\n    consumerMarkedDirty() {\n        this.scheduler.schedule(this);\n        this.notifier.notify(12 /* NotificationSource.RootEffect */);\n    },\n    destroy() {\n        consumerDestroy(this);\n        this.onDestroyFn();\n        this.maybeCleanup();\n        this.scheduler.remove(this);\n    },\n}))();\nconst VIEW_EFFECT_NODE = \n/* @__PURE__ */ (() => ({\n    ...BASE_EFFECT_NODE,\n    consumerMarkedDirty() {\n        this.view[FLAGS] |= 8192 /* LViewFlags.HasChildViewsToRefresh */;\n        markAncestorsForTraversal(this.view);\n        this.notifier.notify(13 /* NotificationSource.ViewEffect */);\n    },\n    destroy() {\n        consumerDestroy(this);\n        this.onDestroyFn();\n        this.maybeCleanup();\n        this.view[EFFECTS]?.delete(this);\n    },\n}))();\nfunction createViewEffect(view, notifier, fn) {\n    const node = Object.create(VIEW_EFFECT_NODE);\n    node.view = view;\n    node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n    node.notifier = notifier;\n    node.fn = fn;\n    view[EFFECTS] ??= new Set();\n    view[EFFECTS].add(node);\n    node.consumerMarkedDirty(node);\n    return node;\n}\nfunction createRootEffect(fn, scheduler, notifier) {\n    const node = Object.create(ROOT_EFFECT_NODE);\n    node.fn = fn;\n    node.scheduler = scheduler;\n    node.notifier = notifier;\n    node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n    node.scheduler.add(node);\n    node.notifier.notify(12 /* NotificationSource.RootEffect */);\n    return node;\n}\n\n/**\n * Status of a `Resource`.\n *\n * @experimental\n */\nvar ResourceStatus;\n(function (ResourceStatus) {\n    /**\n     * The resource has no valid request and will not perform any loading.\n     *\n     * `value()` will be `undefined`.\n     */\n    ResourceStatus[ResourceStatus[\"Idle\"] = 0] = \"Idle\";\n    /**\n     * Loading failed with an error.\n     *\n     * `value()` will be `undefined`.\n     */\n    ResourceStatus[ResourceStatus[\"Error\"] = 1] = \"Error\";\n    /**\n     * The resource is currently loading a new value as a result of a change in its `request`.\n     *\n     * `value()` will be `undefined`.\n     */\n    ResourceStatus[ResourceStatus[\"Loading\"] = 2] = \"Loading\";\n    /**\n     * The resource is currently reloading a fresh value for the same request.\n     *\n     * `value()` will continue to return the previously fetched value during the reloading operation.\n     */\n    ResourceStatus[ResourceStatus[\"Reloading\"] = 3] = \"Reloading\";\n    /**\n     * Loading has completed and the resource has the value returned from the loader.\n     */\n    ResourceStatus[ResourceStatus[\"Resolved\"] = 4] = \"Resolved\";\n    /**\n     * The resource's value was set locally via `.set()` or `.update()`.\n     */\n    ResourceStatus[ResourceStatus[\"Local\"] = 5] = \"Local\";\n})(ResourceStatus || (ResourceStatus = {}));\n\nconst identityFn = (v) => v;\nfunction linkedSignal(optionsOrComputation, options) {\n    if (typeof optionsOrComputation === 'function') {\n        const getter = createLinkedSignal(optionsOrComputation, (identityFn), options?.equal);\n        return upgradeLinkedSignalGetter(getter);\n    }\n    else {\n        const getter = createLinkedSignal(optionsOrComputation.source, optionsOrComputation.computation, optionsOrComputation.equal);\n        return upgradeLinkedSignalGetter(getter);\n    }\n}\nfunction upgradeLinkedSignalGetter(getter) {\n    if (ngDevMode) {\n        getter.toString = () => `[LinkedSignal: ${getter()}]`;\n    }\n    const node = getter[SIGNAL];\n    const upgradedGetter = getter;\n    upgradedGetter.set = (newValue) => linkedSignalSetFn(node, newValue);\n    upgradedGetter.update = (updateFn) => linkedSignalUpdateFn(node, updateFn);\n    upgradedGetter.asReadonly = signalAsReadonlyFn.bind(getter);\n    return upgradedGetter;\n}\n\nfunction resource(options) {\n    options?.injector || assertInInjectionContext(resource);\n    const request = (options.request ?? (() => null));\n    return new ResourceImpl(request, getLoader(options), options.defaultValue, options.equal ? wrapEqualityFn(options.equal) : undefined, options.injector ?? inject(Injector));\n}\n/**\n * Base class which implements `.value` as a `WritableSignal` by delegating `.set` and `.update`.\n */\nclass BaseWritableResource {\n    value;\n    constructor(value) {\n        this.value = value;\n        this.value.set = this.set.bind(this);\n        this.value.update = this.update.bind(this);\n        this.value.asReadonly = signalAsReadonlyFn;\n    }\n    update(updateFn) {\n        this.set(updateFn(untracked(this.value)));\n    }\n    isLoading = computed(() => this.status() === ResourceStatus.Loading || this.status() === ResourceStatus.Reloading);\n    hasValue() {\n        return this.value() !== undefined;\n    }\n    asReadonly() {\n        return this;\n    }\n}\n/**\n * Implementation for `resource()` which uses a `linkedSignal` to manage the resource's state.\n */\nclass ResourceImpl extends BaseWritableResource {\n    loaderFn;\n    defaultValue;\n    equal;\n    pendingTasks;\n    /**\n     * The current state of the resource. Status, value, and error are derived from this.\n     */\n    state;\n    /**\n     * Combines the current request with a reload counter which allows the resource to be reloaded on\n     * imperative command.\n     */\n    extRequest;\n    effectRef;\n    pendingController;\n    resolvePendingTask = undefined;\n    destroyed = false;\n    constructor(request, loaderFn, defaultValue, equal, injector) {\n        super(\n        // Feed a computed signal for the value to `BaseWritableResource`, which will upgrade it to a\n        // `WritableSignal` that delegates to `ResourceImpl.set`.\n        computed(() => {\n            const streamValue = this.state().stream?.();\n            return streamValue && isResolved(streamValue) ? streamValue.value : this.defaultValue;\n        }, { equal }));\n        this.loaderFn = loaderFn;\n        this.defaultValue = defaultValue;\n        this.equal = equal;\n        // Extend `request()` to include a writable reload signal.\n        this.extRequest = linkedSignal({\n            source: request,\n            computation: (request) => ({ request, reload: 0 }),\n        });\n        // The main resource state is managed in a `linkedSignal`, which allows the resource to change\n        // state instantaneously when the request signal changes.\n        this.state = linkedSignal({\n            // Whenever the request changes,\n            source: this.extRequest,\n            // Compute the state of the resource given a change in status.\n            computation: (extRequest, previous) => {\n                const status = extRequest.request === undefined ? ResourceStatus.Idle : ResourceStatus.Loading;\n                if (!previous) {\n                    return {\n                        extRequest,\n                        status,\n                        previousStatus: ResourceStatus.Idle,\n                        stream: undefined,\n                    };\n                }\n                else {\n                    return {\n                        extRequest,\n                        status,\n                        previousStatus: projectStatusOfState(previous.value),\n                        // If the request hasn't changed, keep the previous stream.\n                        stream: previous.value.extRequest.request === extRequest.request\n                            ? previous.value.stream\n                            : undefined,\n                    };\n                }\n            },\n        });\n        this.effectRef = effect(this.loadEffect.bind(this), {\n            injector,\n            manualCleanup: true,\n        });\n        this.pendingTasks = injector.get(PendingTasks);\n        // Cancel any pending request when the resource itself is destroyed.\n        injector.get(DestroyRef).onDestroy(() => this.destroy());\n    }\n    status = computed(() => projectStatusOfState(this.state()));\n    error = computed(() => {\n        const stream = this.state().stream?.();\n        return stream && !isResolved(stream) ? stream.error : undefined;\n    });\n    /**\n     * Called either directly via `WritableResource.set` or via `.value.set()`.\n     */\n    set(value) {\n        if (this.destroyed) {\n            return;\n        }\n        const current = untracked(this.value);\n        const state = untracked(this.state);\n        if (state.status === ResourceStatus.Local &&\n            (this.equal ? this.equal(current, value) : current === value)) {\n            return;\n        }\n        // Enter Local state with the user-defined value.\n        this.state.set({\n            extRequest: state.extRequest,\n            status: ResourceStatus.Local,\n            previousStatus: ResourceStatus.Local,\n            stream: signal({ value }),\n        });\n        // We're departing from whatever state the resource was in previously, so cancel any in-progress\n        // loading operations.\n        this.abortInProgressLoad();\n    }\n    reload() {\n        // We don't want to restart in-progress loads.\n        const { status } = untracked(this.state);\n        if (status === ResourceStatus.Idle || status === ResourceStatus.Loading) {\n            return false;\n        }\n        // Increment the request reload to trigger the `state` linked signal to switch us to `Reload`\n        this.extRequest.update(({ request, reload }) => ({ request, reload: reload + 1 }));\n        return true;\n    }\n    destroy() {\n        this.destroyed = true;\n        this.effectRef.destroy();\n        this.abortInProgressLoad();\n        // Destroyed resources enter Idle state.\n        this.state.set({\n            extRequest: { request: undefined, reload: 0 },\n            status: ResourceStatus.Idle,\n            previousStatus: ResourceStatus.Idle,\n            stream: undefined,\n        });\n    }\n    async loadEffect() {\n        const extRequest = this.extRequest();\n        // Capture the previous status before any state transitions. Note that this is `untracked` since\n        // we do not want the effect to depend on the state of the resource, only on the request.\n        const { status: currentStatus, previousStatus } = untracked(this.state);\n        if (extRequest.request === undefined) {\n            // Nothing to load (and we should already be in a non-loading state).\n            return;\n        }\n        else if (currentStatus !== ResourceStatus.Loading) {\n            // We're not in a loading or reloading state, so this loading request is stale.\n            return;\n        }\n        // Cancel any previous loading attempts.\n        this.abortInProgressLoad();\n        // Capturing _this_ load's pending task in a local variable is important here. We may attempt to\n        // resolve it twice:\n        //\n        //  1. when the loading function promise resolves/rejects\n        //  2. when cancelling the loading operation\n        //\n        // After the loading operation is cancelled, `this.resolvePendingTask` no longer represents this\n        // particular task, but this `await` may eventually resolve/reject. Thus, when we cancel in\n        // response to (1) below, we need to cancel the locally saved task.\n        let resolvePendingTask = (this.resolvePendingTask =\n            this.pendingTasks.add());\n        const { signal: abortSignal } = (this.pendingController = new AbortController());\n        try {\n            // The actual loading is run through `untracked` - only the request side of `resource` is\n            // reactive. This avoids any confusion with signals tracking or not tracking depending on\n            // which side of the `await` they are.\n            const stream = await untracked(() => {\n                return this.loaderFn({\n                    request: extRequest.request,\n                    abortSignal,\n                    previous: {\n                        status: previousStatus,\n                    },\n                });\n            });\n            // If this request has been aborted, or the current request no longer\n            // matches this load, then we should ignore this resolution.\n            if (abortSignal.aborted || untracked(this.extRequest) !== extRequest) {\n                return;\n            }\n            this.state.set({\n                extRequest,\n                status: ResourceStatus.Resolved,\n                previousStatus: ResourceStatus.Resolved,\n                stream,\n            });\n        }\n        catch (err) {\n            if (abortSignal.aborted || untracked(this.extRequest) !== extRequest) {\n                return;\n            }\n            this.state.set({\n                extRequest,\n                status: ResourceStatus.Resolved,\n                previousStatus: ResourceStatus.Error,\n                stream: signal({ error: err }),\n            });\n        }\n        finally {\n            // Resolve the pending task now that the resource has a value.\n            resolvePendingTask?.();\n            resolvePendingTask = undefined;\n        }\n    }\n    abortInProgressLoad() {\n        untracked(() => this.pendingController?.abort());\n        this.pendingController = undefined;\n        // Once the load is aborted, we no longer want to block stability on its resolution.\n        this.resolvePendingTask?.();\n        this.resolvePendingTask = undefined;\n    }\n}\n/**\n * Wraps an equality function to handle either value being `undefined`.\n */\nfunction wrapEqualityFn(equal) {\n    return (a, b) => (a === undefined || b === undefined ? a === b : equal(a, b));\n}\nfunction getLoader(options) {\n    if (isStreamingResourceOptions(options)) {\n        return options.stream;\n    }\n    return async (params) => {\n        try {\n            return signal({ value: await options.loader(params) });\n        }\n        catch (err) {\n            return signal({ error: err });\n        }\n    };\n}\nfunction isStreamingResourceOptions(options) {\n    return !!options.stream;\n}\n/**\n * Project from a state with `ResourceInternalStatus` to the user-facing `ResourceStatus`\n */\nfunction projectStatusOfState(state) {\n    switch (state.status) {\n        case ResourceStatus.Loading:\n            return state.extRequest.reload === 0 ? ResourceStatus.Loading : ResourceStatus.Reloading;\n        case ResourceStatus.Resolved:\n            return isResolved(untracked(state.stream)) ? ResourceStatus.Resolved : ResourceStatus.Error;\n        default:\n            return state.status;\n    }\n}\nfunction isResolved(state) {\n    return state.error === undefined;\n}\n\nexport { OutputEmitterRef, ResourceImpl, ResourceStatus, computed, effect, getOutputDestroyRef, linkedSignal, resource, untracked };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAM,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,UAAU,EAAEC,0BAA0B,EAAEC,wBAAwB,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,wBAAwB,EAAEC,eAAe,EAAEC,0BAA0B,EAAEC,sBAAsB,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,yBAAyB,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,MAAM,QAAQ,sCAAsC;AAC9b,SAASC,iBAAiB,EAAEC,cAAc,EAAEC,MAAM,EAAEC,aAAa,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,8BAA8B,EAAEC,yBAAyB,EAAEC,wBAAwB,QAAQ,uBAAuB;AAC7N,SAASC,SAAS,IAAIC,WAAW,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,oBAAoB,QAAQ,0BAA0B;;AAEhI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG,IAAI;EAChBC,YAAY,GAAGxC,MAAM,CAACG,YAAY,EAAE;IAAEsC,QAAQ,EAAE;EAAK,CAAC,CAAC;EACvD;EACAC,UAAU,GAAG1C,MAAM,CAACI,UAAU,CAAC;EAC/BuC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACD,UAAU,CAACE,SAAS,CAAC,MAAM;MAC5B,IAAI,CAACN,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,SAAS,GAAG,IAAI;IACzB,CAAC,CAAC;EACN;EACAM,SAASA,CAACC,QAAQ,EAAE;IAChB,IAAI,IAAI,CAACR,SAAS,EAAE;MAChB,MAAM,IAAIrC,YAAY,CAAC,GAAG,CAAC,6CAA6C8C,SAAS,IAC7E,oDAAoD,GAChD,8CAA8C,CAAC;IAC3D;IACA,CAAC,IAAI,CAACR,SAAS,KAAK,EAAE,EAAES,IAAI,CAACF,QAAQ,CAAC;IACtC,OAAO;MACHG,WAAW,EAAEA,CAAA,KAAM;QACf,MAAMC,GAAG,GAAG,IAAI,CAACX,SAAS,EAAEY,OAAO,CAACL,QAAQ,CAAC;QAC7C,IAAII,GAAG,KAAKE,SAAS,IAAIF,GAAG,KAAK,CAAC,CAAC,EAAE;UACjC,IAAI,CAACX,SAAS,EAAEc,MAAM,CAACH,GAAG,EAAE,CAAC,CAAC;QAClC;MACJ;IACJ,CAAC;EACL;EACA;EACAI,IAAIA,CAACC,KAAK,EAAE;IACR,IAAI,IAAI,CAACjB,SAAS,EAAE;MAChBkB,OAAO,CAACC,IAAI,CAACvD,kBAAkB,CAAC,GAAG,CAAC,6CAA6C6C,SAAS,IACtF,6CAA6C,GACzC,8CAA8C,CAAC,CAAC;MACxD;IACJ;IACA,IAAI,IAAI,CAACR,SAAS,KAAK,IAAI,EAAE;MACzB;IACJ;IACA,MAAMmB,gBAAgB,GAAGnC,iBAAiB,CAAC,IAAI,CAAC;IAChD,IAAI;MACA,KAAK,MAAMoC,UAAU,IAAI,IAAI,CAACpB,SAAS,EAAE;QACrC,IAAI;UACAoB,UAAU,CAACJ,KAAK,CAAC;QACrB,CAAC,CACD,OAAOK,GAAG,EAAE;UACR,IAAI,CAACpB,YAAY,EAAEqB,WAAW,CAACD,GAAG,CAAC;QACvC;MACJ;IACJ,CAAC,SACO;MACJrC,iBAAiB,CAACmC,gBAAgB,CAAC;IACvC;EACJ;AACJ;AACA;AACA,SAASI,mBAAmBA,CAACC,GAAG,EAAE;EAC9B,OAAOA,GAAG,CAACrB,UAAU;AACzB;;AAEA;AACA;AACA;AACA;AACA,SAASV,SAASA,CAACgC,kBAAkB,EAAE;EACnC,OAAO/B,WAAW,CAAC+B,kBAAkB,CAAC;AAC1C;;AAEA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,WAAW,EAAEC,OAAO,EAAE;EACpC,MAAMC,MAAM,GAAG5C,cAAc,CAAC0C,WAAW,EAAEC,OAAO,EAAEE,KAAK,CAAC;EAC1D,IAAItB,SAAS,EAAE;IACXqB,MAAM,CAACE,QAAQ,GAAG,MAAM,cAAcF,MAAM,CAAC,CAAC,GAAG;IACjDA,MAAM,CAAC3C,MAAM,CAAC,CAAC8C,SAAS,GAAGJ,OAAO,EAAEI,SAAS;EACjD;EACA,OAAOH,MAAM;AACjB;AAEA,MAAMI,aAAa,CAAC;EAChB,CAAC/C,MAAM;EACPkB,WAAWA,CAAC8B,IAAI,EAAE;IACd,IAAI,CAAChD,MAAM,CAAC,GAAGgD,IAAI;EACvB;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACjD,MAAM,CAAC,CAACiD,OAAO,CAAC,CAAC;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,QAAQ,EAAET,OAAO,EAAE;EAC/BpB,SAAS,IACL1C,0BAA0B,CAACsE,MAAM,EAAE,yEAAyE,GACxG,0CAA0C,CAAC;EACnD,CAACR,OAAO,EAAEU,QAAQ,IAAIvE,wBAAwB,CAACqE,MAAM,CAAC;EACtD,IAAI5B,SAAS,IAAIoB,OAAO,EAAEW,iBAAiB,KAAK1B,SAAS,EAAE;IACvDI,OAAO,CAACC,IAAI,CAAC,uGAAuG,CAAC;EACzH;EACA,MAAMoB,QAAQ,GAAGV,OAAO,EAAEU,QAAQ,IAAI7E,MAAM,CAACO,QAAQ,CAAC;EACtD,IAAImC,UAAU,GAAGyB,OAAO,EAAEY,aAAa,KAAK,IAAI,GAAGF,QAAQ,CAACG,GAAG,CAAC5E,UAAU,CAAC,GAAG,IAAI;EAClF,IAAIqE,IAAI;EACR,MAAMQ,WAAW,GAAGJ,QAAQ,CAACG,GAAG,CAACxE,WAAW,EAAE,IAAI,EAAE;IAAEiC,QAAQ,EAAE;EAAK,CAAC,CAAC;EACvE,MAAMyC,QAAQ,GAAGL,QAAQ,CAACG,GAAG,CAACvE,wBAAwB,CAAC;EACvD,IAAIwE,WAAW,KAAK,IAAI,EAAE;IACtB;IACAR,IAAI,GAAGU,gBAAgB,CAACF,WAAW,CAACG,IAAI,EAAEF,QAAQ,EAAEN,QAAQ,CAAC;IAC7D,IAAIlC,UAAU,YAAYxB,sBAAsB,IAAIwB,UAAU,CAAC2C,MAAM,KAAKJ,WAAW,CAACG,IAAI,EAAE;MACxF;MACA;MACA1C,UAAU,GAAG,IAAI;IACrB;EACJ,CAAC,MACI;IACD;IACA+B,IAAI,GAAGa,gBAAgB,CAACV,QAAQ,EAAEC,QAAQ,CAACG,GAAG,CAACtE,eAAe,CAAC,EAAEwE,QAAQ,CAAC;EAC9E;EACAT,IAAI,CAACI,QAAQ,GAAGA,QAAQ;EACxB,IAAInC,UAAU,KAAK,IAAI,EAAE;IACrB;IACA+B,IAAI,CAACc,WAAW,GAAG7C,UAAU,CAACE,SAAS,CAAC,MAAM6B,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;EACjE;EACA,MAAMc,SAAS,GAAG,IAAIhB,aAAa,CAACC,IAAI,CAAC;EACzC,IAAI1B,SAAS,EAAE;IACX0B,IAAI,CAACF,SAAS,GAAGJ,OAAO,EAAEI,SAAS,IAAI,EAAE;IACzC,MAAMkB,2BAA2B,GAAG9E,0BAA0B,CAAC;MAAEkE,QAAQ;MAAEa,KAAK,EAAE;IAAK,CAAC,CAAC;IACzF,IAAI;MACA9E,sBAAsB,CAAC4E,SAAS,CAAC;IACrC,CAAC,SACO;MACJ7E,0BAA0B,CAAC8E,2BAA2B,CAAC;IAC3D;EACJ;EACA,OAAOD,SAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AAAgB,IAAIrE,cAAc,CAAC,EAAE,EAAE;EACnCwE,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM5F,MAAM,CAACU,eAAe;AACzC,CAAC,CAAC;AACF,MAAMmF,gBAAgB,GACtB,eAAgB,CAAC,OAAO;EACpB,GAAGnE,aAAa;EAChBoE,oBAAoB,EAAE,IAAI;EAC1BC,yBAAyB,EAAE,IAAI;EAC/BC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,KAAK;EACbC,UAAU,EAAE9C,SAAS;EACrB+C,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,QAAQ;EACdb,WAAW,EAAEzE,IAAI;EACjBuF,GAAGA,CAAA,EAAG;IACF,IAAI,CAACL,KAAK,GAAG,KAAK;IAClB,IAAIjD,SAAS,IAAInB,qBAAqB,CAAC,CAAC,EAAE;MACtC,MAAM,IAAI0E,KAAK,CAAC,mEAAmE,CAAC;IACxF;IACA,IAAI,IAAI,CAACL,MAAM,IAAI,CAACpE,8BAA8B,CAAC,IAAI,CAAC,EAAE;MACtD;IACJ;IACA,IAAI,CAACoE,MAAM,GAAG,IAAI;IAClB,MAAMM,iBAAiB,GAAIC,SAAS,IAAK,CAAC,IAAI,CAACN,UAAU,KAAK,EAAE,EAAElD,IAAI,CAACwD,SAAS,CAAC;IACjF,MAAMC,QAAQ,GAAG3E,yBAAyB,CAAC,IAAI,CAAC;IAChD;IACA;IACA,MAAM4E,mBAAmB,GAAGzF,oBAAoB,CAAC,KAAK,CAAC;IACvD,IAAI;MACA,IAAI,CAAC0F,YAAY,CAAC,CAAC;MACnB,IAAI,CAACC,EAAE,CAACL,iBAAiB,CAAC;IAC9B,CAAC,SACO;MACJtF,oBAAoB,CAACyF,mBAAmB,CAAC;MACzC3E,wBAAwB,CAAC,IAAI,EAAE0E,QAAQ,CAAC;IAC5C;EACJ,CAAC;EACDE,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACT,UAAU,EAAEW,MAAM,EAAE;MAC1B;IACJ;IACA,MAAMC,YAAY,GAAGvF,iBAAiB,CAAC,IAAI,CAAC;IAC5C,IAAI;MACA;MACA;MACA;MACA,OAAO,IAAI,CAAC2E,UAAU,CAACW,MAAM,EAAE;QAC3B,IAAI,CAACX,UAAU,CAACa,GAAG,CAAC,CAAC,CAAC,CAAC;MAC3B;IACJ,CAAC,SACO;MACJ,IAAI,CAACb,UAAU,GAAG,EAAE;MACpB3E,iBAAiB,CAACuF,YAAY,CAAC;IACnC;EACJ;AACJ,CAAC,CAAC,EAAE,CAAC;AACL,MAAME,gBAAgB,GACtB,eAAgB,CAAC,OAAO;EACpB,GAAGnB,gBAAgB;EACnBoB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,IAAI,CAAC;IAC7B,IAAI,CAACjC,QAAQ,CAACkC,MAAM,CAAC,EAAE,CAAC,mCAAmC,CAAC;EAChE,CAAC;EACD1C,OAAOA,CAAA,EAAG;IACN/C,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI,CAAC4D,WAAW,CAAC,CAAC;IAClB,IAAI,CAACoB,YAAY,CAAC,CAAC;IACnB,IAAI,CAACO,SAAS,CAACG,MAAM,CAAC,IAAI,CAAC;EAC/B;AACJ,CAAC,CAAC,EAAE,CAAC;AACL,MAAMC,gBAAgB,GACtB,eAAgB,CAAC,OAAO;EACpB,GAAGzB,gBAAgB;EACnBoB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC7B,IAAI,CAACrE,KAAK,CAAC,IAAI,IAAI,CAAC;IACzBC,yBAAyB,CAAC,IAAI,CAACoE,IAAI,CAAC;IACpC,IAAI,CAACF,QAAQ,CAACkC,MAAM,CAAC,EAAE,CAAC,mCAAmC,CAAC;EAChE,CAAC;EACD1C,OAAOA,CAAA,EAAG;IACN/C,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI,CAAC4D,WAAW,CAAC,CAAC;IAClB,IAAI,CAACoB,YAAY,CAAC,CAAC;IACnB,IAAI,CAACvB,IAAI,CAACvE,OAAO,CAAC,EAAE0G,MAAM,CAAC,IAAI,CAAC;EACpC;AACJ,CAAC,CAAC,EAAE,CAAC;AACL,SAASpC,gBAAgBA,CAACC,IAAI,EAAEF,QAAQ,EAAE0B,EAAE,EAAE;EAC1C,MAAMnC,IAAI,GAAG+C,MAAM,CAACC,MAAM,CAACH,gBAAgB,CAAC;EAC5C7C,IAAI,CAACW,IAAI,GAAGA,IAAI;EAChBX,IAAI,CAAC0B,IAAI,GAAG,OAAOuB,IAAI,KAAK,WAAW,GAAGA,IAAI,CAACC,OAAO,GAAG,IAAI;EAC7DlD,IAAI,CAACS,QAAQ,GAAGA,QAAQ;EACxBT,IAAI,CAACmC,EAAE,GAAGA,EAAE;EACZxB,IAAI,CAACvE,OAAO,CAAC,KAAK,IAAI+G,GAAG,CAAC,CAAC;EAC3BxC,IAAI,CAACvE,OAAO,CAAC,CAACgH,GAAG,CAACpD,IAAI,CAAC;EACvBA,IAAI,CAACwC,mBAAmB,CAACxC,IAAI,CAAC;EAC9B,OAAOA,IAAI;AACf;AACA,SAASa,gBAAgBA,CAACsB,EAAE,EAAEM,SAAS,EAAEhC,QAAQ,EAAE;EAC/C,MAAMT,IAAI,GAAG+C,MAAM,CAACC,MAAM,CAACT,gBAAgB,CAAC;EAC5CvC,IAAI,CAACmC,EAAE,GAAGA,EAAE;EACZnC,IAAI,CAACyC,SAAS,GAAGA,SAAS;EAC1BzC,IAAI,CAACS,QAAQ,GAAGA,QAAQ;EACxBT,IAAI,CAAC0B,IAAI,GAAG,OAAOuB,IAAI,KAAK,WAAW,GAAGA,IAAI,CAACC,OAAO,GAAG,IAAI;EAC7DlD,IAAI,CAACyC,SAAS,CAACW,GAAG,CAACpD,IAAI,CAAC;EACxBA,IAAI,CAACS,QAAQ,CAACkC,MAAM,CAAC,EAAE,CAAC,mCAAmC,CAAC;EAC5D,OAAO3C,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIqD,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvB;AACJ;AACA;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACnD;AACJ;AACA;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACrD;AACJ;AACA;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACzD;AACJ;AACA;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EAC7D;AACJ;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EAC3D;AACJ;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;AACzD,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAE3C,MAAMC,UAAU,GAAIC,CAAC,IAAKA,CAAC;AAC3B,SAASC,YAAYA,CAACC,oBAAoB,EAAE/D,OAAO,EAAE;EACjD,IAAI,OAAO+D,oBAAoB,KAAK,UAAU,EAAE;IAC5C,MAAM9D,MAAM,GAAGlC,kBAAkB,CAACgG,oBAAoB,EAAGH,UAAU,EAAG5D,OAAO,EAAEE,KAAK,CAAC;IACrF,OAAO8D,yBAAyB,CAAC/D,MAAM,CAAC;EAC5C,CAAC,MACI;IACD,MAAMA,MAAM,GAAGlC,kBAAkB,CAACgG,oBAAoB,CAACE,MAAM,EAAEF,oBAAoB,CAAChE,WAAW,EAAEgE,oBAAoB,CAAC7D,KAAK,CAAC;IAC5H,OAAO8D,yBAAyB,CAAC/D,MAAM,CAAC;EAC5C;AACJ;AACA,SAAS+D,yBAAyBA,CAAC/D,MAAM,EAAE;EACvC,IAAIrB,SAAS,EAAE;IACXqB,MAAM,CAACE,QAAQ,GAAG,MAAM,kBAAkBF,MAAM,CAAC,CAAC,GAAG;EACzD;EACA,MAAMK,IAAI,GAAGL,MAAM,CAAC3C,MAAM,CAAC;EAC3B,MAAM4G,cAAc,GAAGjE,MAAM;EAC7BiE,cAAc,CAACC,GAAG,GAAIC,QAAQ,IAAKpG,iBAAiB,CAACsC,IAAI,EAAE8D,QAAQ,CAAC;EACpEF,cAAc,CAACG,MAAM,GAAIC,QAAQ,IAAKrG,oBAAoB,CAACqC,IAAI,EAAEgE,QAAQ,CAAC;EAC1EJ,cAAc,CAACK,UAAU,GAAGtH,kBAAkB,CAACuH,IAAI,CAACvE,MAAM,CAAC;EAC3D,OAAOiE,cAAc;AACzB;AAEA,SAASO,QAAQA,CAACzE,OAAO,EAAE;EACvBA,OAAO,EAAEU,QAAQ,IAAIvE,wBAAwB,CAACsI,QAAQ,CAAC;EACvD,MAAMC,OAAO,GAAI1E,OAAO,CAAC0E,OAAO,KAAK,MAAM,IAAI,CAAE;EACjD,OAAO,IAAIC,YAAY,CAACD,OAAO,EAAEE,SAAS,CAAC5E,OAAO,CAAC,EAAEA,OAAO,CAAC6E,YAAY,EAAE7E,OAAO,CAACE,KAAK,GAAG4E,cAAc,CAAC9E,OAAO,CAACE,KAAK,CAAC,GAAGjB,SAAS,EAAEe,OAAO,CAACU,QAAQ,IAAI7E,MAAM,CAACO,QAAQ,CAAC,CAAC;AAC/K;AACA;AACA;AACA;AACA,MAAM2I,oBAAoB,CAAC;EACvB3F,KAAK;EACLZ,WAAWA,CAACY,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACA,KAAK,CAAC+E,GAAG,GAAG,IAAI,CAACA,GAAG,CAACK,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACpF,KAAK,CAACiF,MAAM,GAAG,IAAI,CAACA,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACpF,KAAK,CAACmF,UAAU,GAAGtH,kBAAkB;EAC9C;EACAoH,MAAMA,CAACC,QAAQ,EAAE;IACb,IAAI,CAACH,GAAG,CAACG,QAAQ,CAACzG,SAAS,CAAC,IAAI,CAACuB,KAAK,CAAC,CAAC,CAAC;EAC7C;EACA4F,SAAS,GAAGlF,QAAQ,CAAC,MAAM,IAAI,CAACmF,MAAM,CAAC,CAAC,KAAKtB,cAAc,CAACuB,OAAO,IAAI,IAAI,CAACD,MAAM,CAAC,CAAC,KAAKtB,cAAc,CAACwB,SAAS,CAAC;EAClHC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAChG,KAAK,CAAC,CAAC,KAAKH,SAAS;EACrC;EACAsF,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI;EACf;AACJ;AACA;AACA;AACA;AACA,MAAMI,YAAY,SAASI,oBAAoB,CAAC;EAC5CM,QAAQ;EACRR,YAAY;EACZ3E,KAAK;EACLoF,YAAY;EACZ;AACJ;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,UAAU;EACVnE,SAAS;EACToE,iBAAiB;EACjBC,kBAAkB,GAAGzG,SAAS;EAC9Bd,SAAS,GAAG,KAAK;EACjBK,WAAWA,CAACkG,OAAO,EAAEW,QAAQ,EAAER,YAAY,EAAE3E,KAAK,EAAEQ,QAAQ,EAAE;IAC1D,KAAK;IACL;IACA;IACAZ,QAAQ,CAAC,MAAM;MACX,MAAM6F,WAAW,GAAG,IAAI,CAACJ,KAAK,CAAC,CAAC,CAACK,MAAM,GAAG,CAAC;MAC3C,OAAOD,WAAW,IAAIE,UAAU,CAACF,WAAW,CAAC,GAAGA,WAAW,CAACvG,KAAK,GAAG,IAAI,CAACyF,YAAY;IACzF,CAAC,EAAE;MAAE3E;IAAM,CAAC,CAAC,CAAC;IACd,IAAI,CAACmF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACR,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC3E,KAAK,GAAGA,KAAK;IAClB;IACA,IAAI,CAACsF,UAAU,GAAG1B,YAAY,CAAC;MAC3BG,MAAM,EAAES,OAAO;MACf3E,WAAW,EAAG2E,OAAO,KAAM;QAAEA,OAAO;QAAEoB,MAAM,EAAE;MAAE,CAAC;IACrD,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAACP,KAAK,GAAGzB,YAAY,CAAC;MACtB;MACAG,MAAM,EAAE,IAAI,CAACuB,UAAU;MACvB;MACAzF,WAAW,EAAEA,CAACyF,UAAU,EAAEO,QAAQ,KAAK;QACnC,MAAMd,MAAM,GAAGO,UAAU,CAACd,OAAO,KAAKzF,SAAS,GAAG0E,cAAc,CAACqC,IAAI,GAAGrC,cAAc,CAACuB,OAAO;QAC9F,IAAI,CAACa,QAAQ,EAAE;UACX,OAAO;YACHP,UAAU;YACVP,MAAM;YACNgB,cAAc,EAAEtC,cAAc,CAACqC,IAAI;YACnCJ,MAAM,EAAE3G;UACZ,CAAC;QACL,CAAC,MACI;UACD,OAAO;YACHuG,UAAU;YACVP,MAAM;YACNgB,cAAc,EAAEC,oBAAoB,CAACH,QAAQ,CAAC3G,KAAK,CAAC;YACpD;YACAwG,MAAM,EAAEG,QAAQ,CAAC3G,KAAK,CAACoG,UAAU,CAACd,OAAO,KAAKc,UAAU,CAACd,OAAO,GAC1DqB,QAAQ,CAAC3G,KAAK,CAACwG,MAAM,GACrB3G;UACV,CAAC;QACL;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACoC,SAAS,GAAGb,MAAM,CAAC,IAAI,CAAC2F,UAAU,CAAC3B,IAAI,CAAC,IAAI,CAAC,EAAE;MAChD9D,QAAQ;MACRE,aAAa,EAAE;IACnB,CAAC,CAAC;IACF,IAAI,CAAC0E,YAAY,GAAG5E,QAAQ,CAACG,GAAG,CAAC3D,YAAY,CAAC;IAC9C;IACAwD,QAAQ,CAACG,GAAG,CAAC5E,UAAU,CAAC,CAACwC,SAAS,CAAC,MAAM,IAAI,CAAC8B,OAAO,CAAC,CAAC,CAAC;EAC5D;EACA0E,MAAM,GAAGnF,QAAQ,CAAC,MAAMoG,oBAAoB,CAAC,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC,CAAC;EAC3Da,KAAK,GAAGtG,QAAQ,CAAC,MAAM;IACnB,MAAM8F,MAAM,GAAG,IAAI,CAACL,KAAK,CAAC,CAAC,CAACK,MAAM,GAAG,CAAC;IACtC,OAAOA,MAAM,IAAI,CAACC,UAAU,CAACD,MAAM,CAAC,GAAGA,MAAM,CAACQ,KAAK,GAAGnH,SAAS;EACnE,CAAC,CAAC;EACF;AACJ;AACA;EACIkF,GAAGA,CAAC/E,KAAK,EAAE;IACP,IAAI,IAAI,CAACjB,SAAS,EAAE;MAChB;IACJ;IACA,MAAMqF,OAAO,GAAG3F,SAAS,CAAC,IAAI,CAACuB,KAAK,CAAC;IACrC,MAAMmG,KAAK,GAAG1H,SAAS,CAAC,IAAI,CAAC0H,KAAK,CAAC;IACnC,IAAIA,KAAK,CAACN,MAAM,KAAKtB,cAAc,CAAC0C,KAAK,KACpC,IAAI,CAACnG,KAAK,GAAG,IAAI,CAACA,KAAK,CAACsD,OAAO,EAAEpE,KAAK,CAAC,GAAGoE,OAAO,KAAKpE,KAAK,CAAC,EAAE;MAC/D;IACJ;IACA;IACA,IAAI,CAACmG,KAAK,CAACpB,GAAG,CAAC;MACXqB,UAAU,EAAED,KAAK,CAACC,UAAU;MAC5BP,MAAM,EAAEtB,cAAc,CAAC0C,KAAK;MAC5BJ,cAAc,EAAEtC,cAAc,CAAC0C,KAAK;MACpCT,MAAM,EAAEzI,MAAM,CAAC;QAAEiC;MAAM,CAAC;IAC5B,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAACkH,mBAAmB,CAAC,CAAC;EAC9B;EACAR,MAAMA,CAAA,EAAG;IACL;IACA,MAAM;MAAEb;IAAO,CAAC,GAAGpH,SAAS,CAAC,IAAI,CAAC0H,KAAK,CAAC;IACxC,IAAIN,MAAM,KAAKtB,cAAc,CAACqC,IAAI,IAAIf,MAAM,KAAKtB,cAAc,CAACuB,OAAO,EAAE;MACrE,OAAO,KAAK;IAChB;IACA;IACA,IAAI,CAACM,UAAU,CAACnB,MAAM,CAAC,CAAC;MAAEK,OAAO;MAAEoB;IAAO,CAAC,MAAM;MAAEpB,OAAO;MAAEoB,MAAM,EAAEA,MAAM,GAAG;IAAE,CAAC,CAAC,CAAC;IAClF,OAAO,IAAI;EACf;EACAvF,OAAOA,CAAA,EAAG;IACN,IAAI,CAACpC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACkD,SAAS,CAACd,OAAO,CAAC,CAAC;IACxB,IAAI,CAAC+F,mBAAmB,CAAC,CAAC;IAC1B;IACA,IAAI,CAACf,KAAK,CAACpB,GAAG,CAAC;MACXqB,UAAU,EAAE;QAAEd,OAAO,EAAEzF,SAAS;QAAE6G,MAAM,EAAE;MAAE,CAAC;MAC7Cb,MAAM,EAAEtB,cAAc,CAACqC,IAAI;MAC3BC,cAAc,EAAEtC,cAAc,CAACqC,IAAI;MACnCJ,MAAM,EAAE3G;IACZ,CAAC,CAAC;EACN;EACMkH,UAAUA,CAAA,EAAG;IAAA,IAAAI,KAAA;IAAA,OAAAC,iBAAA;MACf,MAAMhB,UAAU,GAAGe,KAAI,CAACf,UAAU,CAAC,CAAC;MACpC;MACA;MACA,MAAM;QAAEP,MAAM,EAAEwB,aAAa;QAAER;MAAe,CAAC,GAAGpI,SAAS,CAAC0I,KAAI,CAAChB,KAAK,CAAC;MACvE,IAAIC,UAAU,CAACd,OAAO,KAAKzF,SAAS,EAAE;QAClC;QACA;MACJ,CAAC,MACI,IAAIwH,aAAa,KAAK9C,cAAc,CAACuB,OAAO,EAAE;QAC/C;QACA;MACJ;MACA;MACAqB,KAAI,CAACD,mBAAmB,CAAC,CAAC;MAC1B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIZ,kBAAkB,GAAIa,KAAI,CAACb,kBAAkB,GAC7Ca,KAAI,CAACjB,YAAY,CAAC5B,GAAG,CAAC,CAAE;MAC5B,MAAM;QAAEvG,MAAM,EAAEuJ;MAAY,CAAC,GAAIH,KAAI,CAACd,iBAAiB,GAAG,IAAIkB,eAAe,CAAC,CAAE;MAChF,IAAI;QACA;QACA;QACA;QACA,MAAMf,MAAM,SAAS/H,SAAS,CAAC,MAAM;UACjC,OAAO0I,KAAI,CAAClB,QAAQ,CAAC;YACjBX,OAAO,EAAEc,UAAU,CAACd,OAAO;YAC3BgC,WAAW;YACXX,QAAQ,EAAE;cACNd,MAAM,EAAEgB;YACZ;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;QACF;QACA;QACA,IAAIS,WAAW,CAACE,OAAO,IAAI/I,SAAS,CAAC0I,KAAI,CAACf,UAAU,CAAC,KAAKA,UAAU,EAAE;UAClE;QACJ;QACAe,KAAI,CAAChB,KAAK,CAACpB,GAAG,CAAC;UACXqB,UAAU;UACVP,MAAM,EAAEtB,cAAc,CAACkD,QAAQ;UAC/BZ,cAAc,EAAEtC,cAAc,CAACkD,QAAQ;UACvCjB;QACJ,CAAC,CAAC;MACN,CAAC,CACD,OAAOnG,GAAG,EAAE;QACR,IAAIiH,WAAW,CAACE,OAAO,IAAI/I,SAAS,CAAC0I,KAAI,CAACf,UAAU,CAAC,KAAKA,UAAU,EAAE;UAClE;QACJ;QACAe,KAAI,CAAChB,KAAK,CAACpB,GAAG,CAAC;UACXqB,UAAU;UACVP,MAAM,EAAEtB,cAAc,CAACkD,QAAQ;UAC/BZ,cAAc,EAAEtC,cAAc,CAACxB,KAAK;UACpCyD,MAAM,EAAEzI,MAAM,CAAC;YAAEiJ,KAAK,EAAE3G;UAAI,CAAC;QACjC,CAAC,CAAC;MACN,CAAC,SACO;QACJ;QACAiG,kBAAkB,GAAG,CAAC;QACtBA,kBAAkB,GAAGzG,SAAS;MAClC;IAAC;EACL;EACAqH,mBAAmBA,CAAA,EAAG;IAClBzI,SAAS,CAAC,MAAM,IAAI,CAAC4H,iBAAiB,EAAEqB,KAAK,CAAC,CAAC,CAAC;IAChD,IAAI,CAACrB,iBAAiB,GAAGxG,SAAS;IAClC;IACA,IAAI,CAACyG,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACA,kBAAkB,GAAGzG,SAAS;EACvC;AACJ;AACA;AACA;AACA;AACA,SAAS6F,cAAcA,CAAC5E,KAAK,EAAE;EAC3B,OAAO,CAAC6G,CAAC,EAAEC,CAAC,KAAMD,CAAC,KAAK9H,SAAS,IAAI+H,CAAC,KAAK/H,SAAS,GAAG8H,CAAC,KAAKC,CAAC,GAAG9G,KAAK,CAAC6G,CAAC,EAAEC,CAAC,CAAE;AACjF;AACA,SAASpC,SAASA,CAAC5E,OAAO,EAAE;EACxB,IAAIiH,0BAA0B,CAACjH,OAAO,CAAC,EAAE;IACrC,OAAOA,OAAO,CAAC4F,MAAM;EACzB;EACA;IAAA,IAAAsB,IAAA,GAAAV,iBAAA,CAAO,WAAOW,MAAM,EAAK;MACrB,IAAI;QACA,OAAOhK,MAAM,CAAC;UAAEiC,KAAK,QAAQY,OAAO,CAACoH,MAAM,CAACD,MAAM;QAAE,CAAC,CAAC;MAC1D,CAAC,CACD,OAAO1H,GAAG,EAAE;QACR,OAAOtC,MAAM,CAAC;UAAEiJ,KAAK,EAAE3G;QAAI,CAAC,CAAC;MACjC;IACJ,CAAC;IAAA,iBAAA4H,EAAA;MAAA,OAAAH,IAAA,CAAAI,KAAA,OAAAC,SAAA;IAAA;EAAA;AACL;AACA,SAASN,0BAA0BA,CAACjH,OAAO,EAAE;EACzC,OAAO,CAAC,CAACA,OAAO,CAAC4F,MAAM;AAC3B;AACA;AACA;AACA;AACA,SAASM,oBAAoBA,CAACX,KAAK,EAAE;EACjC,QAAQA,KAAK,CAACN,MAAM;IAChB,KAAKtB,cAAc,CAACuB,OAAO;MACvB,OAAOK,KAAK,CAACC,UAAU,CAACM,MAAM,KAAK,CAAC,GAAGnC,cAAc,CAACuB,OAAO,GAAGvB,cAAc,CAACwB,SAAS;IAC5F,KAAKxB,cAAc,CAACkD,QAAQ;MACxB,OAAOhB,UAAU,CAAChI,SAAS,CAAC0H,KAAK,CAACK,MAAM,CAAC,CAAC,GAAGjC,cAAc,CAACkD,QAAQ,GAAGlD,cAAc,CAACxB,KAAK;IAC/F;MACI,OAAOoD,KAAK,CAACN,MAAM;EAC3B;AACJ;AACA,SAASY,UAAUA,CAACN,KAAK,EAAE;EACvB,OAAOA,KAAK,CAACa,KAAK,KAAKnH,SAAS;AACpC;AAEA,SAASf,gBAAgB,EAAEyG,YAAY,EAAEhB,cAAc,EAAE7D,QAAQ,EAAEU,MAAM,EAAEb,mBAAmB,EAAEmE,YAAY,EAAEW,QAAQ,EAAE5G,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}