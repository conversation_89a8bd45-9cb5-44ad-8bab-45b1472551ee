{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormElementsRoutingModule } from './form-elements-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class FormElementsModule {\n  static {\n    this.ɵfac = function FormElementsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormElementsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: FormElementsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormElementsRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(FormElementsModule, {\n    imports: [CommonModule, FormElementsRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormElementsRoutingModule", "FormElementsModule", "imports"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\form-elements\\form-elements.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { FormElementsRoutingModule } from './form-elements-routing.module';\r\n\r\n@NgModule({\r\n  declarations: [],\r\n  imports: [CommonModule, FormElementsRoutingModule]\r\n})\r\nexport class FormElementsModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,yBAAyB,QAAQ,gCAAgC;;AAM1E,OAAM,MAAOC,kBAAkB;;;uCAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAFnBF,YAAY,EAAEC,yBAAyB;IAAA;EAAA;;;2EAEtCC,kBAAkB;IAAAC,OAAA,GAFnBH,YAAY,EAAEC,yBAAyB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}