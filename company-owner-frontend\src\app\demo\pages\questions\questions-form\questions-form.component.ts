// src/app/demo/pages/questions/question-form/question-form.component.ts
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { QuestionService } from '../../../../core/services/question.service';
import { Question } from '../../../../core/models/question.model';

@Component({
  selector: 'app-question-form',
  templateUrl: './question-form.component.html',
  styleUrls: ['./question-form.component.scss']
})
export class QuestionFormComponent implements OnInit {
  questionForm: FormGroup;
  questionId: string | null = null;
  isEditMode = false;
  loading = false;
  submitting = false;
  error = '';

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private questionService: QuestionService
  ) {
    this.questionForm = this.fb.group({
      question_text: ['', [Validators.required, Validators.minLength(5)]],
      options: this.fb.array([])
    });
  }

  ngOnInit(): void {
    this.questionId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.questionId;

    if (this.isEditMode && this.questionId) {
      this.loadQuestion(this.questionId);
    } else {
      // Add at least two options for a new question
      this.addOption();
      this.addOption();
    }
  }

  get options(): FormArray {
    return this.questionForm.get('options') as FormArray;
  }

  addOption(): void {
    this.options.push(this.fb.group({
      option_text: ['', Validators.required],
      option_value: [0, [Validators.required, Validators.min(0)]]
    }));
  }

  removeOption(index: number): void {
    if (this.options.length > 2) {
      this.options.removeAt(index);
    } else {
      alert('A question must have at least 2 options');
    }
  }

  loadQuestion(id: string): void {
    this.loading = true;
    this.questionService.getQuestion(id)
      .subscribe({
        next: question => {
          this.questionForm.patchValue({
            question_text: question.question_text
          });

          // Clear default options
          while (this.options.length) {
            this.options.removeAt(0);
          }

          // Add existing options
          question.options.forEach(option => {
            this.options.push(this.fb.group({
              id: [option.id],
              option_text: [option.option_text, Validators.required],
              option_value: [option.option_value, [Validators.required, Validators.min(0)]]
            }));
          });

          this.loading = false;
        },
        error: error => {
          this.error = 'Failed to load question';
          this.loading = false;
          console.error('Error loading question:', error);
        }
      });
  }

  onSubmit(): void {
    if (this.questionForm.invalid) {
      return;
    }

    this.submitting = true;
    const questionData: Question = this.questionForm.value;

    if (this.isEditMode && this.questionId) {
      this.questionService.updateQuestion(this.questionId, questionData)
        .subscribe({
          next: () => {
            this.router.navigate(['/questions']);
          },
          error: error => {
            this.error = 'Failed to update question';
            this.submitting = false;
            console.error('Error updating question:', error);
          }
        });
    } else {
      this.questionService.createQuestion(questionData)
        .subscribe({
          next: () => {
            this.router.navigate(['/questions']);
          },
          error: error => {
            this.error = 'Failed to create question';
            this.submitting = false;
            console.error('Error creating question:', error);
          }
        });
    }
  }
}