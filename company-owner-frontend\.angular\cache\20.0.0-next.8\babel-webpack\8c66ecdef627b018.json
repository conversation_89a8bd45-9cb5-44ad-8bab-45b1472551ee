{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/question.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction QuestionsListComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"p\");\n    i0.ɵɵtext(2, \"Loading questions...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QuestionsListComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction QuestionsListComponent_div_11_tr_12_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    const last_r4 = ctx.last;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", option_r3.option_text, \" (\", option_r3.option_value, \")\", !last_r4 ? \", \" : \"\", \" \");\n  }\n}\nfunction QuestionsListComponent_div_11_tr_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtemplate(4, QuestionsListComponent_div_11_tr_12_span_4_Template, 2, 3, \"span\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function QuestionsListComponent_div_11_tr_12_Template_button_click_6_listener() {\n      const question_r5 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.editQuestion(question_r5.id));\n    });\n    i0.ɵɵtext(7, \"Edit\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const question_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(question_r5.question_text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", question_r5.options);\n  }\n}\nfunction QuestionsListComponent_div_11_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 15);\n    i0.ɵɵtext(2, \"No questions found\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QuestionsListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 11)(2, \"table\", 12)(3, \"thead\")(4, \"tr\")(5, \"th\");\n    i0.ɵɵtext(6, \"Question\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"tbody\");\n    i0.ɵɵtemplate(12, QuestionsListComponent_div_11_tr_12_Template, 8, 2, \"tr\", 13)(13, QuestionsListComponent_div_11_tr_13_Template, 3, 0, \"tr\", 8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.questions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.questions.length === 0);\n  }\n}\nexport class QuestionsListComponent {\n  constructor(questionService, router) {\n    this.questionService = questionService;\n    this.router = router;\n    this.questions = [];\n    this.loading = false;\n    this.error = '';\n  }\n  ngOnInit() {\n    this.loadQuestions();\n  }\n  loadQuestions() {\n    this.loading = true;\n    this.questionService.getQuestions().subscribe({\n      next: data => {\n        this.questions = data;\n        this.loading = false;\n      },\n      error: error => {\n        this.error = 'Failed to load questions';\n        this.loading = false;\n        console.error('Error loading questions:', error);\n      }\n    });\n  }\n  createQuestion() {\n    this.router.navigate(['/questions/new']);\n  }\n  editQuestion(id) {\n    this.router.navigate(['/questions/edit', id]);\n  }\n  static {\n    this.ɵfac = function QuestionsListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuestionsListComponent)(i0.ɵɵdirectiveInject(i1.QuestionService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QuestionsListComponent,\n      selectors: [[\"app-questions-list\"]],\n      decls: 12,\n      vars: 3,\n      consts: [[1, \"row\"], [1, \"col-sm-12\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"card-body\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\"], [1, \"alert\", \"alert-danger\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", \"mr-2\", 3, \"click\"], [\"colspan\", \"3\", 1, \"text-center\"]],\n      template: function QuestionsListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h5\");\n          i0.ɵɵtext(5, \"Questions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function QuestionsListComponent_Template_button_click_6_listener() {\n            return ctx.createQuestion();\n          });\n          i0.ɵɵtext(7, \"Create Question\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 5);\n          i0.ɵɵtemplate(9, QuestionsListComponent_div_9_Template, 3, 0, \"div\", 6)(10, QuestionsListComponent_div_10_Template, 2, 1, \"div\", 7)(11, QuestionsListComponent_div_11_Template, 14, 2, \"div\", 8);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "error", "ɵɵtextInterpolate3", "option_r3", "option_text", "option_value", "last_r4", "ɵɵtemplate", "QuestionsListComponent_div_11_tr_12_span_4_Template", "ɵɵlistener", "QuestionsListComponent_div_11_tr_12_Template_button_click_6_listener", "question_r5", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵnextContext", "ɵɵresetView", "editQuestion", "id", "ɵɵtextInterpolate", "question_text", "ɵɵproperty", "options", "QuestionsListComponent_div_11_tr_12_Template", "QuestionsListComponent_div_11_tr_13_Template", "questions", "length", "QuestionsListComponent", "constructor", "questionService", "router", "loading", "ngOnInit", "loadQuestions", "getQuestions", "subscribe", "next", "data", "console", "createQuestion", "navigate", "ɵɵdirectiveInject", "i1", "QuestionService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "QuestionsListComponent_Template", "rf", "ctx", "QuestionsListComponent_Template_button_click_6_listener", "QuestionsListComponent_div_9_Template", "QuestionsListComponent_div_10_Template", "QuestionsListComponent_div_11_Template"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\questions\\questions-list\\questions-list.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\questions\\questions-list\\questions-list.component.html"], "sourcesContent": ["// src/app/demo/pages/questions/question-list/question-list.component.ts\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { QuestionService } from '../../../../core/services/question.service';\r\nimport { Question } from '../../../../core/models/question.model';\r\n\r\n@Component({\r\n  selector: 'app-questions-list',\r\n  templateUrl: './questions-list.component.html',\r\n  styleUrls: ['./questions-list.component.scss']\r\n})\r\nexport class QuestionsListComponent implements OnInit {\r\n  questions: Question[] = [];\r\n  loading = false;\r\n  error = '';\r\n\r\n  constructor(\r\n    private questionService: QuestionService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadQuestions();\r\n  }\r\n\r\n  loadQuestions(): void {\r\n    this.loading = true;\r\n    this.questionService.getQuestions()\r\n      .subscribe({\r\n        next: (data) => {\r\n          this.questions = data;\r\n          this.loading = false;\r\n        },\r\n        error: (error) => {\r\n          this.error = 'Failed to load questions';\r\n          this.loading = false;\r\n          console.error('Error loading questions:', error);\r\n        }\r\n      });\r\n  }\r\n\r\n  createQuestion(): void {\r\n    this.router.navigate(['/questions/new']);\r\n  }\r\n\r\n  editQuestion(id: string): void {\r\n    this.router.navigate(['/questions/edit', id]);\r\n  }\r\n}\r\n", "<div class=\"row\">\n  <div class=\"col-sm-12\">\n    <div class=\"card\">\n      <div class=\"card-header d-flex justify-content-between\">\n        <h5>Questions</h5>\n        <button class=\"btn btn-primary\" (click)=\"createQuestion()\">Create Question</button>\n      </div>\n      <div class=\"card-body\">\n        <div *ngIf=\"loading\" class=\"text-center\">\n          <p>Loading questions...</p>\n        </div>\n        \n        <div *ngIf=\"error\" class=\"alert alert-danger\">\n          {{ error }}\n        </div>\n        \n        <div *ngIf=\"!loading && !error\">\n          <div class=\"table-responsive\">\n            <table class=\"table table-hover\">\n              <thead>\n                <tr>\n                  <th>Question</th>\n                  <th>Options</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let question of questions\">\n                  <td>{{ question.question_text }}</td>\n                  <td>\n                    <span *ngFor=\"let option of question.options; let last = last\">\n                      {{ option.option_text }} ({{ option.option_value }}){{ !last ? ', ' : '' }}\n                    </span>\n                  </td>\n                  <td>\n                    <button class=\"btn btn-sm btn-primary mr-2\" (click)=\"editQuestion(question.id)\">Edit</button>\n                  </td>\n                </tr>\n                <tr *ngIf=\"questions.length === 0\">\n                  <td colspan=\"3\" class=\"text-center\">No questions found</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>"], "mappings": ";;;;;;ICSUA,EADF,CAAAC,cAAA,aAAyC,QACpC;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IACzBF,EADyB,CAAAG,YAAA,EAAI,EACvB;;;;;IAENH,EAAA,CAAAC,cAAA,cAA8C;IAC5CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAgBYP,EAAA,CAAAC,cAAA,WAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAQ,kBAAA,MAAAC,SAAA,CAAAC,WAAA,QAAAD,SAAA,CAAAE,YAAA,QAAAC,OAAA,kBACF;;;;;;IAJFZ,EADF,CAAAC,cAAA,SAAuC,SACjC;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAa,UAAA,IAAAC,mDAAA,mBAA+D;IAGjEd,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAC8E;IAApCD,EAAA,CAAAe,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAShB,MAAA,CAAAiB,YAAA,CAAAN,WAAA,CAAAO,EAAA,CAAyB;IAAA,EAAC;IAACxB,EAAA,CAAAE,MAAA,WAAI;IAExFF,EAFwF,CAAAG,YAAA,EAAS,EAC1F,EACF;;;;IATCH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAyB,iBAAA,CAAAR,WAAA,CAAAS,aAAA,CAA4B;IAEL1B,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAA2B,UAAA,YAAAV,WAAA,CAAAW,OAAA,CAAqB;;;;;IAShD5B,EADF,CAAAC,cAAA,SAAmC,aACG;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IACxDF,EADwD,CAAAG,YAAA,EAAK,EACxD;;;;;IAnBHH,EALV,CAAAC,cAAA,UAAgC,cACA,gBACK,YACxB,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAEfF,EAFe,CAAAG,YAAA,EAAK,EACb,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IAYLD,EAXA,CAAAa,UAAA,KAAAgB,4CAAA,iBAAuC,KAAAC,4CAAA,gBAWJ;IAM3C9B,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACF;;;;IAjB2BH,EAAA,CAAAI,SAAA,IAAY;IAAZJ,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAAyB,SAAA,CAAY;IAWhC/B,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAyB,SAAA,CAAAC,MAAA,OAA4B;;;AD3BjD,OAAM,MAAOC,sBAAsB;EAKjCC,YACUC,eAAgC,EAChCC,MAAc;IADd,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IANhB,KAAAL,SAAS,GAAe,EAAE;IAC1B,KAAAM,OAAO,GAAG,KAAK;IACf,KAAA9B,KAAK,GAAG,EAAE;EAKP;EAEH+B,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAA,aAAaA,CAAA;IACX,IAAI,CAACF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,eAAe,CAACK,YAAY,EAAE,CAChCC,SAAS,CAAC;MACTC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACZ,SAAS,GAAGY,IAAI;QACrB,IAAI,CAACN,OAAO,GAAG,KAAK;MACtB,CAAC;MACD9B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,0BAA0B;QACvC,IAAI,CAAC8B,OAAO,GAAG,KAAK;QACpBO,OAAO,CAACrC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEAsC,cAAcA,CAAA;IACZ,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEAvB,YAAYA,CAACC,EAAU;IACrB,IAAI,CAACY,MAAM,CAACU,QAAQ,CAAC,CAAC,iBAAiB,EAAEtB,EAAE,CAAC,CAAC;EAC/C;;;uCApCWS,sBAAsB,EAAAjC,EAAA,CAAA+C,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAjD,EAAA,CAAA+C,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAtBlB,sBAAsB;MAAAmB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP3B1D,EAJR,CAAAC,cAAA,aAAiB,aACQ,aACH,aACwC,SAClD;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,gBAA2D;UAA3BD,EAAA,CAAAe,UAAA,mBAAA6C,wDAAA;YAAA,OAASD,GAAA,CAAAd,cAAA,EAAgB;UAAA,EAAC;UAAC7C,EAAA,CAAAE,MAAA,sBAAe;UAC5EF,EAD4E,CAAAG,YAAA,EAAS,EAC/E;UACNH,EAAA,CAAAC,cAAA,aAAuB;UASrBD,EARA,CAAAa,UAAA,IAAAgD,qCAAA,iBAAyC,KAAAC,sCAAA,iBAIK,KAAAC,sCAAA,kBAId;UAgCxC/D,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;UAxCQH,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAA2B,UAAA,SAAAgC,GAAA,CAAAtB,OAAA,CAAa;UAIbrC,EAAA,CAAAI,SAAA,EAAW;UAAXJ,EAAA,CAAA2B,UAAA,SAAAgC,GAAA,CAAApD,KAAA,CAAW;UAIXP,EAAA,CAAAI,SAAA,EAAwB;UAAxBJ,EAAA,CAAA2B,UAAA,UAAAgC,GAAA,CAAAtB,OAAA,KAAAsB,GAAA,CAAApD,KAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}