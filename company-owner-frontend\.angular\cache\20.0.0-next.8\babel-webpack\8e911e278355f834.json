{"ast": null, "code": "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\nimport packageInfo from '../../package.json';\nexport const environment = {\n  appVersion: packageInfo.version,\n  production: true,\n  apiUrl: \"http://localhost:3000\"\n};\n/*\n * For easier debugging in development mode, you can import the following file\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\n *\n * This import should be commented out in production mode because it will have a negative impact\n * on performance if an error is thrown.\n */\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.", "map": {"version": 3, "names": ["packageInfo", "environment", "appVersion", "version", "production", "apiUrl"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\environments\\environment.ts"], "sourcesContent": ["// This file can be replaced during build by using the `fileReplacements` array.\r\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\r\n// The list of file replacements can be found in `angular.json`.\r\n\r\nimport packageInfo from '../../package.json';\r\n\r\nexport const environment = {\r\n  appVersion: packageInfo.version,\r\n  production: true,\r\n  apiUrl: \"http://localhost:3000\"\r\n};\r\n/*\r\n * For easier debugging in development mode, you can import the following file\r\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\r\n *\r\n * This import should be commented out in production mode because it will have a negative impact\r\n * on performance if an error is thrown.\r\n */\r\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,OAAOA,WAAW,MAAM,oBAAoB;AAE5C,OAAO,MAAMC,WAAW,GAAG;EACzBC,UAAU,EAAEF,WAAW,CAACG,OAAO;EAC/BC,UAAU,EAAE,IAAI;EAChBC,MAAM,EAAE;CACT;AACD;;;;;;;AAOA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}