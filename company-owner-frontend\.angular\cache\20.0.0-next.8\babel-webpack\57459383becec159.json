{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  children: [{\n    path: 'signin',\n    loadComponent: () => import('./auth-signin/auth-signin.component')\n  }, {\n    path: 'signup',\n    loadComponent: () => import('./auth-signup/auth-signup.component')\n  }]\n}];\nexport class AuthenticationRoutingModule {\n  static {\n    this.ɵfac = function AuthenticationRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthenticationRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AuthenticationRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthenticationRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "routes", "path", "children", "loadComponent", "AuthenticationRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\demo\\pages\\authentication\\authentication-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    children: [\r\n      {\r\n        path: 'signin',\r\n        loadComponent: () => import('./auth-signin/auth-signin.component')\r\n      },\r\n      {\r\n        path: 'signup',\r\n        loadComponent: () => import('./auth-signup/auth-signup.component')\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AuthenticationRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;;;AAEtD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,QAAQ;IACdE,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC;GAClE,EACD;IACEF,IAAI,EAAE,QAAQ;IACdE,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC;GAClE;CAEJ,CACF;AAMD,OAAM,MAAOC,2BAA2B;;;uCAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5BL,YAAY,CAACM,QAAQ,CAACL,MAAM,CAAC,EAC7BD,YAAY;IAAA;EAAA;;;2EAEXK,2BAA2B;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAF5BT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}