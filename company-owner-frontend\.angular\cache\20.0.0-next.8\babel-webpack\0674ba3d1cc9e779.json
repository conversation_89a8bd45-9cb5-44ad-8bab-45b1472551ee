{"ast": null, "code": "// angular import\nimport { inject } from '@angular/core';\n// bootstrap import\nimport { NgbDropdownConfig } from '@ng-bootstrap/ng-bootstrap';\n// project import\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nexport class NavRightComponent {\n  // public props\n  // constructor\n  constructor() {\n    const config = inject(NgbDropdownConfig);\n    config.placement = 'bottom-right';\n  }\n  static {\n    this.ɵfac = function NavRightComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NavRightComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavRightComponent,\n      selectors: [[\"app-nav-right\"]],\n      features: [i0.ɵɵProvidersFeature([NgbDropdownConfig])],\n      decls: 88,\n      vars: 0,\n      consts: [[1, \"navbar-nav\"], [\"ngbDropdown\", \"\", \"placement\", \"auto\", 1, \"dropdown\"], [\"ngbDropdownToggle\", \"\", \"href\", \"javascript:\", \"data-toggle\", \"dropdown\"], [1, \"icon\", \"feather\", \"icon-bell\"], [\"ngbDropdownMenu\", \"\", 1, \"dropdown-menu\", \"dropdown-menu-end\", \"notification\"], [1, \"notification-head\"], [1, \"d-inline-block\", \"m-b-0\"], [1, \"float-end\"], [\"href\", \"javascript:\", 1, \"m-r-10\", \"text-decoration-none\"], [\"href\", \"javascript:\", 1, \"text-decoration-none\"], [1, \"notification-body\"], [1, \"n-title\"], [1, \"m-b-0\"], [1, \"notification\"], [1, \"d-flex\"], [\"src\", \"assets/images/user/avatar-1.jpg\", \"alt\", \"Generic placeholder image\", 1, \"img-radius\"], [1, \"flex-grow-1\"], [1, \"n-time\", \"text-muted\"], [1, \"icon\", \"feather\", \"icon-clock\", \"m-r-10\"], [\"src\", \"assets/images/user/avatar-2.jpg\", \"alt\", \"Generic placeholder image\", 1, \"img-radius\"], [\"src\", \"assets/images/user/avatar-3.jpg\", \"alt\", \"Generic placeholder image\", 1, \"img-radius\"], [1, \"notification-footer\"], [\"href\", \"javascript:\"], [\"ngbDropdown\", \"\", \"placement\", \"auto\", 1, \"dropdown\", \"drp-user\"], [\"href\", \"javascript:\", \"ngbDropdownToggle\", \"\", \"data-toggle\", \"dropdown\"], [1, \"icon\", \"feather\", \"icon-settings\"], [\"ngbDropdownMenu\", \"\", 1, \"dropdown-menu\", \"dropdown-menu-end\", \"profile-notification\"], [1, \"pro-head\"], [\"src\", \"assets/images/user/avatar-1.jpg\", \"alt\", \"User-Profile-Image\", 1, \"img-radius\"], [\"href\", \"javascript:\", \"title\", \"Logout\", 1, \"dud-logout\"], [1, \"feather\", \"icon-log-out\"], [1, \"pro-body\"], [\"href\", \"javascript:\", 1, \"dropdown-item\"], [1, \"feather\", \"icon-settings\"], [1, \"feather\", \"icon-user\"], [1, \"feather\", \"icon-mail\"], [1, \"feather\", \"icon-lock\"]],\n      template: function NavRightComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ul\", 0)(1, \"li\")(2, \"div\", 1)(3, \"a\", 2);\n          i0.ɵɵelement(4, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"h6\", 6);\n          i0.ɵɵtext(8, \"Notifications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"a\", 8);\n          i0.ɵɵtext(11, \"mark as read\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"a\", 9);\n          i0.ɵɵtext(13, \"clear all\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"ul\", 10)(15, \"li\", 11)(16, \"p\", 12);\n          i0.ɵɵtext(17, \"NEW\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"li\", 13)(19, \"div\", 14);\n          i0.ɵɵelement(20, \"img\", 15);\n          i0.ɵɵelementStart(21, \"div\", 16)(22, \"p\")(23, \"strong\");\n          i0.ɵɵtext(24, \"John Doe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\", 17);\n          i0.ɵɵelement(26, \"i\", 18);\n          i0.ɵɵtext(27, \" now \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"p\");\n          i0.ɵɵtext(29, \"New ticket Added\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(30, \"li\", 11)(31, \"p\", 12);\n          i0.ɵɵtext(32, \"EARLIER\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"li\", 13)(34, \"div\", 14);\n          i0.ɵɵelement(35, \"img\", 19);\n          i0.ɵɵelementStart(36, \"div\", 16)(37, \"p\")(38, \"strong\");\n          i0.ɵɵtext(39, \"Joseph William\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"span\", 17);\n          i0.ɵɵelement(41, \"i\", 18);\n          i0.ɵɵtext(42, \" 10 min \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"p\");\n          i0.ɵɵtext(44, \"Prchace New Theme and make payment\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(45, \"li\", 13)(46, \"div\", 14);\n          i0.ɵɵelement(47, \"img\", 20);\n          i0.ɵɵelementStart(48, \"div\", 16)(49, \"p\")(50, \"strong\");\n          i0.ɵɵtext(51, \"Sara Soudein\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"span\", 17);\n          i0.ɵɵelement(53, \"i\", 18);\n          i0.ɵɵtext(54, \" 30 min \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"p\");\n          i0.ɵɵtext(56, \"currently login\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(57, \"div\", 21)(58, \"a\", 22);\n          i0.ɵɵtext(59, \"show all\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(60, \"li\")(61, \"div\", 23)(62, \"a\", 24);\n          i0.ɵɵelement(63, \"i\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"div\", 26)(65, \"div\", 27);\n          i0.ɵɵelement(66, \"img\", 28);\n          i0.ɵɵelementStart(67, \"span\");\n          i0.ɵɵtext(68, \"John Doe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"a\", 29);\n          i0.ɵɵelement(70, \"i\", 30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"ul\", 31)(72, \"li\")(73, \"a\", 32);\n          i0.ɵɵelement(74, \"i\", 33);\n          i0.ɵɵtext(75, \" Settings \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"li\")(77, \"a\", 32);\n          i0.ɵɵelement(78, \"i\", 34);\n          i0.ɵɵtext(79, \" Profile \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"li\")(81, \"a\", 32);\n          i0.ɵɵelement(82, \"i\", 35);\n          i0.ɵɵtext(83, \" My Messages \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(84, \"li\")(85, \"a\", 32);\n          i0.ɵɵelement(86, \"i\", 36);\n          i0.ɵɵtext(87, \" Lock Screen \");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n      },\n      dependencies: [SharedModule, i1.NgbDropdown, i1.NgbDropdownToggle, i1.NgbDropdownMenu],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["inject", "NgbDropdownConfig", "SharedModule", "NavRightComponent", "constructor", "config", "placement", "selectors", "features", "i0", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "NavRightComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "i1", "NgbDropdown", "NgbDropdownToggle", "NgbDropdownMenu", "styles"], "sources": ["D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\nav-bar\\nav-right\\nav-right.component.ts", "D:\\employee-survey-app\\company-owner-frontend\\src\\app\\theme\\layout\\admin\\nav-bar\\nav-right\\nav-right.component.html"], "sourcesContent": ["// angular import\r\nimport { Component, inject } from '@angular/core';\r\n\r\n// bootstrap import\r\nimport { NgbDropdownConfig } from '@ng-bootstrap/ng-bootstrap';\r\n\r\n// project import\r\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\r\n\r\n@Component({\r\n  selector: 'app-nav-right',\r\n  imports: [SharedModule],\r\n  templateUrl: './nav-right.component.html',\r\n  styleUrls: ['./nav-right.component.scss'],\r\n  providers: [NgbDropdownConfig]\r\n})\r\nexport class NavRightComponent {\r\n  // public props\r\n\r\n  // constructor\r\n  constructor() {\r\n    const config = inject(NgbDropdownConfig);\r\n\r\n    config.placement = 'bottom-right';\r\n  }\r\n}\r\n", "<ul class=\"navbar-nav\">\r\n  <li>\r\n    <div class=\"dropdown\" ngbDropdown placement=\"auto\">\r\n      <a ngbDropdownToggle href=\"javascript:\" data-toggle=\"dropdown\"><i class=\"icon feather icon-bell\"></i></a>\r\n      <div ngbDropdownMenu class=\"dropdown-menu dropdown-menu-end notification\">\r\n        <div class=\"notification-head\">\r\n          <h6 class=\"d-inline-block m-b-0\">Notifications</h6>\r\n          <div class=\"float-end\">\r\n            <a href=\"javascript:\" class=\"m-r-10 text-decoration-none\">mark as read</a>\r\n            <a href=\"javascript:\" class=\"text-decoration-none\">clear all</a>\r\n          </div>\r\n        </div>\r\n        <ul class=\"notification-body\">\r\n          <li class=\"n-title\">\r\n            <p class=\"m-b-0\">NEW</p>\r\n          </li>\r\n          <li class=\"notification\">\r\n            <div class=\"d-flex\">\r\n              <img class=\"img-radius\" src=\"assets/images/user/avatar-1.jpg\" alt=\"Generic placeholder image\" />\r\n              <div class=\"flex-grow-1\">\r\n                <p>\r\n                  <strong><PERSON></strong>\r\n                  <span class=\"n-time text-muted\">\r\n                    <i class=\"icon feather icon-clock m-r-10\"></i>\r\n                    now\r\n                  </span>\r\n                </p>\r\n                <p>New ticket Added</p>\r\n              </div>\r\n            </div>\r\n          </li>\r\n          <li class=\"n-title\">\r\n            <p class=\"m-b-0\">EARLIER</p>\r\n          </li>\r\n          <li class=\"notification\">\r\n            <div class=\"d-flex\">\r\n              <img class=\"img-radius\" src=\"assets/images/user/avatar-2.jpg\" alt=\"Generic placeholder image\" />\r\n              <div class=\"flex-grow-1\">\r\n                <p>\r\n                  <strong>Joseph William</strong>\r\n                  <span class=\"n-time text-muted\">\r\n                    <i class=\"icon feather icon-clock m-r-10\"></i>\r\n                    10 min\r\n                  </span>\r\n                </p>\r\n                <p>Prchace New Theme and make payment</p>\r\n              </div>\r\n            </div>\r\n          </li>\r\n          <li class=\"notification\">\r\n            <div class=\"d-flex\">\r\n              <img class=\"img-radius\" src=\"assets/images/user/avatar-3.jpg\" alt=\"Generic placeholder image\" />\r\n              <div class=\"flex-grow-1\">\r\n                <p>\r\n                  <strong>Sara Soudein</strong>\r\n                  <span class=\"n-time text-muted\">\r\n                    <i class=\"icon feather icon-clock m-r-10\"></i>\r\n                    30 min\r\n                  </span>\r\n                </p>\r\n                <p>currently login</p>\r\n              </div>\r\n            </div>\r\n          </li>\r\n        </ul>\r\n        <div class=\"notification-footer\">\r\n          <a href=\"javascript:\">show all</a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </li>\r\n  <li>\r\n    <div class=\"dropdown drp-user\" ngbDropdown placement=\"auto\">\r\n      <a href=\"javascript:\" ngbDropdownToggle data-toggle=\"dropdown\">\r\n        <i class=\"icon feather icon-settings\"></i>\r\n      </a>\r\n      <div class=\"dropdown-menu dropdown-menu-end profile-notification\" ngbDropdownMenu>\r\n        <div class=\"pro-head\">\r\n          <img src=\"assets/images/user/avatar-1.jpg\" class=\"img-radius\" alt=\"User-Profile-Image\" />\r\n          <span>John Doe</span>\r\n          <a href=\"javascript:\" class=\"dud-logout\" title=\"Logout\">\r\n            <i class=\"feather icon-log-out\"></i>\r\n          </a>\r\n        </div>\r\n        <ul class=\"pro-body\">\r\n          <li>\r\n            <a href=\"javascript:\" class=\"dropdown-item\">\r\n              <i class=\"feather icon-settings\"></i>\r\n              Settings\r\n            </a>\r\n          </li>\r\n          <li>\r\n            <a href=\"javascript:\" class=\"dropdown-item\">\r\n              <i class=\"feather icon-user\"></i>\r\n              Profile\r\n            </a>\r\n          </li>\r\n          <li>\r\n            <a href=\"javascript:\" class=\"dropdown-item\">\r\n              <i class=\"feather icon-mail\"></i>\r\n              My Messages\r\n            </a>\r\n          </li>\r\n          <li>\r\n            <a href=\"javascript:\" class=\"dropdown-item\">\r\n              <i class=\"feather icon-lock\"></i>\r\n              Lock Screen\r\n            </a>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n    </div>\r\n  </li>\r\n</ul>\r\n"], "mappings": "AAAA;AACA,SAAoBA,MAAM,QAAQ,eAAe;AAEjD;AACA,SAASC,iBAAiB,QAAQ,4BAA4B;AAE9D;AACA,SAASC,YAAY,QAAQ,oCAAoC;;;AASjE,OAAM,MAAOC,iBAAiB;EAC5B;EAEA;EACAC,YAAA;IACE,MAAMC,MAAM,GAAGL,MAAM,CAACC,iBAAiB,CAAC;IAExCI,MAAM,CAACC,SAAS,GAAG,cAAc;EACnC;;;uCARWH,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAI,SAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,kBAAA,CAFjB,CAACT,iBAAiB,CAAC;MAAAU,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX1BP,EAHN,CAAAS,cAAA,YAAuB,SACjB,aACiD,WACc;UAAAT,EAAA,CAAAU,SAAA,WAAsC;UAAAV,EAAA,CAAAW,YAAA,EAAI;UAGrGX,EAFJ,CAAAS,cAAA,aAA0E,aACzC,YACI;UAAAT,EAAA,CAAAY,MAAA,oBAAa;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UAEjDX,EADF,CAAAS,cAAA,aAAuB,YACqC;UAAAT,EAAA,CAAAY,MAAA,oBAAY;UAAAZ,EAAA,CAAAW,YAAA,EAAI;UAC1EX,EAAA,CAAAS,cAAA,YAAmD;UAAAT,EAAA,CAAAY,MAAA,iBAAS;UAEhEZ,EAFgE,CAAAW,YAAA,EAAI,EAC5D,EACF;UAGFX,EAFJ,CAAAS,cAAA,cAA8B,cACR,aACD;UAAAT,EAAA,CAAAY,MAAA,WAAG;UACtBZ,EADsB,CAAAW,YAAA,EAAI,EACrB;UAEHX,EADF,CAAAS,cAAA,cAAyB,eACH;UAClBT,EAAA,CAAAU,SAAA,eAAgG;UAG5FV,EAFJ,CAAAS,cAAA,eAAyB,SACpB,cACO;UAAAT,EAAA,CAAAY,MAAA,gBAAQ;UAAAZ,EAAA,CAAAW,YAAA,EAAS;UACzBX,EAAA,CAAAS,cAAA,gBAAgC;UAC9BT,EAAA,CAAAU,SAAA,aAA8C;UAC9CV,EAAA,CAAAY,MAAA,aACF;UACFZ,EADE,CAAAW,YAAA,EAAO,EACL;UACJX,EAAA,CAAAS,cAAA,SAAG;UAAAT,EAAA,CAAAY,MAAA,wBAAgB;UAGzBZ,EAHyB,CAAAW,YAAA,EAAI,EACnB,EACF,EACH;UAEHX,EADF,CAAAS,cAAA,cAAoB,aACD;UAAAT,EAAA,CAAAY,MAAA,eAAO;UAC1BZ,EAD0B,CAAAW,YAAA,EAAI,EACzB;UAEHX,EADF,CAAAS,cAAA,cAAyB,eACH;UAClBT,EAAA,CAAAU,SAAA,eAAgG;UAG5FV,EAFJ,CAAAS,cAAA,eAAyB,SACpB,cACO;UAAAT,EAAA,CAAAY,MAAA,sBAAc;UAAAZ,EAAA,CAAAW,YAAA,EAAS;UAC/BX,EAAA,CAAAS,cAAA,gBAAgC;UAC9BT,EAAA,CAAAU,SAAA,aAA8C;UAC9CV,EAAA,CAAAY,MAAA,gBACF;UACFZ,EADE,CAAAW,YAAA,EAAO,EACL;UACJX,EAAA,CAAAS,cAAA,SAAG;UAAAT,EAAA,CAAAY,MAAA,0CAAkC;UAG3CZ,EAH2C,CAAAW,YAAA,EAAI,EACrC,EACF,EACH;UAEHX,EADF,CAAAS,cAAA,cAAyB,eACH;UAClBT,EAAA,CAAAU,SAAA,eAAgG;UAG5FV,EAFJ,CAAAS,cAAA,eAAyB,SACpB,cACO;UAAAT,EAAA,CAAAY,MAAA,oBAAY;UAAAZ,EAAA,CAAAW,YAAA,EAAS;UAC7BX,EAAA,CAAAS,cAAA,gBAAgC;UAC9BT,EAAA,CAAAU,SAAA,aAA8C;UAC9CV,EAAA,CAAAY,MAAA,gBACF;UACFZ,EADE,CAAAW,YAAA,EAAO,EACL;UACJX,EAAA,CAAAS,cAAA,SAAG;UAAAT,EAAA,CAAAY,MAAA,uBAAe;UAI1BZ,EAJ0B,CAAAW,YAAA,EAAI,EAClB,EACF,EACH,EACF;UAEHX,EADF,CAAAS,cAAA,eAAiC,aACT;UAAAT,EAAA,CAAAY,MAAA,gBAAQ;UAItCZ,EAJsC,CAAAW,YAAA,EAAI,EAC9B,EACF,EACF,EACH;UAGDX,EAFJ,CAAAS,cAAA,UAAI,eAC0D,aACK;UAC7DT,EAAA,CAAAU,SAAA,aAA0C;UAC5CV,EAAA,CAAAW,YAAA,EAAI;UAEFX,EADF,CAAAS,cAAA,eAAkF,eAC1D;UACpBT,EAAA,CAAAU,SAAA,eAAyF;UACzFV,EAAA,CAAAS,cAAA,YAAM;UAAAT,EAAA,CAAAY,MAAA,gBAAQ;UAAAZ,EAAA,CAAAW,YAAA,EAAO;UACrBX,EAAA,CAAAS,cAAA,aAAwD;UACtDT,EAAA,CAAAU,SAAA,aAAoC;UAExCV,EADE,CAAAW,YAAA,EAAI,EACA;UAGFX,EAFJ,CAAAS,cAAA,cAAqB,UACf,aAC0C;UAC1CT,EAAA,CAAAU,SAAA,aAAqC;UACrCV,EAAA,CAAAY,MAAA,kBACF;UACFZ,EADE,CAAAW,YAAA,EAAI,EACD;UAEHX,EADF,CAAAS,cAAA,UAAI,aAC0C;UAC1CT,EAAA,CAAAU,SAAA,aAAiC;UACjCV,EAAA,CAAAY,MAAA,iBACF;UACFZ,EADE,CAAAW,YAAA,EAAI,EACD;UAEHX,EADF,CAAAS,cAAA,UAAI,aAC0C;UAC1CT,EAAA,CAAAU,SAAA,aAAiC;UACjCV,EAAA,CAAAY,MAAA,qBACF;UACFZ,EADE,CAAAW,YAAA,EAAI,EACD;UAEHX,EADF,CAAAS,cAAA,UAAI,aAC0C;UAC1CT,EAAA,CAAAU,SAAA,aAAiC;UACjCV,EAAA,CAAAY,MAAA,qBACF;UAMZZ,EANY,CAAAW,YAAA,EAAI,EACD,EACF,EACD,EACF,EACH,EACF;;;qBDtGOlB,YAAY,EAAAoB,EAAA,CAAAC,WAAA,EAAAD,EAAA,CAAAE,iBAAA,EAAAF,EAAA,CAAAG,eAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}