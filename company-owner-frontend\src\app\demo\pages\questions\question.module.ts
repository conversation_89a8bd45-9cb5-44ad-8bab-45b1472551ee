import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { QuestionsListComponent } from './questions-list/questions-list.component';
import { QuestionFormComponent } from './questions-form/questions-form.component';
import { QuestionsComponent } from './questions.component';

const routes: Routes = [
  {
    path: '',
    component: QuestionsComponent,
    children: [
      {
        path: '',
        component: QuestionsListComponent
      },
      {
        path: 'new',
        component: QuestionFormComponent
      },
      {
        path: 'edit/:id',
        component: QuestionFormComponent
      }
    ]
  }
];

@NgModule({
  declarations: [
    QuestionsComponent,
    QuestionsListComponent,
    QuestionFormComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes)
  ]
})
export class QuestionModule { }